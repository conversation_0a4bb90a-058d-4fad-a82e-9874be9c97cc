# Исправление проблемы с двойной кнопкой в критическом окне

## Проблема
В полноэкранном критическом окне (уровень эскалации 4+) кнопка "Take a break now" отображалась с двойным эффектом - внутри зеленой кнопки была видна еще одна прозрачная кнопка.

## Причина
Проблема была в методе `setupTakeBreakButton()` в файле `CriticalZoneWindow.swift`:

```swift
// НЕПРАВИЛЬНО - заменяли основной слой кнопки
takeBreakButton.layer = gradientLayer
```

Это приводило к конфликту между собственным слоем NSButton и нашим градиентным слоем.

## Решение
1. **Убрали стандартную рамку кнопки**: `takeBreakButton.isBordered = false`
2. **Добавили градиент как подслой**: `takeBreakButton.layer?.insertSublayer(gradientLayer, at: 0)`
3. **Настроили прозрачный фон основного слоя**: `takeBreakButton.layer?.backgroundColor = NSColor.clear.cgColor`
4. **Добавили автоматическое обновление размера градиента** при изменении layout

## Изменения в коде

### CriticalZoneWindow.swift
- Исправлен метод `setupTakeBreakButton()`
- Добавлен класс `LayoutObservingView` для отслеживания изменений layout
- Добавлен callback для обновления размера градиента

### AppDelegate.swift
- Добавлен тестовый пункт меню "🚨 ТЕСТ КРИТИЧЕСКОГО ОКНА"
- Добавлен метод `testCriticalWindow()` для демонстрации исправления

## Новая функция: Обратный отсчет перед критическим окном

### Проблема UX
Критическое окно появлялось внезапно и блокировало весь экран, что могло прервать важную работу (сохранение файлов, завершение задач).

### Решение
Добавлен **компактный предупреждающий обратный отсчет** перед показом критического окна:

1. **Компактное окно 300x200** по центру экрана вместо полноэкранного
2. **Прозрачный фон** - пользователь может кликать везде под цифрами
3. **Красивая анимация**: scale + fade + цветовая индикация (зеленый → желтый → красный)
4. **Возможность отсрочки**: ESC для отсрочки на 2 минуты
5. **Полупрозрачный фон цифр** для видимости на любом фоне
6. **НЕ блокирует работу** - можно сохранять файлы, кликать мышкой

### Новые файлы
- `SimplePomodoroTest/CountdownWindow.swift` - окно обратного отсчета
- `Tests/CountdownWindowTest.swift` - тесты для нового компонента

## Как протестировать

### Тест 1: Критическое окно (без отсчета)
1. **Запустите приложение uProd**
2. **Кликните на иконку в menu bar**
3. **Выберите "🧪 Другие тесты" → "🚨 ТЕСТ КРИТИЧЕСКОГО ОКНА"**
4. **Проверьте кнопку**: должна быть одна зеленая кнопка без двойного эффекта
5. **Окно автоматически закроется через 5 секунд**

### Тест 2: Компактный обратный отсчет + критическое окно
1. **Запустите приложение uProd**
2. **Кликните на иконку в menu bar**
3. **Выберите "🧪 Другие тесты" → "⏰ ТЕСТ ОБРАТНОГО ОТСЧЕТА"**
4. **Увидите**: Компактное окно 300x200 по центру с цифрами 5, 4, 3, 2, 1
5. **Попробуйте кликать** мышкой везде - должно работать!
6. **Попробуйте**: нажать ESC во время отсчета для отсрочки
7. **Затем**: критическое окно с исправленной кнопкой

### ✨ Ключевые улучшения UX:
- **Не блокирует экран** - можно продолжать работать
- **Видно на любом фоне** - полупрозрачный черный фон цифр
- **Компактный размер** - не мешает обзору
- **Сохранена функциональность ESC** для отсрочки

## Альтернативный способ тестирования
Можно также дождаться естественного появления критического окна:
1. Запустите неформальную сессию (работайте 52+ минут)
2. Игнорируйте напоминания 11+ минут
3. Критическое окно появится автоматически

## Результат
✅ Кнопка "Take a break now" теперь отображается корректно - одна зеленая кнопка с градиентом без двойного эффекта.

## Файлы изменены
- `SimplePomodoroTest/CriticalZoneWindow.swift` - основное исправление
- `SimplePomodoroTest/AppDelegate.swift` - добавлен тестовый метод
- `Tests/CriticalZoneWindowTest.swift` - тест для проверки исправления
