# Миграция InformalSessionDetector → ActivityStateManager

## 🚨 ВАЖНО: InformalSessionDetector УСТАРЕЛ!

### ⚠️ Статус миграции
- **Старая система**: `InformalSessionDetector` - **НЕ ИСПОЛЬЗОВАТЬ!**
- **Новая система**: `ActivityStateManager` - **ИСПОЛЬЗУЙТЕ ЭТО!**
- **Дата миграции**: 28 июля 2025
- **Статус**: ✅ Завершена, тестируется

### 🔄 Что изменилось

#### Старая система (УСТАРЕЛА):
```swift
// ❌ НЕ ДЕЛАЙТЕ ТАК!
let detector = InformalSessionDetector()
detector.recordMinuteActivity(isActive: true)
detector.onRestSuggestionNeeded = { showWindow() }
```

#### Новая система (ИСПОЛЬЗУЙТЕ):
```swift
// ✅ ПРАВИЛЬНО!
let manager = ActivityStateManager()
manager.recordMinuteActivity(isActive: true)
manager.onInformalRestSuggestion = { showWindow() }
```

### 📋 Функциональность

| Функция | Старая система | Новая система | Статус |
|---------|---------------|---------------|--------|
| Отслеживание 52-минутных окон | ✅ InformalSessionDetector | ✅ ActivityStateManager | ✅ Мигрировано |
| Определение 42+ активных минут | ✅ InformalSessionDetector | ✅ ActivityStateManager | ✅ Мигрировано |
| Cooldown 10 минут | ✅ InformalSessionDetector | ✅ ActivityStateManager | ✅ Мигрировано |
| Показ окон отдыха | ✅ InformalSessionDetector | ✅ ActivityStateManager | ✅ Мигрировано |
| Интеграция с MinuteActivityTracker | ✅ InformalSessionDetector | ✅ ActivityStateManager | ✅ Мигрировано |

### 🏗️ Архитектурные изменения

#### Принцип унификации:
- **Раньше**: Множество отдельных систем (InformalSessionDetector, ActivityStateManager, etc.)
- **Теперь**: Единая система ActivityStateManager для всех сценариев активности

#### Источник данных:
- **Раньше**: InformalSessionDetector создавал свой MinuteActivityTracker
- **Теперь**: ActivityStateManager использует единый MinuteActivityTracker

### 🚫 Что НЕ использовать

#### Файлы помеченные как устаревшие:
- `SimplePomodoroTest/InformalSessionDetector.swift` - ⚠️ УСТАРЕЛ
- `Tests/Archive/RealInformalSessionIntegrationTest.swift` - ⚠️ УСТАРЕЛ  
- `Tests/Archive/RealInformalDetectorBasicTest.swift` - ⚠️ УСТАРЕЛ

#### Методы помеченные как устаревшие:
- `AppDelegate.setupInformalSessionDetector()` - ⚠️ УСТАРЕЛ
- `AppDelegate.informalSessionDetector` - ⚠️ УСТАРЕЛ

### ✅ Что использовать

#### Новые компоненты:
- `SimplePomodoroTest/ActivityStateManager.swift` - ✅ АКТУАЛЬНО
- `ActivityStateManager.recordMinuteActivity()` - ✅ АКТУАЛЬНО
- `ActivityStateManager.onInformalRestSuggestion` - ✅ АКТУАЛЬНО

### 🧪 Тестирование

#### Проверка работы новой системы:
1. Запустите приложение
2. Работайте 52+ минуты с активностью 42+ минут
3. Должно появиться окно "Time for rest"
4. Проверьте логи: `ActivityState 🔍 Записана активность минуты`

#### Логи для отладки:
```
ActivityState 🔍 Записана активность минуты: true, история: 1 мин
ActivityState 🔍 Недостаточно истории для проверки: 1/52
ActivityState 🔔 НЕФОРМАЛЬНАЯ СЕССИЯ: 42 активных минут из 52!
```

### 📅 План удаления устаревшего кода

1. **Фаза 1** (текущая): Пометить как устаревший, отключить
2. **Фаза 2** (через 1-2 недели): Убедиться что новая система работает стабильно
3. **Фаза 3** (через месяц): Удалить устаревший код полностью

### 🔗 Связанные документы
- `UNIFIED_ACTIVITY_SYSTEM.md` - Общая архитектура унифицированной системы
- `TESTING.md` - Стандарты тестирования
