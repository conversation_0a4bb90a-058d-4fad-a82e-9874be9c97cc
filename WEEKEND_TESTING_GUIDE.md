# 🏖️ Руководство по тестированию системы выходных

## 🎯 Цель
Система тестовых данных позволяет проверить логику определения выходных дней без необходимости накапливать реальную историю работы.

## 🚀 Быстрый старт

### 1. Открытие окна тестовых данных
- Запустите uProd
- Кликните на иконку в статус-баре
- Выберите "🧪 Тестовые данные для выходных"

### 2. Готовые сценарии
Нажмите на любой из готовых сценариев:

**Сценарий 1: Работал только сегодня**
- Добавьте реальную микросессию сегодня
- Результат: выходной не предлагается (нужно 3 дня)

**Сценарий 2: Работал вчера + сегодня**  
- Автоматически добавляется сессия за вчера
- Добавьте реальную сессию сегодня
- Результат: выходной не предлагается (нужно 3 дня)

**Сценарий 3: Работал ЧТ+ПТ+СБ, сегодня НЕ работал**
- Автоматически добавляются сессии за 3 дня
- НЕ добавляйте сессию сегодня
- Результат: выходной предлагается СЕГОДНЯ ✅

**Сценарий 4: Работал ЧТ+ПТ+СБ+ВСК**
- Автоматически добавляются сессии за 3 дня + сегодня
- Результат: выходной предлагается ЗАВТРА ✅

### 3. Ручное добавление
- Укажите количество дней назад (0 = сегодня, 1 = вчера)
- Укажите продолжительность в минутах
- Нажмите "Добавить сессию"

### 4. Проверка результата
- Нажмите "🔄 Обновить информацию"
- Посмотрите статус в информационной панели
- Откройте "🐛 Отладка системы выходных" для детального анализа

### 5. Очистка данных
- Нажмите "🧹 Очистить все тестовые данные"
- Удаляются только тестовые данные
- Реальные сессии остаются нетронутыми

## 🔍 Проверка логики

### Ожидаемое поведение для стратегии 3/1:

| Рабочих дней | Сегодня работал | Показать выходной | Сегодня выходной |
|--------------|-----------------|-------------------|------------------|
| 1            | Да              | НЕТ               | НЕТ              |
| 2            | Да              | НЕТ               | НЕТ              |
| 3            | НЕТ             | ДА                | ДА               |
| 3+           | Да              | ДА                | НЕТ (завтра)     |

### Как проверить:
1. Создайте сценарий
2. Откройте отладку выходных
3. Проверьте:
   - Количество рабочих дней
   - Статус "Показать выходной"
   - Статус "Сегодня выходной"
   - Дату следующего предполагаемого выходного

## ⚠️ Важные моменты

### Безопасность данных
- Тестовые данные помечены типом "test"
- Очистка удаляет только тестовые данные
- Реальные сессии (formal/informal) остаются

### Логика работы
- Система считает уникальные дни с активностью
- Учитывается только наличие сессий, не их продолжительность
- Сброс происходит после взятия выходного

### Отладка
- Все действия логируются в консоль
- Используйте отладочное окно для детального анализа
- Проверяйте логи в случае неожиданного поведения

## 🧪 Тестирование

### Автоматические тесты
```bash
# Тест системы тестовых данных
swift Tests/TestDataManagerTest.swift

# Тест логики выходных
swift Tests/WeekendLogicTest.swift
```

### Ручное тестирование
1. Создайте каждый сценарий
2. Проверьте соответствие ожидаемому поведению
3. Очистите данные между тестами
4. Убедитесь, что реальные данные не затрагиваются

## 🐛 Устранение проблем

### Неправильное количество рабочих дней
- Проверьте, что сессии добавлены за правильные дни
- Убедитесь, что нет дублирующих сессий
- Очистите и пересоздайте тестовые данные

### Выходной не предлагается
- Проверьте стратегию в настройках (должна быть 3/1)
- Убедитесь, что накоплено 3+ рабочих дня
- Проверьте, что сегодня нет рабочих сессий (для сценария 3)

### Данные не очищаются
- Убедитесь, что используете кнопку очистки тестовых данных
- Проверьте логи на наличие ошибок
- Перезапустите приложение при необходимости

## 📝 Примеры использования

### Тестирование нового алгоритма
1. Очистите все тестовые данные
2. Создайте сценарий 3 (ЧТ+ПТ+СБ)
3. Проверьте, что предлагается выходной сегодня
4. Добавьте сессию сегодня
5. Проверьте, что выходной переносится на завтра

### Проверка граничных случаев
1. Добавьте ровно 3 дня работы
2. Убедитесь, что выходной предлагается
3. Добавьте 4-й день
4. Проверьте изменение логики

### Валидация после изменений кода
1. Запустите автоматические тесты
2. Проверьте все 4 сценария вручную
3. Убедитесь в корректности очистки данных
