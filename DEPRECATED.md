# 🚫 Устаревший код uProd

Этот документ содержит список всех устаревших компонентов, методов и систем в проекте uProd.

**КРИТИЧЕСКИ ВАЖНО**: Перед редактированием любого кода ВСЕГДА проверяйте этот список!

## 📋 Статус устаревшего кода

### ❌ ПОЛНОСТЬЮ УСТАРЕВШИЕ (НЕ ИСПОЛЬЗУЮТСЯ)

#### 1. InformalSessionDetector.swift
- **Статус**: УСТАРЕВШИЙ, НЕ ИСПОЛЬЗУЕТСЯ В ЛОГИКЕ
- **Заменен на**: ActivityStateManager
- **Расположение**: `SimplePomodoroTest/InformalSessionDetector.swift`
- **Причина**: Логика неформальных интервалов интегрирована в ActivityStateManager
- **Действие**: Помечен `@available(*, deprecated)`, закомментирован в AppDelegate
- **Остаточные использования**:
  - AppDelegate.swift: переменная `informalSessionDetector`, метод `setupInformalSessionDetector()`
  - SettingsWindow.swift: настройки `InformalSessionDetectorEnabled`
- **Планы**: Удалить после полного тестирования новой системы

#### 2. Методы в AppDelegate.swift
- **setupInformalSessionDetector()** - УСТАРЕВШИЙ (закомментирован)
- **var informalSessionDetector** - УСТАРЕВШАЯ ПЕРЕМЕННАЯ
- **UserDefaults "InformalSessionDetectorEnabled"** - УСТАРЕВШАЯ НАСТРОЙКА

#### 3. Настройки в SettingsWindow.swift
- **InformalSessionDetectorEnabled** - УСТАРЕВШАЯ НАСТРОЙКА
- **Все UI для InformalSessionDetector** - НЕ НУЖНЫ

### ⚠️ ЧАСТИЧНО УСТАРЕВШИЕ

#### 1. Старые системы уведомлений
- **NSUserNotification** - устаревший API (заменен на UserNotifications)
- **Расположение**: AppDelegate.swift, строки ~2212
- **Статус**: Работает, но deprecated в macOS 11.0+

#### 2. LaunchAtLoginManager.swift
- **SMLoginItemSetEnabled** - устаревший API
- **Расположение**: LaunchAtLoginManager.swift, строка 67
- **Статус**: Работает, но deprecated в macOS 13.0+
- **Заменить на**: SMAppService

### 🔍 КАК ПРОВЕРИТЬ ПЕРЕД РЕДАКТИРОВАНИЕМ

1. **Поиск в этом файле**: Ctrl+F по имени класса/метода
2. **Проверка в коде**: Ищите комментарии `⚠️ УСТАРЕВШИЙ` или `@available(*, deprecated)`
3. **Проверка использования**: Поиск по всему проекту

### 📝 ПРАВИЛА РАБОТЫ С УСТАРЕВШИМ КОДОМ

#### ✅ МОЖНО:
- Читать для понимания логики
- Использовать как референс для новой реализации
- Тестировать (если нужно для совместимости)

#### ❌ НЕЛЬЗЯ:
- Редактировать устаревший код
- Добавлять новую функциональность в устаревшие компоненты
- Исправлять баги в устаревшем коде (исправляйте в новой системе)

### 🎯 АКТИВНЫЕ ЗАМЕНЫ

| Устаревший компонент | Активная замена | Статус миграции |
|----------------------|-----------------|-----------------|
| InformalSessionDetector | ActivityStateManager | ✅ Завершена |
| NSUserNotification | UserNotifications | 🔄 В планах |
| SMLoginItemSetEnabled | SMAppService | 🔄 В планах |

### 📅 ПЛАН ОЧИСТКИ

#### Фаза 1 (Текущая)
- [x] Пометить InformalSessionDetector как deprecated
- [x] Закомментировать использование в AppDelegate
- [x] Создать документацию (этот файл)

#### Фаза 2 (Следующая)
- [ ] Полностью удалить InformalSessionDetector.swift
- [ ] Удалить связанные методы из AppDelegate
- [ ] Обновить NSUserNotification на UserNotifications

#### Фаза 3 (Будущая)
- [ ] Обновить LaunchAtLoginManager на SMAppService
- [ ] Финальная очистка всех deprecated API

### 🚨 ПРЕДУПРЕЖДЕНИЯ ДЛЯ ИИ-АССИСТЕНТОВ

**ПЕРЕД ЛЮБЫМ РЕДАКТИРОВАНИЕМ:**

1. **ОБЯЗАТЕЛЬНО** проверьте этот файл DEPRECATED.md
2. **НЕ РЕДАКТИРУЙТЕ** компоненты из раздела "ПОЛНОСТЬЮ УСТАРЕВШИЕ"
3. **ВСЕГДА СПРАШИВАЙТЕ** пользователя, если не уверены
4. **ИСПОЛЬЗУЙТЕ** активные замены вместо устаревших компонентов

### 📞 Контакты для вопросов
- При сомнениях - спрашивайте пользователя
- Для добавления в список - обновляйте этот файл
- Для удаления из списка - согласовывайте с пользователем

---

**Последнее обновление**: 2025-08-05  
**Версия документа**: 1.0  
**Ответственный**: Система управления устаревшим кодом uProd
