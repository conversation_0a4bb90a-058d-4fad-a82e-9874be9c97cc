#!/bin/bash

#
# check_deprecated.sh
# Скрипт для проверки использования устаревшего кода в uProd
#

echo "🔍 Проверка устаревшего кода в uProd..."
echo "========================================"

# Цвета для вывода
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# Счетчики
deprecated_found=0
warnings_found=0

echo ""
echo "📋 Проверяем использование устаревших компонентов..."

# Проверка InformalSessionDetector
echo ""
echo "🔍 InformalSessionDetector (УСТАРЕВШИЙ):"
informal_usage=$(grep -r "InformalSessionDetector" SimplePomodoroTest/ --exclude="InformalSessionDetector.swift" | grep -v "deprecated" | grep -v "УСТАРЕВШИЙ" | wc -l)
if [ $informal_usage -gt 0 ]; then
    echo -e "${RED}❌ Найдено $informal_usage использований InformalSessionDetector:${NC}"
    grep -r "InformalSessionDetector" SimplePomodoroTest/ --exclude="InformalSessionDetector.swift" | grep -v "deprecated" | grep -v "УСТАРЕВШИЙ"
    deprecated_found=$((deprecated_found + informal_usage))
else
    echo -e "${GREEN}✅ InformalSessionDetector не используется${NC}"
fi

# Проверка NSUserNotification
echo ""
echo "🔍 NSUserNotification (УСТАРЕВШИЙ API):"
nsuser_usage=$(grep -r "NSUserNotification" SimplePomodoroTest/ | wc -l)
if [ $nsuser_usage -gt 0 ]; then
    echo -e "${YELLOW}⚠️ Найдено $nsuser_usage использований NSUserNotification (deprecated):${NC}"
    grep -r "NSUserNotification" SimplePomodoroTest/ | head -5
    warnings_found=$((warnings_found + nsuser_usage))
else
    echo -e "${GREEN}✅ NSUserNotification не используется${NC}"
fi

# Проверка SMLoginItemSetEnabled
echo ""
echo "🔍 SMLoginItemSetEnabled (УСТАРЕВШИЙ API):"
smlogin_usage=$(grep -r "SMLoginItemSetEnabled" SimplePomodoroTest/ | wc -l)
if [ $smlogin_usage -gt 0 ]; then
    echo -e "${YELLOW}⚠️ Найдено $smlogin_usage использований SMLoginItemSetEnabled (deprecated):${NC}"
    grep -r "SMLoginItemSetEnabled" SimplePomodoroTest/
    warnings_found=$((warnings_found + smlogin_usage))
else
    echo -e "${GREEN}✅ SMLoginItemSetEnabled не используется${NC}"
fi

# Проверка @available(*, deprecated)
echo ""
echo "🔍 Компоненты помеченные как deprecated:"
deprecated_marked=$(grep -r "@available.*deprecated" SimplePomodoroTest/ | wc -l)
if [ $deprecated_marked -gt 0 ]; then
    echo -e "${YELLOW}📝 Найдено $deprecated_marked помеченных как deprecated:${NC}"
    grep -r "@available.*deprecated" SimplePomodoroTest/
else
    echo -e "${GREEN}✅ Нет помеченных deprecated компонентов${NC}"
fi

# Итоговый отчет
echo ""
echo "========================================"
echo "📊 ИТОГОВЫЙ ОТЧЕТ:"
echo "========================================"

if [ $deprecated_found -gt 0 ]; then
    echo -e "${RED}❌ КРИТИЧЕСКИЕ ПРОБЛЕМЫ: $deprecated_found${NC}"
    echo -e "${RED}   Найдено использование полностью устаревших компонентов!${NC}"
    echo -e "${RED}   Требуется немедленное исправление.${NC}"
fi

if [ $warnings_found -gt 0 ]; then
    echo -e "${YELLOW}⚠️ ПРЕДУПРЕЖДЕНИЯ: $warnings_found${NC}"
    echo -e "${YELLOW}   Найдены устаревшие API, рекомендуется обновление.${NC}"
fi

if [ $deprecated_found -eq 0 ] && [ $warnings_found -eq 0 ]; then
    echo -e "${GREEN}🎉 ВСЕ ОТЛИЧНО!${NC}"
    echo -e "${GREEN}   Использование устаревшего кода не найдено.${NC}"
fi

echo ""
echo "📖 Подробности в файле DEPRECATED.md"
echo "🔧 Для исправлений используйте активные замены"

# Возвращаем код ошибки если есть критические проблемы
if [ $deprecated_found -gt 0 ]; then
    exit 1
else
    exit 0
fi
