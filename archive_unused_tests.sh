#!/bin/bash

# Скрипт для безопасного архивирования неиспользуемых тестов
# Основан на анализе в test_analysis.md

echo "🗂️ АРХИВИРОВАНИЕ НЕИСПОЛЬЗУЕМЫХ ТЕСТОВ"
echo "======================================"

# Создаем папку Archive если её нет
mkdir -p Tests/Archive

# Счетчики
MOVED=0
TOTAL=27

echo "📦 Перемещаем неиспользуемые тесты в Tests/Archive..."

# МОКОВЫЕ ТЕСТЫ (4 файла)
echo ""
echo "🎭 Архивируем моковые тесты..."
mv Tests/QuickActivityStateManagerTest.swift Tests/Archive/ && echo "✅ QuickActivityStateManagerTest.swift" && ((MOVED++))
mv Tests/QuickComputerTimeTrackerTest.swift Tests/Archive/ && echo "✅ QuickComputerTimeTrackerTest.swift" && ((MOVED++))
mv Tests/QuickMinuteActivityTrackerTest.swift Tests/Archive/ && echo "✅ QuickMinuteActivityTrackerTest.swift" && ((MOVED++))
mv Tests/QuickUnifiedSystemTest.swift Tests/Archive/ && echo "✅ QuickUnifiedSystemTest.swift" && ((MOVED++))

# ДУБЛИРУЮЩИЕ ТЕСТЫ (4 файла)
echo ""
echo "🔄 Архивируем дублирующие тесты..."
mv Tests/ActivityStateManagerTest.swift Tests/Archive/ && echo "✅ ActivityStateManagerTest.swift" && ((MOVED++))
mv Tests/MinuteActivityTrackerTest.swift Tests/Archive/ && echo "✅ MinuteActivityTrackerTest.swift" && ((MOVED++))
mv Tests/RealActivityStateManagerTest.swift Tests/Archive/ && echo "✅ RealActivityStateManagerTest.swift" && ((MOVED++))
mv Tests/RealMinuteActivityTrackerTest.swift Tests/Archive/ && echo "✅ RealMinuteActivityTrackerTest.swift" && ((MOVED++))

# СТАРЫЕ ИНТЕГРАЦИОННЫЕ ТЕСТЫ (7 файлов)
echo ""
echo "🔗 Архивируем старые интеграционные тесты..."
mv Tests/ComputerTimeTrackerUpgradeTest.swift Tests/Archive/ && echo "✅ ComputerTimeTrackerUpgradeTest.swift" && ((MOVED++))
mv Tests/InformalSessionDetectorIntegrationTest.swift Tests/Archive/ && echo "✅ InformalSessionDetectorIntegrationTest.swift" && ((MOVED++))
mv Tests/InformalSessionDetectorUpgradeTest.swift Tests/Archive/ && echo "✅ InformalSessionDetectorUpgradeTest.swift" && ((MOVED++))
mv Tests/RealBreakTimerIntegrationTest.swift Tests/Archive/ && echo "✅ RealBreakTimerIntegrationTest.swift" && ((MOVED++))
mv Tests/RealInformalDetectorBasicTest.swift Tests/Archive/ && echo "✅ RealInformalDetectorBasicTest.swift" && ((MOVED++))
mv Tests/RealInformalDetectorSimpleTest.swift Tests/Archive/ && echo "✅ RealInformalDetectorSimpleTest.swift" && ((MOVED++))
mv Tests/RealInformalSessionIntegrationTest.swift Tests/Archive/ && echo "✅ RealInformalSessionIntegrationTest.swift" && ((MOVED++))

# ЭКСПЕРИМЕНТАЛЬНЫЕ ТЕСТЫ (12 файлов)
echo ""
echo "🧪 Архивируем экспериментальные тесты..."
mv Tests/RealSleepIntegrationTest.swift Tests/Archive/ && echo "✅ RealSleepIntegrationTest.swift" && ((MOVED++))
mv Tests/RealStatisticsManagerTest.swift Tests/Archive/ && echo "✅ RealStatisticsManagerTest.swift" && ((MOVED++))
mv Tests/RealUnifiedSystemIntegrationTest.swift Tests/Archive/ && echo "✅ RealUnifiedSystemIntegrationTest.swift" && ((MOVED++))
mv Tests/RealUnifiedSystemTest.swift Tests/Archive/ && echo "✅ RealUnifiedSystemTest.swift" && ((MOVED++))
mv Tests/RealUseCaseTest.swift Tests/Archive/ && echo "✅ RealUseCaseTest.swift" && ((MOVED++))
mv Tests/SimpleComputerTimeTrackerTest.swift Tests/Archive/ && echo "✅ SimpleComputerTimeTrackerTest.swift" && ((MOVED++))
mv Tests/SimpleUnifiedSystemTest.swift Tests/Archive/ && echo "✅ SimpleUnifiedSystemTest.swift" && ((MOVED++))
mv Tests/UnifiedActivitySystemTest.swift Tests/Archive/ && echo "✅ UnifiedActivitySystemTest.swift" && ((MOVED++))

echo ""
echo "📊 РЕЗУЛЬТАТЫ АРХИВИРОВАНИЯ:"
echo "   Перемещено: $MOVED из $TOTAL файлов"
echo "   Папка архива: Tests/Archive/"

if [ $MOVED -eq $TOTAL ]; then
    echo "✅ ВСЕ НЕИСПОЛЬЗУЕМЫЕ ТЕСТЫ УСПЕШНО АРХИВИРОВАНЫ!"
else
    echo "⚠️ Некоторые файлы не удалось переместить"
fi

echo ""
echo "🔍 Проверяем что осталось в Tests/:"
ls Tests/*.swift | wc -l | xargs echo "Осталось файлов:"
ls Tests/*.swift

echo ""
echo "📁 Содержимое архива:"
ls Tests/Archive/ | wc -l | xargs echo "В архиве файлов:"

echo ""
echo "🚀 СЛЕДУЮЩИЙ ШАГ: Запустите ./test.sh для проверки"
