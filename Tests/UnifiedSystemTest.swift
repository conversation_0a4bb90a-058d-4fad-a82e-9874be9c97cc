#!/usr/bin/env swift

//
//  UnifiedSystemTest.swift
//  uProd Tests
//
//  Тесты для новой единой системы напоминаний
//

import Foundation

// MARK: - Test Framework

class TestRunner {
    private var testCount = 0
    private var passedCount = 0
    private var failedTests: [String] = []
    
    func test(_ name: String, _ testBlock: () throws -> Void) {
        testCount += 1
        do {
            try testBlock()
            passedCount += 1
            print("✅ \(name)")
        } catch {
            failedTests.append(name)
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) throws {
        if actual != expected {
            throw TestError.assertionFailed("\(message): ожидалось \(expected), получено \(actual)")
        }
    }
    
    func printSummary() {
        print("\n🧪 Результаты тестов единой системы:")
        print("📊 Всего тестов: \(testCount)")
        print("✅ Прошло: \(passedCount)")
        print("❌ Провалилось: \(testCount - passedCount)")
        
        if !failedTests.isEmpty {
            print("\n❌ Провалившиеся тесты:")
            for test in failedTests {
                print("   • \(test)")
            }
        }
        
        let successRate = testCount > 0 ? (passedCount * 100) / testCount : 0
        print("📈 Успешность: \(successRate)%")
        
        if passedCount == testCount {
            print("🎉 Все тесты прошли успешно!")
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Mock Classes для тестирования

// Упрощенная версия OvertimeConfig для тестов
struct MockOvertimeConfig {
    static func getLevelNumber(for minutes: Int) -> Int {
        switch minutes {
        case 0: return 0      // Зеленый
        case 1..<3: return 1  // Желтый
        case 3..<6: return 2  // Оранжевый
        default: return 3     // Красный
        }
    }
}

// Mock для делегата
class MockDelegate {
    var reminderCalls: [(minutes: Int, level: Int)] = []
    var statusBarCalls: [Int] = []
    
    func showEscalationReminder(minutes: Int, level: Int) {
        reminderCalls.append((minutes: minutes, level: level))
    }
    
    func updateStatusBar(minutes: Int) {
        statusBarCalls.append(minutes)
    }
    
    func reset() {
        reminderCalls.removeAll()
        statusBarCalls.removeAll()
    }
}

// MARK: - Тесты

let runner = TestRunner()

// Тест 1: Проверка правильности уровней эскалации
runner.test("OvertimeConfig возвращает правильные уровни") {
    let testCases = [
        (minutes: 0, expectedLevel: 0),
        (minutes: 1, expectedLevel: 1),
        (minutes: 2, expectedLevel: 1),
        (minutes: 3, expectedLevel: 2),
        (minutes: 5, expectedLevel: 2),
        (minutes: 6, expectedLevel: 3),
        (minutes: 10, expectedLevel: 4)
    ]
    
    for testCase in testCases {
        let actualLevel = MockOvertimeConfig.getLevelNumber(for: testCase.minutes)
        // Для простоты теста используем упрощенную логику
        let expectedSimple = testCase.minutes == 0 ? 0 : (testCase.minutes < 3 ? 1 : (testCase.minutes < 6 ? 2 : 3))
        try runner.assertEqual(actualLevel, expectedSimple, "Уровень для \(testCase.minutes) минут")
    }
}

// Тест 2: Проверка логики "только при изменении уровня"
runner.test("Напоминания показываются только при изменении уровня") {
    let delegate = MockDelegate()
    
    // Симулируем последовательность минут: 0, 0, 1, 1, 2, 3, 3, 6
    let minuteSequence = [0, 0, 1, 1, 2, 3, 3, 6]
    var lastLevel = -1
    
    for minutes in minuteSequence {
        let currentLevel = MockOvertimeConfig.getLevelNumber(for: minutes)
        
        // Показываем напоминание только при изменении уровня
        if currentLevel != lastLevel {
            lastLevel = currentLevel
            delegate.showEscalationReminder(minutes: minutes, level: currentLevel)
        }
    }
    
    // Ожидаем вызовы только при изменении уровня: 0->0, 0->1, 1->2, 2->3
    try runner.assertEqual(delegate.reminderCalls.count, 4, "Количество вызовов напоминаний")
    
    let expectedCalls = [(0, 0), (1, 1), (3, 2), (6, 3)]
    for (index, expected) in expectedCalls.enumerated() {
        let actual = delegate.reminderCalls[index]
        try runner.assertEqual(actual.minutes, expected.0, "Минуты в вызове \(index)")
        try runner.assertEqual(actual.level, expected.1, "Уровень в вызове \(index)")
    }
}

// Тест 3: Проверка начала с уровня 0 (зеленая зона)
runner.test("Система начинает с уровня 0 (зеленая зона)") {
    let delegate = MockDelegate()
    
    // Первый вызов должен быть с уровнем 0
    let firstLevel = MockOvertimeConfig.getLevelNumber(for: 0)
    delegate.showEscalationReminder(minutes: 0, level: firstLevel)
    
    try runner.assertEqual(delegate.reminderCalls.count, 1, "Должен быть один вызов")
    try runner.assertEqual(delegate.reminderCalls[0].minutes, 0, "Первый вызов с 0 минут")
    try runner.assertEqual(delegate.reminderCalls[0].level, 0, "Первый вызов с уровнем 0")
}

// Тест 4: Проверка обновления статус-бара
runner.test("Статус-бар обновляется при каждом изменении уровня") {
    let delegate = MockDelegate()
    
    // Симулируем изменения уровня
    let changes = [0, 1, 3, 6]
    for minutes in changes {
        delegate.updateStatusBar(minutes: minutes)
    }
    
    try runner.assertEqual(delegate.statusBarCalls.count, 4, "Количество обновлений статус-бара")
    try runner.assertEqual(delegate.statusBarCalls, changes, "Правильные значения минут")
}

// Тест 5: Проверка интеграции с реальными компонентами
runner.test("Интеграция с реальными компонентами работает") {
    // Проверяем что наши типы совместимы с реальными
    let testMinutes = [0, 1, 3, 6, 10]
    
    for minutes in testMinutes {
        let level = MockOvertimeConfig.getLevelNumber(for: minutes)
        try runner.assert(level >= 0, "Уровень должен быть неотрицательным")
        try runner.assert(level <= 10, "Уровень должен быть разумным")
    }
}

// Запуск тестов
runner.printSummary()
