import Cocoa
@testable import uProd

/// Тест для WeekendChoiceWindow - проверяет новый дизайн с заголовком и подзаголовком
@main struct WeekendChoiceWindowTest {
    static func main() {
        print("🧪 Тестирование WeekendChoiceWindow...")
        
        // Тест 1: Создание окна
        testWindowCreation()
        
        // Тест 2: Размеры окна (как у BreakEndWindow)
        testWindowSize()
        
        // Тест 3: Структура UI (заголовок + подзаголовок)
        testUIStructure()
        
        // Тест 4: Тексты в зависимости от контекста
        testContextualTexts()
        
        print("✅ Все тесты WeekendChoiceWindow прошли!")
    }
    
    static func testWindowCreation() {
        print("  🔍 Тест создания окна...")
        
        let window = WeekendChoiceWindow()
        
        // Проверяем что окно создалось
        assert(window.contentView != nil, "Окно должно иметь contentView")
        assert(window.styleMask.contains(.borderless), "Окно должно быть borderless")
        assert(window.level == .floating, "Окно должно быть floating")
        
        print("    ✅ Окно создается корректно")
    }
    
    static func testWindowSize() {
        print("  🔍 Тест размеров окна...")
        
        let window = WeekendChoiceWindow()
        let frame = window.frame
        
        // Проверяем размеры (увеличенная ширина для длинных подзаголовков)
        assert(frame.width == 480, "Ширина должна быть 480px, получили \(frame.width)")
        assert(frame.height == 120, "Высота должна быть 120px, получили \(frame.height)")

        print("    ✅ Размеры окна корректны (480x120)")
    }
    
    static func testUIStructure() {
        print("  🔍 Тест структуры UI...")
        
        let window = WeekendChoiceWindow()
        guard let contentView = window.contentView else {
            fatalError("ContentView не найден")
        }
        
        // Ищем элементы UI через рефлексию
        let titleLabel = findLabel(in: contentView, containing: "🏖️")
        let subtitleLabel = findLabel(in: contentView, containing: "хотите")
        let restButton = findButton(in: contentView, withTitle: "Отдохнуть")
        let workButton = findButton(in: contentView, withTitle: "Поработать")
        
        assert(titleLabel != nil, "Заголовок с эмодзи должен существовать")
        assert(subtitleLabel != nil, "Подзаголовок должен существовать")
        assert(restButton != nil, "Кнопка отдыха должна существовать")
        assert(workButton != nil, "Кнопка работы должна существовать")
        
        // Проверяем стили заголовка (как в BreakEndWindow)
        if let titleLabel = titleLabel {
            assert(titleLabel.font?.pointSize == 15, "Размер шрифта заголовка должен быть 15")
            assert(titleLabel.textColor == NSColor.white, "Цвет заголовка должен быть белым")
        }
        
        // Проверяем стили подзаголовка
        if let subtitleLabel = subtitleLabel {
            assert(subtitleLabel.font?.pointSize == 12, "Размер шрифта подзаголовка должен быть 12")
            assert(subtitleLabel.textColor == NSColor.lightGray, "Цвет подзаголовка должен быть lightGray")
        }
        
        print("    ✅ Структура UI корректна")
    }
    
    static func testContextualTexts() {
        print("  🔍 Тест контекстных текстов...")
        
        let window = WeekendChoiceWindow()
        
        // Симулируем разные сценарии
        // Сценарий 1: Обычный выходной (daysWithoutRest = 0)
        UserDefaults.standard.set(0, forKey: "daysWithoutRest")
        window.showWeekendChoice(onRest: {}, onWork: {})
        
        guard let contentView = window.contentView else {
            fatalError("ContentView не найден")
        }
        
        let titleLabel = findLabel(in: contentView, containing: "🏖️")
        let subtitleLabel = findLabel(in: contentView, containing: "хотите")
        
        assert(titleLabel?.stringValue.contains("🏖️ Сегодня выходной!") == true, 
               "Заголовок должен содержать эмодзи и текст")
        assert(subtitleLabel?.stringValue.contains("Как хотите провести день?") == true, 
               "Подзаголовок для обычного выходного")
        
        // Сценарий 2: Пропущенные выходные (daysWithoutRest > 0)
        UserDefaults.standard.set(2, forKey: "daysWithoutRest")
        window.showWeekendChoice(onRest: {}, onWork: {})
        
        let subtitleLabel2 = findLabel(in: contentView, containing: "силы")
        assert(subtitleLabel2?.stringValue.contains("Пора восстановить силы") == true, 
               "Подзаголовок для пропущенных выходных")
        
        print("    ✅ Контекстные тексты корректны")
    }
    
    // MARK: - Helper Methods
    
    static func findLabel(in view: NSView, containing text: String) -> NSTextField? {
        if let label = view as? NSTextField, label.stringValue.lowercased().contains(text.lowercased()) {
            return label
        }
        
        for subview in view.subviews {
            if let found = findLabel(in: subview, containing: text) {
                return found
            }
        }
        
        return nil
    }
    
    static func findButton(in view: NSView, withTitle title: String) -> NSButton? {
        if let button = view as? NSButton, button.title == title {
            return button
        }
        
        for subview in view.subviews {
            if let found = findButton(in: subview, withTitle: title) {
                return found
            }
        }
        
        return nil
    }
}
