#!/usr/bin/env swift

import Foundation

// Подключаем реальную систему
let earlyEngagementPath = "EarlyEngagementSystem.swift"
let messageMatrixPath = "MessageConstructionMatrix.swift"
let loggerPath = "Logger.swift"

/// РЕАЛЬНЫЙ ТЕСТ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Тестирует НАСТОЯЩУЮ систему, а не моки!

func main() {
        print("🔥 РЕАЛЬНЫЙ ТЕСТ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ")
        print("==============================================")
        print("⚠️  Этот тест использует РЕАЛЬНУЮ систему EarlyEngagementSystem")
        print("⚠️  Если система сломана, тест это обнаружит!")
        print("")
        
        testRealUserBarAdaptation()
        testRealSystemIntegration()
        
        print("\n✅ Все реальные тесты завершены!")
}

// Запуск тестов
main()
    
func testRealUserBarAdaptation() {
        print("🎯 ТЕСТ 1: Реальная адаптация планки")
        print("------------------------------------")
        
        // Создаем реальную систему
        let system = EarlyEngagementSystem.shared
        
        // Сохраняем исходное состояние
        let originalBar = system.debugCurrentUserBar
        print("📊 Исходная планка: \(Int(originalBar / 60)) минут")
        
        // Тестируем успешный интервал
        print("\n🟢 Тестируем успешный интервал...")
        system.recordIntervalCompleted(duration: 25*60, projectId: "test-project")
        let barAfterSuccess = system.debugCurrentUserBar
        print("📈 Планка после успеха: \(Int(barAfterSuccess / 60)) минут")
        
        // Проверяем что планка увеличилась
        if barAfterSuccess > originalBar {
            print("✅ Планка увеличилась после успеха")
        } else if barAfterSuccess == 0 {
            print("❌ КРИТИЧЕСКАЯ ОШИБКА: Планка стала 0! Система сломана!")
            print("🔧 Это означает что функция adaptUserBar работает неправильно")
        } else {
            print("❌ Планка не увеличилась после успеха")
        }
        
        // Тестируем отказ (планка НЕ должна измениться)
        print("\n🔴 Тестируем отказ пользователя...")
        let barBeforeRefusal = system.debugCurrentUserBar
        system.recordUserRefused(proposedDuration: 20*60)
        let barAfterRefusal = system.debugCurrentUserBar
        print("📊 Планка после отказа: \(Int(barAfterRefusal / 60)) минут")

        // Проверяем что планка НЕ изменилась
        if barAfterRefusal == barBeforeRefusal {
            print("✅ Планка осталась неизменной после отказа (правильно)")
        } else if barAfterRefusal == 0 {
            print("❌ КРИТИЧЕСКАЯ ОШИБКА: Планка стала 0! Система сломана!")
            print("🔧 Это означает что функция adaptUserBar работает неправильно")
        } else {
            print("❌ Планка изменилась после отказа (не должна)")
        }
        
        print("\n📋 Итоговая планка: \(Int(system.debugCurrentUserBar / 60)) минут")
    }
    
func testRealSystemIntegration() {
        print("\n🎯 ТЕСТ 2: Интеграция реальной системы")
        print("--------------------------------------")
        
        let system = EarlyEngagementSystem.shared
        
        // Тестируем получение сообщения
        print("💬 Получаем текущее сообщение...")
        let message = system.getCurrentMessage()
        print("📝 Сообщение: \"\(message.title)\"")
        print("⏰ Длительность: \(Int(message.duration / 60)) минут")
        
        if message.title.isEmpty {
            print("❌ Сообщение пустое!")
        } else {
            print("✅ Сообщение получено корректно")
        }
        
        if message.duration > 0 {
            print("✅ Длительность корректна")
        } else {
            print("❌ Длительность некорректна")
        }
        
        // Тестируем состояние системы
        print("\n📊 Проверяем состояние системы...")
        let daysWithoutWork = system.debugDaysWithoutWork
        let timeOfDay = system.debugCurrentTimeOfDay
        let currentBar = system.debugCurrentUserBar
        
        print("📅 Дни без работы: \(daysWithoutWork)")
        print("🕐 Время дня: \(timeOfDay)")
        print("⏱️  Текущая планка: \(Int(currentBar / 60)) минут")
        
        // Критические проверки
        if currentBar == 0 {
            print("❌ КРИТИЧЕСКАЯ ОШИБКА: Планка равна 0!")
            print("🚨 Система адаптации планки сломана!")
        } else if currentBar < 60 { // Меньше 1 минуты
            print("⚠️  Планка очень маленькая: \(Int(currentBar)) секунд")
        } else {
            print("✅ Планка в разумных пределах")
        }
        
        if daysWithoutWork < 0 {
            print("❌ Дни без работы отрицательные!")
        } else {
            print("✅ Дни без работы корректны")
        }
        
        if timeOfDay < 0 || timeOfDay > 3 {
            print("❌ Время дня вне допустимого диапазона!")
        } else {
            print("✅ Время дня корректно")
        }
    }

// Заглушка для EarlyEngagementSystem если система не найдена
#if !canImport(EarlyEngagementSystem)
class EarlyEngagementSystem {
    static let shared = EarlyEngagementSystem()
    
    var debugCurrentUserBar: TimeInterval = 25 * 60
    var debugDaysWithoutWork: Int = 0
    var debugCurrentTimeOfDay: Int = 0
    
    func recordIntervalCompleted(duration: TimeInterval, projectId: String) {
        print("⚠️  Заглушка: recordIntervalCompleted не реализована")
    }
    
    func recordUserRefused(proposedDuration: TimeInterval) {
        print("⚠️  Заглушка: recordUserRefused не реализована")
    }
    
    func getCurrentMessage() -> EngagementMessage {
        return EngagementMessage(
            title: "Тестовое сообщение",
            description: "Описание",
            duration: 25 * 60,
            priority: .medium,
            actionType: .startWork
        )
    }
}

struct EngagementMessage {
    let title: String
    let description: String
    let duration: TimeInterval
    let priority: Priority
    let actionType: ActionType
    
    enum Priority {
        case low, medium, high, critical
    }
    
    enum ActionType {
        case startWork, continueWork, takeBreak
    }
}
#endif
