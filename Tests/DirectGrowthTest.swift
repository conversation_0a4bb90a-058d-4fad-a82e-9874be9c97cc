#!/usr/bin/env swift

import Foundation

print("🧪 ПРЯМОЙ ТЕСТ ГРАДАЦИОННОЙ СИСТЕМЫ")
print(String(repeating: "=", count: 40))

// Тестируем логику напрямую
print("📊 Тестируем 30 минут в диапазоне 26-40:")

let currentBar = 30
let multiplier = 1.15
let result = Double(currentBar) * multiplier
let rounded = Int(round(result))

print("   \(currentBar) × \(multiplier) = \(result)")
print("   round(\(result)) = \(round(result))")
print("   Int(round(\(result))) = \(rounded)")

print("\n🎯 ПРОВЕРКА ДРУГИХ ЗНАЧЕНИЙ:")
let testValues = [25, 26, 30, 35, 40]
for value in testValues {
    let calc = Double(value) * 1.15
    let roundedValue = Int(round(calc))
    print("   \(value) мин → \(calc) → \(roundedValue) мин")
}

print("\n📝 АНАЛИЗ:")
if rounded == 35 {
    print("   ✅ 30 минут дает 35 минут (как ожидает пользователь)")
} else {
    print("   ❌ 30 минут дает \(rounded) минут (пользователь ожидает 35)")
}

print("\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ РАСХОЖДЕНИЯ:")
print("   1. Разные версии округления в Swift")
print("   2. Разная логика в отладочном vs реальном окне")
print("   3. Кэширование результатов")
print("   4. Другие параметры влияют на расчет")

