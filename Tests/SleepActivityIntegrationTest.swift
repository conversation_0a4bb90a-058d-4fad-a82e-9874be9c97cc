import Foundation

/// Тест интеграции ActivityStateManager с SleepWakeDetector
/// Проверяет правильную обработку времени сна как неактивности

@main struct SleepActivityIntegrationTest {

    // Простая заглушка для ActivityStateManager для тестирования
    class TestActivityStateManager {
        private var minuteActivityHistory: [Bool] = []
        private var lastRestSuggestionTime: Date?

        func recordMinuteActivity(isActive: Bool) {
            minuteActivityHistory.append(isActive)
            print("🔍 Записана активность: \(isActive), история: \(minuteActivityHistory.count) записей")
        }

        func resetAfterSleep(sleepDuration: TimeInterval = 0) {
            let sleepMinutes = Int(sleepDuration / 60)
            let resetThreshold: TimeInterval = 15 * 60 // 15 минут

            if sleepDuration > 0 && sleepDuration < resetThreshold {
                // Короткий сон - записываем как неактивные минуты
                print("🎯 Короткий сон (\(sleepMinutes) мин) - записываем неактивность")
                for _ in 0..<sleepMinutes {
                    recordMinuteActivity(isActive: false)
                }
            } else if sleepDuration >= resetThreshold {
                // Длительный сон - сбрасываем лог
                print("🎯 Длительный сон (\(sleepMinutes) мин) - сбрасываем лог")
                minuteActivityHistory.removeAll()
                lastRestSuggestionTime = nil
            }

            print("🎯 ActivityStateManager: Сброс после реального сна (\(sleepMinutes) мин)")
        }

        func getActivityHistory() -> [Bool] {
            return minuteActivityHistory
        }
    }
    static func main() {
        print("🧪 ========================================")
        print("🧪 ТЕСТ: Интеграция сна и активности")
        print("🧪 ========================================")
        
        testShortSleepRecordsInactivity()
        testLongSleepResetsLog()
        testZeroDurationHandling()
        
        print("🧪 ========================================")
        print("🧪 ВСЕ ТЕСТЫ ЗАВЕРШЕНЫ")
        print("🧪 ========================================")
    }
    
    /// Тест: короткий сон записывает неактивные минуты
    static func testShortSleepRecordsInactivity() {
        print("\n🧪 ТЕСТ 1: Короткий сон записывает неактивность")

        // Создаем тестовый ActivityStateManager
        let manager = TestActivityStateManager()
        
        // Записываем несколько активных минут
        manager.recordMinuteActivity(isActive: true)
        manager.recordMinuteActivity(isActive: true)
        manager.recordMinuteActivity(isActive: true)
        
        let historyBefore = manager.getActivityHistory()
        print("🔍 История ДО сна: \(historyBefore)")
        
        // Симулируем короткий сон (7 минут)
        let shortSleepDuration: TimeInterval = 7 * 60
        manager.resetAfterSleep(sleepDuration: shortSleepDuration)
        
        let historyAfter = manager.getActivityHistory()
        print("🔍 История ПОСЛЕ сна: \(historyAfter)")
        
        // Проверяем что добавились неактивные минуты
        let expectedInactiveMinutes = 7
        let actualInactiveMinutes = historyAfter.suffix(expectedInactiveMinutes).filter { !$0 }.count
        
        if actualInactiveMinutes == expectedInactiveMinutes {
            print("✅ ТЕСТ 1 ПРОЙДЕН: Добавлено \(actualInactiveMinutes) неактивных минут")
        } else {
            print("❌ ТЕСТ 1 ПРОВАЛЕН: Ожидалось \(expectedInactiveMinutes), получено \(actualInactiveMinutes)")
        }
    }
    
    /// Тест: длительный сон сбрасывает лог
    static func testLongSleepResetsLog() {
        print("\n🧪 ТЕСТ 2: Длительный сон сбрасывает лог")

        // Создаем тестовый ActivityStateManager
        let manager = TestActivityStateManager()
        
        // Записываем активность
        manager.recordMinuteActivity(isActive: true)
        manager.recordMinuteActivity(isActive: false)
        manager.recordMinuteActivity(isActive: true)
        
        let historyBefore = manager.getActivityHistory()
        print("🔍 История ДО сна: \(historyBefore)")
        
        // Симулируем длительный сон (20 минут)
        let longSleepDuration: TimeInterval = 20 * 60
        manager.resetAfterSleep(sleepDuration: longSleepDuration)
        
        let historyAfter = manager.getActivityHistory()
        print("🔍 История ПОСЛЕ сна: \(historyAfter)")
        
        // Проверяем что лог сброшен
        if historyAfter.isEmpty {
            print("✅ ТЕСТ 2 ПРОЙДЕН: Лог активности сброшен")
        } else {
            print("❌ ТЕСТ 2 ПРОВАЛЕН: Лог не сброшен, осталось \(historyAfter.count) записей")
        }
    }
    
    /// Тест: обработка нулевой длительности
    static func testZeroDurationHandling() {
        print("\n🧪 ТЕСТ 3: Обработка нулевой длительности")

        // Создаем тестовый ActivityStateManager
        let manager = TestActivityStateManager()
        
        // Записываем активность
        manager.recordMinuteActivity(isActive: true)
        manager.recordMinuteActivity(isActive: true)
        
        let historyBefore = manager.getActivityHistory()
        print("🔍 История ДО сброса: \(historyBefore)")
        
        // Вызываем resetAfterSleep без параметра (старое поведение)
        manager.resetAfterSleep()
        
        let historyAfter = manager.getActivityHistory()
        print("🔍 История ПОСЛЕ сброса: \(historyAfter)")
        
        // Проверяем что история не изменилась (нет информации о длительности)
        if historyAfter == historyBefore {
            print("✅ ТЕСТ 3 ПРОЙДЕН: История не изменилась при нулевой длительности")
        } else {
            print("❌ ТЕСТ 3 ПРОВАЛЕН: История изменилась неожиданно")
        }
    }
}


