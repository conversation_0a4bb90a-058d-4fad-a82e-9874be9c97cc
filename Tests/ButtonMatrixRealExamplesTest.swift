import Foundation

/// Тест реальных примеров из отладочного окна
/// Проверяет конкретные случаи которые показывали несоответствие

func runRealExamplesTest() {
    print("🔍 ТЕСТ РЕАЛЬНЫХ ПРИМЕРОВ ИЗ ОТЛАДОЧНОГО ОКНА")
    print(String(repeating: "=", count: 70))
    
    var passed = 0
    var total = 0
    
    // ПРИМЕР 1: 4-6 дней, 1-е сообщение, планка 20 мин
    total += 1
    print("\n📋 ПРИМЕР 1: 4-6 дней, 1-е сообщение, планка 20 мин")
    print("   Ожидается: рассчитанная планка = кнопка")
    
    let example1_early = simulateEarlyEngagementCalculation(
        daysLevel: 4, messageIndex: 0, initialBar: 20
    )
    let example1_button = simulateButtonMatrixCalculation(
        daysWithoutWork: 4, messageIndex: 0, baseBarMinutes: 20
    )
    
    print("   EarlyEngagementSystem: \(example1_early) мин")
    print("   ButtonMatrix: \(example1_button) мин")
    
    if example1_early == example1_button {
        print("   ✅ ПРОЙДЕН: Значения совпадают")
        passed += 1
    } else {
        print("   ❌ ПРОВАЛЕН: Значения НЕ совпадают!")
        print("   Разница: \(abs(example1_early - example1_button)) мин")
    }
    
    // ПРИМЕР 2: 7+ дней, 1-е сообщение, планка 40 мин
    total += 1
    print("\n📋 ПРИМЕР 2: 7+ дней, 1-е сообщение, планка 40 мин")
    print("   Ожидается: рассчитанная планка = кнопка = 3 мин (план-минимум)")
    
    let example2_early = simulateEarlyEngagementCalculation(
        daysLevel: 7, messageIndex: 0, initialBar: 40
    )
    let example2_button = simulateButtonMatrixCalculation(
        daysWithoutWork: 7, messageIndex: 0, baseBarMinutes: 40
    )
    
    print("   EarlyEngagementSystem: \(example2_early) мин")
    print("   ButtonMatrix: \(example2_button) мин")
    
    if example2_early == example2_button && example2_early == 3 {
        print("   ✅ ПРОЙДЕН: Значения совпадают и равны план-минимуму")
        passed += 1
    } else {
        print("   ❌ ПРОВАЛЕН: Проблема с план-минимумом!")
        print("   Ожидалось: 3 мин")
        print("   Разница: \(abs(example2_early - example2_button)) мин")
    }
    
    // ПРИМЕР 3: Проверка граничного случая 6 дней
    total += 1
    print("\n📋 ПРИМЕР 3: 6 дней, 1-е сообщение, планка 30 мин")
    print("   Ожидается: использование логики 4-6 дней (× 0.29)")
    
    let example3_early = simulateEarlyEngagementCalculation(
        daysLevel: 6, messageIndex: 0, initialBar: 30
    )
    let example3_button = simulateButtonMatrixCalculation(
        daysWithoutWork: 6, messageIndex: 0, baseBarMinutes: 30
    )
    
    print("   EarlyEngagementSystem: \(example3_early) мин")
    print("   ButtonMatrix: \(example3_button) мин")
    
    if example3_early == example3_button {
        print("   ✅ ПРОЙДЕН: Граничный случай работает корректно")
        passed += 1
    } else {
        print("   ❌ ПРОВАЛЕН: Проблема с граничным случаем!")
        print("   Разница: \(abs(example3_early - example3_button)) мин")
    }
    
    // Итоговый отчет
    print("\n" + String(repeating: "=", count: 70))
    print("📊 ИТОГОВЫЙ ОТЧЕТ РЕАЛЬНЫХ ПРИМЕРОВ")
    print("✅ Пройдено: \(passed)/\(total)")
    print("❌ Провалено: \(total - passed)/\(total)")
    print("📈 Процент успеха: \(passed * 100 / total)%")
    
    if passed == total {
        print("🎉 ВСЕ ПРИМЕРЫ ИСПРАВЛЕНЫ! Несоответствие устранено.")
    } else {
        print("🚨 ЕСТЬ ПРОБЛЕМЫ! Требуется дополнительная работа.")
    }
}

/// Симулирует расчет EarlyEngagementSystem
func simulateEarlyEngagementCalculation(daysLevel: Int, messageIndex: Int, initialBar: Int) -> Int {
    let initialBarTime = TimeInterval(initialBar * 60)
    
    // Вертикальная адаптация
    let verticalBarTime: TimeInterval
    switch daysLevel {
    case 0:
        verticalBarTime = simulateGradualGrowth(currentBar: initialBarTime)
    case 1:
        verticalBarTime = initialBarTime * 0.77
    case 2...3:
        verticalBarTime = initialBarTime * 0.48
    case 4...6:
        verticalBarTime = initialBarTime * 0.29
    default:
        verticalBarTime = 3 * 60
    }
    
    // Горизонтальная адаптация
    let finalBarTime: TimeInterval
    switch messageIndex {
    case 0:
        finalBarTime = max(verticalBarTime, 3 * 60)
    case 1:
        finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
    case 2:
        finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
    case 3:
        finalBarTime = 3 * 60
    default:
        finalBarTime = max(verticalBarTime, 3 * 60)
    }
    
    let limitedBarTime = min(finalBarTime, 52 * 60)
    return Int(limitedBarTime / 60)
}

/// Симулирует расчет ButtonMatrix
func simulateButtonMatrixCalculation(daysWithoutWork: Int, messageIndex: Int, baseBarMinutes: Int) -> Int {
    // ИСПРАВЛЕННАЯ логика маппинга
    let vertical = max(0, daysWithoutWork)
    let horizontal = min(3, max(0, messageIndex))
    let baseBar = TimeInterval(baseBarMinutes * 60)
    
    // Вертикальная адаптация
    let verticalBar: TimeInterval
    switch vertical {
    case 0:
        verticalBar = simulateGradualGrowth(currentBar: baseBar)
    case 1:
        verticalBar = baseBar * 0.77
    case 2...3:
        verticalBar = baseBar * 0.48
    case 4...6:
        verticalBar = baseBar * 0.29
    default:
        verticalBar = 3 * 60
    }
    
    // Горизонтальная адаптация
    let finalBar: TimeInterval
    switch horizontal {
    case 0:
        finalBar = max(verticalBar, 3 * 60)
    case 1:
        finalBar = max(verticalBar * 0.5, 3 * 60)
    case 2:
        finalBar = max(verticalBar * 0.25, 3 * 60)
    case 3:
        finalBar = 3 * 60
    default:
        finalBar = max(verticalBar, 3 * 60)
    }
    
    return Int(finalBar / 60)
}

/// Симулирует GradualGrowthSystem
func simulateGradualGrowth(currentBar: TimeInterval) -> TimeInterval {
    let currentMinutes = Int(currentBar / 60)
    let multiplier: Double
    
    switch currentMinutes {
    case 3...7: multiplier = 1.60
    case 8...15: multiplier = 1.40
    case 16...25: multiplier = 1.25
    case 26...40: multiplier = 1.15
    case 41...52: multiplier = 1.10
    default: multiplier = 1.05
    }
    
    let newMinutes = Int(round(Double(currentMinutes) * multiplier))
    return TimeInterval(min(newMinutes, 52) * 60)
}

// Запускаем тест
runRealExamplesTest()
