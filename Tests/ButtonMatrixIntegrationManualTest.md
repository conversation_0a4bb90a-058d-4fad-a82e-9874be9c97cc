# Ручной тест интеграции ButtonMatrix с EarlyEngagementWindow

## Цель теста
Проверить что новая система динамических кнопок работает правильно и показывает красивые градиентные кнопки вместо старых.

## Шаги тестирования

### 1. Запуск отладочного окна
1. Запустить приложение uProd
2. Открыть отладочное окно Early Engagement (через меню или горячие клавиши)
3. Нажать кнопку "Показать сообщение"

### 2. Проверка кнопок
Ожидаемый результат:
- ✅ Должны показаться **2-3 кнопки** (вместо старых 4)
- ✅ Кнопки должны быть **градиентными** (зеленая для основной, серая для вторичных)
- ✅ Текст основной кнопки: **"Начать работу (X мин)"** (где X - время)
- ✅ Текст кнопки "Позже" вместо старых "Не сейчас" и "Через 30 минут"
- ✅ Кнопки должны быть **выровнены вертикально** (одна под другой)
- ✅ **НЕ должно быть** старых кнопок "Не сейчас", "Через 30 минут"

### 3. Проверка функциональности
1. Нажать на основную кнопку "Начать работу" - должна запуститься сессия
2. Нажать на кнопку "Позже" - окно должно закрыться
3. Если есть кнопка "Максимальная сессия" - должна запуститься полная сессия

### 4. Проверка логов
В консоли должны появиться сообщения:
```
🎯 Используем кнопки от ButtonMatrix: X
🎯 Нажата кнопка: [название] (тип: [тип])
```

## Проблемы которые исправлены

### ДО (старая система):
- 4 кнопки: "Начать работу", "Полная сессия", "Не сейчас", "Через 30 мин"
- Уродские кнопки без градиента
- Плохое выравнивание
- Хардкод текстов

### ПОСЛЕ (новая система):
- 2-3 динамические кнопки от ButtonMatrix
- Красивые градиентные кнопки как в ModernCompletionWindow
- Правильное вертикальное выравнивание
- Динамические тексты от ButtonMatrix

## Технические детали

### Изменения в коде:
1. **EarlyEngagementWindow.swift**:
   - Удалены старые статические кнопки
   - Добавлена система динамических кнопок
   - Интеграция с ButtonMatrix через `message.buttonMatrix`
   - Новый стиль кнопок с градиентом

2. **EarlyEngagementSystem.swift**:
   - Добавлено поле `buttonMatrix` в `EngagementMessage`
   - Интеграция ButtonMatrix в `createEngagementMessage()`

3. **EarlyEngagementDebugWindow.swift**:
   - Передача кнопок ButtonMatrix в сообщение
   - Правильная интеграция с новой системой

### Fallback система:
Если ButtonMatrix не работает, система автоматически создает базовые кнопки:
- "Начать работу" (из message.buttonText)
- "Позже"
- "Полная сессия" (если включена)

## Критерии успеха
- [ ] Кнопки выглядят красиво (градиент)
- [ ] Правильное количество кнопок (2-3)
- [ ] Правильные тексты от ButtonMatrix
- [ ] Кнопки работают функционально
- [ ] Нет старых кнопок
- [ ] Логи показывают интеграцию ButtonMatrix
