import Foundation

// Копируем класс для тестирования (в реальном проекте он уже есть)
class GradualGrowthSystem {
    static func calculateGrowth(currentBarMinutes: Int) -> Int {
        let newBar: Int
        switch currentBarMinutes {
        case 3...7:   newBar = Int(Double(currentBarMinutes) * 1.60)
        case 8...15:  newBar = Int(Double(currentBarMinutes) * 1.40)
        case 16...25: newBar = Int(Double(currentBarMinutes) * 1.25)
        case 26...40: newBar = Int(Double(currentBarMinutes) * 1.15)
        case 41...52: newBar = Int(Double(currentBarMinutes) * 1.10)
        default:      newBar = currentBarMinutes + 1
        }
        return min(newBar, 52)
    }

    static func calculateGrowth(currentBar: TimeInterval) -> TimeInterval {
        let currentMinutes = Int(currentBar / 60)
        let newMinutes = calculateGrowth(currentBarMinutes: currentMinutes)
        return TimeInterval(newMinutes * 60)
    }

    static func getGrowthMultiplier(currentBarMinutes: Int) -> Double {
        switch currentBarMinutes {
        case 3...7:   return 1.60
        case 8...15:  return 1.40
        case 16...25: return 1.25
        case 26...40: return 1.15
        case 41...52: return 1.10
        default:      return 1.05
        }
    }

    static func getGrowthDescription(currentBarMinutes: Int) -> String {
        let multiplier = getGrowthMultiplier(currentBarMinutes: currentBarMinutes)
        let percentage = Int((multiplier - 1.0) * 100)
        return "+\(percentage)%"
    }

    static func getGrowthCategory(currentBarMinutes: Int) -> String {
        switch currentBarMinutes {
        case 3...7:   return "🚀 Быстрый старт"
        case 8...15:  return "⚡ Активный рост"
        case 16...25: return "⚡ Активный рост"
        case 26...40: return "🎯 Стабилизация"
        case 41...52: return "🎯 Финал"
        default:      return "🔧 Особый случай"
        }
    }

    static func estimateDaysToMaximum(currentBarMinutes: Int) -> Int {
        var bar = currentBarMinutes
        var days = 0
        while bar < 52 && days < 100 {
            bar = calculateGrowth(currentBarMinutes: bar)
            days += 1
        }
        return days
    }

    static func getDebugInfo(currentBarMinutes: Int) -> String {
        let newBar = calculateGrowth(currentBarMinutes: currentBarMinutes)
        let category = getGrowthCategory(currentBarMinutes: currentBarMinutes)
        let description = getGrowthDescription(currentBarMinutes: currentBarMinutes)
        let daysToMax = estimateDaysToMaximum(currentBarMinutes: currentBarMinutes)

        return """
        📊 Градационная система роста:
        • Текущая планка: \(currentBarMinutes) мин
        • Категория: \(category)
        • Рост: \(description)
        • Новая планка: \(newBar) мин
        • До 52 мин: ~\(daysToMax) дней
        """
    }

    static func adaptUserBar(currentBar: TimeInterval, success: Bool, failureMultiplier: Double = 0.85) -> TimeInterval {
        if success {
            return calculateGrowth(currentBar: currentBar)
        } else {
            let newBar = currentBar * failureMultiplier
            return max(newBar, 2 * 60)
        }
    }

    static func applyLevel0Growth(currentBar: TimeInterval) -> TimeInterval {
        let newBar = calculateGrowth(currentBar: currentBar)
        return min(newBar, 52 * 60)
    }
}

struct GradualGrowthSystemTest {
    static func runTests() {
        print("🧪 Тестирование градационной системы роста планок...")
        
        // Тест 1: Проверка всех диапазонов роста
        testGrowthRanges()
        
        // Тест 2: Проверка максимального ограничения
        testMaximumLimit()
        
        // Тест 3: Проверка скорости восстановления
        testRecoverySpeed()
        
        // Тест 4: Проверка интеграции с EarlyEngagementSystem
        testEarlyEngagementIntegration()
        
        // Тест 5: Проверка отладочных методов
        testDebugMethods()
        
        print("✅ Все тесты градационной системы роста прошли успешно!")
    }
    
    static func testGrowthRanges() {
        print("\n📊 Тест диапазонов роста:")
        
        let testCases = [
            // Быстрый старт: 3-7 мин → +60%
            (3, 5, "🚀 Быстрый старт"),
            (5, 8, "🚀 Быстрый старт"),
            (7, 11, "🚀 Быстрый старт"),
            
            // Активный рост: 8-15 мин → +40%
            (8, 11, "⚡ Активный рост"),
            (10, 14, "⚡ Активный рост"),
            (15, 21, "⚡ Активный рост"),
            
            // Активный рост: 16-25 мин → +25%
            (16, 20, "⚡ Активный рост"),
            (20, 25, "⚡ Активный рост"),
            (25, 31, "⚡ Активный рост"),
            
            // Стабилизация: 26-40 мин → +15%
            (26, 30, "🎯 Стабилизация"),
            (30, 35, "🎯 Стабилизация"),
            (40, 46, "🎯 Стабилизация"),
            
            // Финал: 41-52 мин → +10%
            (41, 45, "🎯 Финал"),
            (45, 50, "🎯 Финал"),
            (50, 52, "🎯 Финал")
        ]
        
        for (input, expected, category) in testCases {
            let result = GradualGrowthSystem.calculateGrowth(currentBarMinutes: input)
            let actualCategory = GradualGrowthSystem.getGrowthCategory(currentBarMinutes: input)
            
            assert(result == expected, "❌ Ошибка: \(input) мин → ожидали \(expected), получили \(result)")
            assert(actualCategory == category, "❌ Ошибка категории: \(input) мин → ожидали '\(category)', получили '\(actualCategory)'")
            
            print("  ✅ \(input) мин → \(result) мин (\(category))")
        }
    }
    
    static func testMaximumLimit() {
        print("\n🔒 Тест максимального ограничения:")
        
        // Проверяем, что планка не превышает 52 минуты
        let testValues = [50, 51, 52, 60, 100]
        
        for value in testValues {
            let result = GradualGrowthSystem.calculateGrowth(currentBarMinutes: value)
            assert(result <= 52, "❌ Ошибка: планка \(value) мин дала результат \(result) мин (больше 52)")
            print("  ✅ \(value) мин → \(result) мин (≤ 52)")
        }
    }
    
    static func testRecoverySpeed() {
        print("\n🚀 Тест скорости восстановления:")
        
        // Проверяем время восстановления с 3 минут до 52
        let startBar = 3
        var currentBar = startBar
        var days = 0
        
        print("  Восстановление с \(startBar) мин:")
        while currentBar < 52 && days < 50 {
            let newBar = GradualGrowthSystem.calculateGrowth(currentBarMinutes: currentBar)
            days += 1
            print("    День \(days): \(currentBar) → \(newBar) мин")
            currentBar = newBar
        }
        
        // Проверяем, что восстановление происходит быстрее старого +10%
        assert(days <= 30, "❌ Ошибка: восстановление заняло \(days) дней (должно быть ≤ 30)")
        print("  ✅ Восстановление за \(days) дней (быстрее старого +10%)")
        
        // Проверяем оценку времени
        let estimatedDays = GradualGrowthSystem.estimateDaysToMaximum(currentBarMinutes: 3)
        assert(abs(estimatedDays - days) <= 2, "❌ Ошибка оценки: оценили \(estimatedDays), реально \(days)")
        print("  ✅ Оценка времени: \(estimatedDays) дней (точность ±2)")
    }
    
    static func testEarlyEngagementIntegration() {
        print("\n🔗 Тест интеграции с EarlyEngagementSystem:")
        
        // Тестируем TimeInterval версию
        let testBars: [TimeInterval] = [3*60, 10*60, 25*60, 40*60, 50*60]
        
        for bar in testBars {
            let minutes = Int(bar / 60)
            let expectedMinutes = GradualGrowthSystem.calculateGrowth(currentBarMinutes: minutes)
            let expectedSeconds = TimeInterval(expectedMinutes * 60)
            
            let result = GradualGrowthSystem.calculateGrowth(currentBar: bar)
            
            assert(result == expectedSeconds, "❌ Ошибка TimeInterval: \(minutes) мин → ожидали \(expectedSeconds)с, получили \(result)с")
            print("  ✅ \(minutes) мин → \(Int(result/60)) мин (TimeInterval)")
        }
        
        // Тестируем метод adaptUserBar
        let testSuccess = GradualGrowthSystem.adaptUserBar(currentBar: 10*60, success: true)
        let testFailure = GradualGrowthSystem.adaptUserBar(currentBar: 10*60, success: false)
        
        assert(testSuccess == 14*60, "❌ Ошибка успеха: 10 мин → ожидали 14 мин, получили \(Int(testSuccess/60)) мин")
        assert(testFailure == 8.5*60, "❌ Ошибка неудачи: 10 мин → ожидали 8.5 мин, получили \(Int(testFailure/60)) мин")
        
        print("  ✅ adaptUserBar: успех 10→14 мин, неудача 10→8.5 мин")
        
        // Тестируем applyLevel0Growth
        let level0Result = GradualGrowthSystem.applyLevel0Growth(currentBar: 30*60)
        assert(level0Result == 35*60, "❌ Ошибка Level0: 30 мин → ожидали 35 мин, получили \(Int(level0Result/60)) мин")
        print("  ✅ applyLevel0Growth: 30→35 мин")
    }
    
    static func testDebugMethods() {
        print("\n🔍 Тест отладочных методов:")
        
        // Тестируем getGrowthMultiplier
        let multiplier3 = GradualGrowthSystem.getGrowthMultiplier(currentBarMinutes: 3)
        let multiplier15 = GradualGrowthSystem.getGrowthMultiplier(currentBarMinutes: 15)
        let multiplier40 = GradualGrowthSystem.getGrowthMultiplier(currentBarMinutes: 40)
        
        assert(multiplier3 == 1.60, "❌ Ошибка множителя: 3 мин → ожидали 1.60, получили \(multiplier3)")
        assert(multiplier15 == 1.40, "❌ Ошибка множителя: 15 мин → ожидали 1.40, получили \(multiplier15)")
        assert(multiplier40 == 1.15, "❌ Ошибка множителя: 40 мин → ожидали 1.15, получили \(multiplier40)")
        
        print("  ✅ Множители: 3мин=1.60, 15мин=1.40, 40мин=1.15")
        
        // Тестируем getGrowthDescription
        let desc3 = GradualGrowthSystem.getGrowthDescription(currentBarMinutes: 3)
        let desc15 = GradualGrowthSystem.getGrowthDescription(currentBarMinutes: 15)
        let desc40 = GradualGrowthSystem.getGrowthDescription(currentBarMinutes: 40)
        
        assert(desc3 == "+60%", "❌ Ошибка описания: 3 мин → ожидали '+60%', получили '\(desc3)'")
        assert(desc15 == "+40%", "❌ Ошибка описания: 15 мин → ожидали '+40%', получили '\(desc15)'")
        assert(desc40 == "+15%", "❌ Ошибка описания: 40 мин → ожидали '+15%', получили '\(desc40)'")
        
        print("  ✅ Описания: 3мин='+60%', 15мин='+40%', 40мин='+15%'")
        
        // Тестируем getDebugInfo
        let debugInfo = GradualGrowthSystem.getDebugInfo(currentBarMinutes: 10)
        assert(debugInfo.contains("10 мин"), "❌ Ошибка отладки: не содержит '10 мин'")
        assert(debugInfo.contains("14 мин"), "❌ Ошибка отладки: не содержит '14 мин'")
        assert(debugInfo.contains("⚡ Активный рост"), "❌ Ошибка отладки: не содержит категорию")
        
        print("  ✅ getDebugInfo содержит всю необходимую информацию")
    }
}

// Запуск тестов
GradualGrowthSystemTest.runTests()
