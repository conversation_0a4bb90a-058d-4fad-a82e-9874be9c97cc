#!/usr/bin/env swift

import Foundation

/// Тест системы адаптации планки пользователя по дням без работы
/// 
/// ПРОВЕРЯЕТ:
/// - Планка остается стабильной при daysWithoutWork = 0 (работал вчера)
/// - Планка уменьшается при пропусках работы (1, 2-3, 4-6, 7+ дней)
/// - Градационный рост применяется только после успешного завершения интервала
/// - Система восстанавливается после возвращения к работе

struct UserBarAdaptationTest {

    static func main() {
        print("🧪 Тест системы адаптации планки пользователя")
        print(String(repeating: "=", count: 50))
        
        testStableBarWhenWorkedYesterday()
        print()
        testBarReductionByDaysWithoutWork()
        print()
        testGradualGrowthAfterSuccess()
        print()
        testRecoveryAfterBreak()

        print(String(repeating: "=", count: 50))
        print("✅ Все тесты пройдены!")
    }

    /// Тест 1: Планка остается стабильной при daysWithoutWork = 0
    static func testStableBarWhenWorkedYesterday() {
        print("🔄 Тест 1: Стабильность планки при работе вчера")
        
        let testCases: [TimeInterval] = [3*60, 10*60, 30*60, 52*60] // 3, 10, 30, 52 минуты
        
        for initialBar in testCases {
            let adaptedBar = adaptUserBarByDaysWithoutWork(
                currentBar: initialBar,
                daysWithoutWork: 0
            )
            
            let initialMinutes = Int(initialBar / 60)
            let adaptedMinutes = Int(adaptedBar / 60)
            
            let status = (adaptedMinutes == initialMinutes) ? "✅" : "❌"
            print("   \(status) \(initialMinutes) мин → \(adaptedMinutes) мин (должно остаться \(initialMinutes) мин)")
            
            if adaptedMinutes != initialMinutes {
                print("      ⚠️  ОШИБКА: Планка изменилась при daysWithoutWork = 0!")
            }
        }
    }
    
    /// Тест 2: Уменьшение планки по дням без работы
    static func testBarReductionByDaysWithoutWork() {
        print("🔄 Тест 2: Уменьшение планки по дням без работы")
        
        let baseBar: TimeInterval = 52 * 60 // Базовая планка 52 минуты
        let testCases = [
            (days: 1, expectedMultiplier: 0.77, description: "1 день пропуск"),
            (days: 2, expectedMultiplier: 0.48, description: "2-3 дня пропуск"),
            (days: 3, expectedMultiplier: 0.48, description: "2-3 дня пропуск"),
            (days: 4, expectedMultiplier: 0.29, description: "4-6 дней пропуск"),
            (days: 6, expectedMultiplier: 0.29, description: "4-6 дней пропуск"),
            (days: 7, expectedMultiplier: 0.0, description: "7+ дней (план-минимум 3 мин)"),
            (days: 10, expectedMultiplier: 0.0, description: "7+ дней (план-минимум 3 мин)")
        ]
        
        for testCase in testCases {
            let adaptedBar = adaptUserBarByDaysWithoutWork(
                currentBar: baseBar,
                daysWithoutWork: testCase.days
            )
            
            let expectedBar: TimeInterval
            if testCase.expectedMultiplier == 0.0 {
                expectedBar = 3 * 60 // План-минимум
            } else {
                expectedBar = baseBar * testCase.expectedMultiplier
            }
            
            let adaptedMinutes = Int(adaptedBar / 60)
            let expectedMinutes = Int(expectedBar / 60)
            
            let status = (adaptedMinutes == expectedMinutes) ? "✅" : "❌"
            print("   \(status) \(testCase.days) дней: 52 мин → \(adaptedMinutes) мин (ожидалось: \(expectedMinutes) мин) - \(testCase.description)")
            
            if adaptedMinutes != expectedMinutes {
                print("      ⚠️  ОШИБКА: Неправильная адаптация для \(testCase.days) дней!")
            }
        }
    }
    
    /// Тест 3: Градационный рост после успешного завершения
    static func testGradualGrowthAfterSuccess() {
        print("🔄 Тест 3: Градационный рост после успешного завершения")
        
        let testCases: [TimeInterval] = [3*60, 5*60, 10*60, 20*60] // 3, 5, 10, 20 минут
        
        for currentBar in testCases {
            let grownBar = applyGradualGrowth(currentBar: currentBar)
            
            let currentMinutes = Int(currentBar / 60)
            let grownMinutes = Int(grownBar / 60)
            
            let growthPercent = Int(((grownBar - currentBar) / currentBar) * 100)
            let status = (grownBar > currentBar) ? "✅" : "❌"
            
            print("   \(status) \(currentMinutes) мин → \(grownMinutes) мин (+\(growthPercent)%)")
            
            if grownBar <= currentBar {
                print("      ⚠️  ОШИБКА: Планка не выросла после успешного завершения!")
            }
        }
    }
    
    /// Тест 4: Восстановление после перерыва
    static func testRecoveryAfterBreak() {
        print("🔄 Тест 4: Восстановление планки после перерыва")
        
        // Симуляция: была планка 30 мин, не работал 3 дня, потом вернулся
        let originalBar: TimeInterval = 30 * 60
        
        // Шаг 1: Адаптация после 3 дней без работы
        let reducedBar = adaptUserBarByDaysWithoutWork(
            currentBar: originalBar,
            daysWithoutWork: 3
        )
        
        // Шаг 2: Возвращение к работе (daysWithoutWork = 0)
        let stableBar = adaptUserBarByDaysWithoutWork(
            currentBar: reducedBar,
            daysWithoutWork: 0
        )
        
        // Шаг 3: Рост после успешного завершения
        let grownBar = applyGradualGrowth(currentBar: stableBar)
        
        let originalMinutes = Int(originalBar / 60)
        let reducedMinutes = Int(reducedBar / 60)
        let stableMinutes = Int(stableBar / 60)
        let grownMinutes = Int(grownBar / 60)
        
        print("   📊 Исходная планка: \(originalMinutes) мин")
        print("   📉 После 3 дней без работы: \(reducedMinutes) мин")
        print("   📊 При возвращении (0 дней): \(stableMinutes) мин (должна остаться \(reducedMinutes) мин)")
        print("   📈 После успешного завершения: \(grownMinutes) мин")
        
        let stableStatus = (stableMinutes == reducedMinutes) ? "✅" : "❌"
        let growthStatus = (grownMinutes > stableMinutes) ? "✅" : "❌"
        
        print("   \(stableStatus) Стабильность при возвращении")
        print("   \(growthStatus) Рост после успешного завершения")
    }
    
    // MARK: - Helper Methods (копии логики из EarlyEngagementSystem)
    
    /// Адаптирует планку по дням без работы (логика из EarlyEngagementSystem)
    static func adaptUserBarByDaysWithoutWork(currentBar: TimeInterval, daysWithoutWork: Int) -> TimeInterval {
        let baseBar: TimeInterval = 52 * 60 // Базовая планка 52 минуты
        
        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка остается неизменной при запуске
            return currentBar
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%)
            return baseBar * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%)
            return baseBar * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%)
            return baseBar * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум)
            return 3 * 60
        }
    }
    
    /// Применяет градационный рост (упрощенная версия)
    static func applyGradualGrowth(currentBar: TimeInterval) -> TimeInterval {
        let currentMinutes = Int(currentBar / 60)
        
        // Упрощенная логика градационного роста
        switch currentMinutes {
        case 1...5:
            return currentBar * 1.3 // +30%
        case 6...15:
            return currentBar * 1.2 // +20%
        case 16...30:
            return currentBar * 1.15 // +15%
        default:
            return currentBar * 1.1 // +10%
        }
    }
}

UserBarAdaptationTest.main()
