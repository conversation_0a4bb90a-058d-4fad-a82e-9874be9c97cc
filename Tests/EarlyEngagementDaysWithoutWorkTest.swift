#!/usr/bin/env swift

import Foundation

/// Тест для проверки исправления логики дней без работы в EarlyEngagementSystem
///
/// ПРОБЛЕМА: При 7+ днях без работы система показывала 8 минут вместо 3 минут
/// ПРИЧИНА: Двойное применение коэффициентов в adaptUserBarByDaysWithoutWork() и calculateBarTime()
/// ИСПРАВЛЕНИЕ: Унифицировали логику, чтобы для 7+ дней всегда использовался план-минимум 3 минуты

struct EarlyEngagementDaysWithoutWorkTest {
    
    static func main() {
        print("🧪 Тест исправления логики дней без работы")
        print("=" * 50)
        
        testAdaptUserBarByDaysWithoutWork()
        print()
        testCalculateBarTimeConsistency()
        print()
        testDocumentationCompliance()
        
        print("=" * 50)
        print("✅ Все тесты пройдены!")
    }
    
    /// Тестирует логику адаптации планки по дням без работы
    static func testAdaptUserBarByDaysWithoutWork() {
        print("📊 Тест 1: Адаптация планки по дням без работы")
        
        let baseBar: TimeInterval = 52 * 60 // 52 минуты
        
        let testCases = [
            (days: 0, expected: 52, description: "Работал вчера"),
            (days: 1, expected: 40, description: "1 день пропуск"),
            (days: 2, expected: 25, description: "2 дня пропуск"),
            (days: 3, expected: 25, description: "3 дня пропуск"),
            (days: 4, expected: 15, description: "4 дня пропуск"),
            (days: 5, expected: 15, description: "5 дней пропуск"),
            (days: 6, expected: 15, description: "6 дней пропуск"),
            (days: 7, expected: 3, description: "7+ дней пропуск"),
            (days: 10, expected: 3, description: "10+ дней пропуск")
        ]
        
        for testCase in testCases {
            let adaptedBar = calculateAdaptedBar(daysWithoutWork: testCase.days, baseBar: baseBar)
            let actualMinutes = Int(adaptedBar / 60)
            
            let status = actualMinutes == testCase.expected ? "✅" : "❌"
            print("   \(status) \(testCase.description): \(actualMinutes) мин (ожидалось: \(testCase.expected))")
            
            if actualMinutes != testCase.expected {
                print("      ⚠️  ОШИБКА: Ожидалось \(testCase.expected) мин, получено \(actualMinutes) мин")
            }
        }
    }
    
    /// Тестирует согласованность между adaptUserBarByDaysWithoutWork и calculateBarTime
    static func testCalculateBarTimeConsistency() {
        print("🔄 Тест 2: Согласованность логики между методами")
        
        let baseBar: TimeInterval = 30 * 60 // 30 минут (типичная планка)
        
        // Тестируем критический случай: 7+ дней без работы
        let daysWithoutWork = 7
        
        // Логика из adaptUserBarByDaysWithoutWork
        let adaptedBar = calculateAdaptedBar(daysWithoutWork: daysWithoutWork, baseBar: baseBar)
        
        // Логика из calculateBarTime (первое сообщение, 100% планки)
        let calculatedBarTime = calculateBarTime(
            daysWithoutWork: daysWithoutWork,
            messageIndex: 0,
            initialBarTime: baseBar
        )
        
        let adaptedMinutes = Int(adaptedBar / 60)
        let calculatedMinutes = calculatedBarTime
        
        print("   📊 Дней без работы: \(daysWithoutWork)")
        print("   📊 Базовая планка: \(Int(baseBar/60)) мин")
        print("   🔽 adaptUserBarByDaysWithoutWork: \(adaptedMinutes) мин")
        print("   🔽 calculateBarTime (1-е сообщение): \(calculatedMinutes) мин")
        
        let isConsistent = adaptedMinutes == calculatedMinutes
        let status = isConsistent ? "✅" : "❌"
        print("   \(status) Согласованность: \(isConsistent ? "ОК" : "ОШИБКА")")
        
        if !isConsistent {
            print("      ⚠️  КРИТИЧЕСКАЯ ОШИБКА: Методы возвращают разные значения!")
        }
    }
    
    /// Тестирует соответствие документации
    static func testDocumentationCompliance() {
        print("📋 Тест 3: Соответствие документации")
        
        let documentationExpectations = [
            (days: 1, expected: 40, description: "1 день → 40 мин"),
            (days: 3, expected: 25, description: "2-3 дня → 25 мин"),
            (days: 5, expected: 15, description: "4-6 дней → 15 мин"),
            (days: 7, expected: 3, description: "7+ дней → 3 мин (план-минимум)")
        ]
        
        let baseBar: TimeInterval = 52 * 60
        
        for expectation in documentationExpectations {
            let actualBar = calculateAdaptedBar(daysWithoutWork: expectation.days, baseBar: baseBar)
            let actualMinutes = Int(actualBar / 60)
            
            let status = actualMinutes == expectation.expected ? "✅" : "❌"
            print("   \(status) \(expectation.description): \(actualMinutes) мин")
            
            if actualMinutes != expectation.expected {
                print("      ⚠️  НЕ СООТВЕТСТВУЕТ ДОКУМЕНТАЦИИ!")
            }
        }
    }
    
    // MARK: - Helper Methods
    
    /// Рассчитывает адаптированную планку по дням без работы (логика из EarlyEngagementSystem)
    static func calculateAdaptedBar(daysWithoutWork: Int, baseBar: TimeInterval) -> TimeInterval {
        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка остается базовой
            return baseBar
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%) = ~40 мин
            return baseBar * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%) = ~25 мин
            return baseBar * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%) = ~15 мин
            return baseBar * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            return 3 * 60
        }
    }
    
    /// Рассчитывает время интервала (логика из calculateBarTime в EarlyEngagementSystem)
    static func calculateBarTime(daysWithoutWork: Int, messageIndex: Int, initialBarTime: TimeInterval) -> Int {
        // ВЕРТИКАЛЬНАЯ адаптация (по дням без работы)
        let verticalBarTime: TimeInterval
        switch daysWithoutWork {
        case 0:
            verticalBarTime = initialBarTime
        case 1:
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            verticalBarTime = initialBarTime * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            verticalBarTime = 3 * 60
        }
        
        // ГОРИЗОНТАЛЬНАЯ дескалация (по времени дня)
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            // 1-е предложение: 100% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            // 2-е предложение: 50% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            // 3-е предложение: 25% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
        case 3:
            // 4-е предложение: план-минимум (3 мин)
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }
        
        // Ограничиваем максимум 52 минуты
        let limitedBarTime = min(finalBarTime, 52 * 60)
        
        return Int(limitedBarTime / 60)
    }
}

// Расширение для повторения строк
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// Запуск тестов
EarlyEngagementDaysWithoutWorkTest.main()
