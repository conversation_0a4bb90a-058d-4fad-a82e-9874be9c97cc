import Foundation

/// Тест системы выходных
print("🏖️ Запуск тестов системы выходных...")

// Простая проверка что файлы существуют
print("📁 Проверяем наличие файлов системы выходных...")

let fileManager = FileManager.default
let currentPath = fileManager.currentDirectoryPath

let weekendManagerPath = "\(currentPath)/SimplePomodoroTest/WeekendManager.swift"
let messageManagerPath = "\(currentPath)/SimplePomodoroTest/WeekendMessageManager.swift"
let choiceWindowPath = "\(currentPath)/SimplePomodoroTest/WeekendChoiceWindow.swift"
let adviceWindowPath = "\(currentPath)/SimplePomodoroTest/RestAdviceWindow.swift"

print("📄 WeekendManager.swift: \(fileManager.fileExists(atPath: weekendManagerPath) ? "✅" : "❌")")
print("📄 WeekendMessageManager.swift: \(fileManager.fileExists(atPath: messageManagerPath) ? "✅" : "❌")")
print("📄 WeekendChoiceWindow.swift: \(fileManager.fileExists(atPath: choiceWindowPath) ? "✅" : "❌")")
print("📄 RestAdviceWindow.swift: \(fileManager.fileExists(atPath: adviceWindowPath) ? "✅" : "❌")")

// Проверяем что файлы не пустые
func checkFileContent(_ path: String, name: String) {
    if let content = try? String(contentsOfFile: path) {
        let lines = content.components(separatedBy: .newlines).filter { !$0.trimmingCharacters(in: .whitespaces).isEmpty }
        print("📊 \(name): \(lines.count) строк кода")

        // Проверяем ключевые элементы
        if name.contains("WeekendManager") {
            print("   - Содержит WorkStrategy: \(content.contains("enum WorkStrategy") ? "✅" : "❌")")
            print("   - Содержит singleton: \(content.contains("static let shared") ? "✅" : "❌")")
            print("   - Содержит isWeekendDay: \(content.contains("func isWeekendDay") ? "✅" : "❌")")
        }

        if name.contains("MessageManager") {
            print("   - Содержит WeekendMessage: \(content.contains("struct WeekendMessage") ? "✅" : "❌")")
            print("   - Содержит getMessageForDaysWithoutRest: \(content.contains("func getMessageForDaysWithoutRest") ? "✅" : "❌")")
        }

        if name.contains("ChoiceWindow") {
            print("   - Содержит NSWindow: \(content.contains("class WeekendChoiceWindow: NSWindow") ? "✅" : "❌")")
            print("   - Содержит showWeekendChoice: \(content.contains("func showWeekendChoice") ? "✅" : "❌")")
        }

        if name.contains("AdviceWindow") {
            print("   - Содержит NSWindow: \(content.contains("class RestAdviceWindow: NSWindow") ? "✅" : "❌")")
            print("   - Содержит советы: \(content.contains("restAdvices") ? "✅" : "❌")")
        }
    } else {
        print("❌ Не удалось прочитать файл \(name)")
    }
}

if fileManager.fileExists(atPath: weekendManagerPath) {
    checkFileContent(weekendManagerPath, name: "WeekendManager")
}

if fileManager.fileExists(atPath: messageManagerPath) {
    checkFileContent(messageManagerPath, name: "MessageManager")
}

if fileManager.fileExists(atPath: choiceWindowPath) {
    checkFileContent(choiceWindowPath, name: "ChoiceWindow")
}

if fileManager.fileExists(atPath: adviceWindowPath) {
    checkFileContent(adviceWindowPath, name: "AdviceWindow")
}
        print("🏖️ Запуск тестов системы выходных...")

// Проверяем интеграцию с существующими файлами
print("\n🔗 Проверяем интеграцию с существующими файлами...")

let appDelegatePath = "\(currentPath)/SimplePomodoroTest/AppDelegate.swift"
let activityManagerPath = "\(currentPath)/SimplePomodoroTest/ActivityStateManager.swift"
let dailyWorkloadPath = "\(currentPath)/SimplePomodoroTest/DailyWorkloadManager.swift"
let settingsWindowPath = "\(currentPath)/SimplePomodoroTest/SettingsWindow.swift"

func checkIntegration(_ path: String, name: String, searchTerms: [String]) {
    if let content = try? String(contentsOfFile: path) {
        print("🔍 \(name):")
        for term in searchTerms {
            let found = content.contains(term)
            print("   - \(term): \(found ? "✅" : "❌")")
        }
    } else {
        print("❌ Файл \(name) не найден")
    }
}

if fileManager.fileExists(atPath: appDelegatePath) {
    checkIntegration(appDelegatePath, name: "AppDelegate", searchTerms: [
        "var weekendChoiceWindow",
        "onWeekendChoice",
        "showWeekendChoice",
        "WeekendManager.shared"
    ])
}

if fileManager.fileExists(atPath: activityManagerPath) {
    checkIntegration(activityManagerPath, name: "ActivityStateManager", searchTerms: [
        "onWeekendChoice",
        "WeekendManager.shared.shouldShowWeekendChoice"
    ])
}

if fileManager.fileExists(atPath: dailyWorkloadPath) {
    checkIntegration(dailyWorkloadPath, name: "DailyWorkloadManager", searchTerms: [
        "WeekendManager.shared.shouldShowWeekendChoice",
        "didWorkTodayOnPriorityProject"
    ])
}

if fileManager.fileExists(atPath: settingsWindowPath) {
    checkIntegration(settingsWindowPath, name: "SettingsWindow", searchTerms: [
        "weekendStrategyPopUp",
        "createWeekendStrategyContainer",
        "weekendStrategyChanged"
    ])
}

print("\n✅ Проверка файлов системы выходных завершена!")
print("🎯 Система выходных готова к тестированию в приложении!")
