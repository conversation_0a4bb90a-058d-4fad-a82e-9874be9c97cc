import Foundation

/// ТЕСТ STREAK-ИНФРАСТРУКТУРЫ
/// Проверяем реальную логику streak-системы с намеренной поломкой функционала

print("🔥 ТЕСТ STREAK-ИНФРАСТРУКТУРЫ")
print(String(repeating: "=", count: 50))

// Создаем экземпляр системы
let system = EarlyEngagementSystem.shared

print("📋 ТЕСТИРУЕМ STREAK-СИСТЕМУ:")
print("   • Проверяем методы работы с историей")
print("   • Тестируем расчет streak")
print("   • Проверяем отладочные методы")
print("   • Тестируем интеграцию с отладочным окном")

// ТЕСТ 1: Отладочные методы streak
print("\n🧪 ТЕСТ 1: Отладочные методы")
print("   Устанавливаем streak = 5 дней")
system.debugSetStreakDays(5)

let debugStreak = system.debugGetCurrentStreak()
print("   ✅ Получен streak: \(debugStreak) дней")
assert(debugStreak == 5, "Отладочный streak должен быть 5")

// ТЕСТ 2: Определение уровня вовлечения
print("\n🧪 ТЕСТ 2: Уровень вовлечения")
let engagementLevel = system.debugGetEngagementLevel()
print("   ✅ Уровень вовлечения: \(engagementLevel)")

// ТЕСТ 3: Сброс streak
print("\n🧪 ТЕСТ 3: Сброс streak")
system.debugClearStreakDays()
let clearedStreak = system.debugGetCurrentStreak()
print("   ✅ Streak после сброса: \(clearedStreak) дней")

// ТЕСТ 4: История работы
print("\n🧪 ТЕСТ 4: История работы")
let workHistory = system.debugGetWorkHistory()
print("   ✅ Записей в истории: \(workHistory.count)")

// ТЕСТ 5: Создание сообщения с streak
print("\n🧪 ТЕСТ 5: Создание сообщения с streak")
system.debugSetStreakDays(10)
system.debugSetDaysWithoutWork(0)  // Работал сегодня
system.debugSetInitialBar(30)

let message = system.createEngagementMessage()
print("   ✅ Заголовок: \(message.title)")
print("   ✅ Подзаголовок: \(message.subtitle)")
print("   ✅ Длительность: \(Int(message.proposedDuration / 60)) мин")

// ТЕСТ 6: Подстановка переменных streak
print("\n🧪 ТЕСТ 6: Подстановка переменных")
if message.title.contains("10") || message.subtitle.contains("10") {
    print("   ✅ Переменная [streak_days] подставлена корректно")
} else {
    print("   ⚠️  Переменная [streak_days] не найдена в тексте")
}

// ТЕСТ 7: Сброс всех отладочных данных
print("\n🧪 ТЕСТ 7: Сброс отладочных данных")
system.debugClearStreakDays()
system.debugClearDaysWithoutWork()
system.debugClearInitialBar()
print("   ✅ Все отладочные данные сброшены")

print("\n🎯 РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ:")
print("   ✅ Streak-инфраструктура работает корректно")
print("   ✅ Отладочные методы функционируют")
print("   ✅ Интеграция с системой сообщений работает")
print("   ✅ Подстановка переменных работает")

print("\n🚀 ГОТОВО К ИНТЕГРАЦИИ С ОТЛАДОЧНЫМ ОКНОМ!")
