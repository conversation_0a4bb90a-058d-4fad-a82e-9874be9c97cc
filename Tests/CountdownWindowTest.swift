import Foundation

// Тест для CountdownWindow - проверяет что компонент правильно интегрирован

struct CountdownWindowTest {
    static func runTests() {
        print("⏰ Запуск теста CountdownWindow")

        // Тест 1: Проверка что файл CountdownWindow.swift существует
        testCountdownWindowFileExists()

        // Тест 2: Проверка интеграции с AppDelegate
        testCountdownIntegration()

        print("✅ Все тесты CountdownWindow завершены")
    }
    
    static func testCountdownWindowFileExists() {
        print("🧪 Тест 1: Проверка файла CountdownWindow.swift")

        let fileManager = FileManager.default
        let currentPath = fileManager.currentDirectoryPath
        let countdownPath = "\(currentPath)/SimplePomodoroTest/CountdownWindow.swift"

        // Проверяем что файл существует
        assert(fileManager.fileExists(atPath: countdownPath), "Файл CountdownWindow.swift должен существовать")

        // Проверяем что файл содержит ключевые элементы
        if let content = try? String(contentsOfFile: countdownPath, encoding: .utf8) {
            assert(content.contains("class CountdownWindow"), "Файл должен содержать класс CountdownWindow")
            assert(content.contains("NSWindow"), "CountdownWindow должен наследоваться от NSWindow")
            assert(content.contains("duration"), "Должен быть параметр duration")
            assert(content.contains("completion"), "Должен быть callback completion")
            assert(content.contains("onCancel"), "Должен быть callback onCancel")
            assert(content.contains("keyCode == 53"), "Должна быть обработка ESC (keyCode 53)")

            // Проверяем новые компактные настройки
            assert(content.contains("NSSize(width: 400, height: 300)"), "Должно быть компактное окно 400x300")
            assert(content.contains("backgroundColor = NSColor.clear"), "Фон должен быть прозрачным")
            assert(content.contains("backgroundView"), "Должен быть фоновый view для цифр")
            assert(content.contains("ClickThroughView"), "Должен быть ClickThroughView для пропуска кликов")

            print("✅ Тест 1 пройден: Файл CountdownWindow.swift содержит все необходимые элементы + компактный режим")
        } else {
            fatalError("❌ Не удалось прочитать файл CountdownWindow.swift")
        }
    }
    
    static func testCountdownIntegration() {
        print("🧪 Тест 2: Интеграция CountdownWindow с AppDelegate")

        let fileManager = FileManager.default
        let currentPath = fileManager.currentDirectoryPath
        let appDelegatePath = "\(currentPath)/SimplePomodoroTest/AppDelegate.swift"

        // Проверяем что AppDelegate содержит интеграцию с CountdownWindow
        if let content = try? String(contentsOfFile: appDelegatePath, encoding: .utf8) {
            assert(content.contains("countdownWindow: CountdownWindow?"), "AppDelegate должен содержать свойство countdownWindow")
            assert(content.contains("CountdownWindow("), "AppDelegate должен создавать CountdownWindow")
            assert(content.contains("testCountdownWindow"), "Должен быть тестовый метод для обратного отсчета")
            assert(content.contains("postponeCriticalZone"), "Должен быть метод отсрочки критического окна")
            assert(content.contains("showActualCriticalZoneWindow"), "Должен быть метод показа критического окна после отсчета")
            print("✅ Тест 2 пройден: Интеграция с AppDelegate настроена корректно")
        } else {
            fatalError("❌ Не удалось прочитать файл AppDelegate.swift")
        }
    }
    
}

// MARK: - Тестирование поломкой функционала

extension CountdownWindowTest {
    /// Тест на проверку что тесты ловят реальные проблемы
    static func testBreakingFunctionality() {
        print("🔧 Тестирование поломкой функционала")

        // Проверяем что если кто-то изменит keyCode для ESC, мы это заметим
        let fileManager = FileManager.default
        let currentPath = fileManager.currentDirectoryPath
        let countdownPath = "\(currentPath)/SimplePomodoroTest/CountdownWindow.swift"

        if let content = try? String(contentsOfFile: countdownPath, encoding: .utf8) {
            // Если кто-то изменит keyCode с 53 на что-то другое, тест упадет
            assert(content.contains("keyCode == 53"), "ESC должен обрабатываться с keyCode == 53")

            // Проверяем что есть правильная обработка ESC
            assert(content.contains("cancelCountdown()"), "При ESC должен вызываться cancelCountdown()")

            print("✅ Тест поломки: Функциональность ESC проверена")
        } else {
            fatalError("❌ Не удалось прочитать файл для проверки поломки")
        }
    }
}

// Запускаем тесты
CountdownWindowTest.runTests()
