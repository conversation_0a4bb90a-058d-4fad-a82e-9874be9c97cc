import Foundation

/// ФИНАЛЬНЫЙ РЕАЛЬНЫЙ ТЕСТ унифицированной системы активности
/// Тестируем MinuteActivityTracker + ActivityStateManager без сложных зависимостей

@main
struct RealUnifiedSystemFinalTest {
    static func main() {
        print("🚀 ФИНАЛЬНЫЙ РЕАЛЬНЫЙ ТЕСТ унифицированной системы активности")
        print(String(repeating: "=", count: 70))
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Тест MinuteActivityTracker
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Тест MinuteActivityTracker")
        
        let tracker = MinuteActivityTracker()
        tracker.startTracking()
        
        // Симулируем активность в текущем банде
        tracker.simulateActivityInCurrentBand()
        let hasActivity = tracker.wasCurrentMinuteActive()
        
        tracker.stopTracking()
        
        if hasActivity {
            print("✅ ПРОЙДЕН: MinuteActivityTracker работает корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: MinuteActivityTracker не обнаруживает активность")
        }
        
        // СЦЕНАРИЙ 2: Тест ActivityStateManager
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Тест ActivityStateManager")
        
        let stateManager = ActivityStateManager()
        stateManager.start()
        
        let startInfo = stateManager.getCurrentStateInfo()
        
        // Симулируем активность
        stateManager.simulateActivity()
        let afterActivityInfo = stateManager.getCurrentStateInfo()
        
        stateManager.stop()
        
        if startInfo.state == .working && afterActivityInfo.state == .working {
            print("✅ ПРОЙДЕН: ActivityStateManager работает корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: ActivityStateManager неправильно управляет состояниями")
            print("   Начальное: \(startInfo.state), После активности: \(afterActivityInfo.state)")
        }
        
        // СЦЕНАРИЙ 3: Интеграционный тест
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Интеграционный тест")
        
        let integratedStateManager = ActivityStateManager()
        integratedStateManager.start()
        
        // Симулируем активность через внутренний MinuteActivityTracker
        integratedStateManager.simulateActivity()
        
        let debugInfo = integratedStateManager.getDebugInfo()
        
        integratedStateManager.stop()
        
        if debugInfo.contains("Трекер активен: true") {
            print("✅ ПРОЙДЕН: Интеграция MinuteActivityTracker + ActivityStateManager работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с интеграцией")
            print("   Debug info: \(debugInfo)")
        }
        
        // СЦЕНАРИЙ 4: Тест состояний активности
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Тест состояний активности")
        
        let stateTestManager = ActivityStateManager()
        stateTestManager.start()
        
        // Симулируем неактивность (16 секунд - больше порога в 15 секунд)
        stateTestManager.simulateInactivity(duration: 16.0)
        
        let inactiveInfo = stateTestManager.getCurrentStateInfo()
        
        stateTestManager.stop()
        
        if inactiveInfo.state != .working {
            print("✅ ПРОЙДЕН: Переходы состояний работают корректно")
            print("   Состояние после неактивности: \(inactiveInfo.state)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Состояния не переключаются при неактивности")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ УНИФИЦИРОВАННОЙ СИСТЕМЫ ПРОЙДЕНЫ!")
            print("✅ MinuteActivityTracker работает корректно")
            print("✅ ActivityStateManager работает корректно")
            print("✅ Интеграция между компонентами работает")
            print("✅ Переходы состояний функционируют правильно")
            
            print("\n🔨 Для проверки обнаружения поломок:")
            print("   1. Сломай метод wasCurrentMinuteActive() в MinuteActivityTracker.swift")
            print("      (например, всегда возвращай false)")
            print("   2. Запусти тест снова - СЦЕНАРИЙ 1 должен провалиться")
            print("   3. Верни код обратно")
            print("\n💡 Это подтверждает что тесты проверяют РЕАЛЬНЫЙ код!")
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В УНИФИЦИРОВАННОЙ СИСТЕМЕ!")
            exit(1)
        }
    }
}
