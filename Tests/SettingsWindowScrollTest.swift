import Cocoa
import Foundation

/// Тест для проверки скролла во вкладке "Время" окна настроек
struct SettingsWindowScrollTest {
    static func runTest() {
        print("🧪 Тестирование скролла в окне настроек...")
        
        // Создаем окно настроек
        let settingsWindow = SettingsWindow()
        
        // Проверяем, что окно создано
        guard settingsWindow.contentView != nil else {
            print("❌ ОШИБКА: ContentView не создан")
            exit(1)
        }
        
        // Ищем TabView в иерархии
        guard let tabView = findTabView(in: settingsWindow.contentView!) else {
            print("❌ ОШИБКА: TabView не найден")
            exit(1)
        }
        
        // Проверяем количество вкладок (должно быть минимум 2: "Общие" и "Время")
        guard tabView.numberOfTabViewItems >= 2 else {
            print("❌ ОШИБКА: Недостаточно вкладок в TabView")
            exit(1)
        }
        
        // Находим вкладку "Время" (обычно вторая)
        var timingTabItem: NSTabViewItem?
        for i in 0..<tabView.numberOfTabViewItems {
            let item = tabView.tabViewItem(at: i)
            if item.label == "Время" {
                timingTabItem = item
                break
            }
        }
        
        guard let timingTab = timingTabItem else {
            print("❌ ОШИБКА: Вкладка 'Время' не найдена")
            exit(1)
        }
        
        // Проверяем, что у вкладки есть view
        guard let timingView = timingTab.view else {
            print("❌ ОШИБКА: У вкладки 'Время' нет view")
            exit(1)
        }
        
        // Ищем NSScrollView в иерархии вкладки "Время"
        guard let scrollView = findScrollView(in: timingView) else {
            print("❌ ОШИБКА: NSScrollView не найден во вкладке 'Время'")
            exit(1)
        }
        
        // Проверяем настройки scroll view
        if !scrollView.hasVerticalScroller {
            print("❌ ОШИБКА: Вертикальный скроллер не включен")
            exit(1)
        }
        
        if scrollView.hasHorizontalScroller {
            print("❌ ОШИБКА: Горизонтальный скроллер должен быть отключен")
            exit(1)
        }
        
        // Проверяем, что у scroll view есть document view
        guard let documentView = scrollView.documentView else {
            print("❌ ОШИБКА: У NSScrollView нет documentView")
            exit(1)
        }
        
        // Проверяем, что в document view есть элементы (контейнеры настроек)
        let subviews = documentView.subviews
        if subviews.count < 5 { // Должно быть минимум 5 контейнеров настроек
            print("❌ ОШИБКА: Недостаточно элементов в documentView (найдено: \(subviews.count))")
            exit(1)
        }
        
        // Проверяем, что высота document view больше высоты scroll view
        // (это означает, что контент действительно требует прокрутки)
        let scrollViewHeight = scrollView.frame.height
        let documentViewHeight = documentView.frame.height
        
        if documentViewHeight <= scrollViewHeight {
            print("⚠️  ПРЕДУПРЕЖДЕНИЕ: DocumentView не больше ScrollView по высоте")
            print("   ScrollView высота: \(scrollViewHeight)")
            print("   DocumentView высота: \(documentViewHeight)")
        }
        
        // Проверяем constraints для scroll view
        let scrollViewConstraints = scrollView.constraints
        if scrollViewConstraints.isEmpty {
            print("❌ ОШИБКА: У NSScrollView нет constraints")
            exit(1)
        }
        
        print("✅ Все проверки прошли успешно!")
        print("📊 Статистика:")
        print("   - ScrollView найден: ✅")
        print("   - Вертикальный скроллер: ✅")
        print("   - Горизонтальный скроллер отключен: ✅")
        print("   - DocumentView создан: ✅")
        print("   - Элементов в documentView: \(subviews.count)")
        print("   - ScrollView высота: \(Int(scrollViewHeight))")
        print("   - DocumentView высота: \(Int(documentViewHeight))")
        print("   - Constraints у ScrollView: \(scrollViewConstraints.count)")
        
        print("🎉 Тест скролла в окне настроек завершен успешно!")
    }
    
    /// Рекурсивно ищет NSTabView в иерархии view
    static func findTabView(in view: NSView) -> NSTabView? {
        if let tabView = view as? NSTabView {
            return tabView
        }
        
        for subview in view.subviews {
            if let found = findTabView(in: subview) {
                return found
            }
        }
        
        return nil
    }
    
    /// Рекурсивно ищет NSScrollView в иерархии view
    static func findScrollView(in view: NSView) -> NSScrollView? {
        if let scrollView = view as? NSScrollView {
            return scrollView
        }
        
        for subview in view.subviews {
            if let found = findScrollView(in: subview) {
                return found
            }
        }
        
        return nil
    }
}

// Запускаем тест
SettingsWindowScrollTest.runTest()
