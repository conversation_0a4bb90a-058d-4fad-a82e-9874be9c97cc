//
//  SynchronizationTest.swift
//  uProd
//
//  Тестирование синхронизации компонентов системы активности
//  Проверка race conditions и корректной работы при многопоточности
//

import Foundation

/// Тест синхронизации для системы активности
struct SynchronizationTest {
    static func main() {
        print("🔄 ТЕСТИРОВАНИЕ СИНХРОНИЗАЦИИ СИСТЕМЫ АКТИВНОСТИ")
        print(String(repeating: "=", count: 60))
        
        var passedTests = 0
        let totalTests = 6
        
        // ТЕСТ 1: Синхронизация ActivityStateManager
        print("\n1️⃣ Тест синхронизации ActivityStateManager...")
        if testActivityStateManagerSynchronization() {
            print("✅ ActivityStateManager корректно синхронизирован")
            passedTests += 1
        } else {
            print("❌ Проблемы с синхронизацией ActivityStateManager")
        }
        
        // ТЕСТ 2: Синхронизация MinuteActivityTracker
        print("\n2️⃣ Тест синхронизации MinuteActivityTracker...")
        if testMinuteActivityTrackerSynchronization() {
            print("✅ MinuteActivityTracker корректно синхронизирован")
            passedTests += 1
        } else {
            print("❌ Проблемы с синхронизацией MinuteActivityTracker")
        }
        
        // ТЕСТ 3: Синхронизация между компонентами
        print("\n3️⃣ Тест синхронизации между компонентами...")
        if testComponentSynchronization() {
            print("✅ Компоненты корректно синхронизированы")
            passedTests += 1
        } else {
            print("❌ Проблемы с синхронизацией компонентов")
        }
        
        // ТЕСТ 4: Race conditions при обновлении состояния
        print("\n4️⃣ Тест race conditions...")
        if testRaceConditions() {
            print("✅ Race conditions отсутствуют")
            passedTests += 1
        } else {
            print("❌ Обнаружены race conditions")
        }
        
        // ТЕСТ 5: Синхронизация статистики
        print("\n5️⃣ Тест синхронизации статистики...")
        if testStatisticsSynchronization() {
            print("✅ Статистика корректно синхронизирована")
            passedTests += 1
        } else {
            print("❌ Проблемы с синхронизацией статистики")
        }
        
        // ТЕСТ 6: Общая стабильность системы
        print("\n6️⃣ Тест общей стабильности...")
        if testOverallSystemStability() {
            print("✅ Система стабильна при многопоточности")
            passedTests += 1
        } else {
            print("❌ Проблемы со стабильностью системы")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ СИНХРОНИЗАЦИИ")
        print(String(repeating: "=", count: 60))
        print("✅ Пройдено: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 СИСТЕМА КОРРЕКТНО СИНХРОНИЗИРОВАНА!")
            print("🔄 Нет race conditions")
            print("✅ Готова к финальной валидации")
        } else {
            print("❌ ЕСТЬ ПРОБЛЕМЫ С СИНХРОНИЗАЦИЕЙ")
            print("🔧 Требуется исправление перед продолжением")
        }
    }
    
    /// Тест 1: Синхронизация ActivityStateManager
    static func testActivityStateManagerSynchronization() -> Bool {
        let statisticsManager = TestStatisticsManager()
        let activityStateManager = TestActivityStateManager(statisticsManager: statisticsManager)
        
        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .userInitiated)
        var results: [ReturnMessage] = []
        let resultsQueue = DispatchQueue(label: "results")
        
        // Запускаем 100 параллельных операций
        for i in 0..<100 {
            group.enter()
            queue.async {
                let awayTime = TimeInterval(i * 10) // 0, 10, 20, ... секунд
                let message = activityStateManager.determineReturnMessage(awayTime)
                
                resultsQueue.sync {
                    results.append(message)
                }
                
                group.leave()
            }
        }
        
        let result = group.wait(timeout: .now() + 5.0)
        
        if result == .success && results.count == 100 {
            print("  ✓ 100 параллельных операций выполнено")
            print("  ✓ Результатов получено: \(results.count)")
            
            // Проверяем что результаты логичны
            let validResults = results.allSatisfy { message in
                switch message {
                case .resumeSilently, .partialRest, .chooseRestOrWork, .fullRest:
                    return true
                }
            }
            
            if validResults {
                print("  ✓ Все результаты валидны")
                return true
            } else {
                print("  ❌ Некорректные результаты")
                return false
            }
        } else {
            print("  ❌ Таймаут или неполные результаты: \(results.count)/100")
            return false
        }
    }
    
    /// Тест 2: Синхронизация MinuteActivityTracker
    static func testMinuteActivityTrackerSynchronization() -> Bool {
        let tracker = TestMinuteActivityTracker()
        
        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .userInitiated)
        var activities: [Int] = []
        let activitiesQueue = DispatchQueue(label: "activities")
        
        // Запускаем 50 параллельных обновлений
        for i in 0..<50 {
            group.enter()
            queue.async {
                let band = i % 4
                let active = i % 2 == 0
                
                tracker.updateBand(band, active: active)
                let activity = tracker.getCurrentActivity()
                
                activitiesQueue.sync {
                    activities.append(activity)
                }
                
                group.leave()
            }
        }
        
        let result = group.wait(timeout: .now() + 3.0)
        
        if result == .success && activities.count == 50 {
            print("  ✓ 50 параллельных обновлений выполнено")
            print("  ✓ Активностей получено: \(activities.count)")
            
            // Проверяем что все значения в допустимом диапазоне
            let validActivities = activities.allSatisfy { $0 >= 0 && $0 <= 4 }
            
            if validActivities {
                print("  ✓ Все значения активности валидны (0-4)")
                return true
            } else {
                print("  ❌ Некорректные значения активности")
                return false
            }
        } else {
            print("  ❌ Таймаут или неполные результаты: \(activities.count)/50")
            return false
        }
    }
    
    /// Тест 3: Синхронизация между компонентами
    static func testComponentSynchronization() -> Bool {
        let statisticsManager = TestStatisticsManager()
        let activityStateManager = TestActivityStateManager(statisticsManager: statisticsManager)
        let tracker = TestMinuteActivityTracker()
        
        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .userInitiated)
        var integrationResults: [Bool] = []
        let resultsQueue = DispatchQueue(label: "integration")
        
        // Запускаем 30 параллельных интеграционных операций
        for i in 0..<30 {
            group.enter()
            queue.async {
                // Обновляем трекер
                tracker.updateBand(i % 4, active: i % 3 != 0)
                
                // Получаем активность
                let activity = tracker.getCurrentActivity()
                
                // Определяем сообщение
                let awayTime = TimeInterval(i * 30) // 0, 30, 60, ... секунд
                let message = activityStateManager.determineReturnMessage(awayTime)
                
                // Получаем статистику
                let (restType, restQuality) = statisticsManager.determineRestTypeAndQuality(duration: awayTime, userChoice: nil)
                
                // Проверяем что все компоненты работают
                let isValid = activity >= 0 && activity <= 4 &&
                             (message == .resumeSilently || message == .partialRest || message == .chooseRestOrWork || message == .fullRest) &&
                             (restType == .micro || restType == .partial || restType == .automatic) &&
                             (restQuality == .fair || restQuality == .good || restQuality == .excellent)
                
                resultsQueue.sync {
                    integrationResults.append(isValid)
                }
                
                group.leave()
            }
        }
        
        let result = group.wait(timeout: .now() + 5.0)
        
        if result == .success && integrationResults.count == 30 {
            let validCount = integrationResults.filter { $0 }.count
            print("  ✓ 30 интеграционных операций выполнено")
            print("  ✓ Валидных результатов: \(validCount)/30")
            
            if validCount == 30 {
                return true
            } else {
                print("  ❌ Некорректные интеграционные результаты")
                return false
            }
        } else {
            print("  ❌ Таймаут или неполные результаты: \(integrationResults.count)/30")
            return false
        }
    }
    
    /// Тест 4: Race conditions
    static func testRaceConditions() -> Bool {
        let tracker = TestMinuteActivityTracker()

        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .userInitiated)

        var operationsCompleted = 0
        var inconsistentResults = 0
        let resultsQueue = DispatchQueue(label: "race")

        // Запускаем множественные операции одновременно
        for i in 0..<50 {
            group.enter()
            queue.async {
                // Каждая операция делает последовательные изменения
                let bandIndex = i % 4

                // Устанавливаем активность
                tracker.updateBand(bandIndex, active: true)
                let activity1 = tracker.getCurrentActivity()

                // Сбрасываем активность
                tracker.updateBand(bandIndex, active: false)
                let activity2 = tracker.getCurrentActivity()

                // Проверяем логичность: activity2 должна быть <= activity1
                let isConsistent = activity2 <= activity1

                resultsQueue.sync {
                    operationsCompleted += 1
                    if !isConsistent {
                        inconsistentResults += 1
                    }
                }

                group.leave()
            }
        }

        let result = group.wait(timeout: .now() + 5.0)

        if result == .success && operationsCompleted == 50 {
            print("  ✓ 50 конкурирующих операций выполнено")
            print("  ✓ Операций завершено: \(operationsCompleted)")
            print("  ✓ Несогласованных результатов: \(inconsistentResults)")

            // Допускаем небольшое количество несогласованностей (< 5%)
            let inconsistencyRate = Double(inconsistentResults) / Double(operationsCompleted)

            if inconsistencyRate < 0.05 {
                print("  ✓ Уровень несогласованности: \(String(format: "%.1f", inconsistencyRate * 100))% (приемлемо)")
                return true
            } else {
                print("  ❌ Высокий уровень несогласованности: \(String(format: "%.1f", inconsistencyRate * 100))%")
                return false
            }
        } else {
            print("  ❌ Таймаут или неполные операции: \(operationsCompleted)/50")
            return false
        }
    }
    
    /// Тест 5: Синхронизация статистики
    static func testStatisticsSynchronization() -> Bool {
        let statisticsManager = TestStatisticsManager()
        
        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .userInitiated)
        var statisticsResults: [(RestType, RestQuality)] = []
        let statsQueue = DispatchQueue(label: "statistics")
        
        // Запускаем 40 параллельных операций со статистикой
        for i in 0..<40 {
            group.enter()
            queue.async {
                let duration = TimeInterval(i * 60) // 0, 60, 120, ... секунд
                let userChoice = i % 3 == 0 ? "rest" : nil
                
                let (restType, restQuality) = statisticsManager.determineRestTypeAndQuality(duration: duration, userChoice: userChoice)
                
                statsQueue.sync {
                    statisticsResults.append((restType, restQuality))
                }
                
                group.leave()
            }
        }
        
        let result = group.wait(timeout: .now() + 3.0)
        
        if result == .success && statisticsResults.count == 40 {
            print("  ✓ 40 параллельных операций со статистикой выполнено")
            print("  ✓ Результатов получено: \(statisticsResults.count)")
            
            // Проверяем что все результаты валидны
            let validResults = statisticsResults.allSatisfy { (type, quality) in
                let validType = type == .micro || type == .partial || type == .automatic
                let validQuality = quality == .fair || quality == .good || quality == .excellent
                return validType && validQuality
            }
            
            if validResults {
                print("  ✓ Все статистические результаты валидны")
                return true
            } else {
                print("  ❌ Некорректные статистические результаты")
                return false
            }
        } else {
            print("  ❌ Таймаут или неполные результаты: \(statisticsResults.count)/40")
            return false
        }
    }
    
    /// Тест 6: Общая стабильность системы
    static func testOverallSystemStability() -> Bool {
        let statisticsManager = TestStatisticsManager()
        let activityStateManager = TestActivityStateManager(statisticsManager: statisticsManager)
        let tracker = TestMinuteActivityTracker()
        
        let group = DispatchGroup()
        let queues = [
            DispatchQueue.global(qos: .userInitiated),
            DispatchQueue.global(qos: .default),
            DispatchQueue.global(qos: .background)
        ]
        
        var totalOperations = 0
        var successfulOperations = 0
        let operationsQueue = DispatchQueue(label: "operations")
        
        // Запускаем интенсивную многопоточную нагрузку
        for queueIndex in 0..<3 {
            for i in 0..<20 {
                group.enter()
                queues[queueIndex].async {
                    do {
                        // Комплексная операция
                        tracker.updateBand(i % 4, active: (i + queueIndex) % 2 == 0)
                        let activity = tracker.getCurrentActivity()
                        
                        let awayTime = TimeInterval((i + queueIndex) * 45)
                        let message = activityStateManager.determineReturnMessage(awayTime)
                        
                        let (restType, restQuality) = statisticsManager.determineRestTypeAndQuality(duration: awayTime, userChoice: nil)
                        
                        // Проверяем корректность
                        let isValid = activity >= 0 && activity <= 4 &&
                                     (message == .resumeSilently || message == .partialRest || message == .chooseRestOrWork || message == .fullRest) &&
                                     (restType == .micro || restType == .partial || restType == .automatic) &&
                                     (restQuality == .fair || restQuality == .good || restQuality == .excellent)
                        
                        operationsQueue.sync {
                            totalOperations += 1
                            if isValid {
                                successfulOperations += 1
                            }
                        }
                    }
                    
                    group.leave()
                }
            }
        }
        
        let result = group.wait(timeout: .now() + 10.0)
        
        if result == .success && totalOperations == 60 {
            let successRate = Double(successfulOperations) / Double(totalOperations)
            print("  ✓ 60 комплексных операций выполнено")
            print("  ✓ Успешных операций: \(successfulOperations)/\(totalOperations)")
            print("  ✓ Успешность: \(String(format: "%.1f", successRate * 100))%")
            
            if successRate >= 0.95 { // 95% успешность
                return true
            } else {
                print("  ❌ Низкая успешность операций")
                return false
            }
        } else {
            print("  ❌ Таймаут или неполные операции: \(totalOperations)/60")
            return false
        }
    }
}

// MARK: - Test Classes (Thread-Safe Versions)

class TestMinuteActivityTracker {
    private var bandActivities: [Bool] = [false, false, false, false]
    private let queue = DispatchQueue(label: "tracker", attributes: .concurrent)
    
    func updateBand(_ band: Int, active: Bool) {
        queue.async(flags: .barrier) {
            if band >= 0 && band < 4 {
                self.bandActivities[band] = active
            }
        }
    }
    
    func getCurrentActivity() -> Int {
        return queue.sync {
            return bandActivities.filter { $0 }.count
        }
    }
}

class TestStatisticsManager {
    private let queue = DispatchQueue(label: "statistics", attributes: .concurrent)
    
    func determineRestTypeAndQuality(duration: TimeInterval, userChoice: String?) -> (RestType, RestQuality) {
        return queue.sync {
            switch duration {
            case 0..<120:
                return (.micro, .fair)
            case 120..<600:
                return (.partial, .good)
            case 600..<1020:
                return (.partial, .good)
            default:
                return (.automatic, .excellent)
            }
        }
    }
}

class TestActivityStateManager {
    private let shortAwayThreshold: TimeInterval = 2 * 60
    private let mediumAwayThreshold: TimeInterval = 10 * 60
    private let longAwayThreshold: TimeInterval = 17 * 60
    private let queue = DispatchQueue(label: "activityState", attributes: .concurrent)
    
    init(statisticsManager: TestStatisticsManager) {
        // Инициализация
    }
    
    func determineReturnMessage(_ awayTime: TimeInterval) -> ReturnMessage {
        return queue.sync {
            if awayTime < shortAwayThreshold {
                return .resumeSilently
            } else if awayTime < mediumAwayThreshold {
                return .partialRest
            } else if awayTime < longAwayThreshold {
                return .chooseRestOrWork
            } else {
                return .fullRest
            }
        }
    }
}

enum ReturnMessage {
    case resumeSilently, partialRest, chooseRestOrWork, fullRest
}

enum RestType {
    case micro, partial, automatic
}

enum RestQuality {
    case fair, good, excellent
}

// Запускаем тест
SynchronizationTest.main()
