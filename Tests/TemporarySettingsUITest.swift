import Foundation

/// Тест для проверки исправления UI вкладки "Временные" в настройках
/// Этот тест проверяет, что код компилируется и содержит необходимые исправления

print("🧪 Запуск теста исправления UI вкладки 'Временные'...")

// Проверяем, что исправления присутствуют в коде
testCodeCorrections()

print("🎉 Тест завершен успешно!")

func testCodeCorrections() {
    print("🔍 Проверяем наличие исправлений в коде...")

    // Читаем файл SettingsWindow.swift
    let currentDir = FileManager.default.currentDirectoryPath
    let settingsFilePath = "\(currentDir)/SimplePomodoroTest/SettingsWindow.swift"

    guard let fileContent = try? String(contentsOfFile: settingsFilePath, encoding: .utf8) else {
        print("❌ ОШИБКА: Не удалось прочитать файл SettingsWindow.swift")
        exit(1)
    }

    // Проверяем наличие исправлений
    var testsPassedCount = 0
    let totalTests = 4

    // Тест 1: Проверяем наличие стилизации PopUpButton
    if fileContent.contains("positionPopUp.wantsLayer = true") &&
       fileContent.contains("positionPopUp.layer?.cornerRadius = 6") &&
       fileContent.contains("positionPopUp.layer?.backgroundColor = NSColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0).cgColor") {
        print("✅ Тест 1: Стилизация PopUpButton добавлена")
        testsPassedCount += 1
    } else {
        print("❌ Тест 1: Стилизация PopUpButton отсутствует")
    }

    // Тест 2: Проверяем наличие фиксированной ширины
    if fileContent.contains("positionPopUp.widthAnchor.constraint(equalToConstant: 140)") {
        print("✅ Тест 2: Фиксированная ширина PopUpButton установлена")
        testsPassedCount += 1
    } else {
        print("❌ Тест 2: Фиксированная ширина PopUpButton не установлена")
    }

    // Тест 3: Проверяем наличие комментария об исправлении
    if fileContent.contains("// ИСПРАВЛЕНО: Добавляем стилизацию как у других PopUpButton") {
        print("✅ Тест 3: Комментарий об исправлении стилизации найден")
        testsPassedCount += 1
    } else {
        print("❌ Тест 3: Комментарий об исправлении стилизации не найден")
    }

    // Тест 4: Проверяем наличие комментария о ширине
    if fileContent.contains("// ИСПРАВЛЕНО: Добавляем фиксированную ширину как у звуковых PopUpButton") {
        print("✅ Тест 4: Комментарий об исправлении ширины найден")
        testsPassedCount += 1
    } else {
        print("❌ Тест 4: Комментарий об исправлении ширины не найден")
    }

    // Итоговый результат
    print("\n📊 Результат: \(testsPassedCount)/\(totalTests) тестов прошли")

    if testsPassedCount == totalTests {
        print("🎉 Все исправления применены корректно!")
    } else {
        print("⚠️ Некоторые исправления отсутствуют")
        exit(1)
    }
}
