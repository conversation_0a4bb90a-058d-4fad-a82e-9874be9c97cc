import Foundation

/// ТЕСТ ОТЛАДОЧНОГО ИНТЕРФЕЙСА СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Проверяет корректность работы улучшенного отладочного интерфейса
@main struct EarlyEngagementDebugInterfaceTest {
    static func main() {
        print("🧪 ТЕСТ: Отладочный интерфейс системы раннего вовлечения")
        print("=" * 60)
        
        // Тест 1: Проверка правильности расчета планки для разных уровней
        testBarCalculationForAllLevels()
        
        // Тест 2: Проверка корректности индексов матрицы
        testMatrixIndexMapping()
        
        // Тест 3: Проверка отображения информации о сообщениях
        testMessageInformationDisplay()
        
        print("\n✅ Все тесты отладочного интерфейса пройдены!")
    }
    
    /// Тест 1: Проверка правильности расчета планки для разных уровней
    static func testBarCalculationForAllLevels() {
        print("\n📊 Тест 1: Расчет планки для всех уровней")
        
        let system = EarlyEngagementSystem.shared
        
        // Проверяем расчет планки для каждого уровня дней
        let testCases = [
            (days: 0, expectedMinutes: 52),  // Уровень 0: базовая планка
            (days: 1, expectedMinutes: 40),  // Уровень 1: ×0.77
            (days: 2, expectedMinutes: 30),  // Уровень 2-3: ×0.58
            (days: 3, expectedMinutes: 30),  // Уровень 2-3: ×0.58
            (days: 4, expectedMinutes: 15),  // Уровень 4-6: ×0.29
            (days: 5, expectedMinutes: 15),  // Уровень 4-6: ×0.29
            (days: 6, expectedMinutes: 15),  // Уровень 4-6: ×0.29
            (days: 7, expectedMinutes: 3),   // Уровень 7+: фиксированные 3 мин
            (days: 10, expectedMinutes: 3),  // Уровень 7+: фиксированные 3 мин
            (days: 30, expectedMinutes: 3)   // Уровень 7+: фиксированные 3 мин
        ]
        
        for testCase in testCases {
            // Эмулируем установку дней без работы
            system.debugSetDaysWithoutWork(testCase.days)
            
            // Получаем текущую планку
            let currentBarSeconds = system.debugCurrentUserBar
            let currentBarMinutes = Int(currentBarSeconds / 60)
            
            print("  📅 Дней: \(testCase.days) → Планка: \(currentBarMinutes) мин (ожидалось: \(testCase.expectedMinutes) мин)")
            
            // Проверяем соответствие ожидаемому значению (с допуском ±1 минута)
            assert(abs(currentBarMinutes - testCase.expectedMinutes) <= 1, 
                   "Неверная планка для \(testCase.days) дней: получено \(currentBarMinutes), ожидалось \(testCase.expectedMinutes)")
        }
        
        print("  ✅ Расчет планки работает корректно для всех уровней")
    }
    
    /// Тест 2: Проверка корректности индексов матрицы
    static func testMatrixIndexMapping() {
        print("\n🗂️ Тест 2: Маппинг дней в индексы матрицы")
        
        // Проверяем правильность преобразования дней в индексы матрицы
        let testCases = [
            (days: 0, expectedIndex: 0),   // Уровень 0
            (days: 1, expectedIndex: 1),   // Уровень 1
            (days: 2, expectedIndex: 2),   // Уровень 2-3
            (days: 3, expectedIndex: 2),   // Уровень 2-3
            (days: 4, expectedIndex: 3),   // Уровень 4-6
            (days: 5, expectedIndex: 3),   // Уровень 4-6
            (days: 6, expectedIndex: 3),   // Уровень 4-6
            (days: 7, expectedIndex: 4),   // Уровень 7+
            (days: 10, expectedIndex: 4),  // Уровень 7+
            (days: 100, expectedIndex: 4)  // Уровень 7+
        ]
        
        for testCase in testCases {
            // Получаем сообщение для проверки правильности индекса
            let message = MessageConstructionMatrix.getMessage(vertical: testCase.expectedIndex, horizontal: 0)
            
            print("  📅 Дней: \(testCase.days) → Индекс: \(testCase.expectedIndex) → Сообщение: \"\(message.title)\"")
            
            // Проверяем, что сообщение не пустое (что означает корректный индекс)
            assert(!message.title.isEmpty, "Пустое сообщение для дней: \(testCase.days), индекс: \(testCase.expectedIndex)")
        }
        
        print("  ✅ Маппинг дней в индексы матрицы работает корректно")
    }
    
    /// Тест 3: Проверка отображения информации о сообщениях
    static func testMessageInformationDisplay() {
        print("\n📝 Тест 3: Отображение информации о сообщениях")
        
        let system = EarlyEngagementSystem.shared
        
        // Тестируем несколько комбинаций матрицы
        let testCombinations = [
            (daysLevel: 0, messageIndex: 0, description: "0 дней, утреннее сообщение"),
            (daysLevel: 2, messageIndex: 1, description: "2-3 дня, дневное сообщение"),
            (daysLevel: 4, messageIndex: 2, description: "7+ дней, вечернее сообщение"),
            (daysLevel: 4, messageIndex: 3, description: "7+ дней, ночное сообщение")
        ]
        
        for combination in testCombinations {
            // Получаем сообщение из матрицы
            let message = MessageConstructionMatrix.getMessage(
                vertical: combination.daysLevel, 
                horizontal: combination.messageIndex
            )
            let processedMessage = system.substituteVariables(in: message)
            
            // Проверяем, что все поля заполнены
            assert(!processedMessage.title.isEmpty, "Пустой заголовок для: \(combination.description)")
            assert(!processedMessage.subtitle.isEmpty, "Пустой текст для: \(combination.description)")
            assert(!processedMessage.buttonText.isEmpty, "Пустой текст кнопки для: \(combination.description)")
            assert(processedMessage.proposedDuration > 0, "Нулевая длительность для: \(combination.description)")
            
            let proposedMinutes = Int(processedMessage.proposedDuration / 60)
            
            print("  📍 \(combination.description):")
            print("    📝 Заголовок: \"\(processedMessage.title)\"")
            print("    📄 Текст: \"\(processedMessage.subtitle)\"")
            print("    🎯 Предложение: \(proposedMinutes) мин")
            print("    🔘 Кнопка: \"\(processedMessage.buttonText)\"")
        }
        
        print("  ✅ Отображение информации о сообщениях работает корректно")
    }
}

// Расширение для повторения строки
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
