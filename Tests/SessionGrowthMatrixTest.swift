import Foundation

/// Тест новой матрицы взращивания сессий
struct SessionGrowthMatrixTest {

    static func runAllTests() {
        print("🧪 ТЕСТ НОВОЙ МАТРИЦЫ ВЗРАЩИВАНИЯ СЕССИЙ")
        print(String(repeating: "=", count: 60))

        // Тестируем базовую логику
        testBasicLogic()

        print("\n" + String(repeating: "=", count: 60))

        // Тестируем реальные сценарии
        testRealWorldScenarios()

        print("\n✅ ВСЕ ТЕСТЫ ЗАВЕРШЕНЫ")
    }

    /// Тестирует базовую логику матрицы
    static func testBasicLogic() {
        print("🔧 ТЕСТ БАЗОВОЙ ЛОГИКИ")

        // Простой тест алгоритма без зависимостей
        let testCases = [
            (start: 3, target: 52),
            (start: 3, target: 30),
            (start: 8, target: 40),
            (start: 15, target: 45),
            (start: 50, target: 52)
        ]

        for testCase in testCases {
            print("\n📍 Тест: \(testCase.start) мин → планка \(testCase.target) мин")

            // Тестируем оба режима
            testGrowthCalculation(start: testCase.start, target: testCase.target, mode: "gentle")
            testGrowthCalculation(start: testCase.start, target: testCase.target, mode: "confident")
        }
    }
    
    /// Тестирует расчет взращивания (упрощенная версия без зависимостей)
    static func testGrowthCalculation(start: Int, target: Int, mode: String) {
        let remaining = target - start

        print("   \(mode.capitalized): остается \(remaining) мин", terminator: "")

        // Простая проверка логики
        if remaining <= 1 {
            print(" → нет взращивания (≤1 мин)")
            return
        }

        if remaining <= 10 {
            print(" → простое: \(start)→\(target) (один шаг)")
            return
        }

        // Симулируем алгоритм
        let steps = mode == "gentle" ?
            (remaining > 35 ? 3 : remaining > 20 ? 2 : 1) :
            (remaining > 20 ? 2 : 1)

        print(" → \(steps) шагов")

        // Проверяем что сумма будет правильной
        if start + remaining == target {
            print("      ✅ Математика корректна")
        } else {
            print("      ❌ Ошибка в математике")
        }
    }
    
    /// Тестирует реальные сценарии использования
    static func testRealWorldScenarios() {
        print("🌍 ТЕСТ РЕАЛЬНЫХ СЦЕНАРИЕВ")

        let scenarios = [
            (name: "Новичок с низкой планкой", start: 3, target: 15),
            (name: "Опытный пользователь", start: 3, target: 52),
            (name: "Средний пользователь", start: 8, target: 30),
            (name: "Близко к планке", start: 45, target: 52),
            (name: "Очень близко к планке", start: 50, target: 52)
        ]

        for scenario in scenarios {
            print("\n🎭 Сценарий: \(scenario.name)")
            print("   Задача: \(scenario.start) мин → планка \(scenario.target) мин")

            let remaining = scenario.target - scenario.start

            // Анализируем сценарий
            if remaining <= 1 {
                print("   📊 Взращивание не предлагается (остается ≤1 мин)")
            } else if remaining <= 10 {
                print("   📊 Простое взращивание: один шаг +\(remaining) мин")
            } else {
                let gentleSteps = remaining > 35 ? 3 : remaining > 20 ? 2 : 1
                let confidentSteps = remaining > 20 ? 2 : 1

                print("   📊 Щадящий режим: ~\(gentleSteps) шагов")
                print("   📊 Уверенный режим: ~\(confidentSteps) шагов")
            }

            // Проверяем психологическую разумность
            let firstStepPercent = remaining > 0 ? (remaining * 30 / 100) * 100 / remaining : 0
            if firstStepPercent > 60 {
                print("   ⚠️ Первый шаг может быть слишком большим (\(firstStepPercent)% от остатка)")
            } else {
                print("   ✅ Размер первого шага разумный")
            }
        }
    }
}

// Запускаем тесты
print("🚀 Запуск тестов матрицы взращивания...")
SessionGrowthMatrixTest.runAllTests()
