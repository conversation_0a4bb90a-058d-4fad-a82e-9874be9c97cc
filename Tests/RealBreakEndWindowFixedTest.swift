import Cocoa

// Заглушки для недостающих типов
enum BreakEndMode {
    case breakCompleted
    case userReturnPartialRest(minutes: Int)
    case userReturnChoiceRest(minutes: Int)
    case userReturnFullRest(minutes: Int)
}

// Тест исправлений BreakEndWindow: позиция проекта и подтягивание данных
print("🧪 ПРОСТОЙ ТЕСТ ИСПРАВЛЕНИЙ BREAKENDWINDOW")
print("=" * 50)

var passedTests = 0
var totalTests = 0

// Тест 1: Проверка что enum работает
totalTests += 1
print("\n1️⃣ Тест enum BreakEndMode")

let mode1 = BreakEndMode.userReturnPartialRest(minutes: 5)
let mode2 = BreakEndMode.userReturnChoiceRest(minutes: 15)

switch mode1 {
        case .userReturnPartialRest(let minutes):
            if minutes == 5 {
                print("✅ Enum userReturnPartialRest работает")
                passedTests += 1
            } else {
                print("❌ Enum userReturnPartialRest НЕ работает")
            }
        default:
            print("❌ Enum userReturnPartialRest НЕ работает")
        }

        // Тест 2: Проверка второго enum
        totalTests += 1
        print("\n2️⃣ Тест enum userReturnChoiceRest")

        switch mode2 {
        case .userReturnChoiceRest(let minutes):
            if minutes == 15 {
                print("✅ Enum userReturnChoiceRest работает")
                passedTests += 1
            } else {
                print("❌ Enum userReturnChoiceRest НЕ работает")
            }
        default:
            print("❌ Enum userReturnChoiceRest НЕ работает")
        }

        // Тест 3: Проверка логики времени
        totalTests += 1
        print("\n3️⃣ Тест логики времени для кнопок")

        // Проверяем что для 2-10 минут используется partialRest
        let shortTime = BreakEndMode.userReturnPartialRest(minutes: 7)
        // Проверяем что для 10+ минут используется choiceRest
        let longTime = BreakEndMode.userReturnChoiceRest(minutes: 12)

        var shortTimeCorrect = false
        var longTimeCorrect = false

        switch shortTime {
        case .userReturnPartialRest(let minutes):
            shortTimeCorrect = (minutes >= 2 && minutes <= 10)
        default:
            break
        }

        switch longTime {
        case .userReturnChoiceRest(let minutes):
            longTimeCorrect = (minutes >= 10)
        default:
            break
        }

        if shortTimeCorrect && longTimeCorrect {
            print("✅ Логика времени работает правильно")
            print("   - 2-10 мин: partialRest ✓")
            print("   - 10+ мин: choiceRest ✓")
            passedTests += 1
        } else {
            print("❌ Логика времени работает неправильно")
        }
        
// Итоги
print("\n" + "=" * 50)
print("📊 ИТОГИ ТЕСТИРОВАНИЯ ИСПРАВЛЕНИЙ:")
print("   Пройдено: \(passedTests)/\(totalTests)")

if passedTests == totalTests {
    print("🎉 ВСЕ ИСПРАВЛЕНИЯ РАБОТАЮТ ПРАВИЛЬНО!")
} else {
    print("⚠️  ЕСТЬ ПРОБЛЕМЫ В ИСПРАВЛЕНИЯХ")
}

exit(passedTests == totalTests ? 0 : 1)

extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
