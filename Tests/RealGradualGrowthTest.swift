#!/usr/bin/env swift

// Подключаем файлы проекта
import Foundation
import Cocoa

// Загружаем исходники
let projectPath = "/Users/<USER>/EVO/uProd/SimplePomodoroTest"

// Читаем и выполняем GradualGrowthSystem
let gradualGrowthContent = try String(contentsOfFile: "\(projectPath)/GradualGrowthSystem.swift", encoding: .utf8)

// Создаем временный файл с полным кодом
let tempContent = """
import Foundation

\(gradualGrowthContent)

// Тестируем
@main
struct TestRunner {
    static func main() {
        print("🧪 РЕАЛЬНЫЙ ТЕСТ GRADUAL GROWTH SYSTEM")
        print(String(repeating: "=", count: 50))
        
        // Тестируем 30 минут
        let result30 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 30)
        print("📊 30 минут → \(result30) минут")
        
        // Тестируем TimeInterval версию
        let result30Time = GradualGrowthSystem.calculateGrowth(currentBar: TimeInterval(30 * 60))
        print("📊 30 минут (TimeInterval) → \(Int(result30Time / 60)) минут")
        
        // Тестируем applyLevel0Growth
        let result30Level0 = GradualGrowthSystem.applyLevel0Growth(currentBar: TimeInterval(30 * 60))
        print("📊 30 минут (Level0Growth) → \(Int(result30Level0 / 60)) минут")
        
        // Проверяем детали расчета
        print("\\n🔍 ДЕТАЛИ РАСЧЕТА:")
        let calculation = Double(30) * 1.15
        print("   30 × 1.15 = \(calculation)")
        print("   round(\(calculation)) = \(round(calculation))")
        print("   Int(round(\(calculation))) = \(Int(round(calculation)))")
        
        // Проверяем другие значения
        print("\\n🎯 ДРУГИЕ ЗНАЧЕНИЯ:")
        for value in [25, 26, 30, 35, 40] {
            let result = GradualGrowthSystem.calculateGrowth(currentBarMinutes: value)
            print("   \(value) мин → \(result) мин")
        }
    }
}
"""

let tempFile = "/tmp/RealGradualGrowthTest.swift"
try tempContent.write(toFile: tempFile, atomically: true, encoding: .utf8)

// Выполняем
let task = Process()
task.launchPath = "/usr/bin/swift"
task.arguments = [tempFile]
task.launch()
task.waitUntilExit()
