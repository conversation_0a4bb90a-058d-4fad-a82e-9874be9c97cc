import Foundation

/// Тест системы полной сессии
@main struct FullSessionSystemTest {
    static func main() {
        print("🧪 Тестирование системы полной сессии...")
        
        // Тест 1: Инициализация системы
        testSystemInitialization()
        
        // Тест 2: Проверка показа кнопки полной сессии
        testShouldShowFullSession()
        
        // Тест 3: Тестирование механизма блокировки
        testSafetyMechanism()
        
        // Тест 4: Тестирование расчета новой планки
        testBarCalculation()
        
        print("✅ Все тесты системы полной сессии пройдены!")
    }
    
    static func testSystemInitialization() {
        print("\n📋 Тест 1: Инициализация системы")
        
        let system = FullSessionSystem.shared
        let stats = system.getCurrentStatistics()
        
        print("📊 Статистика при инициализации:")
        print(stats)
        
        // Проверяем, что система инициализировалась с правильными значениями
        assert(!stats.contains("🚫 ЗАБЛОКИРОВАНА"), "Система не должна быть заблокирована при инициализации")
        print("✅ Система инициализирована корректно")
    }
    
    static func testShouldShowFullSession() {
        print("\n📋 Тест 2: Проверка показа кнопки полной сессии")
        
        let system = FullSessionSystem.shared
        
        // Тест с разными размерами планки
        let testCases: [(TimeInterval, Bool, String)] = [
            (3 * 60, false, "3 минуты - слишком мало"),
            (15 * 60, true, "15 минут - должна показываться"),
            (30 * 60, true, "30 минут - должна показываться"),
            (52 * 60, false, "52 минуты - уже максимум"),
            (60 * 60, false, "60 минут - больше максимума")
        ]
        
        for (bar, expected, description) in testCases {
            let shouldShow = system.shouldShowFullSession(currentBar: bar)
            print("🔍 \(description): \(shouldShow ? "✅" : "❌")")
            assert(shouldShow == expected, "Неправильное решение для \(description)")
        }
        
        print("✅ Логика показа кнопки работает корректно")
    }
    
    static func testSafetyMechanism() {
        print("\n📋 Тест 3: Тестирование механизма блокировки")
        
        let system = FullSessionSystem.shared
        
        // Симулируем несколько неудачных попыток
        print("🔄 Симулируем 3 неудачные попытки...")
        for i in 1...3 {
            system.recordFullSessionAttempt()
            // Не записываем успешное завершение - это неудача
            print("❌ Неудачная попытка \(i)")
        }
        
        // Проверяем, что система заблокирована
        let shouldShow = system.shouldShowFullSession(currentBar: 30 * 60)
        print("🔍 Кнопка должна быть скрыта после 3 неудач: \(shouldShow ? "❌ ОШИБКА" : "✅ Скрыта")")
        assert(!shouldShow, "Система должна блокировать кнопку после 3 неудач")
        
        // Симулируем восстановление через успешные обычные интервалы
        print("🔄 Симулируем восстановление через успешные интервалы...")
        for i in 1...5 {
            // Симулируем успешный интервал >= 30 минут
            system.recordRegularIntervalSuccess(duration: 30 * 60)
            print("✅ Успешный интервал \(i)")
        }
        
        // Проверяем, что система разблокирована
        let shouldShowAfterRecovery = system.shouldShowFullSession(currentBar: 30 * 60)
        print("🔍 Кнопка должна быть доступна после восстановления: \(shouldShowAfterRecovery ? "✅ Доступна" : "❌ ОШИБКА")")
        assert(shouldShowAfterRecovery, "Система должна разблокировать кнопку после восстановления")
        
        print("✅ Механизм блокировки работает корректно")
    }
    
    static func testBarCalculation() {
        print("\n📋 Тест 4: Тестирование расчета новой планки")
        
        let system = FullSessionSystem.shared
        
        // Тестируем разные контексты
        let testCases: [(FullSessionContext, String)] = [
            (.horizontalDescaling, "Горизонтальное снижение"),
            (.verticalDegradation, "Вертикальная деградация"),
            (.restCompletion, "Завершение отдыха"),
            (.earlyEngagement, "Раннее вовлечение")
        ]
        
        for (context, description) in testCases {
            let newBar = system.calculateNewBarAfterFullSession(context: context)
            let newBarMinutes = Int(newBar / 60)
            print("🔍 \(description): новая планка = \(newBarMinutes) мин")
            
            // Проверяем, что планка в разумных пределах
            assert(newBar >= 3 * 60, "Планка не должна быть меньше 3 минут")
            assert(newBar <= 52 * 60, "Планка не должна быть больше 52 минут")
        }
        
        print("✅ Расчет новой планки работает корректно")
    }
}
