import Foundation

/// Простой тест системы взращивания сессий
/// Проверяет основную логику без зависимостей
struct SessionGrowingEngineTest {
    static func runTests() {
        print("🧪 Запуск простых тестов системы взращивания...")

        // Тест 1: Проверка логики предложения взращивания
        testGrowthLogic()

        // Тест 2: Проверка формулы умеренного роста
        testModerateGrowthFormula()

        // Тест 3: Проверка градационного роста
        testGradualGrowth()

        print("✅ Все простые тесты пройдены!")
    }
    
    static func testGrowthLogic() {
        print("\n🧪 Тест 1: Проверка логики предложения взращивания")

        // Проверяем логику: можно предлагать для интервалов < 52 минут
        func canOfferGrowth(for duration: TimeInterval) -> Bool {
            let minutes = Int(duration / 60)
            return minutes < 52
        }

        // Должно предлагать для интервалов < 52 минут
        assert(canOfferGrowth(for: 15 * 60), "Должно предлагать для 15 минут")
        assert(canOfferGrowth(for: 30 * 60), "Должно предлагать для 30 минут")
        assert(canOfferGrowth(for: 51 * 60), "Должно предлагать для 51 минуты")

        // НЕ должно предлагать для интервалов ≥ 52 минут
        assert(!canOfferGrowth(for: 52 * 60), "НЕ должно предлагать для 52 минут")
        assert(!canOfferGrowth(for: 60 * 60), "НЕ должно предлагать для 60 минут")

        print("✅ Тест 1 пройден")
    }
    
    static func testGradualGrowth() {
        print("\n🧪 Тест 2: Проверка градационного роста")

        // Воспроизводим логику градационного роста
        func calculateGrowth(currentBarMinutes: Int) -> Int {
            let newBar: Int

            switch currentBarMinutes {
            case 3...7:
                // 🚀 Быстрый старт: +60%
                newBar = Int(Double(currentBarMinutes) * 1.60)
            case 8...15:
                // ⚡ Активный рост: +40%
                newBar = Int(Double(currentBarMinutes) * 1.40)
            case 16...25:
                // ⚡ Активный рост: +25%
                newBar = Int(Double(currentBarMinutes) * 1.25)
            case 26...40:
                // 🎯 Стабилизация: +15%
                newBar = Int(Double(currentBarMinutes) * 1.15)
            case 41...52:
                // 🎯 Финал: +10%
                newBar = Int(Double(currentBarMinutes) * 1.10)
            default:
                // Безопасность для краевых случаев
                newBar = currentBarMinutes + 1
            }

            return min(newBar, 52) // Максимум 52 минуты
        }

        let growth3min = calculateGrowth(currentBarMinutes: 3) - 3
        let growth15min = calculateGrowth(currentBarMinutes: 15) - 15
        let growth30min = calculateGrowth(currentBarMinutes: 30) - 30

        print("📊 Рост для 3 мин: +\(growth3min) мин (3 → \(calculateGrowth(currentBarMinutes: 3)))")
        print("📊 Рост для 15 мин: +\(growth15min) мин (15 → \(calculateGrowth(currentBarMinutes: 15)))")
        print("📊 Рост для 30 мин: +\(growth30min) мин (30 → \(calculateGrowth(currentBarMinutes: 30)))")

        // Проверяем что рост разумный
        assert(growth3min > 0, "Рост для 3 минут должен быть положительным")
        assert(growth15min > 0, "Рост для 15 минут должен быть положительным")
        assert(growth30min > 0, "Рост для 30 минут должен быть положительным")

        // Проверяем конкретные значения
        // 3 мин + 60% = 4.8 ≈ 4 мин (рост +1)
        // 15 мин + 40% = 21 мин (рост +6)
        // 30 мин + 15% = 34.5 ≈ 34 мин (рост +4)
        print("📊 Ожидаемые значения: 3→\(calculateGrowth(currentBarMinutes: 3)), 15→\(calculateGrowth(currentBarMinutes: 15)), 30→\(calculateGrowth(currentBarMinutes: 30))")

        // Проверяем что рост есть (точные значения могут отличаться из-за округления)
        assert(calculateGrowth(currentBarMinutes: 3) > 3, "3 минуты должны расти")
        assert(calculateGrowth(currentBarMinutes: 15) > 15, "15 минут должны расти")
        assert(calculateGrowth(currentBarMinutes: 30) > 30, "30 минут должны расти")

        print("✅ Тест 2 пройден")
    }
    
    static func testModerateGrowthFormula() {
        print("\n🧪 Тест 3: Проверка формулы умеренного роста")
        
        // Тестируем формулу: новая_планка = min(общее_выполненное × 0.6, старая_планка × 3)
        
        // Случай 1: Ограничение по выполненному времени
        let totalCompleted1: TimeInterval = 30 * 60 // 30 минут
        let oldBar1: TimeInterval = 20 * 60 // 20 минут
        let expected1 = min(totalCompleted1 * 0.6, oldBar1 * 3.0) // min(18, 60) = 18 минут
        
        // Случай 2: Ограничение по старой планке
        let totalCompleted2: TimeInterval = 60 * 60 // 60 минут
        let oldBar2: TimeInterval = 15 * 60 // 15 минут
        let expected2 = min(totalCompleted2 * 0.6, oldBar2 * 3.0) // min(36, 45) = 36 минут
        
        print("📊 Случай 1: 30 мин выполнено, планка 20 мин → \(Int(expected1/60)) мин")
        print("📊 Случай 2: 60 мин выполнено, планка 15 мин → \(Int(expected2/60)) мин")
        
        // Проверяем логику
        assert(Int(expected1/60) == 18, "Случай 1: должно быть 18 минут")
        assert(Int(expected2/60) == 36, "Случай 2: должно быть 36 минут")
        
        print("✅ Тест 3 пройден")
    }
}

// Запуск тестов
SessionGrowingEngineTest.runTests()
