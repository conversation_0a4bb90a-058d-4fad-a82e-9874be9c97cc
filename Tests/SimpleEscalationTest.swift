//
// SimpleEscalationTest.swift
// Простой тест для проверки исправления бага эскалации
//

import Foundation

// Упрощенная версия ActivityStateManager только с нужной логикой
class TestActivityStateManager {
    private var minuteActivityHistory: [Bool] = []
    private let maxHistoryMinutes = 52
    private let minActiveMinutesForSuggestion = 42
    
    // ИСПРАВЛЕНИЕ: Добавлено отслеживание состояния эскалации
    private var isEscalationActive: Bool = false
    private var currentIntervalId: UUID?
    
    var onInformalRestSuggestion: (() -> Void)?
    
    func recordMinuteActivity(isActive: Bool) {
        minuteActivityHistory.append(isActive)
        
        if minuteActivityHistory.count > maxHistoryMinutes {
            minuteActivityHistory.removeFirst()
        }
        
        print("📝 Записана активность: \(isActive), история: \(minuteActivityHistory.count) мин")
        checkForInformalRestSuggestion()
    }
    
    private func checkForInformalRestSuggestion() {
        guard minuteActivityHistory.count >= maxHistoryMinutes else { 
            print("🔍 Недостаточно истории: \(minuteActivityHistory.count)/\(maxHistoryMinutes)")
            return 
        }
        
        // КРИТИЧЕСКИ ВАЖНО: Проверяем активна ли уже эскалация
        if isEscalationActive {
            print("🔍 Эскалация уже активна для интервала \(currentIntervalId?.uuidString.prefix(8) ?? "unknown"), пропускаем проверку")
            return
        }
        
        let activeMinutes = minuteActivityHistory.filter { $0 }.count
        print("🔍 Активных минут: \(activeMinutes)/\(maxHistoryMinutes)")
        
        if activeMinutes >= minActiveMinutesForSuggestion {
            print("🔔 НЕФОРМАЛЬНАЯ СЕССИЯ: \(activeMinutes) активных минут из \(maxHistoryMinutes)!")
            
            // Отмечаем что эскалация запущена
            notifyEscalationStarted()
            
            onInformalRestSuggestion?()
        }
    }
    
    // Методы управления эскалацией
    func notifyEscalationStarted() {
        isEscalationActive = true
        currentIntervalId = UUID()
        print("🚀 Эскалация запущена для интервала \(currentIntervalId?.uuidString.prefix(8) ?? "unknown")")
    }
    
    func notifyEscalationEnded() {
        isEscalationActive = false
        let intervalId = currentIntervalId?.uuidString.prefix(8) ?? "unknown"
        currentIntervalId = nil
        print("🛑 Эскалация завершена для интервала \(intervalId)")
    }
    
    func resetEscalationState() {
        if isEscalationActive {
            print("🔄 Сброс состояния эскалации")
        }
        isEscalationActive = false
        currentIntervalId = nil
    }
}

// Мок для тестирования
class MockEscalationCallback {
    var callCount = 0
    var lastCallTime: Date?
    
    func onInformalRestSuggestion() {
        callCount += 1
        lastCallTime = Date()
        print("🔔 Callback вызван #\(callCount) в \(Date())")
    }
}

// Основная функция теста
func runSimpleTest() {
        print("🧪 Простой тест исправления бага эскалации")
        print(String(repeating: "=", count: 50))
        
        let activityManager = TestActivityStateManager()
        let mockCallback = MockEscalationCallback()
        
        // Подключаем callback
        activityManager.onInformalRestSuggestion = {
            mockCallback.onInformalRestSuggestion()
        }
        
        print("\n1️⃣ Симулируем активную работу (42+ минут из 52)")
        // Заполняем историю активностью
        for i in 0..<52 {
            let isActive = i < 42 // Первые 42 минуты активны
            activityManager.recordMinuteActivity(isActive: isActive)
        }
        
        print("\n2️⃣ Проверяем состояние после первого срабатывания:")
        print("   Вызовов callback: \(mockCallback.callCount)")
        
        if mockCallback.callCount == 0 {
            print("   ❌ Callback не сработал!")
            return
        }
        
        print("\n3️⃣ Симулируем продолжение работы (еще 5 минут)")
        let initialCallCount = mockCallback.callCount
        
        for minute in 1...5 {
            print("\n   Минута \(minute):")
            activityManager.recordMinuteActivity(isActive: true)
            print("     Вызовов callback: \(mockCallback.callCount)")
            
            if mockCallback.callCount > initialCallCount {
                print("     ❌ БАГ! Callback вызван повторно!")
                break
            } else {
                print("     ✅ Callback заблокирован")
            }
        }
        
        print("\n4️⃣ Симулируем окончание эскалации")
        activityManager.notifyEscalationEnded()
        
        print("\n5️⃣ Симулируем новый интервал")
        activityManager.resetEscalationState()
        
        // Добавляем еще активные минуты
        for _ in 1...3 {
            activityManager.recordMinuteActivity(isActive: true)
        }
        
        print("\n🚨 РЕЗУЛЬТАТ ТЕСТА:")
        print("   Начальных вызовов: \(initialCallCount)")
        print("   Финальных вызовов: \(mockCallback.callCount)")
        
        if mockCallback.callCount == initialCallCount {
            print("   ✅ ИСПРАВЛЕНИЕ РАБОТАЕТ ИДЕАЛЬНО!")
            print("   ✅ Никаких повторных вызовов")
        } else if mockCallback.callCount == initialCallCount + 1 {
            print("   ✅ ИСПРАВЛЕНИЕ РАБОТАЕТ!")
            print("   ✅ Повторные вызовы заблокированы, новый интервал работает")
        } else {
            print("   ❌ ИСПРАВЛЕНИЕ НЕ РАБОТАЕТ!")
        }
        
        print("\n✅ Тест завершен")
}

// Запускаем тест
runSimpleTest()
