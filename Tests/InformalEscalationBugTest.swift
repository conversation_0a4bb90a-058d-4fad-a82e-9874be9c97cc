#!/usr/bin/env swift

//
// InformalEscalationBugTest.swift
// Тест для воспроизведения бага с повторным запуском эскалации
//

import Foundation

// Мок для тестирования
class MockEscalationSystem {
    var startCallCount = 0
    var stopCallCount = 0
    var isActive = false
    
    func startEscalation() {
        startCallCount += 1
        isActive = true
        print("🚀 Эскалация запущена (вызов #\(startCallCount))")
    }
    
    func stopEscalation() {
        stopCallCount += 1
        isActive = false
        print("🛑 Эскалация остановлена (вызов #\(stopCallCount))")
    }
}

// Упрощенная версия ActivityStateManager для тестирования
class TestActivityStateManager {
    private var minuteActivityHistory: [Bool] = []
    private let maxHistoryMinutes = 52
    private let minActiveMinutesForSuggestion = 42
    private var lastRestSuggestionTime: Date?
    
    // ПРОБЛЕМА: Нет отслеживания состояния эскалации!
    var onInformalRestSuggestion: (() -> Void)?
    
    func recordMinuteActivity(isActive: Bool) {
        minuteActivityHistory.append(isActive)
        
        if minuteActivityHistory.count > maxHistoryMinutes {
            minuteActivityHistory.removeFirst()
        }
        
        print("📝 Записана активность: \(isActive), история: \(minuteActivityHistory.count) мин")
        checkForInformalRestSuggestion()
    }
    
    private func checkForInformalRestSuggestion() {
        guard minuteActivityHistory.count >= maxHistoryMinutes else { return }
        
        let activeMinutes = minuteActivityHistory.filter { $0 }.count
        print("🔍 Активных минут: \(activeMinutes)/\(maxHistoryMinutes)")
        
        if activeMinutes >= minActiveMinutesForSuggestion {
            print("🔔 НЕФОРМАЛЬНАЯ СЕССИЯ: \(activeMinutes) активных минут из \(maxHistoryMinutes)!")
            lastRestSuggestionTime = Date()
            onInformalRestSuggestion?()
        }
    }
    
    // Симулирует заполнение истории активностью
    func simulateActiveWork() {
        // Заполняем историю так, чтобы было 42+ активных минут из 52
        for i in 0..<maxHistoryMinutes {
            let isActive = i < 42 // Первые 42 минуты активны, остальные 10 - нет
            recordMinuteActivity(isActive: isActive)
        }
    }
    
    // Симулирует продолжение работы (добавляет еще активные минуты)
    func simulateContinuedWork(minutes: Int) {
        for _ in 0..<minutes {
            recordMinuteActivity(isActive: true)
        }
    }
}

// Основная функция теста
func runTest() {
    print("🧪 Тест бага с повторным запуском эскалации")
    print(String(repeating: "=", count: 50))
        
        let mockEscalation = MockEscalationSystem()
        let activityManager = TestActivityStateManager()
        
        // Подключаем callback
        activityManager.onInformalRestSuggestion = {
            mockEscalation.startEscalation()
        }
        
        print("\n1️⃣ Симулируем активную работу (42+ минут из 52)")
        activityManager.simulateActiveWork()
        
        print("\n2️⃣ Проверяем состояние после первого срабатывания:")
        print("   Запусков эскалации: \(mockEscalation.startCallCount)")
        print("   Эскалация активна: \(mockEscalation.isActive)")
        
        print("\n3️⃣ Симулируем продолжение работы (еще 5 минут)")
        print("   ❌ БАГ: Каждая новая активная минута будет перезапускать эскалацию!")
        
        for minute in 1...5 {
            print("\n   Минута \(minute):")
            activityManager.simulateContinuedWork(minutes: 1)
            print("     Запусков эскалации: \(mockEscalation.startCallCount)")
        }
        
        print("\n🚨 РЕЗУЛЬТАТ ТЕСТА:")
        print("   Ожидалось: 1 запуск эскалации")
        print("   Фактически: \(mockEscalation.startCallCount) запусков")
        
        if mockEscalation.startCallCount > 1 {
            print("   ❌ БАГ ВОСПРОИЗВЕДЕН! Эскалация перезапускается каждую минуту!")
        } else {
            print("   ✅ Баг не воспроизведен (это хорошо)")
        }
        
        print("\n💡 ОБЪЯСНЕНИЕ ПРОБЛЕМЫ:")
        print("   ActivityStateManager каждую минуту проверяет условие '42+ активных минут'")
        print("   Если условие выполняется - вызывает onInformalRestSuggestion?()")
        print("   Это приводит к повторному запуску эскалации, которая сбрасывает предыдущую")
        print("   Результат: эскалация никогда не доходит до финальной стадии")
}

// Запускаем тест
runTest()
