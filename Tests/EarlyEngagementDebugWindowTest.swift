#!/usr/bin/env swift

import Foundation

/// Тест для проверки исправления отладочного окна EarlyEngagementDebugWindow
/// 
/// ПРОБЛЕМА: Отладочное окно показывало старые коэффициенты в формуле расчета
/// ИСПРАВЛЕНИЕ: Обновили debugGetFormulaText для соответствия реальной логике

struct EarlyEngagementDebugWindowTest {
    
    static func main() {
        print("🧪 Тест отладочного окна EarlyEngagementDebugWindow")
        print("=" * 50)
        
        testDebugCalculateBarForScenario()
        print()
        testDebugFormulaText()
        print()
        testCriticalCase7PlusDays()
        
        print("=" * 50)
        print("✅ Все тесты пройдены!")
    }
    
    /// Тестирует метод debugCalculateBarForScenario
    static func testDebugCalculateBarForScenario() {
        print("📊 Тест 1: debugCalculateBarForScenario")
        
        let testCases = [
            (initialBar: 30, days: 0, messageIndex: 0, expected: 30, description: "0 дней, 1-е сообщение"),
            (initialBar: 30, days: 1, messageIndex: 0, expected: 23, description: "1 день, 1-е сообщение"),
            (initialBar: 30, days: 2, messageIndex: 0, expected: 14, description: "2 дня, 1-е сообщение"),
            (initialBar: 30, days: 3, messageIndex: 0, expected: 14, description: "3 дня, 1-е сообщение"),
            (initialBar: 30, days: 4, messageIndex: 0, expected: 8, description: "4 дня, 1-е сообщение"),
            (initialBar: 30, days: 5, messageIndex: 0, expected: 8, description: "5 дней, 1-е сообщение"),
            (initialBar: 30, days: 6, messageIndex: 0, expected: 8, description: "6 дней, 1-е сообщение"),
            (initialBar: 30, days: 7, messageIndex: 0, expected: 3, description: "7+ дней, 1-е сообщение"),
            (initialBar: 30, days: 10, messageIndex: 0, expected: 3, description: "10+ дней, 1-е сообщение")
        ]
        
        for testCase in testCases {
            let result = calculateBarForScenario(
                initialBar: testCase.initialBar,
                daysWithoutWork: testCase.days,
                messageIndex: testCase.messageIndex
            )
            
            let status = result == testCase.expected ? "✅" : "❌"
            print("   \(status) \(testCase.description): \(result) мин (ожидалось: \(testCase.expected))")
            
            if result != testCase.expected {
                print("      ⚠️  ОШИБКА: Ожидалось \(testCase.expected) мин, получено \(result) мин")
            }
        }
    }
    
    /// Тестирует генерацию текста формулы
    static func testDebugFormulaText() {
        print("🧮 Тест 2: Генерация текста формулы")
        
        let testCases = [
            (days: 1, initialBar: 30, expected: "30 × 0.77", description: "1 день"),
            (days: 2, initialBar: 30, expected: "30 × 0.48", description: "2 дня"),
            (days: 3, initialBar: 30, expected: "30 × 0.48", description: "3 дня"),
            (days: 4, initialBar: 30, expected: "30 × 0.29", description: "4 дня"),
            (days: 5, initialBar: 30, expected: "30 × 0.29", description: "5 дней"),
            (days: 6, initialBar: 30, expected: "30 × 0.29", description: "6 дней"),
            (days: 7, initialBar: 30, expected: "план-минимум (3 мин)", description: "7+ дней"),
            (days: 10, initialBar: 30, expected: "план-минимум (3 мин)", description: "10+ дней")
        ]
        
        for testCase in testCases {
            let calculatedBar = calculateBarForScenario(
                initialBar: testCase.initialBar,
                daysWithoutWork: testCase.days,
                messageIndex: 0
            )
            
            let formulaText = getFormulaText(
                daysLevel: testCase.days,
                messageIndex: 0,
                initialBar: testCase.initialBar,
                calculatedBar: calculatedBar
            )
            
            let containsExpected = formulaText.contains(testCase.expected)
            let status = containsExpected ? "✅" : "❌"
            print("   \(status) \(testCase.description): \"\(formulaText)\"")
            
            if !containsExpected {
                print("      ⚠️  ОШИБКА: Ожидалось содержание \"\(testCase.expected)\"")
            }
        }
    }
    
    /// Тестирует критический случай 7+ дней
    static func testCriticalCase7PlusDays() {
        print("🚨 Тест 3: Критический случай 7+ дней без работы")
        
        let initialBars = [3, 5, 8, 10, 15, 20, 25, 30, 35, 40, 45, 50, 52]
        
        for initialBar in initialBars {
            let result = calculateBarForScenario(
                initialBar: initialBar,
                daysWithoutWork: 7,
                messageIndex: 0
            )
            
            let status = result == 3 ? "✅" : "❌"
            print("   \(status) Изначальная планка \(initialBar) мин → Результат: \(result) мин")
            
            if result != 3 {
                print("      ⚠️  КРИТИЧЕСКАЯ ОШИБКА: Для 7+ дней должно быть 3 мин!")
            }
        }
        
        // Проверяем формулу для 7+ дней
        let formulaText = getFormulaText(
            daysLevel: 7,
            messageIndex: 0,
            initialBar: 30,
            calculatedBar: 3
        )
        
        let containsPlanMinimum = formulaText.contains("план-минимум")
        let status = containsPlanMinimum ? "✅" : "❌"
        print("   \(status) Формула для 7+ дней: \"\(formulaText)\"")
        
        if !containsPlanMinimum {
            print("      ⚠️  ОШИБКА: Формула должна содержать 'план-минимум'!")
        }
    }
    
    // MARK: - Helper Methods (копии логики из EarlyEngagementSystem)
    
    /// Рассчитывает планку для отладочного сценария (копия логики из EarlyEngagementSystem)
    static func calculateBarForScenario(initialBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)

        // ВЕРТИКАЛЬНАЯ адаптация (по дням без работы)
        let verticalBarTime: TimeInterval
        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка остается базовой (упрощенно)
            verticalBarTime = initialBarTime
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%) = ~40 мин
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%) = ~25 мин
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%) = ~15 мин
            verticalBarTime = initialBarTime * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            verticalBarTime = 3 * 60
        }

        // ГОРИЗОНТАЛЬНАЯ дескалация (по времени дня)
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            // 1-е предложение: 100% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            // 2-е предложение: 50% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            // 3-е предложение: фиксированный минимум (15 мин)
            finalBarTime = 15 * 60
        case 3:
            // 4-е предложение: план-минимум (3 мин)
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }

        // Ограничиваем максимум 52 минуты
        let limitedBarTime = min(finalBarTime, 52 * 60)

        return Int(limitedBarTime / 60)
    }
    
    /// Генерирует текст формулы для отладки (копия логики из EarlyEngagementSystem)
    static func getFormulaText(daysLevel: Int, messageIndex: Int, initialBar: Int, calculatedBar: Int) -> String {
        // Сначала показываем вертикальную адаптацию
        let verticalText: String
        switch daysLevel {
        case 0:
            verticalText = "\(initialBar) (базовая)"
        case 1:
            verticalText = "\(initialBar) × 0.77"
        case 2...3:
            verticalText = "\(initialBar) × 0.48"
        case 4...6:
            verticalText = "\(initialBar) × 0.29"
        default:
            verticalText = "план-минимум (3 мин)"
        }
        
        // Затем показываем горизонтальную дескалацию
        let horizontalText: String
        switch messageIndex {
        case 0:
            horizontalText = "× 1.0 (100%)"
        case 1:
            horizontalText = "× 0.5 (50%)"
        case 2:
            horizontalText = "→ 15 мин"
        case 3:
            horizontalText = "→ 3 мин"
        default:
            horizontalText = "× 1.0 (100%)"
        }
        
        return "\(verticalText) \(horizontalText) = \(calculatedBar) мин"
    }
}

// Расширение для повторения строк
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// Запуск тестов
EarlyEngagementDebugWindowTest.main()
