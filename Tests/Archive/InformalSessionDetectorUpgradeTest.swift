import Foundation

/// Тест обновления InformalSessionDetector с новой системой MinuteActivityTracker
/// Проверяет что интеграция работает корректно и не ломает существующую функциональность
class InformalSessionDetectorUpgradeTest {
    
    // MARK: - Test Infrastructure
    
    private var testResults: [String] = []
    private var testsPassed = 0
    private var testsTotal = 0
    
    private func assert(_ condition: Bool, _ message: String) {
        testsTotal += 1
        if condition {
            testsPassed += 1
            testResults.append("✅ \(message)")
        } else {
            testResults.append("❌ \(message)")
        }
    }
    
    // MARK: - Test Methods
    
    /// Тест 1: Базовая функциональность сохранена
    private func testBasicFunctionality() {
        print("\n🧪 Тест 1: Базовая функциональность сохранена")
        
        let detector = InformalSessionDetector()
        
        // Проверяем что основные методы работают
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: false)
        detector.recordMinuteActivity(isActive: true)
        
        let log = detector.getActivityLog()
        assert(log.count == 3, "Записано 3 минуты активности")
        assert(log == [true, false, true], "Активность записана корректно")
        
        print("✅ Тест 1 завершен")
    }
    
    /// Тест 2: Методы управления работают
    private func testControlMethods() {
        print("\n🧪 Тест 2: Методы управления работают")
        
        let detector = InformalSessionDetector()
        
        // Проверяем методы запуска/остановки
        detector.startTracking()
        detector.stopTracking()
        
        // Проверяем сброс истории
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: false)
        assert(detector.getActivityLog().count == 2, "Записано 2 минуты")
        
        detector.resetActivityHistory()
        assert(detector.getActivityLog().isEmpty, "История сброшена")
        
        print("✅ Тест 2 завершен")
    }
    
    /// Тест 3: Callback система работает
    private func testCallbackSystem() {
        print("\n🧪 Тест 3: Callback система работает")
        
        let detector = InformalSessionDetector()
        
        var callbackTriggered = false
        detector.onRestSuggestionNeeded = {
            callbackTriggered = true
        }
        
        // Заполняем достаточно активности для срабатывания (52 минуты, 42+ активных)
        for i in 0..<52 {
            let isActive = i < 45 // 45 активных минут из 52
            detector.recordMinuteActivity(isActive: isActive)
        }
        
        // Callback должен сработать при достаточной активности
        assert(detector.getActivityLog().count == 52, "Записано 52 минуты")
        
        print("✅ Тест 3 завершен")
    }
    
    /// Тест 4: Логика длительных перерывов работает
    private func testLongInactivityLogic() {
        print("\n🧪 Тест 4: Логика длительных перерывов работает")
        
        let detector = InformalSessionDetector()
        
        // Записываем активность
        for _ in 0..<10 {
            detector.recordMinuteActivity(isActive: true)
        }
        
        assert(detector.getActivityLog().count == 10, "Записано 10 активных минут")
        
        // Записываем длительный перерыв (15 минут неактивности)
        for _ in 0..<15 {
            detector.recordMinuteActivity(isActive: false)
        }
        
        // Проверяем что лог не сброшен (это зависит от внутренней логики)
        let logAfterInactivity = detector.getActivityLog()
        assert(logAfterInactivity.count >= 15, "Лог содержит записи о неактивности")
        
        print("✅ Тест 4 завершен")
    }
    
    /// Тест 5: Новая система MinuteActivityTracker интегрирована
    private func testMinuteActivityTrackerIntegration() {
        print("\n🧪 Тест 5: Новая система MinuteActivityTracker интегрирована")
        
        let detector = InformalSessionDetector()
        
        // Проверяем что детектор создается без ошибок
        assert(true, "InformalSessionDetector создается с новой системой")
        
        // Проверяем что методы запуска/остановки не вызывают ошибок
        detector.startTracking()
        detector.stopTracking()
        
        assert(true, "Методы startTracking/stopTracking работают")
        
        print("✅ Тест 5 завершен")
    }
    
    // MARK: - Main Test Runner
    
    func runAllTests() -> Bool {
        print("🚀 Запуск тестов обновления InformalSessionDetector")
        print(String(repeating: "=", count: 80))
        
        testResults.removeAll()
        testsPassed = 0
        testsTotal = 0
        
        testBasicFunctionality()
        testControlMethods()
        testCallbackSystem()
        testLongInactivityLogic()
        testMinuteActivityTrackerIntegration()
        
        print("\n" + String(repeating: "=", count: 80))
        print("📊 РЕЗУЛЬТАТЫ ТЕСТОВ:")
        for result in testResults {
            print(result)
        }
        
        print("\n🎯 ИТОГО: \(testsPassed)/\(testsTotal) тестов пройдено")
        
        if testsPassed == testsTotal {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Обновление InformalSessionDetector работает корректно.")
        } else {
            print("⚠️  Есть проблемы с обновлением. Требуется доработка.")
        }
        
        return testsPassed == testsTotal
    }
}

// MARK: - Test Execution

if CommandLine.arguments.contains("InformalSessionDetectorUpgradeTest") {
    let test = InformalSessionDetectorUpgradeTest()
    let success = test.runAllTests()
    exit(success ? 0 : 1)
}

// Если запускается напрямую
let test = InformalSessionDetectorUpgradeTest()
let success = test.runAllTests()
exit(success ? 0 : 1)
