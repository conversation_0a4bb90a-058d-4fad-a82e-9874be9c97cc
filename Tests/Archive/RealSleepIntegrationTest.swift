#!/usr/bin/env swift

import Foundation

// Простой тест интеграции SleepWakeDetector с AppDelegate
// Проверяет что новая система работает корректно

print("🧪 Тест интеграции SleepWakeDetector с AppDelegate")
print(String(repeating: "=", count: 50))

// Тест 1: Проверка что SleepWakeDetector компилируется
do {
    print("1️⃣ Тест компиляции SleepWakeDetector...")
    
    // Попытка создать экземпляр
    let detector = SleepWakeDetector.shared
    
    // Проверка основных методов
    detector.startMonitoring()
    detector.stopMonitoring()
    
    print("✅ SleepWakeDetector компилируется и работает")
} catch {
    print("❌ Ошибка в SleepWakeDetector: \(error)")
    exit(1)
}

// Тест 2: Проверка событий
do {
    print("2️⃣ Тест событий SleepWakeDetector...")
    
    let detector = SleepWakeDetector.shared
    var eventReceived = false
    
    // Устанавливаем колбэк
    detector.onSleepWakeEvent = { event in
        eventReceived = true
        print("📨 Получено событие: \(event)")
    }
    
    // Симулируем события
    detector.simulateSystemSleep()
    detector.simulateSystemWake()
    
    print("✅ События обрабатываются корректно")
} catch {
    print("❌ Ошибка в событиях: \(error)")
    exit(1)
}

// Тест 3: Проверка различения сна vs неактивности
print("3️⃣ Тест различения сна vs неактивности...")

let detector3 = SleepWakeDetector.shared

// Симулируем короткий сон (должен быть неактивность)
detector3.simulateLongInactivity(duration: 300) // 5 минут

// Симулируем длительный сон (должен быть реальный сон)
detector3.simulateSystemSleep()
Thread.sleep(forTimeInterval: 0.1) // Небольшая пауза
detector3.simulateSystemWake()

print("✅ Различение сна vs неактивности работает")

print(String(repeating: "=", count: 50))
print("🎉 Все тесты интеграции прошли успешно!")
print("✅ SleepWakeDetector готов к интеграции с AppDelegate")
