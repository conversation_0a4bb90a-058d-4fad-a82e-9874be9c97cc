import Foundation

/// Реальный тест интеграции BreakTimer с 4-бандовой системой активности
/// Тестирует РЕАЛЬНЫЙ код, не моки!
@main
struct RealBreakTimerIntegrationTest {
    static func main() {
        print("🧪 Запуск реального теста интеграции BreakTimer...")
        
        var passedTests = 0
        let totalTests = 6
        
        // Тест 1: Создание BreakTimer с новой системой
        print("\n🧪 Тест 1: Создание BreakTimer с новой системой")
        let breakTimer = BreakTimer()
        
        // Проверяем что система создалась
        let debugInfo = breakTimer.getActivitySystemDebugInfo()
        if debugInfo.contains("Используется новая система: true") {
            print("✅ BreakTimer создан с новой системой активности")
            passedTests += 1
        } else {
            print("❌ BreakTimer не использует новую систему")
            print("Debug info: \(debugInfo)")
        }
        
        // Тест 2: Переключение между системами
        print("\n🧪 Тест 2: Переключение между системами")
        breakTimer.setUseNewActivitySystem(false)
        let debugInfoOld = breakTimer.getActivitySystemDebugInfo()
        
        breakTimer.setUseNewActivitySystem(true)
        let debugInfoNew = breakTimer.getActivitySystemDebugInfo()
        
        if debugInfoOld.contains("Используется новая система: false") && 
           debugInfoNew.contains("Используется новая система: true") {
            print("✅ Переключение между системами работает")
            passedTests += 1
        } else {
            print("❌ Переключение между системами не работает")
            print("Old: \(debugInfoOld)")
            print("New: \(debugInfoNew)")
        }
        
        // Тест 3: Запуск отдыха с новой системой
        print("\n🧪 Тест 3: Запуск отдыха с новой системой")
        breakTimer.setUseNewActivitySystem(true)
        
        var activityDetected = false
        breakTimer.onActivityDetected = {
            activityDetected = true
        }
        
        breakTimer.startBreak(duration: 5.0) // 5 секунд для теста
        
        if breakTimer.isActive {
            print("✅ Отдых запущен с новой системой")
            passedTests += 1
        } else {
            print("❌ Отдых не запустился")
        }
        
        // Тест 4: Принудительная проверка активности
        print("\n🧪 Тест 4: Принудительная проверка активности")
        breakTimer.forceCheckActivity()
        
        // Даем время на обработку
        Thread.sleep(forTimeInterval: 0.1)
        
        print("✅ Принудительная проверка активности выполнена")
        passedTests += 1
        
        // Тест 5: Остановка отдыха
        print("\n🧪 Тест 5: Остановка отдыха")
        breakTimer.stopBreak()
        
        if !breakTimer.isActive {
            print("✅ Отдых остановлен")
            passedTests += 1
        } else {
            print("❌ Отдых не остановился")
        }
        
        // Тест 6: Отладочная информация содержит MinuteActivityTracker
        print("\n🧪 Тест 6: Отладочная информация содержит MinuteActivityTracker")
        breakTimer.setUseNewActivitySystem(true)
        let finalDebugInfo = breakTimer.getActivitySystemDebugInfo()
        
        if finalDebugInfo.contains("MinuteActivityTracker Debug:") {
            print("✅ Отладочная информация содержит данные MinuteActivityTracker")
            passedTests += 1
        } else {
            print("❌ Отладочная информация не содержит данные MinuteActivityTracker")
            print("Debug info: \(finalDebugInfo)")
        }
        
        // Итоги
        print("\n" + String(repeating: "=", count: 50))
        print("🧪 Результаты теста интеграции BreakTimer:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Интеграция BreakTimer работает корректно")
        } else {
            print("❌ Есть проблемы с интеграцией BreakTimer")
        }
        
        print("\n💡 Для проверки что тест ловит ошибки:")
        print("1. Сломайте метод getActivitySystemDebugInfo() в BreakTimer")
        print("2. Запустите тест - он должен упасть")
        print("3. Восстановите код и убедитесь что тест снова проходит")
    }
}
