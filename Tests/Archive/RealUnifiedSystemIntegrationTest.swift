import Foundation

/// ТЕСТ РЕАЛЬНОЙ ИНТЕГРАЦИИ ВСЕЙ УНИФИЦИРОВАННОЙ СИСТЕМЫ
/// Проверяем что MinuteActivityTracker + ActivityStateManager работают вместе

@main
struct RealUnifiedSystemIntegrationTest {
    static func main() {
        print("🚀 ТЕСТ РЕАЛЬНОЙ ИНТЕГРАЦИИ УНИФИЦИРОВАННОЙ СИСТЕМЫ")
        print(String(repeating: "=", count: 60))
        
        // Создаем РЕАЛЬНЫЕ объекты
        let minuteTracker = MinuteActivityTracker()
        let stateManager = ActivityStateManager()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Запуск полной системы
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Запуск полной системы")
        
        minuteTracker.startTracking()
        stateManager.start()
        
        let trackerState = minuteTracker.getCurrentBandsState()
        let managerDebug = stateManager.getDebugInfo()
        
        if trackerState.2 && managerDebug.contains("Активен: true") {
            print("✅ ПРОЙДЕН: Полная система запускается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ запускается полностью")
            print("   Трекер активен: \(trackerState.2)")
            print("   Менеджер: \(managerDebug)")
        }
        
        // СЦЕНАРИЙ 2: Симуляция активности через всю цепочку
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Симуляция активности через всю цепочку")
        
        // Симулируем активность в MinuteTracker
        minuteTracker.simulateActivityInCurrentBand()
        
        // Симулируем активность в StateManager
        stateManager.simulateActivity()
        
        let (currentState, _) = stateManager.getCurrentStateInfo()
        let wasMinuteActive = minuteTracker.wasCurrentMinuteActive()
        
        if currentState == .working && wasMinuteActive {
            print("✅ ПРОЙДЕН: Активность обрабатывается через всю цепочку")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ обрабатывается полностью")
            print("   Состояние менеджера: \(currentState)")
            print("   Активность трекера: \(wasMinuteActive)")
        }
        
        // СЦЕНАРИЙ 3: Переход в состояние отсутствия
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Переход в состояние отсутствия")
        
        // Симулируем долгую неактивность
        stateManager.simulateInactivity(duration: 5 * 60) // 5 минут
        
        let (awayState, _) = stateManager.getCurrentStateInfo()
        
        if awayState == .awayMedium {
            print("✅ ПРОЙДЕН: Система корректно переходит в состояние отсутствия")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Переход в состояние отсутствия НЕ работает")
            print("   Ожидалось: awayMedium, получено: \(awayState)")
        }
        
        // СЦЕНАРИЙ 4: Возврат пользователя
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Возврат пользователя")
        
        // Симулируем возврат активности
        minuteTracker.simulateActivityInCurrentBand()
        stateManager.simulateActivity()
        
        let (returnState, _) = stateManager.getCurrentStateInfo()
        let isActiveAfterReturn = minuteTracker.wasCurrentMinuteActive()
        
        if returnState == .working && isActiveAfterReturn {
            print("✅ ПРОЙДЕН: Возврат пользователя обрабатывается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Возврат пользователя НЕ работает")
            print("   Состояние: \(returnState), активность: \(isActiveAfterReturn)")
        }
        
        // СЦЕНАРИЙ 5: Проверка счетчиков при неактивности
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Проверка счетчиков при неактивности")
        
        // Симулируем короткую неактивность (должны остановиться счетчики)
        stateManager.simulateInactivity(duration: 20) // 20 секунд
        
        let areCountersStopped = stateManager.areCountersStopped()
        
        if areCountersStopped {
            print("✅ ПРОЙДЕН: Счетчики останавливаются при неактивности")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Счетчики НЕ останавливаются")
        }
        
        // СЦЕНАРИЙ 6: Принудительное завершение минуты
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Принудительное завершение минуты")
        
        // Добавляем активность и принудительно завершаем
        minuteTracker.simulateActivityInCurrentBand()
        let completedWithActivity = minuteTracker.forceCompleteCurrentMinute()
        
        if completedWithActivity {
            print("✅ ПРОЙДЕН: Принудительное завершение работает корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Принудительное завершение НЕ работает")
        }
        
        // СЦЕНАРИЙ 7: Остановка всей системы
        total += 1
        print("\n📋 СЦЕНАРИЙ 7: Остановка всей системы")
        
        minuteTracker.stopTracking()
        stateManager.stop()
        
        let trackerStateAfterStop = minuteTracker.getCurrentBandsState()
        let managerDebugAfterStop = stateManager.getDebugInfo()
        
        if !trackerStateAfterStop.2 && managerDebugAfterStop.contains("Активен: false") {
            print("✅ ПРОЙДЕН: Вся система корректно останавливается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ останавливается полностью")
            print("   Трекер активен: \(trackerStateAfterStop.2)")
            print("   Менеджер: \(managerDebugAfterStop)")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ Унифицированная система работает корректно")
            
            // Проверка обнаружения поломок
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В УНИФИЦИРОВАННОЙ СИСТЕМЕ!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Откройте SimplePomodoroTest/MinuteActivityTracker.swift")
        print("2. В методе wasCurrentMinuteActive() строка 78:")
        print("   Замените: return hasActivity")
        print("   На:       return false")
        print("3. Запустите этот тест снова")
        print("4. СЦЕНАРИЙ 2, 4 или 6 должны провалиться!")
        print("5. Верните код обратно")
        print("\nИЛИ:")
        print("1. Откройте SimplePomodoroTest/ActivityStateManager.swift")
        print("2. В методе simulateActivity() найдите:")
        print("   handleUserActivity()")
        print("3. Замените на:")
        print("   // handleUserActivity()")
        print("4. Запустите тест - СЦЕНАРИЙ 2 или 4 провалятся!")
        print("\nЭто покажет что тест ловит поломки в реальной интеграции.")
        
        exit(0)
    }
}
