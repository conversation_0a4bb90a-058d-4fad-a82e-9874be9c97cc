import Foundation

/// Тест интеграции InformalSessionDetector с новой системой MinuteActivityTracker
/// Проверяет что обновленный детектор работает корректно с 4-бандовым обнаружением активности
class InformalSessionDetectorIntegrationTest {
    
    // MARK: - Test Infrastructure
    
    private var testResults: [String] = []
    private var testsPassed = 0
    private var testsTotal = 0
    
    private func assert(_ condition: Bool, _ message: String) {
        testsTotal += 1
        if condition {
            testsPassed += 1
            testResults.append("✅ \(message)")
        } else {
            testResults.append("❌ \(message)")
        }
    }
    
    // MARK: - Mock Classes
    
    /// Mock MinuteActivityTracker для тестирования
    class MockMinuteActivityTracker: MinuteActivityTracker {
        private var mockResults: [Bool] = []
        private var currentIndex = 0
        
        func setMockResults(_ results: [Bool]) {
            mockResults = results
            currentIndex = 0
        }
        
        override func wasCurrentMinuteActive() -> Bool {
            guard currentIndex < mockResults.count else { return false }
            let result = mockResults[currentIndex]
            currentIndex += 1
            return result
        }
        
        override func forceCompleteCurrentMinute() {
            // Ничего не делаем в моке
        }
        
        override func startTracking() {
            // Ничего не делаем в моке
        }
    }
    
    // MARK: - Test Methods
    
    /// Тест 1: Базовая функциональность с новой системой
    private func testBasicFunctionality() {
        print("\n🧪 Тест 1: Базовая функциональность с новой системой")
        
        let mockTracker = MockMinuteActivityTracker()
        let detector = InformalSessionDetector(minuteActivityTracker: mockTracker)
        
        // Настраиваем мок для возврата активности
        mockTracker.setMockResults([true, false, true, true, false])
        
        var suggestionTriggered = false
        detector.onRestSuggestionNeeded = {
            suggestionTriggered = true
        }
        
        // Записываем несколько минут активности
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: false)
        detector.recordMinuteActivity(isActive: true)
        
        assert(detector.getActivityLog().count == 3, "Записано 3 минуты активности")
        assert(detector.getActivityLog() == [true, false, true], "Активность записана корректно")
        
        print("✅ Тест 1 завершен")
    }
    
    /// Тест 2: Автоматическое обнаружение активности
    private func testAutomaticActivityDetection() {
        print("\n🧪 Тест 2: Автоматическое обнаружение активности")
        
        let mockTracker = MockMinuteActivityTracker()
        let detector = InformalSessionDetector(minuteActivityTracker: mockTracker)
        
        // Настраиваем мок для последовательности активности
        mockTracker.setMockResults([true, true, false, true, false, false])
        
        // Симулируем автоматическую проверку
        detector.recordMinuteActivity(isActive: true)  // Первая минута
        detector.recordMinuteActivity(isActive: true)  // Вторая минута
        detector.recordMinuteActivity(isActive: false) // Третья минута
        
        let log = detector.getActivityLog()
        assert(log.count == 3, "Записано 3 минуты")
        assert(log == [true, true, false], "Последовательность активности корректна")
        
        print("✅ Тест 2 завершен")
    }
    
    /// Тест 3: Интеграция с 4-бандовой системой
    private func testFourBandIntegration() {
        print("\n🧪 Тест 3: Интеграция с 4-бандовой системой")
        
        // Создаем реальный MinuteActivityTracker с mock UnifiedActivityChecker
        let mockActivityChecker = MockUnifiedActivityChecker()
        let tracker = MinuteActivityTracker(activityChecker: mockActivityChecker)
        let detector = InformalSessionDetector(minuteActivityTracker: tracker)
        
        // Симулируем активность в разных бандах
        mockActivityChecker.setMockActivity([
            true, false, false, false,  // Банд 1: активен, остальные нет
            false, false, false, false, // Банд 2-4: неактивны
        ])
        
        tracker.startTracking()
        
        // Симулируем прохождение времени и проверку
        for i in 0..<4 {
            tracker.forceCompleteCurrentMinute()
            let wasActive = tracker.wasCurrentMinuteActive()
            detector.recordMinuteActivity(isActive: wasActive)
            
            if i == 0 {
                assert(wasActive == true, "Первая минута должна быть активной (активность в банде 1)")
            } else {
                assert(wasActive == false, "Минута \(i+1) должна быть неактивной")
            }
        }
        
        print("✅ Тест 3 завершен")
    }
    
    /// Тест 4: Обратная совместимость
    private func testBackwardCompatibility() {
        print("\n🧪 Тест 4: Обратная совместимость")
        
        let detector = InformalSessionDetector()
        
        // Проверяем что старые методы работают
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: false)
        detector.recordMinuteActivity(isActive: true)
        
        let log = detector.getActivityLog()
        assert(log.count == 3, "Старый интерфейс работает")
        assert(log == [true, false, true], "Данные записываются корректно")
        
        // Проверяем методы сброса
        detector.resetActivityHistory()
        assert(detector.getActivityLog().isEmpty, "История сброшена")
        
        print("✅ Тест 4 завершен")
    }
    
    /// Тест 5: Проверка методов управления
    private func testControlMethods() {
        print("\n🧪 Тест 5: Проверка методов управления")

        let detector = InformalSessionDetector()

        // Проверяем методы запуска/остановки
        detector.startTracking()
        detector.stopTracking()

        // Проверяем сброс истории
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: false)
        assert(detector.getActivityLog().count == 2, "Записано 2 минуты")

        detector.resetActivityHistory()
        assert(detector.getActivityLog().isEmpty, "История сброшена")

        print("✅ Тест 5 завершен")
    }
    
    // MARK: - Main Test Runner
    
    func runAllTests() -> Bool {
        print("🚀 Запуск тестов интеграции InformalSessionDetector с новой системой активности")
        print(String(repeating: "=", count: 80))

        testResults.removeAll()
        testsPassed = 0
        testsTotal = 0

        testBasicFunctionality()
        testAutomaticActivityDetection()
        testFourBandIntegration()
        testBackwardCompatibility()
        testControlMethods()

        print("\n" + String(repeating: "=", count: 80))
        print("📊 РЕЗУЛЬТАТЫ ТЕСТОВ:")
        for result in testResults {
            print(result)
        }

        print("\n🎯 ИТОГО: \(testsPassed)/\(testsTotal) тестов пройдено")

        if testsPassed == testsTotal {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Интеграция работает корректно.")
        } else {
            print("⚠️  Есть проблемы с интеграцией. Требуется доработка.")
        }

        return testsPassed == testsTotal
    }
}

// MARK: - Mock Classes

/// Mock PomodoroTimer для тестирования
class MockPomodoroTimer {
    var state: PomodoroState = .idle
}

/// Mock UnifiedActivityChecker для тестирования
class MockUnifiedActivityChecker: UnifiedActivityChecker {
    private var mockActivity: [Bool] = []
    private var currentIndex = 0
    
    func setMockActivity(_ activity: [Bool]) {
        mockActivity = activity
        currentIndex = 0
    }
    
    override func isUserCurrentlyActive() -> Bool {
        guard currentIndex < mockActivity.count else { return false }
        let result = mockActivity[currentIndex]
        currentIndex += 1
        return result
    }
}

// MARK: - Test Execution

if CommandLine.arguments.contains("InformalSessionDetectorIntegrationTest") {
    let test = InformalSessionDetectorIntegrationTest()
    let success = test.runAllTests()
    exit(success ? 0 : 1)
}
