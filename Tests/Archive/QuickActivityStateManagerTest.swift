import Foundation

/// БЫСТРЫЙ тест для проверки КРИТИЧЕСКОЙ функциональности ActivityStateManager
/// Проверяем только то, что ДЕЙСТВИТЕЛЬНО важно

// Mock MinuteActivityTracker для контролируемого тестирования
class QuickMockMinuteActivityTracker {
    private var mockActive = false
    private var trackingActive = false
    
    func setMockActive(_ active: Bool) {
        mockActive = active
    }
    
    func startTracking() {
        trackingActive = true
    }
    
    func stopTracking() {
        trackingActive = false
    }
    
    func wasCurrentMinuteActive() -> Bool {
        return mockActive
    }
    
    func isTrackingActive() -> Bool {
        return trackingActive
    }
}

// Упрощенная версия ActivityStateManager для тестирования
class QuickTestableActivityStateManager {
    enum ActivityState {
        case working
        case awayShort
        case awayMedium  
        case awayLong
        case awayVeryLong
        case formalRest
    }
    
    enum ReturnMessage {
        case resumeSilently
        case partialRest
        case chooseRestOrWork
        case fullRest
    }
    
    private let minuteActivityTracker: QuickMockMinuteActivityTracker
    private var currentState: ActivityState = .working
    private var lastActivityTime = Date()
    private var isActive = false
    
    // Пороги времени (в секундах)
    private let inactivityThreshold: TimeInterval = 15  // 15 секунд
    private let shortAwayThreshold: TimeInterval = 2 * 60  // 2 минуты
    private let mediumAwayThreshold: TimeInterval = 10 * 60  // 10 минут
    private let longAwayThreshold: TimeInterval = 17 * 60  // 17 минут
    
    init(minuteActivityTracker: QuickMockMinuteActivityTracker) {
        self.minuteActivityTracker = minuteActivityTracker
    }
    
    func start() {
        isActive = true
        minuteActivityTracker.startTracking()
        currentState = .working
        lastActivityTime = Date()
        print("🎯 ActivityStateManager запущен")
    }
    
    func stop() {
        isActive = false
        minuteActivityTracker.stopTracking()
        print("🎯 ActivityStateManager остановлен")
    }
    
    func handleMinuteActivity() {
        guard isActive else { return }
        
        let wasActive = minuteActivityTracker.wasCurrentMinuteActive()
        
        if wasActive {
            // Пользователь активен
            if currentState != .working {
                handleUserReturn()
            }
            lastActivityTime = Date()
            currentState = .working
        } else {
            // Пользователь неактивен
            let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)
            
            if timeSinceLastActivity >= inactivityThreshold {
                updateAwayState(timeSinceLastActivity)
            }
        }
    }
    
    private func handleUserReturn() {
        let awayTime = Date().timeIntervalSince(lastActivityTime)
        let message = determineReturnMessage(awayTime)
        
        print("🎯 Пользователь вернулся после \(Int(awayTime)) секунд: \(message)")
    }
    
    private func determineReturnMessage(_ awayTime: TimeInterval) -> ReturnMessage {
        if awayTime < shortAwayThreshold {
            return .resumeSilently
        } else if awayTime < mediumAwayThreshold {
            return .partialRest
        } else if awayTime < longAwayThreshold {
            return .chooseRestOrWork
        } else {
            return .fullRest
        }
    }
    
    private func updateAwayState(_ timeSinceLastActivity: TimeInterval) {
        if timeSinceLastActivity >= longAwayThreshold {
            currentState = .awayVeryLong
        } else if timeSinceLastActivity >= mediumAwayThreshold {
            currentState = .awayLong
        } else if timeSinceLastActivity >= shortAwayThreshold {
            currentState = .awayMedium
        } else {
            currentState = .awayShort
        }
    }
    
    // Для тестирования
    func getCurrentState() -> ActivityState {
        return currentState
    }
    
    func setLastActivityTime(_ time: Date) {
        lastActivityTime = time
    }
    
    func isManagerActive() -> Bool {
        return isActive
    }
}

@main
struct QuickActivityStateManagerTest {
    static func main() {
        print("🚀 БЫСТРЫЙ ТЕСТ: Критическая функциональность ActivityStateManager")
        print(String(repeating: "=", count: 70))
        
        let mockTracker = QuickMockMinuteActivityTracker()
        let stateManager = QuickTestableActivityStateManager(minuteActivityTracker: mockTracker)
        
        var passed = 0
        var total = 0
        
        // ТЕСТ 1: Запуск и остановка
        total += 1
        print("\n🧪 ТЕСТ 1: Запуск и остановка")
        stateManager.start()
        
        if stateManager.isManagerActive() && mockTracker.isTrackingActive() {
            stateManager.stop()
            if !stateManager.isManagerActive() && !mockTracker.isTrackingActive() {
                passed += 1
                print("✅ ПРОЙДЕН: Запуск и остановка работают")
            } else {
                print("❌ ПРОВАЛЕН: Остановка НЕ работает")
            }
        } else {
            print("❌ ПРОВАЛЕН: Запуск НЕ работает")
        }
        
        // ТЕСТ 2: Обработка активности
        total += 1
        print("\n🧪 ТЕСТ 2: Обработка активности")
        stateManager.start()
        
        mockTracker.setMockActive(true)
        stateManager.handleMinuteActivity()
        
        if stateManager.getCurrentState() == .working {
            passed += 1
            print("✅ ПРОЙДЕН: Активность обрабатывается корректно")
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ обрабатывается")
            print("   Текущее состояние: \(stateManager.getCurrentState())")
        }
        
        // ТЕСТ 3: Переход в состояние отсутствия
        total += 1
        print("\n🧪 ТЕСТ 3: Переход в состояние отсутствия")
        
        // Устанавливаем время последней активности в прошлое (5 минут назад)
        let fiveMinutesAgo = Date().addingTimeInterval(-5 * 60)
        stateManager.setLastActivityTime(fiveMinutesAgo)
        
        mockTracker.setMockActive(false)
        stateManager.handleMinuteActivity()
        
        if stateManager.getCurrentState() == .awayMedium {
            passed += 1
            print("✅ ПРОЙДЕН: Переход в состояние отсутствия работает")
        } else {
            print("❌ ПРОВАЛЕН: Переход в состояние отсутствия НЕ работает")
            print("   Ожидалось: awayMedium, получено: \(stateManager.getCurrentState())")
        }
        
        // ТЕСТ 4: Возврат пользователя
        total += 1
        print("\n🧪 ТЕСТ 4: Возврат пользователя")
        
        mockTracker.setMockActive(true)
        stateManager.handleMinuteActivity()
        
        if stateManager.getCurrentState() == .working {
            passed += 1
            print("✅ ПРОЙДЕН: Возврат пользователя работает")
        } else {
            print("❌ ПРОВАЛЕН: Возврат пользователя НЕ работает")
            print("   Ожидалось: working, получено: \(stateManager.getCurrentState())")
        }
        
        // ТЕСТ 5: Проверка обнаружения поломки
        total += 1
        print("\n🧪 ТЕСТ 5: Проверка обнаружения поломки")

        // Устанавливаем время последней активности в прошлое (20 минут назад)
        let twentyMinutesAgo = Date().addingTimeInterval(-20 * 60)
        stateManager.setLastActivityTime(twentyMinutesAgo)

        // Намеренно ломаем: устанавливаем неактивность
        mockTracker.setMockActive(false)
        stateManager.handleMinuteActivity()

        // После долгой неактивности состояние должно быть awayVeryLong
        if stateManager.getCurrentState() == .awayVeryLong {
            passed += 1
            print("✅ ПРОЙДЕН: Тест корректно обнаруживает долгое отсутствие")
        } else {
            print("❌ ПРОВАЛЕН: Тест НЕ обнаруживает проблемы")
            print("   Ожидалось: awayVeryLong, получено: \(stateManager.getCurrentState())")
        }
        
        stateManager.stop()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) тестов пройдено")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
            print("✅ Критическая функциональность ActivityStateManager работает корректно")
            exit(0)
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ!")
            print("❌ Требуется доработка ActivityStateManager")
            exit(1)
        }
    }
}
