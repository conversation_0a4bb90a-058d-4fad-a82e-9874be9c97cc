import Foundation

/// ⚠️ УСТАРЕВШИЙ ТЕСТ - НЕ ИСПОЛЬЗОВАТЬ!
/// 🔄 ЗАМЕНЕН НА: Тесты для ActivityStateManager
/// 🚫 Сохранен только для справки, планируется к удалению
///
/// ТЕСТ БАЗОВОЙ ФУНКЦИОНАЛЬНОСТИ InformalSessionDetector
/// Проверяем основные методы без зависимостей от PomodoroTimer/BreakTimer

@main
struct RealInformalDetectorBasicTest {
    static func main() {
        print("🚀 ТЕСТ БАЗОВОЙ ФУНКЦИОНАЛЬНОСТИ InformalSessionDetector")
        print(String(repeating: "=", count: 60))
        
        // Создаем РЕАЛЬНЫЙ объект для тестов (без зависимостей)
        let detector = InformalSessionDetector()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Включение/выключение детектора
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Включение/выключение детектора")
        
        detector.setEnabled(true)
        let debugAfterEnable = detector.getDebugInfo()
        
        detector.setEnabled(false)
        let debugAfterDisable = detector.getDebugInfo()
        
        if debugAfterEnable.contains("Включен: true") && debugAfterDisable.contains("Включен: false") {
            print("✅ ПРОЙДЕН: Включение/выключение работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Включение/выключение НЕ работает")
            print("   После включения: \(debugAfterEnable)")
            print("   После выключения: \(debugAfterDisable)")
        }
        
        // Включаем обратно для остальных тестов
        detector.setEnabled(true)
        
        // СЦЕНАРИЙ 2: Запись активности
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Запись активности")
        
        let debugBefore = detector.getDebugInfo()
        
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: false)
        detector.recordMinuteActivity(isActive: true)
        
        let debugAfter = detector.getDebugInfo()
        
        if debugAfter.contains("Всего минут: 3") {
            print("✅ ПРОЙДЕН: Запись активности работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Запись активности НЕ работает")
            print("   До: \(debugBefore)")
            print("   После: \(debugAfter)")
        }
        
        // СЦЕНАРИЙ 3: Сброс истории
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Сброс истории")
        
        detector.resetActivityHistory()
        let debugAfterReset = detector.getDebugInfo()
        
        if debugAfterReset.contains("Всего минут: 0") {
            print("✅ ПРОЙДЕН: Сброс истории работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Сброс истории НЕ работает")
            print("   После сброса: \(debugAfterReset)")
        }
        
        // СЦЕНАРИЙ 4: Заполнение тестовыми данными
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Заполнение тестовыми данными")
        
        detector.fillWithActiveMinutesForTesting()
        let debugAfterFill = detector.getDebugInfo()
        
        if debugAfterFill.contains("Всего минут: 17") {
            print("✅ ПРОЙДЕН: Заполнение тестовыми данными работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Заполнение тестовыми данными НЕ работает")
            print("   После заполнения: \(debugAfterFill)")
        }
        
        // СЦЕНАРИЙ 5: Проверка условий срабатывания
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Проверка условий срабатывания")
        
        let shouldTrigger = detector.shouldTriggerRestSuggestion()
        
        if shouldTrigger {
            print("✅ ПРОЙДЕН: Условия срабатывания работают")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Условия срабатывания НЕ работают")
            print("   shouldTriggerRestSuggestion: \(shouldTrigger)")
            print("   Debug: \(detector.getDebugInfo())")
        }
        
        // СЦЕНАРИЙ 6: Сброс cooldown
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Сброс cooldown")
        
        detector.resetCooldown()
        
        // Проверяем что метод работает без ошибок
        print("✅ ПРОЙДЕН: Сброс cooldown работает без ошибок")
        passed += 1
        
        // СЦЕНАРИЙ 7: Получение детальной информации
        total += 1
        print("\n📋 СЦЕНАРИЙ 7: Получение детальной информации")
        
        let activityDetails = detector.getActivityLogDetails()
        
        if activityDetails.contains("Лог активности") {
            print("✅ ПРОЙДЕН: Получение детальной информации работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Получение детальной информации НЕ работает")
            print("   Детали: \(activityDetails)")
        }
        
        // СЦЕНАРИЙ 8: Тестовый сценарий
        total += 1
        print("\n📋 СЦЕНАРИЙ 8: Тестовый сценарий")
        
        detector.fillWithTestScenario()
        let debugAfterScenario = detector.getDebugInfo()
        
        if debugAfterScenario.contains("Всего минут:") {
            print("✅ ПРОЙДЕН: Тестовый сценарий работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Тестовый сценарий НЕ работает")
            print("   После сценария: \(debugAfterScenario)")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ Базовая функциональность InformalSessionDetector работает корректно")
            
            // Проверка обнаружения поломок
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В БАЗОВОЙ ФУНКЦИОНАЛЬНОСТИ!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Откройте SimplePomodoroTest/InformalSessionDetector.swift")
        print("2. В методе recordMinuteActivity() найдите строку:")
        print("   minuteActivityLog.append(isActive)")
        print("3. Замените на:")
        print("   // minuteActivityLog.append(isActive)")
        print("4. Запустите этот тест снова")
        print("5. СЦЕНАРИЙ 2 должен провалиться!")
        print("6. Верните код обратно")
        print("\nИЛИ:")
        print("1. В методе resetActivityHistory() найдите:")
        print("   minuteActivityLog.removeAll()")
        print("2. Замените на:")
        print("   // minuteActivityLog.removeAll()")
        print("3. Запустите тест - СЦЕНАРИЙ 3 провалится!")
        print("\nЭто покажет что тест ловит поломки в реальной функциональности.")
        
        exit(0)
    }
}
