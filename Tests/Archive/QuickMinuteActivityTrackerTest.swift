import Foundation

/// БЫСТРЫЙ тест для проверки КРИТИЧЕСКОЙ функциональности MinuteActivityTracker
/// Проверяем только то, что ДЕЙСТВИТЕЛЬНО важно

// Mock UnifiedActivityChecker для контролируемого тестирования
class QuickMockUnifiedActivityChecker {
    private var mockActive = false
    
    func setMockActive(_ active: Bool) {
        mockActive = active
    }
    
    func isUserCurrentlyActive() -> Bool {
        return mockActive
    }
}

// Тестируемая версия MinuteActivityTracker
class QuickTestableMinuteActivityTracker {
    private let activityChecker: QuickMockUnifiedActivityChecker
    private var currentMinuteBands: [Bool] = [false, false, false, false]
    private var currentBand = 0
    private let bandsPerMinute = 4
    private var isTracking = false
    
    init(activityChecker: QuickMockUnifiedActivityChecker) {
        self.activityChecker = activityChecker
    }
    
    func startTracking() {
        isTracking = true
        resetCurrentMinute()
        print("🎯 Отслеживание запущено")
    }
    
    func stopTracking() {
        isTracking = false
        print("🎯 Отслеживание остановлено")
    }
    
    func wasCurrentMinuteActive() -> Bool {
        return currentMinuteBands.contains(true)
    }
    
    func forceCompleteCurrentMinute() -> Bool {
        // Заполняем оставшиеся банды
        while currentBand < bandsPerMinute {
            checkCurrentBandActivity()
            currentBand += 1
        }
        
        let wasActive = wasCurrentMinuteActive()
        resetCurrentMinute()
        return wasActive
    }
    
    private func checkCurrentBandActivity() {
        guard currentBand < bandsPerMinute else { return }
        let isActive = activityChecker.isUserCurrentlyActive()
        currentMinuteBands[currentBand] = isActive
        print("🎯 Банд \(currentBand): активность = \(isActive)")
    }
    
    private func resetCurrentMinute() {
        currentMinuteBands = [false, false, false, false]
        currentBand = 0
    }
    
    // Для тестирования
    func simulateBandCheck() {
        checkCurrentBandActivity()
        currentBand += 1
    }
    
    func getCurrentBandStates() -> [Bool] {
        return currentMinuteBands
    }
    
    func isTrackingActive() -> Bool {
        return isTracking
    }
}

@main
struct QuickMinuteActivityTrackerTest {
    static func main() {
        print("🚀 БЫСТРЫЙ ТЕСТ: Критическая функциональность MinuteActivityTracker")
        print(String(repeating: "=", count: 70))
        
        let mockChecker = QuickMockUnifiedActivityChecker()
        let tracker = QuickTestableMinuteActivityTracker(activityChecker: mockChecker)
        
        var passed = 0
        var total = 0
        
        // ТЕСТ 1: Запуск и остановка отслеживания
        total += 1
        print("\n🧪 ТЕСТ 1: Запуск и остановка отслеживания")
        tracker.startTracking()
        
        if tracker.isTrackingActive() {
            tracker.stopTracking()
            if !tracker.isTrackingActive() {
                passed += 1
                print("✅ ПРОЙДЕН: Запуск и остановка работают")
            } else {
                print("❌ ПРОВАЛЕН: Остановка НЕ работает")
            }
        } else {
            print("❌ ПРОВАЛЕН: Запуск НЕ работает")
        }
        
        // ТЕСТ 2: Обнаружение активности в бандах
        total += 1
        print("\n🧪 ТЕСТ 2: Обнаружение активности в бандах")
        tracker.startTracking()
        
        // Устанавливаем активность и проверяем банд
        mockChecker.setMockActive(true)
        tracker.simulateBandCheck()
        
        let bandStates = tracker.getCurrentBandStates()
        if bandStates[0] == true {
            passed += 1
            print("✅ ПРОЙДЕН: Активность в банде обнаруживается")
        } else {
            print("❌ ПРОВАЛЕН: Активность в банде НЕ обнаруживается")
            print("   Состояние бандов: \(bandStates)")
        }
        
        // ТЕСТ 3: Определение активности минуты
        total += 1
        print("\n🧪 ТЕСТ 3: Определение активности минуты")
        
        let wasActive = tracker.wasCurrentMinuteActive()
        if wasActive {
            passed += 1
            print("✅ ПРОЙДЕН: Минута определяется как активная")
        } else {
            print("❌ ПРОВАЛЕН: Минута НЕ определяется как активная")
        }
        
        // ТЕСТ 4: Принудительное завершение минуты
        total += 1
        print("\n🧪 ТЕСТ 4: Принудительное завершение минуты")
        
        // Устанавливаем неактивность для оставшихся бандов
        mockChecker.setMockActive(false)
        let completedActive = tracker.forceCompleteCurrentMinute()
        
        if completedActive {
            passed += 1
            print("✅ ПРОЙДЕН: Принудительное завершение работает (минута была активной)")
        } else {
            print("❌ ПРОВАЛЕН: Принудительное завершение НЕ работает")
        }
        
        // ТЕСТ 5: Проверка сброса после завершения
        total += 1
        print("\n🧪 ТЕСТ 5: Проверка сброса после завершения")
        
        let wasActiveAfterReset = tracker.wasCurrentMinuteActive()
        if !wasActiveAfterReset {
            passed += 1
            print("✅ ПРОЙДЕН: Сброс после завершения работает")
        } else {
            print("❌ ПРОВАЛЕН: Сброс после завершения НЕ работает")
        }
        
        // ТЕСТ 6: Проверка обнаружения поломки
        total += 1
        print("\n🧪 ТЕСТ 6: Проверка обнаружения поломки")
        
        // Намеренно ломаем: устанавливаем неактивность
        mockChecker.setMockActive(false)
        tracker.simulateBandCheck()
        
        let shouldBeFalse = tracker.wasCurrentMinuteActive()
        if !shouldBeFalse {
            passed += 1
            print("✅ ПРОЙДЕН: Тест корректно обнаруживает отсутствие активности")
        } else {
            print("❌ ПРОВАЛЕН: Тест НЕ обнаруживает проблемы")
        }
        
        tracker.stopTracking()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) тестов пройдено")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
            print("✅ Критическая функциональность MinuteActivityTracker работает корректно")
            exit(0)
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ!")
            print("❌ Требуется доработка MinuteActivityTracker")
            exit(1)
        }
    }
}
