import Foundation

/// БЫСТРЫЙ интеграционный тест для проверки КРИТИЧЕСКОЙ функциональности всей унифицированной системы
/// Проверяем что MinuteActivityTracker и ActivityStateManager работают вместе

// Mock UnifiedActivityChecker
class QuickMockUnifiedActivityChecker {
    private var mockActive = false
    
    func setMockActive(_ active: Bool) {
        mockActive = active
    }
    
    func isUserCurrentlyActive() -> Bool {
        return mockActive
    }
}

// Упрощенная версия MinuteActivityTracker
class QuickMockMinuteActivityTracker {
    private let activityChecker: QuickMockUnifiedActivityChecker
    private var mockActive = false
    private var trackingActive = false
    
    init(activityChecker: QuickMockUnifiedActivityChecker) {
        self.activityChecker = activityChecker
    }
    
    func setMockActive(_ active: Bool) {
        mockActive = active
    }
    
    func startTracking() {
        trackingActive = true
    }
    
    func stopTracking() {
        trackingActive = false
    }
    
    func wasCurrentMinuteActive() -> Bool {
        return mockActive
    }
    
    func isTrackingActive() -> Bool {
        return trackingActive
    }
}

// Упрощенная версия ActivityStateManager
class QuickMockActivityStateManager {
    enum ActivityState {
        case working
        case awayShort
        case awayMedium
        case awayLong
        case awayVeryLong
    }
    
    private let minuteActivityTracker: QuickMockMinuteActivityTracker
    private var currentState: ActivityState = .working
    private var lastActivityTime = Date()
    private var isActive = false
    
    init(minuteActivityTracker: QuickMockMinuteActivityTracker) {
        self.minuteActivityTracker = minuteActivityTracker
    }
    
    func start() {
        isActive = true
        minuteActivityTracker.startTracking()
        currentState = .working
        lastActivityTime = Date()
        print("🎯 Унифицированная система запущена")
    }
    
    func stop() {
        isActive = false
        minuteActivityTracker.stopTracking()
        print("🎯 Унифицированная система остановлена")
    }
    
    func handleMinuteActivity() {
        guard isActive else { return }
        
        let wasActive = minuteActivityTracker.wasCurrentMinuteActive()
        
        if wasActive {
            if currentState != .working {
                let awayTime = Date().timeIntervalSince(lastActivityTime)
                print("🎯 Пользователь вернулся после \(Int(awayTime)) секунд")
            }
            lastActivityTime = Date()
            currentState = .working
        } else {
            let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)
            updateAwayState(timeSinceLastActivity)
        }
    }
    
    private func updateAwayState(_ timeSinceLastActivity: TimeInterval) {
        if timeSinceLastActivity >= 17 * 60 {
            currentState = .awayVeryLong
        } else if timeSinceLastActivity >= 10 * 60 {
            currentState = .awayLong
        } else if timeSinceLastActivity >= 2 * 60 {
            currentState = .awayMedium
        } else if timeSinceLastActivity >= 15 {
            currentState = .awayShort
        }
    }
    
    func getCurrentState() -> ActivityState {
        return currentState
    }
    
    func setLastActivityTime(_ time: Date) {
        lastActivityTime = time
    }
    
    func isManagerActive() -> Bool {
        return isActive
    }
}

@main
struct QuickUnifiedSystemTest {
    static func main() {
        print("🚀 БЫСТРЫЙ ИНТЕГРАЦИОННЫЙ ТЕСТ: Унифицированная система активности")
        print(String(repeating: "=", count: 70))
        
        let mockChecker = QuickMockUnifiedActivityChecker()
        let mockTracker = QuickMockMinuteActivityTracker(activityChecker: mockChecker)
        let stateManager = QuickMockActivityStateManager(minuteActivityTracker: mockTracker)
        
        var passed = 0
        var total = 0
        
        // ТЕСТ 1: Запуск всей системы
        total += 1
        print("\n🧪 ТЕСТ 1: Запуск всей системы")
        stateManager.start()
        
        if stateManager.isManagerActive() && mockTracker.isTrackingActive() {
            passed += 1
            print("✅ ПРОЙДЕН: Вся система запускается")
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ запускается")
        }
        
        // ТЕСТ 2: Обработка активности через всю цепочку
        total += 1
        print("\n🧪 ТЕСТ 2: Обработка активности через всю цепочку")
        
        // Устанавливаем активность на всех уровнях
        mockChecker.setMockActive(true)
        mockTracker.setMockActive(true)
        stateManager.handleMinuteActivity()
        
        if stateManager.getCurrentState() == .working {
            passed += 1
            print("✅ ПРОЙДЕН: Активность обрабатывается через всю цепочку")
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ обрабатывается")
            print("   Состояние: \(stateManager.getCurrentState())")
        }
        
        // ТЕСТ 3: Переход в состояние отсутствия
        total += 1
        print("\n🧪 ТЕСТ 3: Переход в состояние отсутствия")
        
        // Устанавливаем время в прошлое и неактивность
        let fiveMinutesAgo = Date().addingTimeInterval(-5 * 60)
        stateManager.setLastActivityTime(fiveMinutesAgo)
        
        mockChecker.setMockActive(false)
        mockTracker.setMockActive(false)
        stateManager.handleMinuteActivity()
        
        if stateManager.getCurrentState() == .awayMedium {
            passed += 1
            print("✅ ПРОЙДЕН: Переход в состояние отсутствия работает")
        } else {
            print("❌ ПРОВАЛЕН: Переход в состояние отсутствия НЕ работает")
            print("   Ожидалось: awayMedium, получено: \(stateManager.getCurrentState())")
        }
        
        // ТЕСТ 4: Возврат пользователя через всю цепочку
        total += 1
        print("\n🧪 ТЕСТ 4: Возврат пользователя через всю цепочку")
        
        mockChecker.setMockActive(true)
        mockTracker.setMockActive(true)
        stateManager.handleMinuteActivity()
        
        if stateManager.getCurrentState() == .working {
            passed += 1
            print("✅ ПРОЙДЕН: Возврат пользователя работает через всю цепочку")
        } else {
            print("❌ ПРОВАЛЕН: Возврат пользователя НЕ работает")
            print("   Ожидалось: working, получено: \(stateManager.getCurrentState())")
        }
        
        // ТЕСТ 5: Остановка всей системы
        total += 1
        print("\n🧪 ТЕСТ 5: Остановка всей системы")
        
        stateManager.stop()
        
        if !stateManager.isManagerActive() && !mockTracker.isTrackingActive() {
            passed += 1
            print("✅ ПРОЙДЕН: Вся система останавливается")
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ останавливается")
        }
        
        // ТЕСТ 6: Проверка обнаружения поломки интеграции
        total += 1
        print("\n🧪 ТЕСТ 6: Проверка обнаружения поломки интеграции")
        
        stateManager.start()
        
        // Намеренно ломаем: устанавливаем разные состояния на разных уровнях
        mockChecker.setMockActive(true)  // Checker говорит активен
        mockTracker.setMockActive(false) // Tracker говорит неактивен
        
        stateManager.handleMinuteActivity()
        
        // Должно использоваться состояние Tracker'а (false)
        if stateManager.getCurrentState() != .working {
            passed += 1
            print("✅ ПРОЙДЕН: Тест обнаруживает проблемы интеграции")
        } else {
            print("❌ ПРОВАЛЕН: Тест НЕ обнаруживает проблемы интеграции")
        }
        
        stateManager.stop()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) тестов пройдено")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
            print("✅ Интеграция унифицированной системы работает корректно")
            exit(0)
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ!")
            print("❌ Требуется доработка интеграции")
            exit(1)
        }
    }
}
