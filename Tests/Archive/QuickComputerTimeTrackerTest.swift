import Foundation

/// БЫСТРЫЙ тест для проверки КРИТИЧЕСКОЙ функциональности
/// Проверяем только то, что ДЕЙСТВИТЕЛЬНО важно

// Простейший Mock для StatisticsManager
class QuickMockStatisticsManager {
    var recordedActiveMinutes = 0
    
    func recordActiveMinute() {
        recordedActiveMinutes += 1
        print("📊 Записана активная минута (всего: \(recordedActiveMinutes))")
    }
}

// Простейший Mock для MinuteActivityTracker  
class QuickMockMinuteActivityTracker {
    private var mockActive = false
    private var trackingActive = false
    var onMinuteCompleted: ((Bool) -> Void)?
    
    func setMockActive(_ active: Bool) {
        mockActive = active
    }
    
    func startTracking() {
        trackingActive = true
        print("🎭 Отслеживание запущено")
    }
    
    func stopTracking() {
        trackingActive = false
        print("🎭 Отслеживание остановлено")
    }
    
    func wasCurrentMinuteActive() -> Bool {
        return mockActive
    }
    
    func forceCompleteCurrentMinute() -> Bool {
        return mockActive
    }
    
    func isTrackingActive() -> Bool {
        return trackingActive
    }
}

// Упрощенная версия ComputerTimeTracker для тестирования
class TestableComputerTimeTracker {
    private let statisticsManager: QuickMockStatisticsManager
    private let minuteActivityTracker: QuickMockMinuteActivityTracker
    private var useNewActivitySystem = true
    private var isTracking = false
    
    init(statisticsManager: QuickMockStatisticsManager, minuteActivityTracker: QuickMockMinuteActivityTracker) {
        self.statisticsManager = statisticsManager
        self.minuteActivityTracker = minuteActivityTracker
    }
    
    func setUseNewActivitySystem(_ useNew: Bool) {
        let wasTracking = isTracking
        if wasTracking { stopTracking() }
        
        useNewActivitySystem = useNew
        print("💻 Переключено на \(useNew ? "новую" : "старую") систему")
        
        if wasTracking { startTracking() }
    }
    
    func startTracking() {
        guard !isTracking else { return }
        isTracking = true
        
        if useNewActivitySystem {
            minuteActivityTracker.startTracking()
            print("💻 Запущена новая система")
        } else {
            print("💻 Запущена старая система")
        }
    }
    
    func stopTracking() {
        guard isTracking else { return }
        isTracking = false
        
        if useNewActivitySystem {
            minuteActivityTracker.stopTracking()
        }
        print("💻 Отслеживание остановлено")
    }
    
    func isUserCurrentlyActive() -> Bool {
        if useNewActivitySystem {
            let result = minuteActivityTracker.wasCurrentMinuteActive()
            print("💻 Новая система: активность = \(result)")
            return result
        } else {
            print("💻 Старая система: активность = false (мок)")
            return false
        }
    }
}

@main
struct QuickTest {
    static func main() {
        print("🚀 БЫСТРЫЙ ТЕСТ: Критическая функциональность ComputerTimeTracker")
        print(String(repeating: "=", count: 70))
        
        let mockStats = QuickMockStatisticsManager()
        let mockTracker = QuickMockMinuteActivityTracker()
        let computerTracker = TestableComputerTimeTracker(
            statisticsManager: mockStats, 
            minuteActivityTracker: mockTracker
        )
        
        var passed = 0
        var total = 0
        
        // ТЕСТ 1: Запуск новой системы
        total += 1
        print("\n🧪 ТЕСТ 1: Запуск новой системы")
        computerTracker.setUseNewActivitySystem(true)
        computerTracker.startTracking()
        
        if mockTracker.isTrackingActive() {
            passed += 1
            print("✅ ПРОЙДЕН: Новая система запускается")
        } else {
            print("❌ ПРОВАЛЕН: Новая система НЕ запускается")
        }
        
        // ТЕСТ 2: Определение активности
        total += 1
        print("\n🧪 ТЕСТ 2: Определение активности")
        mockTracker.setMockActive(true)
        let isActive = computerTracker.isUserCurrentlyActive()
        
        if isActive {
            passed += 1
            print("✅ ПРОЙДЕН: Активность определяется корректно")
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ определяется")
        }
        
        // ТЕСТ 3: Переключение систем
        total += 1
        print("\n🧪 ТЕСТ 3: Переключение на старую систему")
        computerTracker.setUseNewActivitySystem(false)
        
        if !mockTracker.isTrackingActive() {
            passed += 1
            print("✅ ПРОЙДЕН: Переключение работает")
        } else {
            print("❌ ПРОВАЛЕН: Переключение НЕ работает")
        }
        
        // ТЕСТ 4: Проверка поломки (намеренно ломаем)
        total += 1
        print("\n🧪 ТЕСТ 4: Проверка обнаружения поломки")
        computerTracker.setUseNewActivitySystem(true)
        computerTracker.startTracking()
        mockTracker.setMockActive(false)  // Устанавливаем неактивность
        
        let shouldBeFalse = computerTracker.isUserCurrentlyActive()
        if !shouldBeFalse {
            passed += 1
            print("✅ ПРОЙДЕН: Тест корректно обнаруживает отсутствие активности")
        } else {
            print("❌ ПРОВАЛЕН: Тест НЕ обнаруживает проблемы")
        }
        
        computerTracker.stopTracking()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) тестов пройдено")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
            print("✅ Критическая функциональность работает корректно")
            exit(0)
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ!")
            print("❌ Требуется доработка интеграции")
            exit(1)
        }
    }
}
