#!/usr/bin/env swift

import Foundation

// Простая функция assert для тестов
func testAssert(_ condition: Bool, _ message: String) {
    if !condition {
        print("❌ ОШИБКА: \(message)")
        exit(1)
    }
}

print("🧪 Unified Activity System Integration Test")
print("==========================================")

// MARK: - Интеграционный тест всей системы

func testUnifiedActivitySystemIntegration() {
    print("\n🧪 Интеграционный тест: Полный сценарий работы системы")
    
    // Создаем компоненты системы
    let minuteTracker = TestableMinuteActivityTracker()
    let stateManager = TestableActivityStateManager()
    
    // Переменные для отслеживания событий
    var minutesCompleted: [Bool] = []
    var stateChanges: [(TestableActivityStateManager.ActivityState, TestableActivityStateManager.ActivityState)] = []
    var counterStops = 0
    var counterResumes = 0
    var returnMessages: [(TestableActivityStateManager.ReturnMessage, TimeInterval)] = []
    
    // Настраиваем колбэки
    minuteTracker.onMinuteCompleted = { isActive in
        minutesCompleted.append(isActive)
        print("📊 Минута завершена: \(isActive ? "АКТИВНА" : "НЕАКТИВНА")")
        // Передаем событие в StateManager
        stateManager.handleMinuteActivity(isActive)
    }
    
    stateManager.onStateChanged = { from, to in
        stateChanges.append((from, to))
        print("📊 Переход состояния: \(from) → \(to)")
    }
    
    stateManager.onStopCounters = {
        counterStops += 1
        print("📊 Счетчики остановлены (всего: \(counterStops))")
    }
    
    stateManager.onResumeCounters = {
        counterResumes += 1
        print("📊 Счетчики возобновлены (всего: \(counterResumes))")
    }
    
    stateManager.onUserReturned = { message, duration in
        returnMessages.append((message, duration))
        print("📊 Пользователь вернулся: \(message), длительность: \(Int(duration/60)) мин")
    }
    
    print("\n🎯 Сценарий 1: Нормальная работа с активностью")
    
    // Запускаем систему
    minuteTracker.startTracking()
    stateManager.start()
    
    // Симулируем 3 активные минуты
    minuteTracker.setActivityChecker(result: true)
    for i in 1...3 {
        minuteTracker.simulateBandCompletion() // Банд 0
        minuteTracker.simulateBandCompletion() // Банд 1
        minuteTracker.simulateBandCompletion() // Банд 2
        minuteTracker.simulateBandCompletion() // Банд 3 (завершает минуту)
        print("🎯 Завершена активная минута \(i)")
    }
    
    // Проверяем результаты
    testAssert(minutesCompleted.count == 3, "Должно быть завершено 3 минуты")
    testAssert(minutesCompleted.allSatisfy { $0 }, "Все минуты должны быть активными")
    testAssert(stateManager.getCurrentStateInfo().state == .working, "Состояние должно быть 'working'")
    testAssert(counterStops == 0, "Счетчики не должны останавливаться при активности")
    
    print("✅ Сценарий 1 прошел успешно")
    
    print("\n🎯 Сценарий 2: Короткое отсутствие (1 минута)")
    
    // Сбрасываем счетчики
    minutesCompleted.removeAll()
    stateChanges.removeAll()
    
    // Устанавливаем время последней активности 1 минуту назад
    stateManager.setLastActivityTime(Date().addingTimeInterval(-60))
    
    // Симулируем неактивную минуту
    minuteTracker.setActivityChecker(result: false)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Проверяем что счетчики остановлены и состояние изменилось
    testAssert(counterStops == 1, "Счетчики должны быть остановлены")
    testAssert(stateChanges.contains { $0.1 == .awayShort }, "Должен быть переход в awayShort")
    
    // Возвращаем активность
    minuteTracker.setActivityChecker(result: true)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Проверяем возврат
    testAssert(counterResumes == 1, "Счетчики должны быть возобновлены")
    testAssert(returnMessages.count == 1, "Должно быть сообщение о возвращении")
    testAssert(returnMessages.last?.0 == .resumeSilently, "Короткое отсутствие должно дать resumeSilently")
    
    print("✅ Сценарий 2 прошел успешно")
    
    print("\n🎯 Сценарий 3: Среднее отсутствие (5 минут)")
    
    // Сбрасываем счетчики
    returnMessages.removeAll()
    
    // Устанавливаем время последней активности 5 минут назад
    stateManager.setLastActivityTime(Date().addingTimeInterval(-5 * 60))
    
    // Симулируем неактивную минуту
    minuteTracker.setActivityChecker(result: false)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Возвращаем активность
    minuteTracker.setActivityChecker(result: true)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Проверяем сообщение
    testAssert(returnMessages.count == 1, "Должно быть сообщение о возвращении")
    testAssert(returnMessages.last?.0 == .partialRest, "5 минут должно дать partialRest")
    
    print("✅ Сценарий 3 прошел успешно")
    
    print("\n🎯 Сценарий 4: Формальный отдых")
    
    // Сбрасываем счетчики
    stateChanges.removeAll()
    
    // Начинаем формальный отдых
    stateManager.startFormalRest()
    testAssert(stateChanges.last?.1 == .formalRest, "Должен перейти в формальный отдых")
    
    // Во время формального отдыха симулируем неактивность
    let stateCountBefore = stateChanges.count
    stateManager.setLastActivityTime(Date().addingTimeInterval(-10 * 60))
    
    minuteTracker.setActivityChecker(result: false)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Состояние не должно измениться
    testAssert(stateChanges.count == stateCountBefore, "Во время формального отдыха состояние не должно меняться")
    testAssert(stateManager.getCurrentStateInfo().state == .formalRest, "Должно оставаться в formalRest")
    
    // Заканчиваем формальный отдых
    stateManager.endFormalRest()
    testAssert(stateChanges.last?.1 == .working, "После формального отдыха должен вернуться к working")
    
    print("✅ Сценарий 4 прошел успешно")
    
    print("\n🎯 Сценарий 5: Очень долгое отсутствие (20 минут)")
    
    // Сбрасываем счетчики
    returnMessages.removeAll()
    
    // Устанавливаем время последней активности 20 минут назад
    stateManager.setLastActivityTime(Date().addingTimeInterval(-20 * 60))
    
    // Симулируем неактивную минуту
    minuteTracker.setActivityChecker(result: false)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Возвращаем активность
    minuteTracker.setActivityChecker(result: true)
    for _ in 1...4 {
        minuteTracker.simulateBandCompletion()
    }
    
    // Проверяем сообщение
    testAssert(returnMessages.count == 1, "Должно быть сообщение о возвращении")
    testAssert(returnMessages.last?.0 == .fullRest, "20 минут должно дать fullRest")
    
    print("✅ Сценарий 5 прошел успешно")
    
    // Останавливаем систему
    minuteTracker.stopTracking()
    stateManager.stop()
    
    print("\n🎉 Интеграционный тест прошел успешно!")
    print("=====================================")
    
    // Итоговая статистика
    print("\n📊 Итоговая статистика:")
    print("- Завершено минут: \(minutesCompleted.count)")
    print("- Переходов состояний: \(stateChanges.count)")
    print("- Остановок счетчиков: \(counterStops)")
    print("- Возобновлений счетчиков: \(counterResumes)")
    print("- Сообщений о возвращении: \(returnMessages.count)")
}

// MARK: - Тестовые классы (копии из предыдущих тестов)

class MockUnifiedActivityChecker {
    static let shared = MockUnifiedActivityChecker()
    private var forcedResult: Bool?
    
    func isUserCurrentlyActive() -> Bool {
        return forcedResult ?? false
    }
    
    func setForcedResult(_ result: Bool?) {
        forcedResult = result
    }
}

class TestableMinuteActivityTracker {
    private let bandsPerMinute = 4
    private let bandDurationSeconds: TimeInterval = 15.0
    private let activityThreshold: TimeInterval = 15.0
    
    private var currentBand = 0
    private var currentBandStartTime = Date()
    private var currentMinuteBands: [Bool] = [false, false, false, false]
    private var bandTimer: Timer?
    private var isActive = false
    
    var onMinuteCompleted: ((Bool) -> Void)?
    private let activityChecker = MockUnifiedActivityChecker.shared
    
    func startTracking() {
        guard !isActive else { return }
        isActive = true
        resetCurrentMinute()
        print("🎯 TestableMinuteActivityTracker: Запущено отслеживание")
    }
    
    func stopTracking() {
        guard isActive else { return }
        isActive = false
        bandTimer?.invalidate()
        bandTimer = nil
        print("🎯 TestableMinuteActivityTracker: Остановлено отслеживание")
    }
    
    func simulateBandCompletion() {
        checkCurrentBandActivity()
        currentBand += 1
        currentBandStartTime = Date()
        
        if currentBand >= bandsPerMinute {
            completeCurrentMinute()
        }
    }
    
    func setActivityChecker(result: Bool?) {
        activityChecker.setForcedResult(result)
    }
    
    private func checkCurrentBandActivity() {
        guard currentBand < bandsPerMinute else { return }
        let isActive = activityChecker.isUserCurrentlyActive()
        currentMinuteBands[currentBand] = isActive
    }
    
    private func completeCurrentMinute() {
        let isMinuteActive = currentMinuteBands.contains(true)
        onMinuteCompleted?(isMinuteActive)
        resetCurrentMinute()
    }
    
    private func resetCurrentMinute() {
        currentBand = 0
        currentBandStartTime = Date()
        currentMinuteBands = [false, false, false, false]
    }
    
    deinit {
        stopTracking()
    }
}

class TestableActivityStateManager {
    enum ActivityState {
        case working, awayShort, awayMedium, awayLong, awayVeryLong, formalRest
    }
    
    enum ReturnMessage {
        case resumeSilently, partialRest, chooseRestOrWork, fullRest
    }
    
    private let shortAwayThreshold: TimeInterval = 2 * 60
    private let mediumAwayThreshold: TimeInterval = 10 * 60
    private let longAwayThreshold: TimeInterval = 17 * 60
    private let inactivityStopThreshold: TimeInterval = 15.0
    
    private(set) var currentState: ActivityState = .working
    private var stateStartTime = Date()
    private var lastActivityTime = Date()
    private var isActive = false
    private var countersStoppedDueToInactivity = false
    
    var onStateChanged: ((ActivityState, ActivityState) -> Void)?
    var onStopCounters: (() -> Void)?
    var onResumeCounters: (() -> Void)?
    var onUserReturned: ((ReturnMessage, TimeInterval) -> Void)?
    
    func start() {
        guard !isActive else { return }
        isActive = true
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false
        print("🎯 TestableActivityStateManager: Запущен")
    }
    
    func stop() {
        guard isActive else { return }
        isActive = false
        print("🎯 TestableActivityStateManager: Остановлен")
    }
    
    func startFormalRest() {
        let previousState = currentState
        currentState = .formalRest
        stateStartTime = Date()
        onStateChanged?(previousState, currentState)
    }
    
    func endFormalRest() {
        let previousState = currentState
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false
        onResumeCounters?()
        onStateChanged?(previousState, currentState)
    }
    
    func getCurrentStateInfo() -> (state: ActivityState, timeInState: TimeInterval) {
        let timeInState = Date().timeIntervalSince(stateStartTime)
        return (currentState, timeInState)
    }
    
    func setLastActivityTime(_ time: Date) {
        lastActivityTime = time
    }
    
    // Симуляция обработки минуты (вызывается извне)
    func handleMinuteActivity(_ isActive: Bool) {
        if isActive {
            handleUserActivity()
        } else {
            handleUserInactivity()
        }
    }
    
    private func handleUserActivity() {
        if countersStoppedDueToInactivity {
            handleUserReturn()
        }
        
        lastActivityTime = Date()
        
        if currentState != .formalRest {
            transitionToState(.working)
        }
        
        if countersStoppedDueToInactivity {
            countersStoppedDueToInactivity = false
            onResumeCounters?()
        }
    }
    
    private func handleUserInactivity() {
        let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)
        
        if timeSinceLastActivity >= inactivityStopThreshold && !countersStoppedDueToInactivity {
            countersStoppedDueToInactivity = true
            onStopCounters?()
        }
        
        if currentState != .formalRest {
            let newState = determineAwayState(timeSinceLastActivity)
            transitionToState(newState)
        }
    }
    
    private func determineAwayState(_ timeAway: TimeInterval) -> ActivityState {
        if timeAway < shortAwayThreshold {
            return .awayShort
        } else if timeAway < mediumAwayThreshold {
            return .awayMedium
        } else if timeAway < longAwayThreshold {
            return .awayLong
        } else {
            return .awayVeryLong
        }
    }
    
    private func transitionToState(_ newState: ActivityState) {
        guard newState != currentState else { return }
        
        let previousState = currentState
        currentState = newState
        stateStartTime = Date()
        onStateChanged?(previousState, newState)
    }
    
    private func handleUserReturn() {
        let awayTime = Date().timeIntervalSince(lastActivityTime)
        let message = determineReturnMessage(awayTime)
        onUserReturned?(message, awayTime)
    }
    
    private func determineReturnMessage(_ awayTime: TimeInterval) -> ReturnMessage {
        if awayTime < shortAwayThreshold {
            return .resumeSilently
        } else if awayTime < mediumAwayThreshold {
            return .partialRest
        } else if awayTime < longAwayThreshold {
            return .chooseRestOrWork
        } else {
            return .fullRest
        }
    }
    
    deinit {
        stop()
    }
}

// Запускаем интеграционный тест
testUnifiedActivitySystemIntegration()
