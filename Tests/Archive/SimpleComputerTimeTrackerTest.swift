import Foundation

/// ПРОСТОЙ тест для проверки ГЛАВНОГО: работает ли интеграция ComputerTimeTracker с новой системой
/// Фокус на критической функциональности, а не на деталях реализации

// Mock StatisticsManager для изоляции тестирования
class MockStatisticsManager: StatisticsManager {
    var recordedActiveMinutes = 0
    
    override func recordActiveMinute() {
        recordedActiveMinutes += 1
        print("📊 Mock: Записана активная минута (всего: \(recordedActiveMinutes))")
    }
}

// Mock MinuteActivityTracker для контролируемого тестирования
class MockMinuteActivityTracker: MinuteActivityTracker {
    private var mockActive = false
    private var trackingActive = false
    
    func setMockActive(_ active: Bool) {
        mockActive = active
        print("🎭 Mock: Установлена активность = \(active)")
    }
    
    override func startTracking() {
        trackingActive = true
        print("🎭 Mock: Отслеживание запущено")
    }
    
    override func stopTracking() {
        trackingActive = false
        print("🎭 Mock: Отслеживание остановлено")
    }
    
    override func wasCurrentMinuteActive() -> Bool {
        print("🎭 Mock: Проверка активности = \(mockActive)")
        return mockActive
    }
    
    override func forceCompleteCurrentMinute() -> Bool {
        print("🎭 Mock: Принудительное завершение минуты = \(mockActive)")
        return mockActive
    }
    
    func isTrackingActive() -> Bool {
        return trackingActive
    }
}

/// ГЛАВНЫЙ ТЕСТ: Проверяем что интеграция работает
func testMainFunctionality() -> Bool {
    print("\n🧪 ТЕСТ: Главная функциональность ComputerTimeTracker")
    print(String(repeating: "=", count: 60))
    
    let mockStats = MockStatisticsManager()
    let mockTracker = MockMinuteActivityTracker()
    let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
    
    var testsPassed = 0
    var testsTotal = 0
    
    // ТЕСТ 1: Запуск новой системы
    testsTotal += 1
    computerTracker.setUseNewActivitySystem(true)
    computerTracker.startTracking()
    
    if mockTracker.isTrackingActive() {
        testsPassed += 1
        print("✅ ТЕСТ 1: Новая система запускается")
    } else {
        print("❌ ТЕСТ 1: Новая система НЕ запускается")
    }
    
    // ТЕСТ 2: Проверка активности через новую систему
    testsTotal += 1
    mockTracker.setMockActive(true)
    let isActive = computerTracker.isUserCurrentlyActive()
    
    if isActive {
        testsPassed += 1
        print("✅ ТЕСТ 2: Новая система определяет активность")
    } else {
        print("❌ ТЕСТ 2: Новая система НЕ определяет активность")
    }
    
    // ТЕСТ 3: Переключение на старую систему
    testsTotal += 1
    computerTracker.setUseNewActivitySystem(false)
    
    if !mockTracker.isTrackingActive() {
        testsPassed += 1
        print("✅ ТЕСТ 3: Переключение на старую систему работает")
    } else {
        print("❌ ТЕСТ 3: Переключение на старую систему НЕ работает")
    }
    
    computerTracker.stopTracking()
    
    print(String(repeating: "=", count: 60))
    print("📊 РЕЗУЛЬТАТ: \(testsPassed)/\(testsTotal) тестов пройдено")
    
    if testsPassed == testsTotal {
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        return true
    } else {
        print("⚠️  ЕСТЬ ПРОБЛЕМЫ!")
        return false
    }
}

/// ТЕСТ ПОЛОМКИ: Проверяем что тест ловит ошибки
func testBreakageDetection() -> Bool {
    print("\n🔨 ТЕСТ ПОЛОМКИ: Проверяем что тест ловит ошибки")
    print(String(repeating: "=", count: 60))
    
    let mockStats = MockStatisticsManager()
    let mockTracker = MockMinuteActivityTracker()
    let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
    
    // Намеренно ломаем: устанавливаем активность false, но ожидаем true
    computerTracker.setUseNewActivitySystem(true)
    computerTracker.startTracking()
    mockTracker.setMockActive(false)  // ЛОМАЕМ!
    
    let isActive = computerTracker.isUserCurrentlyActive()
    
    computerTracker.stopTracking()
    
    if !isActive {
        print("✅ ТЕСТ ПОЛОМКИ: Тест правильно ловит ошибки (активность = false)")
        return true
    } else {
        print("❌ ТЕСТ ПОЛОМКИ: Тест НЕ ловит ошибки!")
        return false
    }
}

// ГЛАВНАЯ СТРУКТУРА ДЛЯ ЗАПУСКА ТЕСТОВ
struct TestRunner {
    static func run() {
        print("🚀 Запуск простых тестов ComputerTimeTracker")

        let mainTestPassed = testMainFunctionality()
        let breakageTestPassed = testBreakageDetection()

        if mainTestPassed && breakageTestPassed {
            print("\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Интеграция работает корректно.")
            exit(0)
        } else {
            print("\n⚠️  ЕСТЬ ПРОБЛЕМЫ! Требуется доработка.")
            exit(1)
        }
    }
}

// ЗАПУСК через @main
@main
struct Main {
    static func main() {
        TestRunner.run()
    }
}
