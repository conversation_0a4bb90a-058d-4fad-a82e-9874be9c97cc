import Foundation

/// ТЕСТ РЕАЛЬНЫХ СЦЕНАРИЕВ ИСПОЛЬЗОВАНИЯ
/// Проверяем что система работает в реальных условиях

@main
struct RealUseCaseTest {
    static func main() {
        print("🚀 ТЕСТ РЕАЛЬНЫХ СЦЕНАРИЕВ ИСПОЛЬЗОВАНИЯ")
        print(String(repeating: "=", count: 60))
        
        let tracker = MinuteActivityTracker()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Пользователь начинает работу
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Пользователь начинает работу")
        
        tracker.startTracking()
        
        // Проверяем что трекер запустился
        let (currentBand, bands, isActive) = tracker.getCurrentBandsState()
        
        if isActive {
            print("✅ ПРОЙДЕН: Трекер запускается при начале работы")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Трекер НЕ запускается")
        }
        
        // СЦЕНАРИЙ 2: Симуляция активности
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Симуляция активности в банде")
        
        // Используем метод для тестирования
        tracker.simulateActivityInCurrentBand()
        
        let (_, bandsAfterActivity, _) = tracker.getCurrentBandsState()
        
        if bandsAfterActivity[0] == true {
            print("✅ ПРОЙДЕН: Активность корректно записывается в банд")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ записывается")
            print("   Банды: \(bandsAfterActivity)")
        }
        
        // СЦЕНАРИЙ 3: Проверка активности минуты после симуляции
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Проверка активности минуты")
        
        let wasActive = tracker.wasCurrentMinuteActive()
        
        if wasActive {
            print("✅ ПРОЙДЕН: Минута определяется как активная после активности")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Минута НЕ определяется как активная")
            print("   Результат wasCurrentMinuteActive: \(wasActive)")
            print("   Банды: \(bandsAfterActivity)")
        }
        
        // СЦЕНАРИЙ 4: Принудительное завершение с активностью
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Принудительное завершение с активностью")
        
        let completedWithActivity = tracker.forceCompleteCurrentMinute()
        
        if completedWithActivity {
            print("✅ ПРОЙДЕН: Принудительное завершение возвращает true при активности")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Принудительное завершение возвращает false при активности")
        }
        
        // СЦЕНАРИЙ 5: Новая минута после сброса
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Новая минута после сброса")
        
        let (_, bandsAfterReset, _) = tracker.getCurrentBandsState()
        let wasActiveAfterReset = tracker.wasCurrentMinuteActive()
        
        if bandsAfterReset == [false, false, false, false] && !wasActiveAfterReset {
            print("✅ ПРОЙДЕН: Новая минута начинается с чистого состояния")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Состояние НЕ сбрасывается")
            print("   Банды после сброса: \(bandsAfterReset)")
            print("   Активность после сброса: \(wasActiveAfterReset)")
        }
        
        // СЦЕНАРИЙ 6: Остановка трекера
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Остановка трекера")
        
        tracker.stopTracking()
        
        let (_, _, isActiveAfterStop) = tracker.getCurrentBandsState()
        
        if !isActiveAfterStop {
            print("✅ ПРОЙДЕН: Трекер корректно останавливается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Трекер НЕ останавливается")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ Система работает корректно в реальных условиях")
            
            // Теперь проверим что тест ловит поломки
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            print("   Сейчас мы сломаем код и проверим что тест это поймает...")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В РЕАЛЬНЫХ СЦЕНАРИЯХ!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("\n🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Откройте SimplePomodoroTest/MinuteActivityTracker.swift")
        print("2. В методе wasCurrentMinuteActive() строка 78:")
        print("   Замените: return hasActivity")
        print("   На:       return false")
        print("3. Запустите этот тест снова")
        print("4. СЦЕНАРИЙ 3 должен провалиться!")
        print("5. Верните код обратно")
        print("\nЭто покажет что тест действительно ловит поломки в реальном коде.")
        
        exit(0)
    }
}
