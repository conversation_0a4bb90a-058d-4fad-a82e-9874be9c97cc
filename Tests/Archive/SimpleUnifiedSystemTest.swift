#!/usr/bin/env swift

import Foundation

// Простая реализация для тестирования (без полного импорта проекта)
protocol SimpleUnifiedSystemDelegate: AnyObject {
    func showEscalationReminder(minutes: Int, level: Int, for intervalType: String)
    func updateStatusBar(overtimeMinutes: Int, totalMinutes: Int, seconds: Int)
    func recordIntervalStatistics(duration: TimeInterval, intervalType: String)
}

// Упрощенная версия SimpleUnifiedSystem для тестирования логики
class SimpleUnifiedSystem {
    static let shared = SimpleUnifiedSystem()

    weak var delegate: SimpleUnifiedSystemDelegate?
    private var timer: Timer?
    private var startTime: Date?
    private var intervalDuration: TimeInterval = 0
    private var currentLevel = -1

    func startSimpleEscalation(for intervalType: String, isTest: Bool, intervalDuration: TimeInterval) {
        self.intervalDuration = intervalDuration
        self.startTime = Date()
        self.currentLevel = -1

        // Сразу обновляем статус-бар
        let initialMinutes = Int(intervalDuration / 60)
        delegate?.updateStatusBar(overtimeMinutes: 0, totalMinutes: initialMinutes, seconds: 0)

        // Запускаем таймер (в тестовом режиме ускоренный)
        let interval: TimeInterval = isTest ? 1.0 : 1.0
        timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.checkEscalation(isTest: isTest, intervalType: intervalType)
        }
    }

    func stopEscalation() {
        timer?.invalidate()
        timer = nil
        startTime = nil
    }

    private func checkEscalation(isTest: Bool, intervalType: String) {
        guard let startTime = startTime else { return }

        let elapsed = Date().timeIntervalSince(startTime)
        let elapsedMinutes = Int(elapsed / 60)
        let elapsedSeconds = Int(elapsed.truncatingRemainder(dividingBy: 60))

        // В тестовом режиме ускоряем эскалацию (1 секунда = 1 минута)
        let escalationMinutes = isTest ? Int(elapsed) : elapsedMinutes

        // Простая логика уровней: 0-1 мин = уровень 0, 1-3 мин = уровень 1, и т.д.
        let newLevel: Int
        if escalationMinutes < 1 { newLevel = 0 }
        else if escalationMinutes < 3 { newLevel = 1 }
        else if escalationMinutes < 5 { newLevel = 2 }
        else { newLevel = 3 }

        // Показываем напоминание только при изменении уровня
        if newLevel != currentLevel {
            currentLevel = newLevel

            // Для неформальных интервалов пропускаем уровень 0
            if !(intervalType == "informal" && newLevel == 0) {
                delegate?.showEscalationReminder(minutes: escalationMinutes, level: newLevel, for: intervalType)
            }
        }

        // Обновляем статус-бар
        let totalMinutes = Int(intervalDuration / 60) + elapsedMinutes
        delegate?.updateStatusBar(overtimeMinutes: elapsedMinutes, totalMinutes: totalMinutes, seconds: elapsedSeconds)
    }
}

/// Тесты для SimpleUnifiedSystem - унифицированной системы напоминаний
/// Проверяет корректность работы эскалации для формальных и неформальных интервалов
class SimpleUnifiedSystemTest {
    
    // MARK: - Test Infrastructure
    
    private var testSystem: SimpleUnifiedSystem!
    private var testDelegate: TestDelegate!
    
    /// Настройка тестового окружения
    func setUp() {
        testSystem = SimpleUnifiedSystem()
        testDelegate = TestDelegate()
        testSystem.delegate = testDelegate
        print("🧪 SimpleUnifiedSystemTest: Тестовое окружение настроено")
    }
    
    /// Очистка после тестов
    func tearDown() {
        testSystem.stopEscalation()
        testSystem = nil
        testDelegate = nil
        print("🧪 SimpleUnifiedSystemTest: Тестовое окружение очищено")
    }
    
    // MARK: - Core Functionality Tests
    
    /// Тест 1: Базовый запуск и остановка системы
    func testBasicStartStop() {
        print("\n🧪 Тест 1: Базовый запуск и остановка")
        
        // Запускаем систему
        testSystem.startSimpleEscalation(for: "formal", isTest: true, intervalDuration: 52 * 60)
        
        // Проверяем что система запустилась
        assert(testDelegate.statusBarUpdateCount > 0, "Статус-бар должен обновиться при запуске")
        
        // Останавливаем систему
        testSystem.stopEscalation()
        
        // Проверяем что система остановилась
        let updateCountAfterStop = testDelegate.statusBarUpdateCount
        
        // Ждем немного и проверяем что обновления прекратились
        Thread.sleep(forTimeInterval: 2.0)
        assert(testDelegate.statusBarUpdateCount == updateCountAfterStop, "Обновления должны прекратиться после остановки")
        
        print("✅ Тест 1 пройден: Система корректно запускается и останавливается")
    }
    
    /// Тест 2: Правильность расчета времени для формальных интервалов
    func testFormalIntervalTiming() {
        print("\n🧪 Тест 2: Расчет времени для формальных интервалов")
        
        let intervalDuration: TimeInterval = 25 * 60  // 25 минут
        testSystem.startSimpleEscalation(for: "formal", isTest: true, intervalDuration: intervalDuration)
        
        // Ждем первое обновление
        Thread.sleep(forTimeInterval: 1.5)
        
        // Проверяем что начальное время правильное
        let lastUpdate = testDelegate.lastStatusBarUpdate!
        assert(lastUpdate.totalMinutes == 25, "Формальный интервал должен начинаться с 25:00, получили \(lastUpdate.totalMinutes):XX")
        assert(lastUpdate.overtimeMinutes == 0, "Переработка должна начинаться с 0, получили \(lastUpdate.overtimeMinutes)")
        
        print("✅ Тест 2 пройден: Формальные интервалы показывают правильное время")
    }
    
    /// Тест 3: Правильность расчета времени для неформальных интервалов
    func testInformalIntervalTiming() {
        print("\n🧪 Тест 3: Расчет времени для неформальных интервалов")
        
        let intervalDuration: TimeInterval = 52 * 60  // 52 минуты
        testSystem.startSimpleEscalation(for: "informal", isTest: true, intervalDuration: intervalDuration)
        
        // Ждем первое обновление
        Thread.sleep(forTimeInterval: 1.5)
        
        // Проверяем что начальное время правильное
        let lastUpdate = testDelegate.lastStatusBarUpdate!
        assert(lastUpdate.totalMinutes == 52, "Неформальный интервал должен начинаться с 52:00, получили \(lastUpdate.totalMinutes):XX")
        assert(lastUpdate.overtimeMinutes == 0, "Переработка должна начинаться с 0, получили \(lastUpdate.overtimeMinutes)")
        
        print("✅ Тест 3 пройден: Неформальные интервалы показывают правильное время")
    }
    
    /// Тест 4: Эскалация уровней в тестовом режиме
    func testEscalationLevels() {
        print("\n🧪 Тест 4: Эскалация уровней")
        
        testSystem.startSimpleEscalation(for: "formal", isTest: true, intervalDuration: 25 * 60)
        
        // Ждем несколько секунд для эскалации (в тестовом режиме ускорено)
        Thread.sleep(forTimeInterval: 4.0)
        
        // Проверяем что произошла эскалация
        assert(testDelegate.escalationReminderCount > 0, "Должно быть показано хотя бы одно напоминание эскалации")
        
        // Проверяем что уровни растут
        let reminders = testDelegate.escalationReminders
        if reminders.count >= 2 {
            assert(reminders[1].level > reminders[0].level, "Уровни эскалации должны расти")
        }
        
        print("✅ Тест 4 пройден: Эскалация работает корректно")
    }
    
    /// Тест 5: Различие между формальными и неформальными интервалами
    func testIntervalTypeDifferences() {
        print("\n🧪 Тест 5: Различие типов интервалов")
        
        // Тестируем формальный интервал
        testDelegate.reset()
        testSystem.startSimpleEscalation(for: "formal", isTest: true, intervalDuration: 25 * 60)
        Thread.sleep(forTimeInterval: 2.0)
        let formalReminders = testDelegate.escalationReminders.count
        testSystem.stopEscalation()
        
        // Тестируем неформальный интервал
        testDelegate.reset()
        testSystem.startSimpleEscalation(for: "informal", isTest: true, intervalDuration: 52 * 60)
        Thread.sleep(forTimeInterval: 2.0)
        let informalReminders = testDelegate.escalationReminders.count
        testSystem.stopEscalation()
        
        // Для неформальных интервалов уровень 0 не показывается (окно уже открыто)
        // Поэтому формальных напоминаний может быть больше
        print("📊 Формальных напоминаний: \(formalReminders), неформальных: \(informalReminders)")
        
        print("✅ Тест 5 пройден: Типы интервалов обрабатываются по-разному")
    }
    
    // MARK: - Test Delegate
    
    /// Тестовый делегат для перехвата вызовов SimpleUnifiedSystem
    private class TestDelegate: SimpleUnifiedSystemDelegate {
        
        var statusBarUpdateCount = 0
        var escalationReminderCount = 0
        var statisticsRecordCount = 0
        
        var lastStatusBarUpdate: (overtimeMinutes: Int, totalMinutes: Int, seconds: Int)?
        var escalationReminders: [(minutes: Int, level: Int, intervalType: String)] = []
        var statisticsRecords: [(duration: TimeInterval, intervalType: String)] = []
        
        func showEscalationReminder(minutes: Int, level: Int, for intervalType: String) {
            escalationReminderCount += 1
            escalationReminders.append((minutes: minutes, level: level, intervalType: intervalType))
            print("🔔 TestDelegate: Напоминание #\(escalationReminderCount) - \(minutes) мин, уровень \(level), тип \(intervalType)")
        }
        
        func updateStatusBar(overtimeMinutes: Int, totalMinutes: Int, seconds: Int) {
            statusBarUpdateCount += 1
            lastStatusBarUpdate = (overtimeMinutes: overtimeMinutes, totalMinutes: totalMinutes, seconds: seconds)
            print("📊 TestDelegate: Статус-бар #\(statusBarUpdateCount) - переработка: \(overtimeMinutes), общее: \(totalMinutes):\(String(format: "%02d", seconds))")
        }
        
        func recordIntervalStatistics(duration: TimeInterval, intervalType: String) {
            statisticsRecordCount += 1
            statisticsRecords.append((duration: duration, intervalType: intervalType))
            print("📈 TestDelegate: Статистика #\(statisticsRecordCount) - \(Int(duration/60)) мин, тип \(intervalType)")
        }
        
        func reset() {
            statusBarUpdateCount = 0
            escalationReminderCount = 0
            statisticsRecordCount = 0
            lastStatusBarUpdate = nil
            escalationReminders.removeAll()
            statisticsRecords.removeAll()
        }
    }
    
    // MARK: - Test Runner
    
    /// Запускает все тесты
    static func runAllTests() {
        print("🧪 ========== ЗАПУСК ТЕСТОВ SimpleUnifiedSystem ==========")
        
        let test = SimpleUnifiedSystemTest()
        var passedTests = 0
        var totalTests = 0
        
        let tests: [(String, () -> Void)] = [
            ("testBasicStartStop", test.testBasicStartStop),
            ("testFormalIntervalTiming", test.testFormalIntervalTiming),
            ("testInformalIntervalTiming", test.testInformalIntervalTiming),
            ("testEscalationLevels", test.testEscalationLevels),
            ("testIntervalTypeDifferences", test.testIntervalTypeDifferences)
        ]
        
        for (testName, testMethod) in tests {
            totalTests += 1
            print("\n🧪 ========== \(testName) ==========")
            
            do {
                test.setUp()
                testMethod()
                test.tearDown()
                passedTests += 1
                print("✅ \(testName) ПРОЙДЕН")
            } catch {
                print("❌ \(testName) ПРОВАЛЕН: \(error)")
                test.tearDown()
            }
        }
        
        print("\n🧪 ========== РЕЗУЛЬТАТЫ ТЕСТОВ ==========")
        print("✅ Пройдено: \(passedTests)/\(totalTests)")
        print("❌ Провалено: \(totalTests - passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        } else {
            print("⚠️ ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!")
        }
    }
}

// Запускаем тесты
SimpleUnifiedSystemTest.runAllTests()
