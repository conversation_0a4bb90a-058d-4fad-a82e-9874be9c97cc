import Foundation

// Импортируем все необходимые файлы
// Это критически важно - без этого тест работает "в вакууме"
let currentDir = FileManager.default.currentDirectoryPath
let sourcePath = "\(currentDir)/SimplePomodoroTest"

// Загружаем исходные файлы
let computerTimeTrackerPath = "\(sourcePath)/ComputerTimeTracker.swift"
let minuteActivityTrackerPath = "\(sourcePath)/MinuteActivityTracker.swift"
let statisticsManagerPath = "\(sourcePath)/StatisticsManager.swift"
let unifiedActivityCheckerPath = "\(sourcePath)/UnifiedActivityChecker.swift"

// Проверяем что файлы существуют
guard FileManager.default.fileExists(atPath: computerTimeTrackerPath) else {
    print("❌ КРИТИЧЕСКАЯ ОШИБКА: Не найден ComputerTimeTracker.swift")
    print("   Путь: \(computerTimeTrackerPath)")
    exit(1)
}

print("✅ Найдены исходные файлы для тестирования")

/// Тест обновления ComputerTimeTracker с новой системой MinuteActivityTracker
/// Проверяет что интеграция работает корректно и обеспечивает более точное обнаружение активности
class ComputerTimeTrackerUpgradeTest {
    
    // MARK: - Test Infrastructure
    
    private var testResults: [String] = []
    private var testsPassed = 0
    private var testsTotal = 0
    
    private func assert(_ condition: Bool, _ message: String) {
        testsTotal += 1
        if condition {
            testsPassed += 1
            testResults.append("✅ \(message)")
        } else {
            testResults.append("❌ \(message)")
        }
    }
    
    // MARK: - Mock Classes
    
    /// Mock StatisticsManager для тестирования
    class MockStatisticsManager: StatisticsManager {
        var recordedActiveMinutes = 0
        
        override func recordActiveMinute() {
            recordedActiveMinutes += 1
        }
        
        func reset() {
            recordedActiveMinutes = 0
        }
    }
    
    /// Mock MinuteActivityTracker для тестирования
    class MockMinuteActivityTracker: MinuteActivityTracker {
        private var mockResults: [Bool] = []
        private var currentIndex = 0
        var isTrackingActive = false
        
        func setMockResults(_ results: [Bool]) {
            mockResults = results
            currentIndex = 0
        }
        
        override func startTracking() {
            isTrackingActive = true
        }
        
        override func stopTracking() {
            isTrackingActive = false
        }
        
        override func wasCurrentMinuteActive() -> Bool {
            guard currentIndex < mockResults.count else { return false }
            let result = mockResults[currentIndex]
            return result
        }
        
        override func forceCompleteCurrentMinute() -> Bool {
            guard currentIndex < mockResults.count else { return false }
            let result = mockResults[currentIndex]
            currentIndex += 1
            
            // Симулируем callback
            onMinuteCompleted?(result)
            
            return result
        }
    }
    
    // MARK: - Test Methods
    
    /// Тест 1: Базовая функциональность с новой системой
    private func testNewSystemBasicFunctionality() {
        print("\n🧪 Тест 1: Базовая функциональность с новой системой")
        
        let mockStats = MockStatisticsManager()
        let mockTracker = MockMinuteActivityTracker()
        let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
        
        // Включаем новую систему
        computerTracker.setUseNewActivitySystem(true)
        
        // Настраиваем мок для возврата активности
        mockTracker.setMockResults([true, false, true])
        
        var activityCallbacks: [Bool] = []
        computerTracker.onActivityRecorded = { isActive in
            activityCallbacks.append(isActive)
        }
        
        // Запускаем отслеживание
        computerTracker.startTracking()
        assert(mockTracker.isTrackingActive, "MinuteActivityTracker должен быть запущен")
        
        // Симулируем принудительное завершение минут
        computerTracker.setCheckInterval(1.0) // Ускоряем для теста
        
        // Ждем немного и проверяем
        Thread.sleep(forTimeInterval: 0.1)
        
        computerTracker.stopTracking()
        assert(!mockTracker.isTrackingActive, "MinuteActivityTracker должен быть остановлен")
        
        print("✅ Тест 1 завершен")
    }
    
    /// Тест 2: Переключение между старой и новой системой
    private func testSystemSwitching() {
        print("\n🧪 Тест 2: Переключение между старой и новой системой")
        
        let mockStats = MockStatisticsManager()
        let mockTracker = MockMinuteActivityTracker()
        let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
        
        // Проверяем переключение на новую систему
        computerTracker.setUseNewActivitySystem(true)
        computerTracker.startTracking()
        assert(mockTracker.isTrackingActive, "Новая система должна быть активна")
        
        // Переключаемся на старую систему
        computerTracker.setUseNewActivitySystem(false)
        assert(!mockTracker.isTrackingActive, "Новая система должна быть остановлена")
        
        // Переключаемся обратно на новую
        computerTracker.setUseNewActivitySystem(true)
        assert(mockTracker.isTrackingActive, "Новая система должна быть снова активна")
        
        computerTracker.stopTracking()
        
        print("✅ Тест 2 завершен")
    }
    
    /// Тест 3: Проверка активности с новой системой
    private func testActivityCheckingNewSystem() {
        print("\n🧪 Тест 3: Проверка активности с новой системой")
        
        let mockStats = MockStatisticsManager()
        let mockTracker = MockMinuteActivityTracker()
        let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
        
        // Включаем новую систему
        computerTracker.setUseNewActivitySystem(true)
        
        // Настраиваем мок
        mockTracker.setMockResults([true, false, true])
        
        // Проверяем активность
        let isActive1 = computerTracker.isUserCurrentlyActive()
        assert(isActive1 == true, "Первая проверка должна вернуть true")
        
        // Переключаемся на старую систему для сравнения
        computerTracker.setUseNewActivitySystem(false)
        let isActive2 = computerTracker.isUserCurrentlyActive()
        // Старая система может вернуть false, так как нет реальной активности
        
        print("✅ Тест 3 завершен")
    }
    
    /// Тест 4: Callback система с новой системой
    private func testCallbackSystemNewSystem() {
        print("\n🧪 Тест 4: Callback система с новой системой")
        
        let mockStats = MockStatisticsManager()
        let mockTracker = MockMinuteActivityTracker()
        let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
        
        // Включаем новую систему
        computerTracker.setUseNewActivitySystem(true)
        
        // Настраиваем мок
        mockTracker.setMockResults([true, false, true])
        
        var callbackResults: [Bool] = []
        computerTracker.onActivityRecorded = { isActive in
            callbackResults.append(isActive)
        }
        
        // Запускаем отслеживание
        computerTracker.startTracking()
        
        // Симулируем завершение минут через callback
        let result1 = mockTracker.forceCompleteCurrentMinute()
        let result2 = mockTracker.forceCompleteCurrentMinute()
        let result3 = mockTracker.forceCompleteCurrentMinute()
        
        assert(callbackResults.count >= 2, "Должно быть получено несколько callback'ов")
        assert(mockStats.recordedActiveMinutes >= 1, "Должна быть записана хотя бы одна активная минута")
        
        computerTracker.stopTracking()
        
        print("✅ Тест 4 завершен")
    }
    
    /// Тест 5: Статистика новой системы
    private func testNewSystemStatistics() {
        print("\n🧪 Тест 5: Статистика новой системы")
        
        let mockStats = MockStatisticsManager()
        let mockTracker = MockMinuteActivityTracker()
        let computerTracker = ComputerTimeTracker(statisticsManager: mockStats, minuteActivityTracker: mockTracker)
        
        // Включаем новую систему
        computerTracker.setUseNewActivitySystem(true)
        
        // Проверяем что статистика доступна
        let stats = computerTracker.getMinuteActivityStats()
        assert(stats != nil, "Статистика новой системы должна быть доступна")
        
        // Переключаемся на старую систему
        computerTracker.setUseNewActivitySystem(false)
        let statsOld = computerTracker.getMinuteActivityStats()
        assert(statsOld == nil, "Статистика не должна быть доступна для старой системы")
        
        print("✅ Тест 5 завершен")
    }
    
    // MARK: - Main Test Runner
    
    func runAllTests() -> Bool {
        print("🚀 Запуск тестов обновления ComputerTimeTracker")
        print(String(repeating: "=", count: 80))
        
        testResults.removeAll()
        testsPassed = 0
        testsTotal = 0
        
        testNewSystemBasicFunctionality()
        testSystemSwitching()
        testActivityCheckingNewSystem()
        testCallbackSystemNewSystem()
        testNewSystemStatistics()
        
        print("\n" + String(repeating: "=", count: 80))
        print("📊 РЕЗУЛЬТАТЫ ТЕСТОВ:")
        for result in testResults {
            print(result)
        }
        
        print("\n🎯 ИТОГО: \(testsPassed)/\(testsTotal) тестов пройдено")
        
        if testsPassed == testsTotal {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Обновление ComputerTimeTracker работает корректно.")
        } else {
            print("⚠️  Есть проблемы с обновлением. Требуется доработка.")
        }
        
        return testsPassed == testsTotal
    }
}

// MARK: - Test Execution

// Если запускается напрямую
let test = ComputerTimeTrackerUpgradeTest()
let success = test.runAllTests()
exit(success ? 0 : 1)
