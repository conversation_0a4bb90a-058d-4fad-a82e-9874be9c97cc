import Foundation

/// РЕАЛЬНЫЙ ТЕСТ InformalSessionDetector (минимальная версия)
/// Тестируем только доступные методы без сложных зависимостей

@main
struct RealInformalDetectorSimpleTest {
    static func main() {
        print("🚀 РЕАЛЬНЫЙ ТЕСТ InformalSessionDetector (минимальная версия)")
        print(String(repeating: "=", count: 70))

        // Создаем РЕАЛЬНЫЙ объект InformalSessionDetector
        let detector = InformalSessionDetector()

        var passed = 0
        var total = 0

        // СЦЕНАРИЙ 1: Проверка создания объекта
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Проверка создания объекта")

        // Проверяем что объект создается без ошибок
        let info = detector.getDebugInfo()

        if info.count > 0 {
            print("✅ ПРОЙДЕН: Объект создается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы при создании объекта")
        }
        
        // СЦЕНАРИЙ 2: Включение/отключение детектора
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Включение/отключение детектора")

        detector.setEnabled(true)
        detector.setEnabled(false)
        // Проверяем что методы не вызывают ошибок
        print("✅ ПРОЙДЕН: Включение/отключение работает без ошибок")
        passed += 1

        // СЦЕНАРИЙ 3: Запуск/остановка отслеживания
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Запуск/остановка отслеживания")

        detector.setEnabled(true)
        detector.startTracking()
        detector.stopTracking()
        // Проверяем что методы не вызывают ошибок
        print("✅ ПРОЙДЕН: Запуск/остановка работает без ошибок")
        passed += 1

        // СЦЕНАРИЙ 4: Получение отладочной информации
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Получение отладочной информации")

        let activityDetails = detector.getActivityLogDetails()

        if activityDetails.count > 0 {
            print("✅ ПРОЙДЕН: Отладочная информация доступна")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с отладочной информацией")
        }
        
        // Финальная очистка
        detector.stopTracking()
        detector.setEnabled(false)
        
        // Финальная очистка
        detector.stopTracking()
        detector.setEnabled(false)
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")

        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ InformalSessionDetector ПРОЙДЕНЫ!")
            print("✅ Основная функциональность работает корректно")
            print("\n🔨 Для проверки обнаружения поломок:")
            print("   1. Сломай метод getDebugInfo() в InformalSessionDetector.swift")
            print("   2. Запусти тест снова - он должен провалиться")
            print("   3. Верни код обратно")
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В InformalSessionDetector!")
            exit(1)
        }
    }
}
