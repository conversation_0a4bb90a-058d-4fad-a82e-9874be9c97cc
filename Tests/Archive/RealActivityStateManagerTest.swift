import Foundation

/// ТЕСТ РЕАЛЬНОГО ActivityStateManager - не моков!
/// Проверяем реальную логику управления состояниями активности

@main
struct RealActivityStateManagerTest {
    static func main() {
        print("🚀 ТЕСТ РЕАЛЬНОГО ActivityStateManager")
        print(String(repeating: "=", count: 60))
        
        // Создаем РЕАЛЬНЫЙ объект
        let stateManager = ActivityStateManager()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Запуск системы управления состояниями
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Запуск системы управления состояниями")
        
        stateManager.start()
        
        // Проверяем что система запустилась
        let debugInfo = stateManager.getDebugInfo()
        let isActive = debugInfo.contains("Активен: true")

        if isActive {
            print("✅ ПРОЙДЕН: Система управления состояниями запускается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ запускается")
            print("   Debug: \(debugInfo)")
        }
        
        // СЦЕНАРИЙ 2: Обработка активности пользователя
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Обработка активности пользователя")

        // Симулируем активность через StateManager
        stateManager.simulateActivity()

        let (currentState, _) = stateManager.getCurrentStateInfo()

        if currentState == .working {
            print("✅ ПРОЙДЕН: Активность пользователя обрабатывается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ обрабатывается")
            print("   Текущее состояние: \(currentState)")
        }
        
        // СЦЕНАРИЙ 3: Переход в состояние отсутствия
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Переход в состояние отсутствия")

        // Симулируем неактивность 5 минут
        stateManager.simulateInactivity(duration: 5 * 60)

        let (stateAfterInactivity, _) = stateManager.getCurrentStateInfo()

        if stateAfterInactivity == .awayMedium {
            print("✅ ПРОЙДЕН: Переход в состояние отсутствия работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Переход в состояние отсутствия НЕ работает")
            print("   Ожидалось: awayMedium, получено: \(stateAfterInactivity)")
        }
        
        // СЦЕНАРИЙ 4: Возврат пользователя с сообщением
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Возврат пользователя с сообщением")

        // Симулируем возврат активности
        stateManager.simulateActivity()

        let (stateAfterReturn, _) = stateManager.getCurrentStateInfo()

        if stateAfterReturn == .working {
            print("✅ ПРОЙДЕН: Возврат пользователя обрабатывается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Возврат пользователя НЕ работает")
            print("   Ожидалось: working, получено: \(stateAfterReturn)")
        }
        
        // СЦЕНАРИЙ 5: Долгое отсутствие (17+ минут)
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Долгое отсутствие (17+ минут)")

        // Симулируем долгую неактивность (20 минут)
        stateManager.simulateInactivity(duration: 20 * 60)

        let (stateAfterLongInactivity, _) = stateManager.getCurrentStateInfo()

        if stateAfterLongInactivity == .awayVeryLong {
            print("✅ ПРОЙДЕН: Долгое отсутствие обрабатывается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Долгое отсутствие НЕ работает")
            print("   Ожидалось: awayVeryLong, получено: \(stateAfterLongInactivity)")
        }
        
        // СЦЕНАРИЙ 6: Остановка системы
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Остановка системы")

        stateManager.stop()

        // Проверяем что система остановилась (нет публичного isActive, проверим через debug)
        let debugInfoAfterStop = stateManager.getDebugInfo()
        let isActiveAfterStop = debugInfoAfterStop.contains("Активен: true")

        if !isActiveAfterStop {
            print("✅ ПРОЙДЕН: Система корректно останавливается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ останавливается")
            print("   Debug: \(debugInfoAfterStop)")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ ActivityStateManager работает корректно")
            
            // Проверка обнаружения поломок
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В ActivityStateManager!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Откройте SimplePomodoroTest/ActivityStateManager.swift")
        print("2. В методе handleMinuteActivity() найдите строку:")
        print("   currentState = .working")
        print("3. Замените на:")
        print("   currentState = .awayShort")
        print("4. Запустите этот тест снова")
        print("5. СЦЕНАРИЙ 2 или 4 должны провалиться!")
        print("6. Верните код обратно")
        print("\nЭто покажет что тест ловит поломки в реальной логике состояний.")
        
        exit(0)
    }
}
