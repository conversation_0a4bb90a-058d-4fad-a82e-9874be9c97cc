#!/usr/bin/env swift

import Foundation

// Простая функция assert для тестов
func testAssert(_ condition: Bool, _ message: String) {
    if !condition {
        print("❌ ОШИБКА: \(message)")
        exit(1)
    }
}

print("🧪 ActivityStateManager Test Suite")
print("==================================")

// MARK: - Mock MinuteActivityTracker для тестирования

class MockMinuteActivityTracker {
    var onMinuteCompleted: ((Bool) -> Void)?
    private var isTracking = false
    
    func startTracking() {
        isTracking = true
        print("🎯 MockMinuteActivityTracker: Запущено отслеживание")
    }
    
    func stopTracking() {
        isTracking = false
        print("🎯 MockMinuteActivityTracker: Остановлено отслеживание")
    }
    
    func getCurrentBandsState() -> (currentBand: Int, bands: [Bool], isActive: Bool) {
        return (0, [false, false, false, false], isTracking)
    }
    
    // Методы для тестирования
    func simulateMinuteActivity(_ isActive: Bool) {
        onMinuteCompleted?(isActive)
    }
}

// MARK: - Тестовая версия ActivityStateManager

class TestableActivityStateManager {
    
    // MARK: - Types
    
    enum ActivityState {
        case working, awayShort, awayMedium, awayLong, awayVeryLong, formalRest
    }
    
    enum ReturnMessage {
        case resumeSilently, partialRest, chooseRestOrWork, fullRest
    }
    
    // MARK: - Configuration
    
    private let shortAwayThreshold: TimeInterval = 2 * 60
    private let mediumAwayThreshold: TimeInterval = 10 * 60
    private let longAwayThreshold: TimeInterval = 17 * 60
    private let inactivityStopThreshold: TimeInterval = 15.0
    
    // MARK: - State
    
    private(set) var currentState: ActivityState = .working
    private var stateStartTime = Date()
    private var lastActivityTime = Date()
    private var isActive = false
    private var countersStoppedDueToInactivity = false
    
    // MARK: - Dependencies (Mock)
    
    private let minuteTracker = MockMinuteActivityTracker()
    
    // MARK: - Callbacks
    
    var onStateChanged: ((ActivityState, ActivityState) -> Void)?
    var onStopCounters: (() -> Void)?
    var onResumeCounters: (() -> Void)?
    var onUserReturned: ((ReturnMessage, TimeInterval) -> Void)?
    
    // MARK: - Public Methods
    
    func start() {
        guard !isActive else { return }
        
        isActive = true
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false
        
        minuteTracker.startTracking()
        minuteTracker.onMinuteCompleted = { [weak self] isActive in
            self?.handleMinuteActivity(isActive)
        }
        
        print("🎯 TestableActivityStateManager: Запущен в состоянии \(currentState)")
    }
    
    func stop() {
        guard isActive else { return }
        
        isActive = false
        minuteTracker.stopTracking()
        
        print("🎯 TestableActivityStateManager: Остановлен")
    }
    
    func startFormalRest() {
        let previousState = currentState
        currentState = .formalRest
        stateStartTime = Date()
        
        print("🎯 TestableActivityStateManager: Переход в формальный отдых")
        onStateChanged?(previousState, currentState)
    }
    
    func endFormalRest() {
        let previousState = currentState
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false
        
        onResumeCounters?()
        
        print("🎯 TestableActivityStateManager: Возврат к работе после формального отдыха")
        onStateChanged?(previousState, currentState)
    }
    
    func resetAfterLongSleep() {
        let previousState = currentState
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false
        
        onResumeCounters?()
        
        print("🎯 TestableActivityStateManager: Сброс после долгого сна")
        onStateChanged?(previousState, currentState)
    }
    
    func getCurrentStateInfo() -> (state: ActivityState, timeInState: TimeInterval) {
        let timeInState = Date().timeIntervalSince(stateStartTime)
        return (currentState, timeInState)
    }
    
    func getTimeSinceLastActivity() -> TimeInterval {
        return Date().timeIntervalSince(lastActivityTime)
    }
    
    func areCountersStopped() -> Bool {
        return countersStoppedDueToInactivity
    }
    
    // MARK: - Test Methods
    
    func simulateMinuteActivity(_ isActive: Bool) {
        minuteTracker.simulateMinuteActivity(isActive)
    }
    
    func setLastActivityTime(_ time: Date) {
        lastActivityTime = time
    }
    
    // MARK: - Private Methods
    
    private func handleMinuteActivity(_ isActive: Bool) {
        print("🎯 TestableActivityStateManager: Минута активности: \(isActive ? "ДА" : "НЕТ")")
        
        if isActive {
            handleUserActivity()
        } else {
            handleUserInactivity()
        }
    }
    
    private func handleUserActivity() {
        // Сначала обрабатываем возвращение (до обновления времени активности)
        if countersStoppedDueToInactivity {
            handleUserReturn()
        }

        // Теперь обновляем время активности
        lastActivityTime = Date()

        if currentState != .formalRest {
            transitionToState(.working)
        }

        if countersStoppedDueToInactivity {
            countersStoppedDueToInactivity = false
            onResumeCounters?()
        }
    }
    
    private func handleUserInactivity() {
        let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)
        
        if timeSinceLastActivity >= inactivityStopThreshold && !countersStoppedDueToInactivity {
            countersStoppedDueToInactivity = true
            onStopCounters?()
            print("🎯 TestableActivityStateManager: Счетчики остановлены из-за неактивности")
        }
        
        if currentState != .formalRest {
            let newState = determineAwayState(timeSinceLastActivity)
            transitionToState(newState)
        }
    }
    
    private func determineAwayState(_ timeAway: TimeInterval) -> ActivityState {
        if timeAway < shortAwayThreshold {
            return .awayShort
        } else if timeAway < mediumAwayThreshold {
            return .awayMedium
        } else if timeAway < longAwayThreshold {
            return .awayLong
        } else {
            return .awayVeryLong
        }
    }
    
    private func transitionToState(_ newState: ActivityState) {
        guard newState != currentState else { return }
        
        let previousState = currentState
        currentState = newState
        stateStartTime = Date()
        
        print("🎯 TestableActivityStateManager: Переход \(previousState) → \(newState)")
        onStateChanged?(previousState, newState)
    }
    
    private func handleUserReturn() {
        let awayTime = Date().timeIntervalSince(lastActivityTime)
        let message = determineReturnMessage(awayTime)
        
        print("🎯 TestableActivityStateManager: Пользователь вернулся после \(Int(awayTime/60)) мин, сообщение: \(message)")
        onUserReturned?(message, awayTime)
    }
    
    private func determineReturnMessage(_ awayTime: TimeInterval) -> ReturnMessage {
        if awayTime < shortAwayThreshold {
            return .resumeSilently
        } else if awayTime < mediumAwayThreshold {
            return .partialRest
        } else if awayTime < longAwayThreshold {
            return .chooseRestOrWork
        } else {
            return .fullRest
        }
    }
    
    deinit {
        stop()
    }
}

// MARK: - Test Functions

func testBasicStateTransitions() {
    print("\n🧪 Тест 1: Базовые переходы состояний")
    
    let manager = TestableActivityStateManager()
    var stateChanges: [(TestableActivityStateManager.ActivityState, TestableActivityStateManager.ActivityState)] = []
    
    manager.onStateChanged = { from, to in
        stateChanges.append((from, to))
        print("📊 Переход состояния: \(from) → \(to)")
    }
    
    manager.start()
    
    // Проверяем начальное состояние
    let initialState = manager.getCurrentStateInfo()
    testAssert(initialState.state == .working, "Начальное состояние должно быть 'working'")

    // Симулируем активность
    manager.simulateMinuteActivity(true)
    testAssert(manager.getCurrentStateInfo().state == .working, "При активности должно оставаться 'working'")
    
    manager.stop()
    print("✅ Базовые переходы состояний работают")
}

func testInactivityHandling() {
    print("\n🧪 Тест 2: Обработка неактивности")
    
    let manager = TestableActivityStateManager()
    var countersStopped = false
    var countersResumed = false
    
    manager.onStopCounters = {
        countersStopped = true
        print("📊 Счетчики остановлены")
    }
    
    manager.onResumeCounters = {
        countersResumed = true
        print("📊 Счетчики возобновлены")
    }
    
    manager.start()
    
    // Устанавливаем время последней активности 20 секунд назад
    manager.setLastActivityTime(Date().addingTimeInterval(-20))
    
    // Симулируем неактивную минуту
    manager.simulateMinuteActivity(false)
    
    testAssert(countersStopped, "Счетчики должны быть остановлены после неактивности")
    testAssert(manager.areCountersStopped(), "Флаг остановки счетчиков должен быть установлен")

    // Симулируем возврат активности
    manager.simulateMinuteActivity(true)

    testAssert(countersResumed, "Счетчики должны быть возобновлены при возврате активности")
    testAssert(!manager.areCountersStopped(), "Флаг остановки счетчиков должен быть сброшен")
    
    manager.stop()
    print("✅ Обработка неактивности работает")
}

func testAwayStates() {
    print("\n🧪 Тест 3: Состояния отсутствия")
    
    let manager = TestableActivityStateManager()
    var stateChanges: [TestableActivityStateManager.ActivityState] = []
    
    manager.onStateChanged = { _, to in
        stateChanges.append(to)
    }
    
    manager.start()
    
    // Тест: Короткое отсутствие (1 минута)
    manager.setLastActivityTime(Date().addingTimeInterval(-60))
    manager.simulateMinuteActivity(false)
    testAssert(stateChanges.last == .awayShort, "1 минута должна дать состояние awayShort")

    // Тест: Среднее отсутствие (5 минут)
    manager.setLastActivityTime(Date().addingTimeInterval(-5 * 60))
    manager.simulateMinuteActivity(false)
    testAssert(stateChanges.last == .awayMedium, "5 минут должно дать состояние awayMedium")

    // Тест: Долгое отсутствие (15 минут)
    manager.setLastActivityTime(Date().addingTimeInterval(-15 * 60))
    manager.simulateMinuteActivity(false)
    testAssert(stateChanges.last == .awayLong, "15 минут должно дать состояние awayLong")

    // Тест: Очень долгое отсутствие (20 минут)
    manager.setLastActivityTime(Date().addingTimeInterval(-20 * 60))
    manager.simulateMinuteActivity(false)
    testAssert(stateChanges.last == .awayVeryLong, "20 минут должно дать состояние awayVeryLong")
    
    manager.stop()
    print("✅ Состояния отсутствия работают")
}

func testReturnMessages() {
    print("\n🧪 Тест 4: Сообщения при возвращении")
    
    let manager = TestableActivityStateManager()
    var returnMessages: [(TestableActivityStateManager.ReturnMessage, TimeInterval)] = []
    
    manager.onUserReturned = { message, duration in
        returnMessages.append((message, duration))
        print("📊 Сообщение при возвращении: \(message), длительность: \(Int(duration/60)) мин")
    }
    
    manager.start()
    
    // Тест: Возврат после короткого отсутствия (1 минута)
    manager.setLastActivityTime(Date().addingTimeInterval(-60))
    manager.simulateMinuteActivity(false) // Остановка счетчиков
    manager.simulateMinuteActivity(true)  // Возврат
    
    testAssert(returnMessages.last?.0 == .resumeSilently, "1 минута должна дать resumeSilently")

    // Тест: Возврат после среднего отсутствия (5 минут)
    returnMessages.removeAll()
    manager.setLastActivityTime(Date().addingTimeInterval(-5 * 60))
    manager.simulateMinuteActivity(false)
    manager.simulateMinuteActivity(true)

    testAssert(returnMessages.last?.0 == .partialRest, "5 минут должно дать partialRest")

    // Тест: Возврат после долгого отсутствия (15 минут)
    returnMessages.removeAll()
    manager.setLastActivityTime(Date().addingTimeInterval(-15 * 60))
    manager.simulateMinuteActivity(false)
    manager.simulateMinuteActivity(true)

    testAssert(returnMessages.last?.0 == .chooseRestOrWork, "15 минут должно дать chooseRestOrWork")

    // Тест: Возврат после очень долгого отсутствия (20 минут)
    returnMessages.removeAll()
    manager.setLastActivityTime(Date().addingTimeInterval(-20 * 60))
    manager.simulateMinuteActivity(false)
    manager.simulateMinuteActivity(true)

    testAssert(returnMessages.last?.0 == .fullRest, "20 минут должно дать fullRest")
    
    manager.stop()
    print("✅ Сообщения при возвращении работают")
}

func testFormalRest() {
    print("\n🧪 Тест 5: Формальный отдых")
    
    let manager = TestableActivityStateManager()
    var stateChanges: [TestableActivityStateManager.ActivityState] = []
    
    manager.onStateChanged = { _, to in
        stateChanges.append(to)
    }
    
    manager.start()
    
    // Начинаем формальный отдых
    manager.startFormalRest()
    testAssert(stateChanges.last == .formalRest, "Должен перейти в состояние formalRest")

    // Во время формального отдыха неактивность не должна менять состояние
    let stateCountBefore = stateChanges.count
    manager.setLastActivityTime(Date().addingTimeInterval(-10 * 60))
    manager.simulateMinuteActivity(false)
    testAssert(stateChanges.count == stateCountBefore, "Во время формального отдыха состояние не должно меняться")
    testAssert(manager.getCurrentStateInfo().state == .formalRest, "Должно оставаться в formalRest")

    // Заканчиваем формальный отдых
    manager.endFormalRest()
    testAssert(stateChanges.last == .working, "После формального отдыха должен вернуться к working")
    
    manager.stop()
    print("✅ Формальный отдых работает")
}

// MARK: - Запуск тестов

func runAllTests() {
    print("🚀 Запуск тестов ActivityStateManager")
    print("====================================")
    
    testBasicStateTransitions()
    testInactivityHandling()
    testAwayStates()
    testReturnMessages()
    testFormalRest()
    
    print("\n🎉 Все тесты ActivityStateManager прошли успешно!")
    print("===============================================")
}

// Запускаем тесты
runAllTests()
