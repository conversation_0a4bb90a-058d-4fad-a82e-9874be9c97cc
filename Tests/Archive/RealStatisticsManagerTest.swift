import Foundation

@main
struct RealStatisticsManagerTest {
    static func main() {
        print("🧪 Запуск теста интеграции StatisticsManager с 4-бандовой системой активности")
        
        let statisticsManager = StatisticsManager()
        var passedTests = 0
        let totalTests = 6
        
        // Очищаем данные перед тестом
        statisticsManager.clearAllActivityRecords()
        
        // Тест 1: Запись состояний активности
        print("\n🧪 Тест 1: Запись состояний активности")
        let testDate = Date()
        let bandData = [true, false, true, false]
        
        statisticsManager.recordActivityState(state: .working, duration: 1800, bandData: bandData) // 30 мин
        statisticsManager.recordActivityState(state: .awayShort, duration: 300, bandData: [false, false, false, false]) // 5 мин
        statisticsManager.recordActivityState(state: .working, duration: 1200, bandData: [true, true, false, true]) // 20 мин
        
        let records = statisticsManager.getAllStoredActivityRecords()
        if records.count == 3 {
            print("✅ Записано 3 состояния активности")
            passedTests += 1
        } else {
            print("❌ Ожидалось 3 записи, получено: \(records.count)")
        }
        
        // Тест 2: Получение статистики за период
        print("\n🧪 Тест 2: Получение статистики за период")
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        let stats = statisticsManager.getActivityStatsForDateRange(from: today, to: tomorrow)
        
        let expectedWorkingTime: TimeInterval = 3000 // 30 + 20 мин = 50 мин = 3000 сек
        let expectedAwayTime: TimeInterval = 300 // 5 мин = 300 сек
        
        if let workingTime = stats[.working], let awayTime = stats[.awayShort] {
            if abs(workingTime - expectedWorkingTime) < 1 && abs(awayTime - expectedAwayTime) < 1 {
                print("✅ Статистика корректна: работа \(Int(workingTime)) сек, отдых \(Int(awayTime)) сек")
                passedTests += 1
            } else {
                print("❌ Неверная статистика: работа \(Int(workingTime)) сек (ожидалось \(Int(expectedWorkingTime))), отдых \(Int(awayTime)) сек (ожидалось \(Int(expectedAwayTime)))")
            }
        } else {
            print("❌ Не найдены ожидаемые состояния в статистике")
        }
        
        // Тест 3: Качество активности
        print("\n🧪 Тест 3: Качество активности")
        let quality = statisticsManager.getActivityQualityForDateRange(from: today, to: tomorrow)
        let expectedQuality = 3000.0 / 3300.0 // 3000 сек работы из 3300 сек общего времени
        
        if abs(quality - expectedQuality) < 0.01 {
            print("✅ Качество активности корректно: \(Int(quality * 100))%")
            passedTests += 1
        } else {
            print("❌ Неверное качество: \(Int(quality * 100))% (ожидалось \(Int(expectedQuality * 100))%)")
        }
        
        // Тест 4: Переходы между состояниями
        print("\n🧪 Тест 4: Переходы между состояниями")
        let recordsBeforeTransition = statisticsManager.getAllStoredActivityRecords().count
        
        statisticsManager.recordActivityTransition(fromState: .working, toState: .awayMedium, duration: 600)
        
        let recordsAfterTransition = statisticsManager.getAllStoredActivityRecords().count
        if recordsAfterTransition == recordsBeforeTransition + 1 {
            print("✅ Переход между состояниями записан корректно")
            passedTests += 1
        } else {
            print("❌ Переход не записан: было \(recordsBeforeTransition), стало \(recordsAfterTransition)")
        }
        
        // Тест 5: Получение записей за период
        print("\n🧪 Тест 5: Получение записей за период")
        let detailedRecords = statisticsManager.getActivityRecordsForDateRange(from: today, to: tomorrow)
        
        if detailedRecords.count >= 4 { // Минимум 4 записи (3 изначальные + 1 переход)
            let hasWorkingState = detailedRecords.contains { $0.state == .working }
            let hasAwayState = detailedRecords.contains { $0.state == .awayShort }
            
            if hasWorkingState && hasAwayState {
                print("✅ Детальные записи содержат ожидаемые состояния")
                passedTests += 1
            } else {
                print("❌ Детальные записи не содержат ожидаемые состояния")
            }
        } else {
            print("❌ Недостаточно детальных записей: \(detailedRecords.count)")
        }
        
        // Тест 6: Статистика за сегодня (быстрые методы)
        print("\n🧪 Тест 6: Статистика за сегодня")
        let todayStats = statisticsManager.getActivityStatsForToday()
        let todayQuality = statisticsManager.getActivityQualityForToday()
        
        if !todayStats.isEmpty && todayQuality >= 0 {
            print("✅ Быстрые методы статистики работают")
            passedTests += 1
        } else {
            print("❌ Проблемы с быстрыми методами статистики")
        }
        
        // Итоги
        print("\n==================================================")
        print("🧪 Результаты теста интеграции StatisticsManager:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Интеграция StatisticsManager работает")
        } else {
            print("❌ Есть проблемы с интеграцией StatisticsManager")
        }
        
        print("\n💡 Для проверки что тест ловит ошибки:")
        print("1. Сломайте метод recordActivityState() в StatisticsManager")
        print("2. Запустите тест - он должен упасть")
        print("3. Восстановите код и убедитесь что тест снова проходит")
        
        print("\n🔧 Этот тест проверяет интеграцию с 4-бандовой системой активности")
        print("🔧 Полная функциональность требует интеграции с ActivityStateManager")
        print("🔧 Но основная логика записи и получения статистики работает корректно!")
    }
}
