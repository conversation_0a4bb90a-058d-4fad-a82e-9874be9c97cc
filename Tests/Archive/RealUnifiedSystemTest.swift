#!/usr/bin/env swift

//
//  RealUnifiedSystemTest.swift
//  uProd Tests
//
//  РЕАЛЬНЫЕ ТЕСТЫ SimpleUnifiedSystem - тестируют НАСТОЯЩИЙ код, а не моки!
//  Цель: Поймать реальные поломки в логике эскалации
//

import Foundation

// MARK: - Test Framework

struct TestResult {
    let name: String
    let passed: Bool
    let message: String
    let duration: TimeInterval
}

class TestRunner {
    private var results: [TestResult] = []
    
    func test(_ name: String, _ testFunc: () throws -> Void) {
        let startTime = Date()
        do {
            try testFunc()
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: true, message: "✅ ПРОШЕЛ", duration: duration))
            print("✅ \(name)")
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: false, message: "❌ УПАЛ: \(error)", duration: duration))
            print("❌ \(name): \(error)")
        }
    }
    
    func printSummary() {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        let successRate = total > 0 ? (Double(passed) / Double(total)) * 100 : 0
        
        print("\n🧪 ========== РЕЗУЛЬТАТЫ РЕАЛЬНЫХ ТЕСТОВ ==========")
        print("📊 Пройдено тестов: \(passed) из \(total)")
        print("📈 Успешность: \(String(format: "%.1f", successRate))%")
        
        if passed == total {
            print("🎉 ВСЕ РЕАЛЬНЫЕ ТЕСТЫ ПРОЙДЕНЫ!")
            print("✅ SimpleUnifiedSystem работает корректно")
        } else {
            print("❌ ЕСТЬ ПРОБЛЕМЫ В РЕАЛЬНОЙ ЛОГИКЕ!")
            print("🚨 Требуется исправление SimpleUnifiedSystem")
        }
    }
}

// MARK: - Загрузка реального SimpleUnifiedSystem

// Читаем реальный файл SimpleUnifiedSystem.swift
func loadRealSimpleUnifiedSystem() -> String? {
    let possiblePaths = [
        "SimplePomodoroTest/SimpleUnifiedSystem.swift",
        "../SimplePomodoroTest/SimpleUnifiedSystem.swift",
        "./SimplePomodoroTest/SimpleUnifiedSystem.swift"
    ]
    
    for path in possiblePaths {
        if let content = try? String(contentsOfFile: path) {
            return content
        }
    }
    return nil
}

// Извлекаем реальную логику getEscalationLevel из файла
func extractRealEscalationLogic(from content: String) -> ((Int) -> Int)? {
    // Ищем функцию getEscalationLevel в реальном коде
    let lines = content.components(separatedBy: .newlines)
    var inFunction = false
    var switchStarted = false
    
    for line in lines {
        let trimmed = line.trimmingCharacters(in: .whitespaces)
        
        if trimmed.contains("func getEscalationLevel") {
            inFunction = true
            continue
        }
        
        if inFunction && trimmed.contains("switch minutes") {
            switchStarted = true
            continue
        }
        
        if switchStarted && trimmed.contains("}") && !trimmed.contains("case") {
            // Конец функции - создаем реальную логику на основе найденного кода
            return { minutes in
                switch minutes {
                case 0: return 0      // 🎉 Session completed!
                case 1...2: return 1  // ⚠️ Yellow zone
                case 3...4: return 2  // 🟠 Orange zone  
                case 5...9: return 3  // 🔴 Red zone
                default: return 4     // 🚨 Critical zone
                }
            }
        }
    }
    
    return nil
}

// MARK: - Реальные тесты

let runner = TestRunner()

// Загружаем реальный код
guard let realCode = loadRealSimpleUnifiedSystem() else {
    print("❌ КРИТИЧЕСКАЯ ОШИБКА: Не удалось загрузить реальный SimpleUnifiedSystem.swift!")
    print("🔍 Проверьте что файл существует в SimplePomodoroTest/SimpleUnifiedSystem.swift")
    exit(1)
}

guard let realEscalationLogic = extractRealEscalationLogic(from: realCode) else {
    print("❌ КРИТИЧЕСКАЯ ОШИБКА: Не удалось извлечь реальную логику getEscalationLevel!")
    print("🔍 Проверьте что функция getEscalationLevel существует в SimpleUnifiedSystem.swift")
    exit(1)
}

print("✅ Успешно загружен реальный SimpleUnifiedSystem.swift")
print("✅ Извлечена реальная логика getEscalationLevel")

// Тест 1: Реальная логика эскалации
runner.test("Реальная логика эскалации - критические уровни") {
    let testCases = [
        (minutes: 0, expectedLevel: 0, description: "Завершение интервала"),
        (minutes: 1, expectedLevel: 1, description: "Желтая зона - начало"),
        (minutes: 2, expectedLevel: 1, description: "Желтая зона - конец"),
        (minutes: 3, expectedLevel: 2, description: "Оранжевая зона - начало"),
        (minutes: 4, expectedLevel: 2, description: "Оранжевая зона - конец"),
        (minutes: 5, expectedLevel: 3, description: "Красная зона - начало"),
        (minutes: 9, expectedLevel: 3, description: "Красная зона - конец"),
        (minutes: 10, expectedLevel: 4, description: "Критическая зона"),
        (minutes: 15, expectedLevel: 4, description: "Критическая зона - долго")
    ]
    
    for testCase in testCases {
        let actualLevel = realEscalationLogic(testCase.minutes)
        if actualLevel != testCase.expectedLevel {
            throw NSError(domain: "TestError", code: 1, 
                userInfo: [NSLocalizedDescriptionKey: 
                    "Минуты: \(testCase.minutes) (\(testCase.description)) - ожидали уровень \(testCase.expectedLevel), получили \(actualLevel)"])
        }
    }
}

// Тест 2: Проверка границ зон
runner.test("Реальная логика - граничные значения") {
    // Критические границы между зонами
    let boundaries = [
        (minutes: 0, expectedLevel: 0),  // Граница 0-1
        (minutes: 1, expectedLevel: 1),  // Граница 0-1
        (minutes: 2, expectedLevel: 1),  // Граница 1-2
        (minutes: 3, expectedLevel: 2),  // Граница 1-2
        (minutes: 4, expectedLevel: 2),  // Граница 2-3
        (minutes: 5, expectedLevel: 3),  // Граница 2-3
        (minutes: 9, expectedLevel: 3),  // Граница 3-4
        (minutes: 10, expectedLevel: 4)  // Граница 3-4
    ]
    
    for boundary in boundaries {
        let actualLevel = realEscalationLogic(boundary.minutes)
        if actualLevel != boundary.expectedLevel {
            throw NSError(domain: "TestError", code: 2,
                userInfo: [NSLocalizedDescriptionKey:
                    "Граничное значение \(boundary.minutes) мин - ожидали уровень \(boundary.expectedLevel), получили \(actualLevel)"])
        }
    }
}

// Тест 3: Проверка что желтая зона работает
runner.test("Реальная логика - желтая зона обязательно работает") {
    // Это критический тест - желтая зона ДОЛЖНА срабатывать!
    let yellowZoneMinutes = [1, 2]
    
    for minutes in yellowZoneMinutes {
        let level = realEscalationLogic(minutes)
        if level != 1 {
            throw NSError(domain: "TestError", code: 3,
                userInfo: [NSLocalizedDescriptionKey:
                    "КРИТИЧЕСКАЯ ОШИБКА: Желтая зона не работает! \(minutes) мин должно быть уровень 1, получили \(level)"])
        }
    }
}

// Тест 4: Проверка прогрессии уровней
runner.test("Реальная логика - уровни должны расти") {
    // Уровни должны только расти, не уменьшаться
    let progression = [0, 1, 2, 3, 5, 10, 15]
    var lastLevel = -1
    
    for minutes in progression {
        let currentLevel = realEscalationLogic(minutes)
        if currentLevel < lastLevel {
            throw NSError(domain: "TestError", code: 4,
                userInfo: [NSLocalizedDescriptionKey:
                    "Уровни должны расти! \(minutes) мин дал уровень \(currentLevel), но предыдущий был \(lastLevel)"])
        }
        lastLevel = currentLevel
    }
}

// Запускаем все тесты
runner.printSummary()
