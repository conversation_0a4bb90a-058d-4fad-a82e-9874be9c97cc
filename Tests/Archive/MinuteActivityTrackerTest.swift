#!/usr/bin/env swift

import Foundation

// Добавляем путь к основному коду
let projectPath = "/Users/<USER>/EVO/uProd/SimplePomodoroTest"
let testPath = "/Users/<USER>/EVO/uProd/Tests"

// Функция для загрузки исходного кода
func loadSourceFile(_ filename: String) -> String? {
    let fullPath = "\(projectPath)/\(filename)"
    return try? String(contentsOfFile: fullPath)
}

// Загружаем необходимые файлы
guard let trackerCode = loadSourceFile("MinuteActivityTracker.swift") else {
    print("❌ Не удалось загрузить MinuteActivityTracker.swift")
    exit(1)
}

guard let unifiedCheckerCode = loadSourceFile("UnifiedActivityChecker.swift") else {
    print("❌ Не удалось загрузить UnifiedActivityChecker.swift")
    exit(1)
}

// Выполняем код (имитируем import)
// В реальном Swift проекте это было бы через import
// Здесь мы просто убеждаемся что код компилируется

// Убираем print на верхнем уровне - переносим в функцию

// MARK: - Mock UnifiedActivityChecker для тестирования

class MockUnifiedActivityChecker {
    static let shared = MockUnifiedActivityChecker()
    
    private var forcedResult: Bool?
    
    func isUserCurrentlyActive() -> Bool {
        return forcedResult ?? false
    }
    
    func setForcedResult(_ result: Bool?) {
        forcedResult = result
    }
}

// MARK: - Тестовая версия MinuteActivityTracker

class TestableMinuteActivityTracker {
    
    // MARK: - Configuration
    
    private let bandsPerMinute = 4
    private let bandDurationSeconds: TimeInterval = 15.0
    private let activityThreshold: TimeInterval = 15.0
    
    // MARK: - State
    
    private var currentBand = 0
    private var currentBandStartTime = Date()
    private var currentMinuteBands: [Bool] = [false, false, false, false]
    private var bandTimer: Timer?
    private var isActive = false
    
    // MARK: - Callbacks
    
    var onMinuteCompleted: ((Bool) -> Void)?
    
    // MARK: - Dependencies (Mock)
    
    private let activityChecker = MockUnifiedActivityChecker.shared
    
    // MARK: - Public Methods
    
    func startTracking() {
        guard !isActive else { return }
        
        isActive = true
        resetCurrentMinute()
        startBandTimer()
        
        print("🎯 TestableMinuteActivityTracker: Запущено отслеживание 4-бандовой активности")
    }
    
    func stopTracking() {
        guard isActive else { return }
        
        isActive = false
        bandTimer?.invalidate()
        bandTimer = nil
        
        print("🎯 TestableMinuteActivityTracker: Остановлено отслеживание активности")
    }
    
    func wasCurrentMinuteActive() -> Bool {
        let completedBands = Array(currentMinuteBands.prefix(currentBand))
        let hasActivity = completedBands.contains(true)
        
        return hasActivity
    }
    
    func forceCompleteCurrentMinute() -> Bool {
        checkCurrentBandActivity()
        
        let isMinuteActive = currentMinuteBands.contains(true)
        
        print("🎯 TestableMinuteActivityTracker: Принудительное завершение минуты - банды: \(currentMinuteBands), результат: \(isMinuteActive)")
        
        resetCurrentMinute()
        
        return isMinuteActive
    }
    
    func getCurrentBandsState() -> (currentBand: Int, bands: [Bool], isActive: Bool) {
        return (currentBand, currentMinuteBands, isActive)
    }
    
    func simulateActivityInCurrentBand() {
        guard currentBand < bandsPerMinute else { return }
        
        currentMinuteBands[currentBand] = true
        print("🧪 TestableMinuteActivityTracker: Симулирована активность в банде \(currentBand)")
    }
    
    func setForcedBandState(band: Int, isActive: Bool) {
        guard band >= 0 && band < bandsPerMinute else { return }
        
        currentMinuteBands[band] = isActive
        print("🧪 TestableMinuteActivityTracker: Принудительно установлен банд \(band) = \(isActive)")
    }
    
    // Дополнительные методы для тестирования
    func simulateBandCompletion() {
        checkCurrentBandActivity()
        currentBand += 1
        currentBandStartTime = Date()
        
        if currentBand >= bandsPerMinute {
            completeCurrentMinute()
        }
    }
    
    func setActivityChecker(result: Bool?) {
        activityChecker.setForcedResult(result)
    }
    
    // MARK: - Private Methods
    
    private func startBandTimer() {
        bandTimer?.invalidate()
        
        // В тестах используем более короткий интервал
        bandTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.onBandTimerFired()
        }
    }
    
    private func onBandTimerFired() {
        checkCurrentBandActivity()
        
        currentBand += 1
        currentBandStartTime = Date()
        
        if currentBand >= bandsPerMinute {
            completeCurrentMinute()
        }
    }
    
    private func checkCurrentBandActivity() {
        guard currentBand < bandsPerMinute else { return }
        
        let isActive = activityChecker.isUserCurrentlyActive()
        currentMinuteBands[currentBand] = isActive
        
        print("🎯 TestableMinuteActivityTracker: Банд \(currentBand) - активность: \(isActive ? "ДА" : "НЕТ")")
    }
    
    private func completeCurrentMinute() {
        let isMinuteActive = currentMinuteBands.contains(true)
        
        print("🎯 TestableMinuteActivityTracker: ✅ Минута завершена - банды: \(currentMinuteBands), результат: \(isMinuteActive ? "АКТИВНА" : "НЕАКТИВНА")")
        
        onMinuteCompleted?(isMinuteActive)
        resetCurrentMinute()
    }
    
    private func resetCurrentMinute() {
        currentBand = 0
        currentBandStartTime = Date()
        currentMinuteBands = [false, false, false, false]
    }
    
    deinit {
        stopTracking()
    }
}

// MARK: - Test Functions

func testBasicFunctionality() {
    print("\n🧪 Тест 1: Базовая функциональность")
    
    let tracker = TestableMinuteActivityTracker()
    
    // Проверяем начальное состояние
    let initialState = tracker.getCurrentBandsState()
    assert(initialState.currentBand == 0, "Начальный банд должен быть 0")
    assert(initialState.bands == [false, false, false, false], "Начальные банды должны быть неактивными")
    assert(!initialState.isActive, "Трекер не должен быть активен изначально")
    
    // Запускаем трекер
    tracker.startTracking()
    let activeState = tracker.getCurrentBandsState()
    assert(activeState.isActive, "Трекер должен быть активен после запуска")
    
    tracker.stopTracking()
    print("✅ Базовая функциональность работает")
}

func testActivityDetection() {
    print("\n🧪 Тест 2: Обнаружение активности")
    
    let tracker = TestableMinuteActivityTracker()
    tracker.startTracking()
    
    // Тест 1: Нет активности
    tracker.setActivityChecker(result: false)
    tracker.simulateBandCompletion()
    
    let state1 = tracker.getCurrentBandsState()
    assert(state1.bands[0] == false, "Банд 0 должен быть неактивным")
    assert(!tracker.wasCurrentMinuteActive(), "Минута не должна быть активной")
    
    // Тест 2: Есть активность
    tracker.setActivityChecker(result: true)
    tracker.simulateBandCompletion()
    
    let state2 = tracker.getCurrentBandsState()
    assert(state2.bands[1] == true, "Банд 1 должен быть активным")
    assert(tracker.wasCurrentMinuteActive(), "Минута должна быть активной")
    
    tracker.stopTracking()
    print("✅ Обнаружение активности работает")
}

func testMinuteCompletion() {
    print("\n🧪 Тест 3: Завершение минуты")
    
    let tracker = TestableMinuteActivityTracker()
    var completedMinutes: [Bool] = []
    
    tracker.onMinuteCompleted = { isActive in
        completedMinutes.append(isActive)
        print("📊 Минута завершена: \(isActive ? "АКТИВНА" : "НЕАКТИВНА")")
    }
    
    tracker.startTracking()
    
    // Симулируем минуту с активностью во втором банде
    tracker.setActivityChecker(result: false)
    tracker.simulateBandCompletion() // Банд 0: неактивен
    
    tracker.setActivityChecker(result: true)
    tracker.simulateBandCompletion() // Банд 1: активен
    
    tracker.setActivityChecker(result: false)
    tracker.simulateBandCompletion() // Банд 2: неактивен
    tracker.simulateBandCompletion() // Банд 3: неактивен (завершает минуту)
    
    assert(completedMinutes.count == 1, "Должна быть завершена 1 минута")
    assert(completedMinutes[0] == true, "Минута должна быть активной")
    
    tracker.stopTracking()
    print("✅ Завершение минуты работает")
}

func testForceCompletion() {
    print("\n🧪 Тест 4: Принудительное завершение")
    
    let tracker = TestableMinuteActivityTracker()
    tracker.startTracking()
    
    // Устанавливаем активность в банде 1
    tracker.setForcedBandState(band: 1, isActive: true)
    
    // Принудительно завершаем минуту
    let result = tracker.forceCompleteCurrentMinute()
    
    assert(result == true, "Принудительное завершение должно вернуть true")
    
    // Проверяем что состояние сброшено
    let state = tracker.getCurrentBandsState()
    assert(state.currentBand == 0, "Банд должен быть сброшен к 0")
    assert(state.bands == [false, false, false, false], "Банды должны быть сброшены")
    
    tracker.stopTracking()
    print("✅ Принудительное завершение работает")
}

func testEdgeCases() {
    print("\n🧪 Тест 5: Граничные случаи")
    
    let tracker = TestableMinuteActivityTracker()
    tracker.startTracking()
    
    // Тест: Все банды активны
    for i in 0..<4 {
        tracker.setForcedBandState(band: i, isActive: true)
    }
    let result1 = tracker.forceCompleteCurrentMinute()
    assert(result1 == true, "Все активные банды должны дать активную минуту")
    
    // Тест: Все банды неактивны
    let result2 = tracker.forceCompleteCurrentMinute()
    assert(result2 == false, "Все неактивные банды должны дать неактивную минуту")
    
    // Тест: Только последний банд активен
    tracker.setForcedBandState(band: 3, isActive: true)
    let result3 = tracker.forceCompleteCurrentMinute()
    assert(result3 == true, "Активность в любом банде должна дать активную минуту")
    
    tracker.stopTracking()
    print("✅ Граничные случаи работают")
}

// MARK: - Запуск тестов

func runAllTests() {
    print("🚀 Запуск тестов MinuteActivityTracker")
    print("=====================================")
    
    testBasicFunctionality()
    testActivityDetection()
    testMinuteCompletion()
    testForceCompletion()
    testEdgeCases()
    
    print("\n🎉 Все тесты MinuteActivityTracker прошли успешно!")
    print("=====================================")
}

// Запускаем тесты
@main
struct TestRunner {
    static func main() {
        runAllTests()
    }
}
