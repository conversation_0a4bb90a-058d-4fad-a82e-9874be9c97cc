import Foundation

/// Тест интеграции системы полной сессии с системой раннего вовлечения
@main struct FullSessionIntegrationTest {
    static func main() {
        print("🧪 Тест интеграции системы полной сессии")
        print("=" * 50)
        
        // Тест 1: Создание системы полной сессии
        testFullSessionSystemCreation()
        
        // Тест 2: Проверка логики показа кнопки
        testShouldShowFullSessionLogic()
        
        // Тест 3: Тест записи попыток и завершений
        testFullSessionStatistics()
        
        // Тест 4: Тест механизма блокировки
        testBlockingMechanism()
        
        // Тест 5: Тест восстановления после блокировки
        testRestorationMechanism()
        
        print("\n✅ Все тесты интеграции полной сессии пройдены!")
    }
    
    static func testFullSessionSystemCreation() {
        print("\n🔧 Тест 1: Создание системы полной сессии")
        
        // Создаем экземпляр системы
        let system = FullSessionSystem.shared
        let stats = system.getStats()
        
        // Проверяем начальное состояние
        assert(stats.totalAttempts == 0, "Начальное количество попыток должно быть 0")
        assert(stats.successfulCompletions == 0, "Начальное количество успехов должно быть 0")
        assert(!stats.isBlocked, "Система не должна быть заблокирована изначально")
        
        print("✅ Система полной сессии создана корректно")
    }
    
    static func testShouldShowFullSessionLogic() {
        print("\n🎯 Тест 2: Логика показа кнопки полной сессии")
        
        let system = FullSessionSystem.shared
        
        // Тест с разными размерами планки
        let testCases = [
            (bar: 3 * 60, shouldShow: true, reason: "Маленькая планка 3 мин"),
            (bar: 15 * 60, shouldShow: true, reason: "Средняя планка 15 мин"),
            (bar: 30 * 60, shouldShow: true, reason: "Большая планка 30 мин"),
            (bar: 52 * 60, shouldShow: false, reason: "Максимальная планка 52 мин"),
            (bar: 60 * 60, shouldShow: false, reason: "Планка больше максимума")
        ]
        
        for testCase in testCases {
            let result = system.shouldShowFullSession(currentBar: TimeInterval(testCase.bar))
            assert(result == testCase.shouldShow, 
                   "Ошибка для \(testCase.reason): ожидали \(testCase.shouldShow), получили \(result)")
            print("  ✓ \(testCase.reason): \(result ? "показывать" : "не показывать")")
        }
        
        print("✅ Логика показа кнопки работает корректно")
    }
    
    static func testFullSessionStatistics() {
        print("\n📊 Тест 3: Статистика полной сессии")
        
        let system = FullSessionSystem.shared
        
        // Сбрасываем статистику для чистого теста
        system.resetStatistics()
        
        // Записываем несколько попыток
        system.recordFullSessionAttempt()
        system.recordFullSessionAttempt()
        system.recordFullSessionAttempt()
        
        var stats = system.getStats()
        assert(stats.totalAttempts == 3, "Должно быть 3 попытки")
        assert(stats.successfulCompletions == 0, "Пока нет завершений")
        
        // Записываем успешное завершение
        system.recordFullSessionCompletion(context: .earlyEngagement(currentBar: 25 * 60))
        
        stats = system.getStats()
        assert(stats.totalAttempts == 3, "Попытки не должны измениться")
        assert(stats.successfulCompletions == 1, "Должно быть 1 успешное завершение")
        
        print("✅ Статистика записывается корректно")
    }
    
    static func testBlockingMechanism() {
        print("\n🚫 Тест 4: Механизм блокировки")
        
        let system = FullSessionSystem.shared
        system.resetStatistics()
        
        // Записываем много неудачных попыток
        for _ in 1...10 {
            system.recordFullSessionAttempt()
            system.recordFullSessionFailure()
        }
        
        let stats = system.getStats()
        let successRate = Double(stats.successfulCompletions) / Double(stats.totalAttempts)
        
        print("  Попыток: \(stats.totalAttempts)")
        print("  Успехов: \(stats.successfulCompletions)")
        print("  Процент успеха: \(Int(successRate * 100))%")
        print("  Заблокирована: \(stats.isBlocked)")
        
        // При 0% успеха после 10 попыток система должна заблокироваться
        assert(stats.isBlocked, "Система должна быть заблокирована при низком проценте успеха")
        
        // Проверяем, что кнопка не показывается
        let shouldShow = system.shouldShowFullSession(currentBar: 25 * 60)
        assert(!shouldShow, "Кнопка не должна показываться при блокировке")
        
        print("✅ Механизм блокировки работает корректно")
    }
    
    static func testRestorationMechanism() {
        print("\n🔄 Тест 5: Механизм восстановления")
        
        let system = FullSessionSystem.shared
        
        // Система уже заблокирована из предыдущего теста
        var stats = system.getStats()
        assert(stats.isBlocked, "Система должна быть заблокирована")
        
        // Симулируем успешные обычные интервалы
        for _ in 1...5 {
            system.recordSuccessfulRegularInterval(duration: 30 * 60) // 30 минут
        }
        
        stats = system.getStats()
        print("  После 5 успешных интервалов:")
        print("  Заблокирована: \(stats.isBlocked)")
        
        // Система должна разблокироваться
        assert(!stats.isBlocked, "Система должна разблокироваться после успешных интервалов")
        
        // Проверяем, что кнопка снова показывается
        let shouldShow = system.shouldShowFullSession(currentBar: 25 * 60)
        assert(shouldShow, "Кнопка должна снова показываться после восстановления")
        
        print("✅ Механизм восстановления работает корректно")
    }
}

// Расширение для FullSessionSystem с методами тестирования
extension FullSessionSystem {
    func resetStatistics() {
        statistics = FullSessionStats()
        recentSuccessfulIntervals = []
        saveStatistics()
    }
    
    func recordFullSessionFailure() {
        // Метод для тестирования - не увеличивает счетчик завершений
        // Блокировка происходит автоматически при низком проценте успеха
    }
}
