#!/usr/bin/env swift

import Foundation

// ТЕСТ РЕАЛЬНОЙ ЛОГИКИ APPDELEGATE
// Этот тест проверяет что реальная логика в AppDelegate работает правильно

print("🔍 ТЕСТ РЕАЛЬНОЙ ЛОГИКИ APPDELEGATE")
print(String(repeating: "=", count: 50))

// Читаем реальный файл AppDelegate.swift
let currentDir = FileManager.default.currentDirectoryPath
let appDelegateFile = "\(currentDir)/SimplePomodoroTest/AppDelegate.swift"

guard let content = try? String(contentsOfFile: appDelegateFile) else {
    print("❌ Не удалось прочитать AppDelegate.swift")
    exit(1)
}

var testsTotal = 0
var testsPassed = 0

func runTest(_ name: String, _ test: () -> Bool) {
    testsTotal += 1
    print("\n🧪 Тест: \(name)")
    
    if test() {
        testsPassed += 1
        print("✅ ПРОШЕЛ")
    } else {
        print("❌ ПРОВАЛЕН")
    }
}

// Тест 1: Проверяем что нет сломанной логики
runTest("Отсутствие сломанной логики") {
    let hasBrokenLogic = content.contains("🐛 ВРЕМЕННО СЛОМАННАЯ ЛОГИКА") ||
                        content.contains("if false { // ВСЕГДА false") ||
                        content.contains("СТАРАЯ СЛОМАННАЯ ЛОГИКА")
    
    if hasBrokenLogic {
        print("   ❌ Обнаружена сломанная логика в AppDelegate!")
        return false
    } else {
        print("   ✅ Сломанная логика не обнаружена")
        return true
    }
}

// Тест 2: Проверяем наличие правильной логики
runTest("Наличие правильной логики проверки окна") {
    let hasCorrectLogic = content.contains("existingWindow.isVisible") &&
                         content.contains("СНАЧАЛА проверяем существующее окно ПЕРЕД созданием нового")
    
    if hasCorrectLogic {
        print("   ✅ Правильная логика проверки окна найдена")
        return true
    } else {
        print("   ❌ Правильная логика проверки окна НЕ найдена")
        return false
    }
}

// Тест 3: Проверяем что логика в правильном порядке
runTest("Правильный порядок проверок") {
    // Ищем позицию комментария и проверки
    let commentRange = content.range(of: "СНАЧАЛА проверяем существующее окно ПЕРЕД созданием нового")
    let checkRange = content.range(of: "if existingWindow.isVisible")
    
    if let commentPos = commentRange?.lowerBound,
       let checkPos = checkRange?.lowerBound {
        
        if commentPos < checkPos {
            print("   ✅ Комментарий идет перед проверкой - порядок правильный")
            return true
        } else {
            print("   ❌ Неправильный порядок: проверка идет перед комментарием")
            return false
        }
    } else {
        print("   ❌ Не найдены необходимые элементы логики")
        return false
    }
}

// Тест 4: Проверяем что нет старой логики создания окна
runTest("Отсутствие старой логики создания окна") {
    // Ищем паттерны старой сломанной логики
    let hasOldLogic = content.contains("проверяем окно ПОСЛЕ создания нового") ||
                     content.contains("создаем окно ПЕРЕД проверкой существующего")
    
    if hasOldLogic {
        print("   ❌ Обнаружена старая сломанная логика!")
        return false
    } else {
        print("   ✅ Старая сломанная логика не найдена")
        return true
    }
}

// Тест 5: Проверяем структуру метода showUnifiedReminder
runTest("Структура метода showUnifiedReminder") {
    // Ищем метод showUnifiedReminder
    let methodPattern = "func showUnifiedReminder"
    
    if content.contains(methodPattern) {
        print("   ✅ Метод showUnifiedReminder найден")
        
        // Проверяем что в методе есть правильная логика
        let hasWindowLogic = content.contains("modernCompletionWindow") &&
                           content.contains("forceNew")
        
        if hasWindowLogic {
            print("   ✅ Метод содержит логику работы с окном")
            return true
        } else {
            print("   ❌ Метод НЕ содержит логику работы с окном")
            return false
        }
    } else {
        print("   ❌ Метод showUnifiedReminder НЕ найден")
        return false
    }
}

// Тест 6: Проверяем что есть логирование
runTest("Наличие логирования") {
    let hasLogging = content.contains("logInfo(\"Window\"") &&
                    content.contains("Окно уже существует и ВИДИМО")
    
    if hasLogging {
        print("   ✅ Логирование найдено")
        return true
    } else {
        print("   ❌ Логирование НЕ найдено")
        return false
    }
}

// ИТОГИ
print("\n" + String(repeating: "=", count: 50))
print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ РЕАЛЬНОЙ ЛОГИКИ")
print(String(repeating: "=", count: 50))
print("✅ Прошло: \(testsPassed)/\(testsTotal)")

if testsPassed == testsTotal {
    print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ!")
    print("✅ Реальная логика AppDelegate работает корректно")
    exit(0)
} else {
    print("❌ ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!")
    print("🚨 Реальная логика AppDelegate сломана")
    exit(1)
}
