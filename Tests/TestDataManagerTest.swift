import Foundation

/// Тест для TestDataManager - системы управления тестовыми данными
/// Проверяет корректность добавления и удаления тестовых интервалов
struct TestDataManagerTest {
    static func runTests() {
        print("🧪 Запуск тестов TestDataManager...")
        
        // Сохраняем исходное состояние
        let originalIntervals = getAllIntervals()
        
        do {
            try testAddTestInterval()
            try testClearTestData()
            try testTestDataInfo()
            try testScenarios()
            
            print("✅ Все тесты TestDataManager прошли успешно!")
        } catch {
            print("❌ Тест провален: \(error)")
            exit(1)
        }
        
        // Восстанавливаем исходное состояние
        restoreIntervals(originalIntervals)
    }
    
    // MARK: - Тесты
    
    static func testAddTestInterval() throws {
        print("🔍 Тест добавления тестового интервала...")
        
        // Очищаем тестовые данные
        clearAllTestData()
        
        let beforeCount = getTestIntervalsCount()
        
        // Добавляем тестовый интервал за вчера
        addTestInterval(daysAgo: 1, duration: 25)
        
        let afterCount = getTestIntervalsCount()
        
        guard afterCount == beforeCount + 1 else {
            throw TestError("Ожидался 1 тестовый интервал, получено: \(afterCount)")
        }
        
        // Проверяем что интервал действительно тестовый
        let testIntervals = getAllIntervals().filter { $0.intervalType == "test" }
        guard testIntervals.count == 1 else {
            throw TestError("Ожидался 1 интервал с типом 'test', получено: \(testIntervals.count)")
        }
        
        let testInterval = testIntervals[0]
        guard testInterval.duration == 25 * 60 else {
            throw TestError("Ожидалась продолжительность 1500 секунд, получено: \(testInterval.duration)")
        }
        
        print("✅ Тестовый интервал добавлен корректно")
    }
    
    static func testClearTestData() throws {
        print("🔍 Тест очистки тестовых данных...")
        
        // Добавляем реальный интервал
        addRealInterval(duration: 30)
        
        // Добавляем тестовые интервалы
        addTestInterval(daysAgo: 1, duration: 25)
        addTestInterval(daysAgo: 2, duration: 25)
        
        let beforeRealCount = getAllIntervals().filter { $0.intervalType != "test" }.count
        let beforeTestCount = getTestIntervalsCount()
        
        guard beforeTestCount >= 2 else {
            throw TestError("Ожидалось минимум 2 тестовых интервала, получено: \(beforeTestCount)")
        }
        
        // Очищаем только тестовые данные
        clearAllTestData()
        
        let afterRealCount = getAllIntervals().filter { $0.intervalType != "test" }.count
        let afterTestCount = getTestIntervalsCount()
        
        guard afterTestCount == 0 else {
            throw TestError("Ожидалось 0 тестовых интервалов после очистки, получено: \(afterTestCount)")
        }
        
        guard afterRealCount == beforeRealCount else {
            throw TestError("Реальные интервалы не должны были удалиться. До: \(beforeRealCount), После: \(afterRealCount)")
        }
        
        print("✅ Очистка тестовых данных работает корректно")
    }
    
    static func testTestDataInfo() throws {
        print("🔍 Тест получения информации о тестовых данных...")
        
        clearAllTestData()
        
        // Проверяем пустое состояние
        let emptyInfo = getTestDataInfo()
        guard emptyInfo.contains("Тестовых данных нет") else {
            throw TestError("Ожидалось сообщение о пустых данных, получено: \(emptyInfo)")
        }
        
        // Добавляем тестовые данные
        addTestInterval(daysAgo: 1, duration: 25)
        addTestInterval(daysAgo: 2, duration: 25)
        
        let infoWithData = getTestDataInfo()
        guard infoWithData.contains("Тестовых интервалов: 2") else {
            throw TestError("Ожидалось 2 тестовых интервала в информации, получено: \(infoWithData)")
        }
        
        print("✅ Информация о тестовых данных корректна")
    }
    
    static func testScenarios() throws {
        print("🔍 Тест создания сценариев...")
        
        clearAllTestData()
        
        // Тест сценария 3 (ЧТ+ПТ+СБ, сегодня НЕ работал)
        createSpecificScenario(3)
        
        let testCount = getTestIntervalsCount()
        guard testCount == 3 else {
            throw TestError("Сценарий 3 должен создать 3 интервала, получено: \(testCount)")
        }
        
        // Проверяем что интервалы за правильные дни
        let testIntervals = getAllIntervals().filter { $0.intervalType == "test" }
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        var daysFound = Set<Int>()
        for interval in testIntervals {
            let intervalDay = calendar.startOfDay(for: interval.date)
            let daysDiff = calendar.dateComponents([.day], from: intervalDay, to: today).day ?? 0
            daysFound.insert(daysDiff)
        }

        let expectedDays = Set([1, 2, 3]) // вчера, позавчера, 3 дня назад
        guard daysFound == expectedDays else {
            throw TestError("Ожидались дни \(expectedDays), получены: \(daysFound)")
        }
        
        print("✅ Сценарии создаются корректно")
    }
    
    // MARK: - Вспомогательные методы
    
    static func addTestInterval(daysAgo: Int, duration: Int) {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDate = calendar.date(byAdding: .day, value: -daysAgo, to: today) ?? today
        let sessionDate = calendar.date(bySettingHour: 14, minute: 0, second: 0, of: targetDate) ?? targetDate

        recordTestInterval(date: sessionDate, duration: TimeInterval(duration * 60))
    }
    
    static func addRealInterval(duration: Int) {
        recordCompletedInterval(duration: TimeInterval(duration * 60), intervalType: "formal")
    }
    
    static func clearAllTestData() {
        clearTestIntervals()
    }
    
    static func getTestIntervalsCount() -> Int {
        return getAllIntervals().filter { $0.intervalType == "test" }.count
    }
    
    static func getTestDataInfo() -> String {
        let testCount = getTestIntervalsCount()
        
        if testCount == 0 {
            return "📊 Тестовых данных нет"
        }
        
        return "📊 Тестовых интервалов: \(testCount)"
    }
    
    static func createSpecificScenario(_ scenario: Int) {
        clearAllTestData()
        
        switch scenario {
        case 3:
            // Работал ЧТ+ПТ+СБ, сегодня НЕ работал
            addTestInterval(daysAgo: 3, duration: 25) // чт
            addTestInterval(daysAgo: 2, duration: 25) // пт
            addTestInterval(daysAgo: 1, duration: 25) // сб
        default:
            break
        }
    }
    
    // MARK: - Интеграция с StatisticsManager
    
    static func getAllIntervals() -> [CompletedInterval] {
        let data = UserDefaults.standard.data(forKey: "completedIntervals") ?? Data()
        return (try? JSONDecoder().decode([CompletedInterval].self, from: data)) ?? []
    }
    
    static func saveIntervals(_ intervals: [CompletedInterval]) {
        let data = (try? JSONEncoder().encode(intervals)) ?? Data()
        UserDefaults.standard.set(data, forKey: "completedIntervals")
    }
    
    static func restoreIntervals(_ intervals: [CompletedInterval]) {
        saveIntervals(intervals)
    }
    
    static func recordTestInterval(date: Date, duration: TimeInterval) {
        let interval = CompletedInterval(date: date, duration: duration, projectId: nil, intervalType: "test")
        var intervals = getAllIntervals()
        intervals.append(interval)
        saveIntervals(intervals)
    }
    
    static func recordCompletedInterval(duration: TimeInterval, intervalType: String) {
        let interval = CompletedInterval(date: Date(), duration: duration, projectId: nil, intervalType: intervalType)
        var intervals = getAllIntervals()
        intervals.append(interval)
        saveIntervals(intervals)
    }
    
    static func clearTestIntervals() {
        var intervals = getAllIntervals()
        intervals = intervals.filter { $0.intervalType != "test" }
        saveIntervals(intervals)
    }
}

// MARK: - Структуры данных

struct CompletedInterval: Codable {
    let date: Date
    let duration: TimeInterval
    let projectId: UUID?
    let intervalType: String
}

struct TestError: Error {
    let message: String
    
    init(_ message: String) {
        self.message = message
    }
}

extension TestError: LocalizedError {
    var errorDescription: String? {
        return message
    }
}

// Запуск тестов
TestDataManagerTest.runTests()
