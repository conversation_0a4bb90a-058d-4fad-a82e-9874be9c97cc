import Foundation

/// Тест исправления округления в градационной системе
struct RoundingFixTest {
    static func main() {
        print("🧪 Тест исправления округления в GradualGrowthSystem")
        print(String(repeating: "=", count: 50))
        
        testRoundingFix()
        testGrowthProgression()
        
        print("\n✅ Все тесты пройдены!")
    }
    
    /// Тестирует исправление округления
    static func testRoundingFix() {
        print("\n📊 Тест округления:")
        
        let testCases = [
            (3, 1.60, 5),   // 3 * 1.60 = 4.8 → должно быть 5
            (4, 1.60, 6),   // 4 * 1.60 = 6.4 → должно быть 6  
            (5, 1.60, 8),   // 5 * 1.60 = 8.0 → должно быть 8
            (6, 1.60, 10),  // 6 * 1.60 = 9.6 → должно быть 10
            (7, 1.60, 11),  // 7 * 1.60 = 11.2 → должно быть 11
        ]
        
        for (input, multiplier, expected) in testCases {
            let calculated = Double(input) * multiplier
            let rounded = Int(round(calculated))
            
            print("  \(input) × \(multiplier) = \(calculated) → \(rounded) мин", terminator: "")
            
            if rounded == expected {
                print(" ✅")
            } else {
                print(" ❌ (ожидали \(expected))")
                fatalError("Тест округления провален!")
            }
        }
    }
    
    /// Тестирует прогрессию роста с 3 до 52 минут
    static func testGrowthProgression() {
        print("\n🚀 Тест прогрессии роста:")
        
        var currentBar = 3
        var day = 1
        
        print("  День \(day): \(currentBar) мин (старт)")
        
        while currentBar < 52 && day < 30 {
            let newBar = calculateGrowth(currentBarMinutes: currentBar)
            day += 1
            
            let growth = newBar - currentBar
            let percent = Int(Double(growth) / Double(currentBar) * 100)
            
            print("  День \(day): \(currentBar) → \(newBar) мин (+\(growth), +\(percent)%)")
            
            currentBar = newBar
            
            if currentBar >= 52 {
                print("  🎯 Достигли максимума 52 минуты за \(day-1) дней!")
                break
            }
        }
        
        // Проверяем что достигли 52 минуты быстро
        assert(day <= 15, "❌ Слишком медленный рост! Достигли 52 мин за \(day-1) дней")
        assert(currentBar >= 52, "❌ Не достигли 52 минут!")
    }
    
    /// Копия функции из GradualGrowthSystem для тестирования
    static func calculateGrowth(currentBarMinutes: Int) -> Int {
        let newBar: Int
        
        switch currentBarMinutes {
        case 3...7:
            newBar = Int(round(Double(currentBarMinutes) * 1.60))
        case 8...15:
            newBar = Int(round(Double(currentBarMinutes) * 1.40))
        case 16...25:
            newBar = Int(round(Double(currentBarMinutes) * 1.25))
        case 26...40:
            newBar = Int(round(Double(currentBarMinutes) * 1.15))
        case 41...52:
            newBar = Int(round(Double(currentBarMinutes) * 1.10))
        default:
            newBar = currentBarMinutes + 1
        }
        
        return min(newBar, 52)
    }
}

// Запускаем тест
RoundingFixTest.main()
