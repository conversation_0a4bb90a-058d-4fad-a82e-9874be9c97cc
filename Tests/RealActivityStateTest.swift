import Foundation

/// РЕАЛЬНЫЙ ТЕСТ ActivityStateManager БЕЗ МОКОВ
/// Проверяем что система состояний работает корректно

@main
struct RealActivityStateTest {
    static func main() {
        print("🚀 РЕАЛЬНЫЙ ТЕСТ ActivityStateManager")
        print(String(repeating: "=", count: 60))
        
        // Создаем РЕАЛЬНЫЙ объект
        let stateManager = ActivityStateManager()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Запуск системы
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Запуск системы")
        
        stateManager.start()
        let startInfo = stateManager.getCurrentStateInfo()

        if startInfo.state == .working {
            print("✅ ПРОЙДЕН: Система запускается в состоянии working")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Система НЕ запускается в состоянии working")
            print("   Состояние: \(startInfo.state)")
        }
        
        // СЦЕНАРИЙ 2: Симуляция активности
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Симуляция активности")
        
        stateManager.simulateActivity()
        let afterActivityInfo = stateManager.getCurrentStateInfo()

        if afterActivityInfo.state == .working {
            print("✅ ПРОЙДЕН: После активности остается в состоянии working")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: После активности состояние изменилось неожиданно")
            print("   Состояние: \(afterActivityInfo.state)")
        }
        
        // СЦЕНАРИЙ 3: Короткая неактивность (1.5 минуты)
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Короткая неактивность (1.5 минуты)")

        stateManager.simulateInactivity(duration: 1.5 * 60) // 1.5 минуты (меньше 2 минут)
        let shortInactivityInfo = stateManager.getCurrentStateInfo()

        if shortInactivityInfo.state == .awayShort {
            print("✅ ПРОЙДЕН: Короткая неактивность переводит в awayShort")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Короткая неактивность НЕ переводит в awayShort")
            print("   Состояние: \(shortInactivityInfo.state)")
        }
        
        // СЦЕНАРИЙ 4: Возврат после короткой неактивности
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Возврат после короткой неактивности")
        
        stateManager.simulateActivity()
        let returnInfo = stateManager.getCurrentStateInfo()

        if returnInfo.state == .working {
            print("✅ ПРОЙДЕН: Возврат после короткой неактивности переводит в working")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Возврат после короткой неактивности НЕ работает")
            print("   Состояние: \(returnInfo.state)")
        }
        
        // СЦЕНАРИЙ 5: Длительная неактивность (20 минут)
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Длительная неактивность (20 минут)")
        
        stateManager.simulateInactivity(duration: 20 * 60) // 20 минут
        let longInactivityInfo = stateManager.getCurrentStateInfo()

        if longInactivityInfo.state == .awayVeryLong {
            print("✅ ПРОЙДЕН: Длительная неактивность переводит в awayVeryLong")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Длительная неактивность НЕ переводит в awayVeryLong")
            print("   Состояние: \(longInactivityInfo.state)")
        }
        
        // СЦЕНАРИЙ 6: Остановка системы
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Остановка системы")
        
        stateManager.stop()

        // После остановки проверяем что система больше не активна через debug info
        let debugInfo = stateManager.getDebugInfo()

        if debugInfo.contains("Активен: false") {
            print("✅ ПРОЙДЕН: Остановка системы работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Остановка системы НЕ работает")
            print("   Debug: \(debugInfo)")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ ActivityStateManager работает корректно")
            
            // Проверка обнаружения поломок
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В ActivityStateManager!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Откройте SimplePomodoroTest/ActivityStateManager.swift")
        print("2. В методе updateState() найдите строки с переходами состояний:")
        print("   currentState = .awayShort")
        print("3. Замените на:")
        print("   // currentState = .awayShort")
        print("4. Запустите этот тест снова")
        print("5. СЦЕНАРИЙ 3 должен провалиться!")
        print("6. Верните код обратно")
        print("\nИЛИ:")
        print("1. В методе simulateActivity() найдите:")
        print("   handleUserActivity()")
        print("2. Замените на:")
        print("   // handleUserActivity()")
        print("3. Запустите тест - СЦЕНАРИЙ 2 или 4 провалятся!")
        print("\nЭто покажет что тест ловит поломки в реальной логике состояний.")
        
        exit(0)
    }
}
