#!/usr/bin/env swift

import Foundation

// Простой тестовый фреймворк
class TestRunner {
    private var testCount = 0
    private var passedCount = 0
    
    func test(_ name: String, _ testBlock: () throws -> Void) {
        testCount += 1
        print("\n🧪 Тест \(testCount): \(name)")
        
        do {
            try testBlock()
            passedCount += 1
            print("✅ ПРОШЕЛ")
        } catch {
            print("❌ ПРОВАЛЕН: \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) throws {
        if actual != expected {
            throw TestError.assertionFailed("\(message). Ожидалось: \(expected), получено: \(actual)")
        }
    }
    
    func printSummary() -> Bool {
        print("\n" + String(repeating: "=", count: 60))
        print("🧪 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ РЕАЛЬНОГО СБРОСА")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(testCount)")
        print("✅ Прошло: \(passedCount)")
        print("❌ Провалено: \(testCount - passedCount)")
        print("📈 Успешность: \(passedCount * 100 / max(testCount, 1))%")
        print(String(repeating: "=", count: 60))
        
        if passedCount == testCount {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Реальная система работает корректно.")
        } else {
            print("🚨 ЕСТЬ ПРОБЛЕМЫ! Нужно исправить интеграцию.")
        }
        
        return passedCount == testCount
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// Тест с реальными классами (но без UI)
func runRealTests() {
    let runner = TestRunner()
    
    print("🚀 Запуск тестов РЕАЛЬНОГО сброса интервала...")
    print("📅 \(Date())")
    print("⚠️  Используем РЕАЛЬНЫЕ классы из приложения")
    
    // Тест 1: Проверяем что реальный ActivityStateManager может сбросить интервал
    runner.test("🧪 РЕАЛЬНЫЙ ActivityStateManager - симуляция длительной неактивности") {
        
        // Создаем реальные объекты (но не запускаем UI)
        let activityStateManager = ActivityStateManager()
        let pomodoroTimer = PomodoroTimer()
        
        // Связываем их
        activityStateManager.pomodoroTimer = pomodoroTimer
        
        var intervalResetCalled = false
        activityStateManager.onIntervalReset = {
            intervalResetCalled = true
            print("✅ onIntervalReset вызван в реальной системе!")
        }
        
        // Запускаем интервал
        pomodoroTimer.startInterval()
        try runner.assertEqual(pomodoroTimer.state.rawValue, "working", "Интервал должен быть запущен")
        
        print("🔍 Состояние до симуляции:")
        print("   Таймер: \(pomodoroTimer.state.rawValue)")
        print("   ActivityStateManager активен: \(activityStateManager.isActive)")
        
        // ПРОБЛЕМА: Как симулировать 15 минут неактивности в реальной системе?
        // Реальный ActivityStateManager использует таймеры и реальное время
        
        print("⚠️  ОГРАНИЧЕНИЕ: Реальная система использует таймеры реального времени")
        print("⚠️  Для полного теста нужна модификация ActivityStateManager")
        print("⚠️  Или создание тестовой версии с инъекцией времени")
        
        // Пока что проверяем что связь установлена
        try runner.assert(activityStateManager.pomodoroTimer === pomodoroTimer, "PomodoroTimer должен быть связан")
        
        print("🔍 Интеграция настроена правильно")
    }
    
    // Тест 2: Проверяем что callback подключен в реальной системе
    runner.test("🧪 РЕАЛЬНАЯ интеграция - проверка callback'ов") {
        let activityStateManager = ActivityStateManager()
        
        // Проверяем что можем установить callback
        var callbackWorks = false
        activityStateManager.onIntervalReset = {
            callbackWorks = true
        }
        
        // Вызываем callback напрямую для проверки
        activityStateManager.onIntervalReset?()
        
        try runner.assert(callbackWorks, "Callback onIntervalReset должен работать")
        print("✅ Callback система работает")
    }
    
    // Тест 3: Проверяем пороги времени
    runner.test("🧪 Проверка порогов времени в реальной системе") {
        // Эти значения должны совпадать с реальными порогами в ActivityStateManager
        let pauseThreshold = 10 * 60  // 10 минут
        let resetThreshold = 15 * 60  // 15 минут
        
        try runner.assertEqual(pauseThreshold, 600, "Порог приостановки: 10 минут")
        try runner.assertEqual(resetThreshold, 900, "Порог сброса: 15 минут")
        
        print("✅ Пороги времени корректны")
        print("   Приостановка: \(pauseThreshold/60) мин")
        print("   Сброс: \(resetThreshold/60) мин")
    }
    
    let success = runner.printSummary()
    
    if success {
        print("\n💡 РЕКОМЕНДАЦИИ ДЛЯ ДАЛЬНЕЙШЕГО ТЕСТИРОВАНИЯ:")
        print("1. 🧪 Запустите приложение")
        print("2. ⏰ Начните интервал")
        print("3. 💤 Закройте ноутбук на 16+ минут")
        print("4. 🌅 Откройте ноутбук")
        print("5. 🔍 Проверьте логи: grep 'ИНТЕРВАЛ СБРОШЕН' логи")
        print("6. 📊 Проверьте состояние таймера")
        print("\n🔧 ЕСЛИ НЕ РАБОТАЕТ:")
        print("- Проверьте что ActivityStateManager.isActive = true")
        print("- Проверьте что pomodoroTimer связан")
        print("- Проверьте что onIntervalReset подключен в AppDelegate")
    }
    
    exit(success ? 0 : 1)
}

// Запускаем тесты
runRealTests()
