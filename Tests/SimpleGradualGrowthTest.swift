import Foundation

// Простой тест градационной системы роста
class GradualGrowthSystem {
    static func calculateGrowth(currentBarMinutes: Int) -> Int {
        let newBar: Int
        switch currentBarMinutes {
        case 3...7:   newBar = Int(Double(currentBarMinutes) * 1.60)
        case 8...15:  newBar = Int(Double(currentBarMinutes) * 1.40)
        case 16...25: newBar = Int(Double(currentBarMinutes) * 1.25)
        case 26...40: newBar = Int(Double(currentBarMinutes) * 1.15)
        case 41...52: newBar = Int(Double(currentBarMinutes) * 1.10)
        default:      newBar = currentBarMinutes + 1
        }
        return min(newBar, 52)
    }
}

print("🧪 Простой тест градационной системы роста...")

// Тест основных случаев
let test3 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 3)
print("3 мин → \(test3) мин (ожидали 5)")

let test10 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 10)
print("10 мин → \(test10) мин (ожидали 14)")

let test20 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 20)
print("20 мин → \(test20) мин (ожидали 25)")

let test30 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 30)
print("30 мин → \(test30) мин (ожидали 35)")

let test45 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 45)
print("45 мин → \(test45) мин (ожидали 50)")

let test50 = GradualGrowthSystem.calculateGrowth(currentBarMinutes: 50)
print("50 мин → \(test50) мин (ожидали 52, максимум)")

// Проверка скорости восстановления
print("\n🚀 Тест скорости восстановления с 3 минут:")
var currentBar = 3
var days = 0

while currentBar < 52 && days < 20 {
    let newBar = GradualGrowthSystem.calculateGrowth(currentBarMinutes: currentBar)
    days += 1
    print("День \(days): \(currentBar) → \(newBar) мин")
    currentBar = newBar
}

print("\n✅ Восстановление за \(days) дней (намного быстрее старого +10%)")
print("✅ Градационная система работает корректно!")
