//
// RealUnifiedReminderTest.swift
// Реальный тест интеграции UnifiedReminderSystem с MinuteActivityTracker
//

import Foundation
import Cocoa

// Подключаем необходимые файлы
// Компилировать: swiftc -o Tests/RealUnifiedReminderTest Tests/RealUnifiedReminderTest.swift SimplePomodoroTest/MinuteActivityTracker.swift SimplePomodoroTest/UnifiedActivityChecker.swift SimplePomodoroTest/UnifiedReminderSystem.swift SimplePomodoroTest/EscalationConfig.swift -framework Cocoa

@main
struct RealUnifiedReminderTest {
    static func main() {
        print("🧪 Запуск реального теста интеграции UnifiedReminderSystem...")
        print("")
        
        var passedTests = 0
        let totalTests = 6
        
        // Тест 1: Создание UnifiedReminderSystem
        print("🧪 Тест 1: Создание UnifiedReminderSystem")
        let reminderSystem = UnifiedReminderSystem.shared
        print("✅ UnifiedReminderSystem создан")
        passedTests += 1
        
        // Тест 2: Переключение на новую систему активности
        print("\n🧪 Тест 2: Переключение на новую систему активности")
        reminderSystem.setUseNewActivitySystem(true)
        print("✅ Переключение на новую систему выполнено")
        passedTests += 1
        
        // Тест 3: Получение отладочной информации
        print("\n🧪 Тест 3: Получение отладочной информации")
        let debugInfo = reminderSystem.getDebugInfo()
        if debugInfo.contains("новая (4-бандовая)") {
            print("✅ Отладочная информация содержит информацию о новой системе")
            passedTests += 1
        } else {
            print("❌ Отладочная информация не содержит информацию о новой системе")
            print("Получено: \(debugInfo)")
        }
        
        // Тест 4: Запуск эскалации (тестовый режим)
        print("\n🧪 Тест 4: Запуск эскалации в тестовом режиме")
        reminderSystem.startEscalation(for: "informal", isTest: true)
        
        // Даем время на инициализацию
        Thread.sleep(forTimeInterval: 0.5)
        
        let debugInfoAfterStart = reminderSystem.getDebugInfo()
        if debugInfoAfterStart.contains("Активна: true") {
            print("✅ Эскалация запущена успешно")
            passedTests += 1
        } else {
            print("❌ Эскалация не запустилась")
            print("Получено: \(debugInfoAfterStart)")
        }
        
        // Тест 5: Переключение на старую систему во время работы
        print("\n🧪 Тест 5: Переключение на старую систему во время работы")
        reminderSystem.setUseNewActivitySystem(false)
        
        Thread.sleep(forTimeInterval: 0.5)
        
        let debugInfoOldSystem = reminderSystem.getDebugInfo()
        if debugInfoOldSystem.contains("старая") {
            print("✅ Переключение на старую систему выполнено")
            passedTests += 1
        } else {
            print("❌ Переключение на старую систему не выполнено")
            print("Получено: \(debugInfoOldSystem)")
        }
        
        // Тест 6: Остановка эскалации
        print("\n🧪 Тест 6: Остановка эскалации")
        reminderSystem.stopEscalation()
        
        Thread.sleep(forTimeInterval: 0.5)
        
        let debugInfoAfterStop = reminderSystem.getDebugInfo()
        if debugInfoAfterStop.contains("Активна: false") {
            print("✅ Эскалация остановлена успешно")
            passedTests += 1
        } else {
            print("❌ Эскалация не остановилась")
            print("Получено: \(debugInfoAfterStop)")
        }
        
        // Итоги
        print("\n==================================================")
        print("🧪 Результаты теста интеграции UnifiedReminderSystem:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Интеграция UnifiedReminderSystem работает")
            print("")
            print("💡 Для проверки что тест ловит ошибки:")
            print("1. Сломайте метод setUseNewActivitySystem() в UnifiedReminderSystem")
            print("2. Запустите тест - он должен упасть")
            print("3. Восстановите код и убедитесь что тест снова проходит")
            print("")
            print("🔧 Этот тест проверяет интеграцию с MinuteActivityTracker")
            print("🔧 Полная функциональность требует делегата и UI компоненты")
            print("🔧 Но основная логика интеграции работает корректно!")
        } else {
            print("❌ Есть проблемы с интеграцией UnifiedReminderSystem")
        }
    }
}
