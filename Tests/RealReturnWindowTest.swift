#!/usr/bin/env swift

import Foundation

// Простой тест для проверки новых режимов окна возвращения пользователя
// Проверяем, что код компилируется и основная логика работает

print("🧪 Тест окна возвращения пользователя")
print("🧪 Проверяем компиляцию и основную функциональность...")

// Компилируем ModernCompletionWindow с новыми режимами
let currentDir = FileManager.default.currentDirectoryPath
let projectDir = currentDir.hasSuffix("Tests") ? String(currentDir.dropLast(6)) : currentDir

let compileCommand = """
cd "\(projectDir)" && swiftc -parse \
SimplePomodoroTest/ModernCompletionWindow.swift \
-framework Cocoa -framework CoreGraphics
"""

print("🧪 Проверка синтаксиса...")
let compileResult = shell(compileCommand)
if compileResult.exitCode != 0 {
    print("❌ Ошибка компиляции:")
    print(compileResult.output)
    exit(1)
}

print("✅ Синтаксис корректен")

// MARK: - Тестовые функции

func testReturnWindowModes() {
    print("🧪 Тест 1: Проверка новых режимов")

    // Проверяем, что новые режимы добавлены в код
    let windowCode = readFile("\(projectDir)/SimplePomodoroTest/ModernCompletionWindow.swift")

    if windowCode.contains("userReturnPartialRest") {
        print("✓ Режим userReturnPartialRest найден в коде")
    } else {
        print("❌ Режим userReturnPartialRest не найден")
        exit(1)
    }

    if windowCode.contains("userReturnChoiceRest") {
        print("✓ Режим userReturnChoiceRest найден в коде")
    } else {
        print("❌ Режим userReturnChoiceRest не найден")
        exit(1)
    }

    if windowCode.contains("userReturnFullRest") {
        print("✓ Режим userReturnFullRest найден в коде")
    } else {
        print("❌ Режим userReturnFullRest не найден")
        exit(1)
    }

    print("✅ Тест 1 пройден: Все новые режимы добавлены в код")
}

func testMinuteWordFunction() {
    print("🧪 Тест 2: Проверка функции minuteWord")

    // Проверяем, что функция minuteWord добавлена в код
    let windowCode = readFile("\(projectDir)/SimplePomodoroTest/ModernCompletionWindow.swift")

    if windowCode.contains("func minuteWord") {
        print("✓ Функция minuteWord найдена в коде")
    } else {
        print("❌ Функция minuteWord не найдена")
        exit(1)
    }

    // Проверяем логику функции через код
    if windowCode.contains("case 1:") && windowCode.contains("return \"минуту\"") {
        print("✓ Логика для 1 минуты корректна")
    } else {
        print("❌ Логика для 1 минуты некорректна")
        exit(1)
    }

    if windowCode.contains("case 2, 3, 4:") && windowCode.contains("return \"минуты\"") {
        print("✓ Логика для 2-4 минут корректна")
    } else {
        print("❌ Логика для 2-4 минут некорректна")
        exit(1)
    }

    print("✅ Тест 2 пройден: Функция minuteWord реализована корректно")
}

func testWindowConfiguration() {
    print("🧪 Тест 3: Проверка конфигурации окна")

    let windowCode = readFile("\(projectDir)/SimplePomodoroTest/ModernCompletionWindow.swift")

    // Проверяем, что добавлены новые case в updateContentForMode
    if windowCode.contains("case .userReturnPartialRest(let minutes):") {
        print("✓ Конфигурация для частичного отдыха добавлена")
    } else {
        print("❌ Конфигурация для частичного отдыха не найдена")
        exit(1)
    }

    if windowCode.contains("case .userReturnChoiceRest(let minutes):") {
        print("✓ Конфигурация для выбора отдыха добавлена")
    } else {
        print("❌ Конфигурация для выбора отдыха не найдена")
        exit(1)
    }

    if windowCode.contains("case .userReturnFullRest(let minutes):") {
        print("✓ Конфигурация для полного отдыха добавлена")
    } else {
        print("❌ Конфигурация для полного отдыха не найдена")
        exit(1)
    }

    print("✅ Тест 3 пройден: Конфигурация окна реализована корректно")
}

func testCallbackSetup() {
    print("🧪 Тест 4: Проверка настройки колбэков")

    let windowCode = readFile("\(projectDir)/SimplePomodoroTest/ModernCompletionWindow.swift")

    // Проверяем, что добавлены новые колбэки
    if windowCode.contains("var onContinueRest: (() -> Void)?") {
        print("✓ Колбэк onContinueRest добавлен")
    } else {
        print("❌ Колбэк onContinueRest не найден")
        exit(1)
    }

    if windowCode.contains("var onStartWork: (() -> Void)?") {
        print("✓ Колбэк onStartWork добавлен")
    } else {
        print("❌ Колбэк onStartWork не найден")
        exit(1)
    }

    // Проверяем action методы
    if windowCode.contains("@objc private func continueRestClicked()") {
        print("✓ Action метод continueRestClicked добавлен")
    } else {
        print("❌ Action метод continueRestClicked не найден")
        exit(1)
    }

    if windowCode.contains("@objc private func startWorkClicked()") {
        print("✓ Action метод startWorkClicked добавлен")
    } else {
        print("❌ Action метод startWorkClicked не найден")
        exit(1)
    }

    print("✅ Тест 4 пройден: Колбэки и action методы реализованы корректно")
}

func testUnifiedDesign() {
    print("🧪 Тест 5: Проверка унифицированного дизайна")

    let windowCode = readFile("\(projectDir)/SimplePomodoroTest/ModernCompletionWindow.swift")

    // Проверяем, что новые режимы используют существующие методы
    if windowCode.contains("setupUserReturnButtons") {
        print("✓ Метод setupUserReturnButtons добавлен")
    } else {
        print("❌ Метод setupUserReturnButtons не найден")
        exit(1)
    }

    if windowCode.contains("setupTextConstraintsForUserReturn") {
        print("✓ Метод setupTextConstraintsForUserReturn добавлен")
    } else {
        print("❌ Метод setupTextConstraintsForUserReturn не найден")
        exit(1)
    }

    // Проверяем, что используются существующие методы создания кнопок
    if windowCode.contains("createGradientButton") && windowCode.contains("createDashedButton") {
        print("✓ Используются существующие методы создания кнопок")
    } else {
        print("❌ Не используются существующие методы создания кнопок")
        exit(1)
    }

    print("✅ Тест 5 пройден: Унифицированный дизайн реализован корректно")
}

// MARK: - Вспомогательные функции

func shell(_ command: String) -> (output: String, exitCode: Int32) {
    let task = Process()
    let pipe = Pipe()

    task.standardOutput = pipe
    task.standardError = pipe
    task.arguments = ["-c", command]
    task.launchPath = "/bin/bash"
    task.launch()

    let data = pipe.fileHandleForReading.readDataToEndOfFile()
    let output = String(data: data, encoding: .utf8) ?? ""

    task.waitUntilExit()
    return (output, task.terminationStatus)
}

func readFile(_ path: String) -> String {
    do {
        return try String(contentsOfFile: path, encoding: .utf8)
    } catch {
        print("❌ Ошибка чтения файла \(path): \(error)")
        exit(1)
    }
}

// Запускаем тесты
testReturnWindowModes()
testMinuteWordFunction()
testWindowConfiguration()
testCallbackSetup()
testUnifiedDesign()

print("\n🎉 Все тесты окна возвращения пройдены успешно!")
