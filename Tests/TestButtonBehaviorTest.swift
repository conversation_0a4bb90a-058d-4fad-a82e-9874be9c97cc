#!/usr/bin/env swift

//
//  TestButtonBehaviorTest.swift
//  uProd Tests
//
//  Тест для проверки правильного поведения кнопки "Тест"
//  Проверяет что тест начинается с зеленой зоны (уровень 0)
//

import Foundation

// MARK: - Test Framework

class TestRunner {
    private var testCount = 0
    private var passedCount = 0
    private var failedTests: [String] = []
    
    func test(_ name: String, _ testBlock: () throws -> Void) {
        testCount += 1
        do {
            try testBlock()
            passedCount += 1
            print("✅ \(name)")
        } catch {
            failedTests.append(name)
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) throws {
        if actual != expected {
            throw TestError.assertionFailed("\(message): ожидалось \(expected), получено \(actual)")
        }
    }
    
    func printSummary() {
        print("\n🧪 Результаты тестов кнопки 'Тест':")
        print("📊 Всего тестов: \(testCount)")
        print("✅ Прошло: \(passedCount)")
        print("❌ Провалилось: \(testCount - passedCount)")
        
        if !failedTests.isEmpty {
            print("\n❌ Провалившиеся тесты:")
            for test in failedTests {
                print("   • \(test)")
            }
        }
        
        let successRate = testCount > 0 ? (passedCount * 100) / testCount : 0
        print("📈 Успешность: \(successRate)%")
        
        if passedCount == testCount {
            print("🎉 Все тесты прошли успешно!")
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Mock OvertimeConfig для тестирования

struct MockOvertimeConfig {
    struct OvertimeLevel {
        let level: Int
        let minutes: Int
        let color: String
        let message: String
    }
    
    static let levels: [OvertimeLevel] = [
        OvertimeLevel(level: 0, minutes: 0, color: "green", message: "Время отдохнуть"),
        OvertimeLevel(level: 1, minutes: 1, color: "yellow", message: "Пора сделать перерыв"),
        OvertimeLevel(level: 2, minutes: 3, color: "orange", message: "Серьезно, отдохните!"),
        OvertimeLevel(level: 3, minutes: 6, color: "red", message: "КРИТИЧНО: Немедленный отдых!")
    ]
    
    static func getLevelNumber(for minutes: Int) -> Int {
        // Находим подходящий уровень
        for level in levels.reversed() {
            if minutes >= level.minutes {
                return level.level
            }
        }
        return 0 // По умолчанию уровень 0
    }
    
    static func getLevel(for minutes: Int) -> OvertimeLevel {
        let levelNumber = getLevelNumber(for: minutes)
        return levels.first { $0.level == levelNumber } ?? levels[0]
    }
}

// MARK: - Тесты

let runner = TestRunner()

// Тест 1: Проверка что уровень 0 соответствует зеленой зоне
runner.test("Уровень 0 - это зеленая зона") {
    let level = MockOvertimeConfig.getLevelNumber(for: 0)
    try runner.assertEqual(level, 0, "Для 0 минут должен быть уровень 0")
    
    let levelInfo = MockOvertimeConfig.getLevel(for: 0)
    try runner.assertEqual(levelInfo.color, "green", "Уровень 0 должен быть зеленым")
    try runner.assertEqual(levelInfo.level, 0, "Уровень должен быть 0")
}

// Тест 2: Проверка последовательности уровней
runner.test("Правильная последовательность уровней") {
    let testCases = [
        (minutes: 0, expectedLevel: 0, expectedColor: "green"),
        (minutes: 1, expectedLevel: 1, expectedColor: "yellow"),
        (minutes: 2, expectedLevel: 1, expectedColor: "yellow"),
        (minutes: 3, expectedLevel: 2, expectedColor: "orange"),
        (minutes: 5, expectedLevel: 2, expectedColor: "orange"),
        (minutes: 6, expectedLevel: 3, expectedColor: "red"),
        (minutes: 10, expectedLevel: 3, expectedColor: "red")
    ]
    
    for testCase in testCases {
        let level = MockOvertimeConfig.getLevelNumber(for: testCase.minutes)
        let levelInfo = MockOvertimeConfig.getLevel(for: testCase.minutes)
        
        try runner.assertEqual(level, testCase.expectedLevel, 
                             "Уровень для \(testCase.minutes) минут")
        try runner.assertEqual(levelInfo.color, testCase.expectedColor, 
                             "Цвет для \(testCase.minutes) минут")
    }
}

// Тест 3: Проверка что тест должен начинаться с уровня 0
runner.test("Тест должен начинаться с уровня 0") {
    // Симулируем начало теста - 0 минут переработки
    let initialLevel = MockOvertimeConfig.getLevelNumber(for: 0)
    let initialLevelInfo = MockOvertimeConfig.getLevel(for: 0)
    
    try runner.assertEqual(initialLevel, 0, "Тест должен начинаться с уровня 0")
    try runner.assertEqual(initialLevelInfo.color, "green", "Первое окно должно быть зеленым")
    try runner.assert(initialLevelInfo.message.contains("отдохнуть"), 
                     "Сообщение должно быть о отдыхе")
}

// Тест 4: Проверка прогрессии во время теста
runner.test("Правильная прогрессия во время теста") {
    // Симулируем последовательность теста: 0 -> 1 -> 3 -> 6 минут
    let testSequence = [0, 1, 3, 6]
    let expectedLevels = [0, 1, 2, 3]
    let expectedColors = ["green", "yellow", "orange", "red"]
    
    for (index, minutes) in testSequence.enumerated() {
        let level = MockOvertimeConfig.getLevelNumber(for: minutes)
        let levelInfo = MockOvertimeConfig.getLevel(for: minutes)
        
        try runner.assertEqual(level, expectedLevels[index], 
                             "Уровень для \(minutes) минут в тесте")
        try runner.assertEqual(levelInfo.color, expectedColors[index], 
                             "Цвет для \(minutes) минут в тесте")
    }
}

// Тест 5: Проверка что НЕ начинаем сразу с желтой зоны
runner.test("НЕ начинаем сразу с желтой зоны") {
    let initialLevel = MockOvertimeConfig.getLevelNumber(for: 0)
    let initialLevelInfo = MockOvertimeConfig.getLevel(for: 0)
    
    try runner.assert(initialLevel != 1, "НЕ должны начинать с уровня 1 (желтый)")
    try runner.assert(initialLevelInfo.color != "yellow", "НЕ должны начинать с желтого цвета")
    try runner.assert(initialLevel == 0, "Должны начинать именно с уровня 0")
}

// Запуск тестов
print("🧪 Запуск тестов поведения кнопки 'Тест'...")
print("📅 \(Date())")
print("")

runner.printSummary()
