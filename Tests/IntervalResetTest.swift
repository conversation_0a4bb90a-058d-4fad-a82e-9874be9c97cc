#!/usr/bin/env swift

import Foundation

// Простой тестовый фреймворк
class TestRunner {
    private var testCount = 0
    private var passedCount = 0

    func test(_ name: String, _ testBlock: () throws -> Void) {
        testCount += 1
        print("\n🧪 Тест \(testCount): \(name)")
        
        do {
            try testBlock()
            passedCount += 1
            print("✅ ПРОШЕЛ")
        } catch {
            print("❌ ПРОВАЛЕН: \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) throws {
        if actual != expected {
            throw TestError.assertionFailed("\(message). Ожидалось: \(expected), получено: \(actual)")
        }
    }
    
    func printSummary() -> Bo<PERSON> {
        print("\n" + String(repeating: "=", count: 60))
        print("🧪 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ СБРОСА ИНТЕРВАЛА")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(testCount)")
        print("✅ Прошло: \(passedCount)")
        print("❌ Провалено: \(testCount - passedCount)")
        print("📈 Успешность: \(passedCount * 100 / max(testCount, 1))%")
        print(String(repeating: "=", count: 60))
        
        if passedCount == testCount {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Логика сброса работает корректно.")
        } else {
            print("🚨 ЕСТЬ ПРОБЛЕМЫ! Нужно исправить логику сброса.")
        }
        
        return passedCount == testCount
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// Мок-версия PomodoroTimer для тестирования
class MockPomodoroTimer {
    enum State: String {
        case idle = "idle"
        case working = "working"
        case overtime = "overtime"
        case onBreak = "onBreak"
    }
    
    var state: State = .idle
    var wasStopIntervalCalled = false
    
    func stopInterval() {
        print("🔄 MockPomodoroTimer: stopInterval() вызван")
        wasStopIntervalCalled = true
        state = .idle
    }
    
    func startInterval() {
        print("▶️ MockPomodoroTimer: startInterval() вызван")
        state = .working
        wasStopIntervalCalled = false
    }
}

// Мок-версия UnifiedActivityChecker для тестирования
class MockActivityChecker {
    var isActive = false
    var lastActivityTime = Date()
    
    func isUserCurrentlyActive() -> Bool {
        return isActive
    }
    
    func simulateInactivity(duration: TimeInterval) {
        isActive = false
        lastActivityTime = Date().addingTimeInterval(-duration)
        print("🧪 MockActivityChecker: Симулируем неактивность \(Int(duration/60)) минут")
    }
    
    func simulateActivity() {
        isActive = true
        lastActivityTime = Date()
        print("🧪 MockActivityChecker: Симулируем активность")
    }
}

// Упрощенная версия ActivityStateManager для тестирования
class TestableActivityStateManager {
    private let pauseIntervalThreshold: TimeInterval = 10 * 60  // 10 минут
    private let resetIntervalThreshold: TimeInterval = 15 * 60  // 15 минут
    
    private var intervalPausedDueToInactivity = false
    private var intervalResetDueToInactivity = false
    private var lastActivityTime = Date()
    
    var mockTimer: MockPomodoroTimer?
    var mockActivityChecker: MockActivityChecker?
    
    var onIntervalReset: (() -> Void)?
    var onStopCounters: (() -> Void)?
    var onResumeCounters: (() -> Void)?
    
    init() {
        mockTimer = MockPomodoroTimer()
        mockActivityChecker = MockActivityChecker()
    }
    
    func simulateInactivity(minutes: Int) {
        print("🧪 Симулируем неактивность \(minutes) минут...")
        
        // Обновляем время последней активности
        lastActivityTime = Date().addingTimeInterval(-TimeInterval(minutes * 60))
        
        // Симулируем проверку неактивности
        checkInactivity()
    }
    
    func simulateActivity() {
        print("🧪 Симулируем возвращение активности...")
        
        lastActivityTime = Date()
        intervalPausedDueToInactivity = false
        intervalResetDueToInactivity = false
        
        mockActivityChecker?.simulateActivity()
    }
    
    private func checkInactivity() {
        let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)
        let minutes = Int(timeSinceLastActivity / 60)
        
        print("🔍 Проверяем неактивность: \(minutes) минут")
        
        // 1. ПРИОСТАНОВКА ИНТЕРВАЛА (10+ минут неактивности)
        if timeSinceLastActivity >= pauseIntervalThreshold && !intervalPausedDueToInactivity {
            if let timer = mockTimer, timer.state == .working {
                intervalPausedDueToInactivity = true
                onStopCounters?()
                print("⏸️ ИНТЕРВАЛ ПРИОСТАНОВЛЕН: \(minutes) мин неактивности")
            }
        }
        
        // 2. СБРОС ИНТЕРВАЛА (15+ минут неактивности)
        if timeSinceLastActivity >= resetIntervalThreshold && !intervalResetDueToInactivity {
            if let timer = mockTimer, (timer.state == .working || timer.state == .overtime) {
                intervalResetDueToInactivity = true
                
                // Сбрасываем интервал
                timer.stopInterval()
                print("🔄 ИНТЕРВАЛ СБРОШЕН: \(minutes) мин неактивности")
                
                // Уведомляем о сбросе интервала
                onIntervalReset?()
            }
        }
    }
    
    func getDebugInfo() -> String {
        let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)
        let minutes = Int(timeSinceLastActivity / 60)
        
        return """
        Время с последней активности: \(minutes) мин
        Интервал приостановлен: \(intervalPausedDueToInactivity)
        Интервал сброшен: \(intervalResetDueToInactivity)
        Состояние таймера: \(mockTimer?.state.rawValue ?? "unknown")
        stopInterval вызван: \(mockTimer?.wasStopIntervalCalled ?? false)
        """
    }
}

// ОСНОВНЫЕ ТЕСТЫ
func runTests() {
        let runner = TestRunner()
        
        print("🚀 Запуск тестов сброса интервала при неактивности...")
        print("📅 \(Date())")
        
        // Тест 1: Проверяем что интервал НЕ сбрасывается при короткой неактивности
        runner.test("🧪 Короткая неактивность (5 мин) - интервал НЕ должен сброситься") {
            let manager = TestableActivityStateManager()
            
            // Запускаем интервал
            manager.mockTimer?.startInterval()
            try runner.assertEqual(manager.mockTimer?.state, .working, "Интервал должен быть запущен")
            
            // Симулируем 5 минут неактивности
            manager.simulateInactivity(minutes: 5)
            
            // Проверяем что интервал НЕ сброшен
            try runner.assertEqual(manager.mockTimer?.wasStopIntervalCalled, false, "stopInterval НЕ должен быть вызван")
            try runner.assertEqual(manager.mockTimer?.state, .working, "Интервал должен продолжаться")
            
            print("🔍 Debug: \(manager.getDebugInfo())")
        }
        
        // Тест 2: Проверяем приостановку при 10+ минутах
        runner.test("🧪 Средняя неактивность (12 мин) - интервал должен приостановиться") {
            let manager = TestableActivityStateManager()
            var stopCountersCalled = false
            
            manager.onStopCounters = {
                stopCountersCalled = true
                print("✅ onStopCounters вызван")
            }
            
            // Запускаем интервал
            manager.mockTimer?.startInterval()
            
            // Симулируем 12 минут неактивности
            manager.simulateInactivity(minutes: 12)
            
            // Проверяем что счетчики остановлены, но интервал НЕ сброшен
            try runner.assert(stopCountersCalled, "onStopCounters должен быть вызван")
            try runner.assertEqual(manager.mockTimer?.wasStopIntervalCalled, false, "stopInterval НЕ должен быть вызван (только приостановка)")
            
            print("🔍 Debug: \(manager.getDebugInfo())")
        }
        
        // Тест 3: ГЛАВНЫЙ ТЕСТ - проверяем сброс при 15+ минутах
        runner.test("🧪 Длительная неактивность (16 мин) - интервал ДОЛЖЕН сброситься") {
            let manager = TestableActivityStateManager()
            var intervalResetCalled = false
            
            manager.onIntervalReset = {
                intervalResetCalled = true
                print("✅ onIntervalReset вызван")
            }
            
            // Запускаем интервал
            manager.mockTimer?.startInterval()
            try runner.assertEqual(manager.mockTimer?.state, .working, "Интервал должен быть запущен")
            
            // Симулируем 16 минут неактивности
            manager.simulateInactivity(minutes: 16)
            
            // КРИТИЧЕСКАЯ ПРОВЕРКА: интервал должен быть сброшен
            try runner.assert(intervalResetCalled, "onIntervalReset ДОЛЖЕН быть вызван")
            try runner.assertEqual(manager.mockTimer?.wasStopIntervalCalled, true, "stopInterval ДОЛЖЕН быть вызван")
            try runner.assertEqual(manager.mockTimer?.state, .idle, "Интервал должен быть сброшен в idle")
            
            print("🔍 Debug: \(manager.getDebugInfo())")
        }
        
        // Тест 4: Проверяем что происходит при возвращении после сброса
        runner.test("🧪 Возвращение после сброса - новый интервал") {
            let manager = TestableActivityStateManager()
            
            // Запускаем интервал
            manager.mockTimer?.startInterval()
            
            // Симулируем длительную неактивность (сброс)
            manager.simulateInactivity(minutes: 20)
            try runner.assertEqual(manager.mockTimer?.state, .idle, "Интервал должен быть сброшен")
            
            // Симулируем возвращение
            manager.simulateActivity()
            
            // Запускаем новый интервал
            manager.mockTimer?.startInterval()
            try runner.assertEqual(manager.mockTimer?.state, .working, "Должен запуститься новый интервал")
            
            print("🔍 Debug: \(manager.getDebugInfo())")
        }
        
        let success = runner.printSummary()
        exit(success ? 0 : 1)
}

// Запускаем тесты
runTests()
