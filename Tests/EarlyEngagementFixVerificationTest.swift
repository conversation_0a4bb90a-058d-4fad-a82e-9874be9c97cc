import Foundation

/// Тест проверяет исправление проблемы с расхождением времени между отладочным и реальным окном
/// Проблема была в том, что debugCalculateBarForScenario использовал индексы, а ButtonMatrix - реальные дни
struct EarlyEngagementFixVerificationTest {
    static func main() {
        print("🧪 ТЕСТ: Проверка исправления расхождения времени")
        print(String(repeating: "=", count: 70))
        
        // Тестируем проблемные случаи, которые упоминал пользователь
        testProblemCases()
        
        // Тестируем граничные случаи
        testBoundaryConditions()
        
        // Тестируем различные сценарии дней без работы
        testVariousDaysScenarios()
        
        print("\n✅ Все тесты пройдены! Исправление работает корректно.")
    }
    
    static func testProblemCases() {
        print("\n📋 Тест проблемных случаев (из жалобы пользователя)")
        
        // Случай 1: Вместо 35 минут показывает 40
        testCase(description: "Случай 1: Ожидалось ~35мин", 
                userBar: 52, daysWithoutWork: 1, timeOfDay: 0, 
                expectedRange: 35...45)
        
        // Случай 2: Вместо 23 показывает 17
        testCase(description: "Случай 2: Ожидалось ~23мин", 
                userBar: 52, daysWithoutWork: 2, timeOfDay: 1, 
                expectedRange: 20...26)
        
        // Случай 3: Вместо 14 показывает 6
        testCase(description: "Случай 3: Ожидалось ~14мин", 
                userBar: 52, daysWithoutWork: 3, timeOfDay: 2, 
                expectedRange: 12...16)
    }
    
    static func testBoundaryConditions() {
        print("\n📋 Тест граничных условий")
        
        // Тест минимальной планки
        testCase(description: "Минимальная планка (3 мин)", 
                userBar: 3, daysWithoutWork: 7, timeOfDay: 3, 
                expectedRange: 3...3)
        
        // Тест максимальной планки
        testCase(description: "Максимальная планка (52 мин)", 
                userBar: 52, daysWithoutWork: 0, timeOfDay: 0, 
                expectedRange: 52...57) // Может быть рост по градационной системе
        
        // Тест нулевых дней без работы
        testCase(description: "Работал вчера (0 дней)", 
                userBar: 30, daysWithoutWork: 0, timeOfDay: 1, 
                expectedRange: 15...20) // 50% от градационного роста
    }
    
    static func testVariousDaysScenarios() {
        print("\n📋 Тест различных сценариев дней без работы")
        
        let scenarios = [
            (days: 1, description: "1 день без работы", multiplier: 0.77),
            (days: 2, description: "2 дня без работы", multiplier: 0.48),
            (days: 3, description: "3 дня без работы", multiplier: 0.48),
            (days: 4, description: "4 дня без работы", multiplier: 0.29),
            (days: 5, description: "5 дней без работы", multiplier: 0.29),
            (days: 6, description: "6 дней без работы", multiplier: 0.29),
            (days: 7, description: "7 дней без работы", multiplier: 0.0) // План-минимум
        ]
        
        for scenario in scenarios {
            let baseBar = 40
            let expectedTime: Int
            if scenario.days >= 7 {
                expectedTime = 3 // План-минимум
            } else {
                expectedTime = Int(Double(baseBar) * scenario.multiplier)
            }
            
            let tolerance = max(2, expectedTime / 5) // 20% погрешность
            let range = (expectedTime - tolerance)...(expectedTime + tolerance)
            
            testCase(description: scenario.description, 
                    userBar: baseBar, daysWithoutWork: scenario.days, timeOfDay: 0, 
                    expectedRange: range)
        }
    }
    
    static func testCase(description: String, userBar: Int, daysWithoutWork: Int, timeOfDay: Int, expectedRange: ClosedRange<Int>) {
        print("\n   🔍 \(description)")
        print("      Входные данные: userBar=\(userBar)мин, дни=\(daysWithoutWork), время=\(timeOfDay)")
        
        // Симулируем расчет реального окна (наше исправление)
        let realWindowTime = calculateRealWindowTime(userBar: userBar, daysWithoutWork: daysWithoutWork, timeOfDay: timeOfDay)
        
        // Симулируем расчет ButtonMatrix (должен совпадать)
        let buttonMatrixTime = calculateButtonMatrixTime(userBar: userBar, daysWithoutWork: daysWithoutWork, timeOfDay: timeOfDay)
        
        print("      🎯 Реальное окно: \(realWindowTime)мин")
        print("      🔧 ButtonMatrix: \(buttonMatrixTime)мин")
        print("      📊 Ожидаемый диапазон: \(expectedRange)")
        
        // Проверяем совпадение
        if realWindowTime == buttonMatrixTime {
            print("      ✅ УСПЕХ: Времена совпадают!")
        } else {
            print("      ❌ ОШИБКА: Времена НЕ совпадают! Разница: \(abs(realWindowTime - buttonMatrixTime))мин")
        }
        
        // Проверяем попадание в ожидаемый диапазон
        if expectedRange.contains(realWindowTime) {
            print("      ✅ УСПЕХ: Время в ожидаемом диапазоне")
        } else {
            print("      ⚠️  ВНИМАНИЕ: Время \(realWindowTime)мин вне диапазона \(expectedRange)")
        }
    }
    
    /// Симулирует расчет реального окна (наша новая функция calculateBarForRealWindow)
    static func calculateRealWindowTime(userBar: Int, daysWithoutWork: Int, timeOfDay: Int) -> Int {
        let initialBarTime = TimeInterval(userBar * 60)
        
        // Вертикальная адаптация (точно как в calculateBarForRealWindow)
        let verticalBarTime: TimeInterval
        switch daysWithoutWork {
        case 0:
            verticalBarTime = simulateGradualGrowth(currentBar: initialBarTime)
        case 1:
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            verticalBarTime = initialBarTime * 0.29
        default:
            verticalBarTime = 3 * 60
        }
        
        // Горизонтальная адаптация (точно как в calculateBarForRealWindow)
        let finalBarTime: TimeInterval
        switch timeOfDay {
        case 0:
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
        case 3:
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }
        
        let limitedBarTime = min(finalBarTime, 52 * 60)
        return Int(limitedBarTime / 60)
    }
    
    /// Симулирует расчет ButtonMatrix
    static func calculateButtonMatrixTime(userBar: Int, daysWithoutWork: Int, timeOfDay: Int) -> Int {
        let baseBar = TimeInterval(userBar * 60)
        
        // Вертикальная адаптация (как в ButtonMatrix)
        let verticalBar: TimeInterval
        switch daysWithoutWork {
        case 0:
            verticalBar = simulateGradualGrowth(currentBar: baseBar)
        case 1:
            verticalBar = baseBar * 0.77
        case 2...3:
            verticalBar = baseBar * 0.48
        case 4...6:
            verticalBar = baseBar * 0.29
        default:
            verticalBar = 3 * 60
        }
        
        // Горизонтальная адаптация (как в ButtonMatrix)
        let finalBar: TimeInterval
        switch timeOfDay {
        case 0:
            finalBar = max(verticalBar, 3 * 60)
        case 1:
            finalBar = max(verticalBar * 0.5, 3 * 60)
        case 2:
            finalBar = max(verticalBar * 0.25, 3 * 60)
        case 3:
            finalBar = 3 * 60
        default:
            finalBar = max(verticalBar, 3 * 60)
        }
        
        return Int(finalBar / 60)
    }
    
    /// Симулирует градационный рост (упрощенная версия)
    static func simulateGradualGrowth(currentBar: TimeInterval) -> TimeInterval {
        let currentMinutes = Int(currentBar / 60)
        let multiplier: Double
        
        switch currentMinutes {
        case 3...7: multiplier = 1.60
        case 8...15: multiplier = 1.40
        case 16...25: multiplier = 1.25
        case 26...40: multiplier = 1.15
        case 41...52: multiplier = 1.10
        default: multiplier = 1.05
        }
        
        let newMinutes = Int(round(Double(currentMinutes) * multiplier))
        return TimeInterval(min(newMinutes, 52) * 60)
    }
}

// Запуск теста
EarlyEngagementFixVerificationTest.main()
