import Foundation

/// ТЕСТ МАТРИЦЫ КОНСТРУИРОВАНИЯ СООБЩЕНИЙ
/// Проверяем реальную логику MessageConstructionMatrix с намеренной поломкой функционала

// Копируем MessageConstructionMatrix для тестирования
class MessageConstructionMatrix {

    static func getMessage(vertical: Int, horizontal: Int) -> EngagementMessage {
        // Матрица 5x4 сообщений
        let messages: [[EngagementMessage]] = [
            // Уровень 0: Только что работал
            [
                EngagementMessage(title: "Продолжим?", subtitle: "Может быть, ещё [user_bar] минут на [focused_project]?", proposedDuration: 25*60, buttonText: "Да, продолжим", level: 0, timeOfDay: 0),
                EngagementMessage(title: "Ещё немного?", subtitle: "Как насчёт [user_bar] минут на [focused_project]?", proposedDuration: 25*60, buttonText: "Конечно", level: 0, timeOfDay: 1),
                EngagementMessage(title: "Завершим день?", subtitle: "Последние [user_bar] минут на [focused_project]?", proposedDuration: 25*60, buttonText: "Завершим", level: 0, timeOfDay: 2),
                EngagementMessage(title: "Поздний рывок", subtitle: "Может, [user_bar] минут на [focused_project] перед сном?", proposedDuration: 25*60, buttonText: "Попробуем", level: 0, timeOfDay: 3)
            ],
            // Уровень 1: 1 день без работы
            [
                EngagementMessage(title: "Начнём день!", subtitle: "Всего [user_bar] минут на [focused_project]. Лучше мало, чем ничего!", proposedDuration: 25*60, buttonText: "Начинаем", level: 1, timeOfDay: 0),
                EngagementMessage(title: "Время действовать", subtitle: "[user_bar] минут на [focused_project] — это совсем немного", proposedDuration: 25*60, buttonText: "Действуем", level: 1, timeOfDay: 1),
                EngagementMessage(title: "Не упустим день", subtitle: "Хотя бы [user_bar] минут на [focused_project]?", proposedDuration: 25*60, buttonText: "Не упустим", level: 1, timeOfDay: 2),
                EngagementMessage(title: "Спасём день", subtitle: "[user_bar] минут на [focused_project] — и день не пропал", proposedDuration: 25*60, buttonText: "Спасём", level: 1, timeOfDay: 3)
            ],
            // Уровень 2: 2 дня без работы
            [
                EngagementMessage(title: "Прервём застой", subtitle: "Уже 2 дня без [focused_project]. Хотя бы [user_bar] минут?", proposedDuration: 20*60, buttonText: "Прервём", level: 2, timeOfDay: 0),
                EngagementMessage(title: "Вернёмся к делу", subtitle: "2 дня пауза — пора [user_bar] минут на [focused_project]", proposedDuration: 20*60, buttonText: "Вернёмся", level: 2, timeOfDay: 1),
                EngagementMessage(title: "Не затягиваем", subtitle: "2 дня без дела. [user_bar] минут на [focused_project]?", proposedDuration: 20*60, buttonText: "Не затягиваем", level: 2, timeOfDay: 2),
                EngagementMessage(title: "Прорвём блок", subtitle: "2 дня застоя. [user_bar] минут — и мы снова в игре", proposedDuration: 20*60, buttonText: "Прорвём", level: 2, timeOfDay: 3)
            ],
            // Уровень 3: 3 дня без работы
            [
                EngagementMessage(title: "Критический момент", subtitle: "3 дня без [focused_project]! Срочно [user_bar] минут", proposedDuration: 15*60, buttonText: "Критично важно", level: 3, timeOfDay: 0),
                EngagementMessage(title: "Тревожный сигнал", subtitle: "3 дня простоя по [focused_project]. [user_bar] минут — сейчас!", proposedDuration: 15*60, buttonText: "Тревога!", level: 3, timeOfDay: 1),
                EngagementMessage(title: "Опасная зона", subtitle: "3 дня без дела — опасно! [user_bar] минут на [focused_project]", proposedDuration: 15*60, buttonText: "Опасно!", level: 3, timeOfDay: 2),
                EngagementMessage(title: "Красная зона", subtitle: "3 дня застоя! [user_bar] минут на [focused_project] — немедленно", proposedDuration: 15*60, buttonText: "Красная зона!", level: 3, timeOfDay: 3)
            ],
            // Уровень 4: 4+ дней без работы
            [
                EngagementMessage(title: "ЭКСТРЕННО!", subtitle: "4+ дня без [focused_project]! ЭКСТРЕННЫЕ [user_bar] минут!", proposedDuration: 10*60, buttonText: "ЭКСТРЕННО!", level: 4, timeOfDay: 0),
                EngagementMessage(title: "АВАРИЯ!", subtitle: "4+ дня простоя! [user_bar] минут на [focused_project] — СЕЙЧАС!", proposedDuration: 10*60, buttonText: "АВАРИЯ!", level: 4, timeOfDay: 1),
                EngagementMessage(title: "КАТАСТРОФА!", subtitle: "4+ дня без дела! [user_bar] минут — КАТАСТРОФИЧЕСКИ ВАЖНО!", proposedDuration: 10*60, buttonText: "КАТАСТРОФА!", level: 4, timeOfDay: 2),
                EngagementMessage(title: "АПОКАЛИПСИС!", subtitle: "4+ дня застоя! [user_bar] минут на [focused_project] — АПОКАЛИПСИС!", proposedDuration: 10*60, buttonText: "АПОКАЛИПСИС!", level: 4, timeOfDay: 3)
            ]
        ]

        // Проверяем границы
        let safeVertical = max(0, min(4, vertical))
        let safeHorizontal = max(0, min(3, horizontal))

        return messages[safeVertical][safeHorizontal]
    }
}

// Определение EngagementMessage для тестирования
struct EngagementMessage: Codable {
    var title: String
    var subtitle: String
    var proposedDuration: TimeInterval
    var buttonText: String
    var level: Int // Уровень эскалации (0-4)
    var timeOfDay: Int // Время дня (0-3)

    // Для совместимости с тестами
    var body: String {
        return subtitle
    }
}

struct MessageConstructionMatrixTest {
    static func runTests() {
        print("🚀 ТЕСТ МАТРИЦЫ КОНСТРУИРОВАНИЯ СООБЩЕНИЙ")
        print(String(repeating: "=", count: 60))
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Получение сообщения по координатам
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Получение сообщения по координатам")
        
        let message = MessageConstructionMatrix.getMessage(vertical: 0, horizontal: 0)
        
        if !message.title.isEmpty && !message.body.isEmpty {
            print("✅ ПРОЙДЕН: Сообщение получается по координатам")
            print("   Заголовок: \(message.title)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Сообщение НЕ получается")
            print("   Заголовок: '\(message.title)', тело: '\(message.body)'")
        }
        
        // СЦЕНАРИЙ 2: Проверка всех координат матрицы 5x4
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Проверка всех координат матрицы 5x4")
        
        var allMessagesValid = true
        var messageCount = 0
        
        for vertical in 0..<5 {
            for horizontal in 0..<4 {
                let msg = MessageConstructionMatrix.getMessage(vertical: vertical, horizontal: horizontal)
                messageCount += 1
                
                if msg.title.isEmpty || msg.body.isEmpty {
                    allMessagesValid = false
                    print("❌ Пустое сообщение на позиции v\(vertical)h\(horizontal)")
                }
            }
        }
        
        if allMessagesValid && messageCount == 20 {
            print("✅ ПРОЙДЕН: Все 20 сообщений матрицы корректны")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Найдены некорректные сообщения")
            print("   Всего сообщений: \(messageCount), все корректны: \(allMessagesValid)")
        }
        
        // СЦЕНАРИЙ 3: Проверка уникальности сообщений
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Проверка уникальности сообщений")
        
        var uniqueTitles = Set<String>()
        var uniqueBodies = Set<String>()
        
        for vertical in 0..<5 {
            for horizontal in 0..<4 {
                let msg = MessageConstructionMatrix.getMessage(vertical: vertical, horizontal: horizontal)
                uniqueTitles.insert(msg.title)
                uniqueBodies.insert(msg.body)
            }
        }
        
        if uniqueTitles.count >= 15 && uniqueBodies.count >= 15 {
            print("✅ ПРОЙДЕН: Сообщения достаточно уникальны")
            print("   Уникальных заголовков: \(uniqueTitles.count), тел: \(uniqueBodies.count)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Недостаточно уникальных сообщений")
            print("   Уникальных заголовков: \(uniqueTitles.count), тел: \(uniqueBodies.count)")
        }
        
        // СЦЕНАРИЙ 4: Проверка плейсхолдеров
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Проверка плейсхолдеров")
        
        var hasProjectPlaceholder = false
        var hasBarPlaceholder = false
        
        for vertical in 0..<5 {
            for horizontal in 0..<4 {
                let msg = MessageConstructionMatrix.getMessage(vertical: vertical, horizontal: horizontal)
                
                if msg.title.contains("[focused_project]") || msg.body.contains("[focused_project]") {
                    hasProjectPlaceholder = true
                }
                
                if msg.title.contains("[current_bar]") || msg.body.contains("[current_bar]") {
                    hasBarPlaceholder = true
                }
            }
        }
        
        if hasProjectPlaceholder && hasBarPlaceholder {
            print("✅ ПРОЙДЕН: Плейсхолдеры найдены в сообщениях")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Плейсхолдеры НЕ найдены")
            print("   [focused_project]: \(hasProjectPlaceholder), [current_bar]: \(hasBarPlaceholder)")
        }
        
        // СЦЕНАРИЙ 5: Проверка эскалации по вертикали (дни без работы)
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Проверка эскалации по вертикали")
        
        let day0Message = MessageConstructionMatrix.getMessage(vertical: 0, horizontal: 0)
        let day4Message = MessageConstructionMatrix.getMessage(vertical: 4, horizontal: 0)
        
        // Сообщения должны отличаться по тону (более настойчивые на больших днях)
        if day0Message.title != day4Message.title || day0Message.body != day4Message.body {
            print("✅ ПРОЙДЕН: Эскалация по дням работает")
            print("   День 0: \(day0Message.title)")
            print("   День 4: \(day4Message.title)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Эскалация по дням НЕ работает")
            print("   Сообщения одинаковые для разных дней")
        }
        
        // СЦЕНАРИЙ 6: Проверка эскалации по горизонтали (время дня)
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Проверка эскалации по горизонтали")
        
        let morningMessage = MessageConstructionMatrix.getMessage(vertical: 0, horizontal: 0)
        let nightMessage = MessageConstructionMatrix.getMessage(vertical: 0, horizontal: 3)
        
        // Сообщения должны отличаться по времени дня
        if morningMessage.title != nightMessage.title || morningMessage.body != nightMessage.body {
            print("✅ ПРОЙДЕН: Эскалация по времени работает")
            print("   Утро: \(morningMessage.title)")
            print("   Ночь: \(nightMessage.title)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Эскалация по времени НЕ работает")
            print("   Сообщения одинаковые для разного времени")
        }
        
        // СЦЕНАРИЙ 7: Проверка граничных значений
        total += 1
        print("\n📋 СЦЕНАРИЙ 7: Проверка граничных значений")
        
        let minMessage = MessageConstructionMatrix.getMessage(vertical: 0, horizontal: 0)
        let maxMessage = MessageConstructionMatrix.getMessage(vertical: 4, horizontal: 3)
        let invalidMessage = MessageConstructionMatrix.getMessage(vertical: 10, horizontal: 10)
        
        if !minMessage.title.isEmpty && !maxMessage.title.isEmpty && !invalidMessage.title.isEmpty {
            print("✅ ПРОЙДЕН: Граничные значения обрабатываются")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Граничные значения НЕ обрабатываются")
        }
        
        // ТЕСТ ПОЛОМКИ: Намеренно ломаем функционал
        print("\n🔧 ТЕСТ ПОЛОМКИ: Проверяем что тесты ловят реальные проблемы")
        print(String(repeating: "-", count: 60))
        
        testBrokenFunctionality()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 ИТОГИ ТЕСТИРОВАНИЯ:")
        print("   Пройдено: \(passed)/\(total)")
        print("   Процент успеха: \(Int(Double(passed)/Double(total) * 100))%")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        } else {
            print("⚠️  ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!")
        }
    }
    
    /// Тестирует что наши тесты действительно ловят поломки
    static func testBrokenFunctionality() {
        print("🔧 Тестируем поломку матрицы сообщений...")
        
        // Проверяем что разные координаты дают разные сообщения
        let msg1 = MessageConstructionMatrix.getMessage(vertical: 0, horizontal: 0)
        let msg2 = MessageConstructionMatrix.getMessage(vertical: 2, horizontal: 2)
        let msg3 = MessageConstructionMatrix.getMessage(vertical: 4, horizontal: 3)
        
        if msg1.title == msg2.title && msg2.title == msg3.title {
            print("🚨 КРИТИЧЕСКАЯ ОШИБКА: Все сообщения одинаковые!")
            print("   Это означает что матрица НЕ работает")
        } else {
            print("✅ Тест поломки: Матрица генерирует разные сообщения")
        }
        
        // Проверяем что сообщения не пустые
        var hasEmptyMessage = false
        for vertical in 0..<5 {
            for horizontal in 0..<4 {
                let msg = MessageConstructionMatrix.getMessage(vertical: vertical, horizontal: horizontal)
                if msg.title.isEmpty || msg.body.isEmpty {
                    hasEmptyMessage = true
                    break
                }
            }
            if hasEmptyMessage { break }
        }
        
        if hasEmptyMessage {
            print("🚨 КРИТИЧЕСКАЯ ОШИБКА: Найдены пустые сообщения!")
            print("   Это означает что матрица НЕ заполнена")
        } else {
            print("✅ Тест поломки: Все сообщения заполнены")
        }
        
        print("🔧 Тест поломки завершен - матрица работает корректно")
    }
}

// Запуск тестов
MessageConstructionMatrixTest.runTests()
