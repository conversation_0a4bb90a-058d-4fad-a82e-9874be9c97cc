import Foundation

print("🧪 ТЕСТ: Новая система текстов раннего вовлечения")
print(String(repeating: "=", count: 60))

// Тест 1: Склонение дней
testDaysDeclension()

// Тест 2: Загрузка JSON (базовый тест)
testJSONStructure()

print("\n✅ БАЗОВЫЕ ТЕСТЫ ЗАВЕРШЕНЫ")
print("📝 Для полного тестирования запустите приложение и используйте отладочное окно")
    
/// Тест структуры JSON файла
func testJSONStructure() {
        print("\n🔧 Тест 2: Структура JSON файла")

        // Проверяем, что файл существует
        let jsonPath = "SimplePomodoroTest/Resources/EarlyEngagementTexts.json"
        let fileManager = FileManager.default

        if fileManager.fileExists(atPath: jsonPath) {
            print("✅ JSON файл найден: \(jsonPath)")

            // Пытаемся прочитать файл
            do {
                let data = try Data(contentsOf: URL(fileURLWithPath: jsonPath))
                let json = try JSONSerialization.jsonObject(with: data, options: [])

                if let dict = json as? [String: Any] {
                    print("✅ JSON файл корректный")
                    print("   Ключи верхнего уровня: \(dict.keys.sorted())")

                    if let version = dict["version"] as? String {
                        print("   Версия: \(version)")
                    }
                } else {
                    print("❌ JSON файл не является словарем")
                }
            } catch {
                print("❌ Ошибка чтения JSON: \(error)")
            }
        } else {
            print("❌ JSON файл не найден: \(jsonPath)")
        }
    }
    

    
/// Тест склонения дней
func testDaysDeclension() {
        print("\n📝 Тест 1: Склонение дней")

        let testNumbers = [1, 2, 3, 4, 5, 11, 12, 13, 14, 21, 22, 23, 24, 25, 31, 32, 100, 101, 102, 111, 112]

        for number in testNumbers {
            let declension = getDaysDeclension(number)
            print("   \(number) \(declension)")
        }

        print("✅ Склонение дней протестировано")
    }

/// Функция склонения дней (копия из EarlyEngagementTextsLoader)
func getDaysDeclension(_ days: Int) -> String {
    let lastDigit = days % 10
    let lastTwoDigits = days % 100

    // Исключения для 11-14
    if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
        return "дней"
    }

    // Основные правила
    switch lastDigit {
    case 1:
        return "день"
    case 2, 3, 4:
        return "дня"
    default:
        return "дней"
    }
}
