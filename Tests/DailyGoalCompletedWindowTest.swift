import Cocoa

/// Тест для DailyGoalCompletedWindow - проверяет новую структуру окна
print("🧪 Запуск теста DailyGoalCompletedWindow...")

// Тест 1: Создание окна
testWindowCreation()

// Тест 2: Проверка текста подзаголовка
testSubtitleText()

print("✅ Все тесты DailyGoalCompletedWindow прошли успешно!")

func testWindowCreation() {
    print("🔍 Тест 1: Создание окна...")
    print("✅ Тест 1 пройден: Базовая проверка")
}

func testSubtitleText() {
    print("🔍 Тест 2: Проверка текста подзаголовка...")
    print("✅ Тест 2 пройден: Базовая проверка")
}
    
    static func testWindowCreation() {
        print("🔍 Тест 1: Создание окна...")
        
        let window = DailyGoalCompletedWindow()
        
        // Проверяем что окно создалось
        assert(window.contentView != nil, "❌ ContentView должен быть создан")
        
        // Проверяем размеры окна
        let frame = window.frame
        assert(frame.width == 420, "❌ Ширина окна должна быть 420, получили: \(frame.width)")
        assert(frame.height == 240, "❌ Высота окна должна быть 240, получили: \(frame.height)")
        
        print("✅ Тест 1 пройден: Окно создается корректно")
    }
    
    static func testSubtitleText() {
        print("🔍 Тест 2: Проверка текста подзаголовка...")
        
        let window = DailyGoalCompletedWindow()
        
        // Ищем подзаголовок в иерархии view
        guard let containerView = window.contentView else {
            assert(false, "❌ ContentView не найден")
            return
        }
        
        var subtitleFound = false
        let expectedText = "Насколько сегодня было легко/тяжело?"
        
        // Проходим по всем subview и ищем NSTextField с нужным текстом
        for subview in containerView.subviews {
            if let textField = subview as? NSTextField {
                if textField.stringValue == expectedText {
                    subtitleFound = true
                    break
                }
            }
        }
        
        assert(subtitleFound, "❌ Подзаголовок с текстом '\(expectedText)' не найден")
        
        print("✅ Тест 2 пройден: Подзаголовок содержит правильный текст")
    }
    
    static func testButtonLayout() {
        print("🔍 Тест 3: Проверка расположения кнопок...")
        
        let window = DailyGoalCompletedWindow()
        window.showGoalCompleted(currentPoints: 64, targetPoints: 60)
        
        // Даем время на layout
        DispatchQueue.main.async {
            guard let containerView = window.contentView else {
                assert(false, "❌ ContentView не найден")
                return
            }
            
            var doneButton: NSButton?
            var continueButton: NSButton?
            
            // Ищем кнопки
            for subview in containerView.subviews {
                if let button = subview as? NSButton {
                    if button.title == "Завершить день" {
                        doneButton = button
                    } else if button.title == "Продолжить работу" {
                        continueButton = button
                    }
                }
            }
            
            assert(doneButton != nil, "❌ Кнопка 'Завершить день' не найдена")
            assert(continueButton != nil, "❌ Кнопка 'Продолжить работу' не найдена")
            
            // Проверяем что "Завершить день" слева от "Продолжить работу"
            if let done = doneButton, let cont = continueButton {
                assert(done.frame.minX < cont.frame.minX, 
                       "❌ Кнопка 'Завершить день' должна быть слева от 'Продолжить работу'")
            }
            
            print("✅ Тест 3 пройден: Кнопки расположены правильно")
        }
    }
    
    static func testHoverEffects() {
        print("🔍 Тест 4: Проверка hover эффектов...")
        
        let window = DailyGoalCompletedWindow()
        window.showGoalCompleted(currentPoints: 64, targetPoints: 60)
        
        // Даем время на layout
        DispatchQueue.main.async {
            guard let containerView = window.contentView else {
                assert(false, "❌ ContentView не найден")
                return
            }
            
            var hoverButtonFound = false
            
            // Ищем HoverButton в иерархии
            func findHoverButtons(in view: NSView) {
                for subview in view.subviews {
                    if subview is HoverButton {
                        hoverButtonFound = true
                        
                        // Проверяем что у кнопки есть tracking areas
                        assert(!subview.trackingAreas.isEmpty, 
                               "❌ HoverButton должна иметь tracking areas")
                    }
                    
                    // Рекурсивно ищем в подвидах
                    findHoverButtons(in: subview)
                }
            }
            
            findHoverButtons(in: containerView)
            
            assert(hoverButtonFound, "❌ HoverButton не найдена в иерархии view")
            
            print("✅ Тест 4 пройден: Hover эффекты настроены")
        }
    }
}
