//
// RealEscalationBugTest.swift
// Тест для проверки исправления бага с повторным запуском эскалации в реальном приложении
//

import Foundation
import Cocoa

// Подключаем необходимые файлы
// Компилировать: swiftc -o Tests/RealEscalationBugTest Tests/RealEscalationBugTest.swift SimplePomodoroTest/ActivityStateManager.swift SimplePomodoroTest/MinuteActivityTracker.swift SimplePomodoroTest/Logger.swift -framework Cocoa

// Мок для тестирования
class MockEscalationCallback {
    var callCount = 0
    var lastCallTime: Date?
    
    func onInformalRestSuggestion() {
        callCount += 1
        lastCallTime = Date()
        print("🔔 Callback вызван #\(callCount) в \(Date())")
    }
}

@main
struct RealEscalationBugTest {
    static func main() {
        print("🧪 Тест исправления бага эскалации в реальном ActivityStateManager")
        print(String(repeating: "=", count: 70))
        
        // Создаем реальный ActivityStateManager
        let activityManager = ActivityStateManager()
        let mockCallback = MockEscalationCallback()
        
        // Подключаем callback
        activityManager.onInformalRestSuggestion = {
            mockCallback.onInformalRestSuggestion()
        }
        
        print("\n1️⃣ Запускаем ActivityStateManager")
        activityManager.start()
        
        print("\n2️⃣ Симулируем активную работу (42+ минут из 52)")
        // Заполняем историю активностью
        for i in 0..<52 {
            let isActive = i < 42 // Первые 42 минуты активны
            activityManager.recordMinuteActivity(isActive: isActive)
            
            // Проверяем вызовы callback после каждой записи
            if mockCallback.callCount > 0 {
                print("   ✅ Callback сработал на минуте \(i + 1)")
                break
            }
        }
        
        print("\n3️⃣ Проверяем состояние после первого срабатывания:")
        print("   Вызовов callback: \(mockCallback.callCount)")
        
        if mockCallback.callCount == 0 {
            print("   ❌ Callback не сработал! Проверьте логику обнаружения.")
            return
        }
        
        print("\n4️⃣ Симулируем продолжение работы (еще 10 минут)")
        print("   ✅ ИСПРАВЛЕНИЕ: Callback НЕ должен вызываться повторно!")
        
        let initialCallCount = mockCallback.callCount
        
        for minute in 1...10 {
            print("\n   Минута \(minute):")
            activityManager.recordMinuteActivity(isActive: true)
            print("     Вызовов callback: \(mockCallback.callCount)")
            
            if mockCallback.callCount > initialCallCount {
                print("     ❌ БАГИ! Callback вызван повторно!")
                break
            } else {
                print("     ✅ Callback заблокирован (эскалация активна)")
            }
        }
        
        print("\n5️⃣ Симулируем окончание эскалации")
        activityManager.notifyEscalationEnded()
        print("   Эскалация завершена")
        
        print("\n6️⃣ Симулируем новый интервал работы")
        activityManager.resetEscalationState()
        print("   Состояние эскалации сброшено")
        
        // Добавляем еще активные минуты для нового интервала
        print("   Добавляем еще 5 активных минут...")
        let beforeNewInterval = mockCallback.callCount
        
        for _ in 1...5 {
            activityManager.recordMinuteActivity(isActive: true)
        }
        
        print("   Вызовов callback до: \(beforeNewInterval), после: \(mockCallback.callCount)")
        
        print("\n🚨 РЕЗУЛЬТАТ ТЕСТА:")
        print("   Общее количество вызовов callback: \(mockCallback.callCount)")
        
        if mockCallback.callCount == initialCallCount {
            print("   ✅ ИСПРАВЛЕНИЕ РАБОТАЕТ!")
            print("   ✅ Повторные вызовы в рамках одного интервала заблокированы")
        } else if mockCallback.callCount == initialCallCount + 1 {
            print("   ✅ ИСПРАВЛЕНИЕ РАБОТАЕТ ЧАСТИЧНО!")
            print("   ✅ Повторные вызовы заблокированы, новый интервал запустил эскалацию")
        } else {
            print("   ❌ ИСПРАВЛЕНИЕ НЕ РАБОТАЕТ!")
            print("   ❌ Callback вызывается слишком часто")
        }
        
        print("\n💡 ОБЪЯСНЕНИЕ:")
        print("   - Первый вызов: нормальное обнаружение неформальной сессии")
        print("   - Повторные вызовы в том же интервале: должны блокироваться")
        print("   - Новый интервал после сброса: может запустить новую эскалацию")
        
        // Останавливаем менеджер
        activityManager.stop()
        print("\n✅ Тест завершен")
    }
}
