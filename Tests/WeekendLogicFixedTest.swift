import Foundation

// Простой тест исправленной логики WeekendManager
print("🧪 Тест исправленной логики WeekendManager")
print(String(repeating: "=", count: 50))

// Создаем экземпляры менеджеров
let testDataManager = TestDataManager()
let weekendManager = WeekendManager()
// Убеждаемся, что стратегия 3/1
UserDefaults.standard.set("3/1 (рекомендуемая)", forKey: "weekendStrategy")

// Тест всех 4 сценариев
testScenario1(testDataManager, weekendManager)
testScenario2(testDataManager, weekendManager)
testScenario3(testDataManager, weekendManager)
testScenario4(testDataManager, weekendManager)

print("\n✅ Все тесты завершены")

func testScenario1(_ testDataManager: TestDataManager, _ weekendManager: WeekendManager) {
        print("\n📋 СЦЕНАРИЙ 1: Работал только сегодня")
        
        // Очищаем данные
        testDataManager.clearAllTestData()
        
        // НЕ добавляем никаких сессий (только сегодня будет работать, если добавить)
        
        // Проверяем результаты
        let isWeekendToday = weekendManager.isWeekendDay()
        let nextWeekendInfo = weekendManager.getNextWeekendInfo()
        
        print("   Показать выходной сегодня: \(isWeekendToday ? "ДА" : "НЕТ")")
        print("   Информация о выходном: \(nextWeekendInfo ?? "НЕТ")")
        print("   Ожидаемый результат: НЕТ (нужно 3 дня)")
        
        if !isWeekendToday {
            print("   ✅ ПРАВИЛЬНО")
        } else {
            print("   ❌ ОШИБКА")
        }
}

func testScenario2(_ testDataManager: TestDataManager, _ weekendManager: WeekendManager) {
        print("\n📋 СЦЕНАРИЙ 2: Работал вчера + сегодня")
        
        // Очищаем данные
        testDataManager.clearAllTestData()
        
        // Добавляем сессию за вчера
        testDataManager.addTestSession(daysAgo: 1, durationMinutes: 30)
        
        // Проверяем результаты
        let isWeekendToday = weekendManager.isWeekendDay()
        let nextWeekendInfo = weekendManager.getNextWeekendInfo()
        
        print("   Показать выходной сегодня: \(isWeekendToday ? "ДА" : "НЕТ")")
        print("   Информация о выходном: \(nextWeekendInfo ?? "НЕТ")")
        print("   Ожидаемый результат: НЕТ (нужно 3 дня)")
        
        if !isWeekendToday {
            print("   ✅ ПРАВИЛЬНО")
        } else {
            print("   ❌ ОШИБКА")
        }
}

func testScenario3(_ testDataManager: TestDataManager, _ weekendManager: WeekendManager) {
        print("\n📋 СЦЕНАРИЙ 3: Работал ЧТ+ПТ+СБ, сегодня НЕ работал")
        
        // Очищаем данные
        testDataManager.clearAllTestData()
        
        // Добавляем сессии за 3 дня (не включая сегодня)
        testDataManager.addTestSession(daysAgo: 3, durationMinutes: 30) // ЧТ
        testDataManager.addTestSession(daysAgo: 2, durationMinutes: 30) // ПТ  
        testDataManager.addTestSession(daysAgo: 1, durationMinutes: 30) // СБ
        
        // НЕ добавляем сессию сегодня (ВСК)
        
        // Проверяем результаты
        let isWeekendToday = weekendManager.isWeekendDay()
        let nextWeekendInfo = weekendManager.getNextWeekendInfo()
        
        print("   Показать выходной сегодня: \(isWeekendToday ? "ДА" : "НЕТ")")
        print("   Информация о выходном: \(nextWeekendInfo ?? "НЕТ")")
        print("   Ожидаемый результат: ДА (3 дня работы, сегодня не работал)")
        
        if isWeekendToday && nextWeekendInfo?.contains("Today") == true {
            print("   ✅ ПРАВИЛЬНО")
        } else {
            print("   ❌ ОШИБКА")
        }
}

func testScenario4(_ testDataManager: TestDataManager, _ weekendManager: WeekendManager) {
        print("\n📋 СЦЕНАРИЙ 4: Работал ЧТ+ПТ+СБ+ВСК (уже поработал сегодня)")
        
        // Очищаем данные
        testDataManager.clearAllTestData()
        
        // Добавляем сессии за 3 дня + сегодня
        testDataManager.addTestSession(daysAgo: 3, durationMinutes: 30) // ЧТ
        testDataManager.addTestSession(daysAgo: 2, durationMinutes: 30) // ПТ
        testDataManager.addTestSession(daysAgo: 1, durationMinutes: 30) // СБ
        testDataManager.addTestSession(daysAgo: 0, durationMinutes: 30) // ВСК (сегодня)
        
        // Проверяем результаты
        let isWeekendToday = weekendManager.isWeekendDay()
        let nextWeekendInfo = weekendManager.getNextWeekendInfo()
        
        print("   Показать выходной сегодня: \(isWeekendToday ? "ДА" : "НЕТ")")
        print("   Информация о выходном: \(nextWeekendInfo ?? "НЕТ")")
        print("   Ожидаемый результат: НЕТ сегодня, ДА завтра (4 дня работы)")
        
        if !isWeekendToday && nextWeekendInfo?.contains("Monday") == true {
            print("   ✅ ПРАВИЛЬНО")
        } else {
            print("   ❌ ОШИБКА")
        }
}
