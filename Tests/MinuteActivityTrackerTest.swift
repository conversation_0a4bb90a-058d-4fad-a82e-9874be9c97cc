import Foundation

/// Тест для MinuteActivityTracker с новой логикой 20 отрезков и погрешности
@main struct MinuteActivityTrackerTest {
    
    static func main() {
        print("🧪 Запуск тестов MinuteActivityTracker...")
        
        testErrorMarginLogic()
        testTimerStartScenario()
        testRealActivityDetection()
        testRandomActivityFiltering()
        
        print("✅ Все тесты MinuteActivityTracker завершены!")
    }
    
    /// Тест основной логики погрешности
    static func testErrorMarginLogic() {
        print("\n🎯 Тест: Логика погрешности")
        
        let tracker = MinuteActivityTracker()
        
        // Тест 1: 0 отрезков = неактивна
        tracker.setForcedSegmentState(segment: 0, isActive: false)
        let result0 = tracker.forceCompleteCurrentMinute()
        assert(!result0, "0 отрезков должно быть неактивно")
        print("✅ 0 отрезков = неактивна")
        
        // Тест 2: 1 отрезок = погрешность = неактивна
        tracker.setForcedSegmentState(segment: 0, isActive: true)
        let result1 = tracker.forceCompleteCurrentMinute()
        assert(!result1, "1 отрезок должен считаться погрешностью")
        print("✅ 1 отрезок = погрешность = неактивна")
        
        // Тест 3: 2 отрезка = активна
        tracker.setForcedSegmentState(segment: 0, isActive: true)
        tracker.setForcedSegmentState(segment: 1, isActive: true)
        let result2 = tracker.forceCompleteCurrentMinute()
        assert(result2, "2 отрезка должно быть активно")
        print("✅ 2 отрезка = активна")
        
        // Тест 4: 5 отрезков = активна
        for i in 0..<5 {
            tracker.setForcedSegmentState(segment: i, isActive: true)
        }
        let result5 = tracker.forceCompleteCurrentMinute()
        assert(result5, "5 отрезков должно быть активно")
        print("✅ 5 отрезков = активна")
    }
    
    /// Тест сценария запуска таймера
    static func testTimerStartScenario() {
        print("\n🎯 Тест: Сценарий запуска таймера")
        
        let tracker = MinuteActivityTracker()
        
        // Симулируем: пользователь запустил таймер (3 сек активности) и ушел
        tracker.setForcedSegmentState(segment: 0, isActive: true)  // Запуск таймера
        // Остальные 19 отрезков неактивны (по умолчанию false)
        
        let result = tracker.forceCompleteCurrentMinute()
        assert(!result, "Запуск таймера должен считаться погрешностью")
        print("✅ Запуск таймера (1 отрезок) = неактивная минута")
    }
    
    /// Тест обнаружения реальной активности
    static func testRealActivityDetection() {
        print("\n🎯 Тест: Обнаружение реальной активности")
        
        let tracker = MinuteActivityTracker()
        
        // Симулируем: пользователь работал 6 секунд (2 отрезка)
        tracker.setForcedSegmentState(segment: 5, isActive: true)
        tracker.setForcedSegmentState(segment: 6, isActive: true)
        
        let result = tracker.forceCompleteCurrentMinute()
        assert(result, "6 секунд работы должно быть активной минутой")
        print("✅ 6 секунд работы = активная минута")
        
        // Симулируем: пользователь работал всю минуту
        let tracker2 = MinuteActivityTracker()
        for i in 0..<20 {
            tracker2.setForcedSegmentState(segment: i, isActive: true)
        }
        
        let result2 = tracker2.forceCompleteCurrentMinute()
        assert(result2, "Работа всю минуту должна быть активной")
        print("✅ Работа всю минуту = активная минута")
    }
    
    /// Тест фильтрации случайной активности
    static func testRandomActivityFiltering() {
        print("\n🎯 Тест: Фильтрация случайной активности")
        
        // Тест 1: Кошка задела мышь
        let tracker1 = MinuteActivityTracker()
        tracker1.setForcedSegmentState(segment: 10, isActive: true)  // Случайное движение
        
        let result1 = tracker1.forceCompleteCurrentMinute()
        assert(!result1, "Случайное движение должно фильтроваться")
        print("✅ Случайное движение мыши = неактивная минута")
        
        // Тест 2: Упало что-то на клавиатуру
        let tracker2 = MinuteActivityTracker()
        tracker2.setForcedSegmentState(segment: 15, isActive: true)  // Случайное нажатие
        
        let result2 = tracker2.forceCompleteCurrentMinute()
        assert(!result2, "Случайное нажатие должно фильтроваться")
        print("✅ Случайное нажатие = неактивная минута")
        
        // Тест 3: Быстрая проверка сообщения (6 сек = 2 отрезка)
        let tracker3 = MinuteActivityTracker()
        tracker3.setForcedSegmentState(segment: 8, isActive: true)
        tracker3.setForcedSegmentState(segment: 9, isActive: true)
        
        let result3 = tracker3.forceCompleteCurrentMinute()
        assert(result3, "Быстрая проверка сообщения должна засчитываться")
        print("✅ Быстрая проверка сообщения (6 сек) = активная минута")
    }
}
