#!/usr/bin/env swift

import Foundation

// Простой тест компиляции SleepWakeDetector
print("🧪 Тест компиляции SleepWakeDetector")
print("===================================")

// Тест 1: Компиляция SleepWakeDetector.swift
print("1️⃣ Проверка компиляции SleepWakeDetector.swift...")

let compileProcess = Process()
compileProcess.launchPath = "/usr/bin/swiftc"
compileProcess.arguments = [
    "-c", "SimplePomodoroTest/SleepWakeDetector.swift",
    "-o", "/tmp/SleepWakeDetector_test.o"
]
compileProcess.currentDirectoryPath = "/Users/<USER>/EVO/uProd"

let pipe = Pipe()
compileProcess.standardError = pipe
compileProcess.launch()
compileProcess.waitUntilExit()

if compileProcess.terminationStatus == 0 {
    print("✅ SleepWakeDetector.swift компилируется успешно")
    
    // Удаляем временный файл
    try? FileManager.default.removeItem(atPath: "/tmp/SleepWakeDetector_test.o")
} else {
    let data = pipe.fileHandleForReading.readDataToEndOfFile()
    let error = String(data: data, encoding: .utf8) ?? "Unknown error"
    print("❌ Ошибка компиляции SleepWakeDetector.swift:")
    print(error)
    exit(1)
}

// Тест 2: Проверка что файл существует и содержит нужные классы
print("2️⃣ Проверка содержимого SleepWakeDetector.swift...")

do {
    let content = try String(contentsOfFile: "SimplePomodoroTest/SleepWakeDetector.swift")
    
    let requiredElements = [
        "class SleepWakeDetector",
        "static let shared",
        "func startMonitoring",
        "func stopMonitoring",
        "onSleepWakeEvent",
        "NSWorkspace"
    ]
    
    var allFound = true
    for element in requiredElements {
        if content.contains(element) {
            print("✅ Найден: \(element)")
        } else {
            print("❌ Не найден: \(element)")
            allFound = false
        }
    }
    
    if allFound {
        print("✅ Все необходимые элементы найдены в SleepWakeDetector.swift")
    } else {
        print("❌ Некоторые элементы отсутствуют в SleepWakeDetector.swift")
        exit(1)
    }
    
} catch {
    print("❌ Ошибка чтения файла SleepWakeDetector.swift: \(error)")
    exit(1)
}

// Тест 3: Проверка интеграции с ActivityStateManager
print("3️⃣ Проверка интеграции с ActivityStateManager...")

do {
    let activityContent = try String(contentsOfFile: "SimplePomodoroTest/ActivityStateManager.swift")
    
    let integrationElements = [
        "resetAfterSleep",
        "handleReturnAfterInactivity"
    ]
    
    var integrationFound = true
    for element in integrationElements {
        if activityContent.contains(element) {
            print("✅ Найден метод интеграции: \(element)")
        } else {
            print("❌ Не найден метод интеграции: \(element)")
            integrationFound = false
        }
    }
    
    if integrationFound {
        print("✅ Методы интеграции с ActivityStateManager найдены")
    } else {
        print("❌ Методы интеграции с ActivityStateManager отсутствуют")
        exit(1)
    }
    
} catch {
    print("❌ Ошибка чтения файла ActivityStateManager.swift: \(error)")
    exit(1)
}

print("")
print("🎉 ВСЕ ТЕСТЫ КОМПИЛЯЦИИ ПРОЙДЕНЫ!")
print("✅ SleepWakeDetector готов к использованию")
print("✅ Интеграция с ActivityStateManager подготовлена")
print("✅ Stage 5.2 завершен успешно")
print("")
print("📋 Следующие шаги:")
print("   - Исправить ошибки компиляции в AppDelegate.swift")
print("   - Включить унифицированную систему сна")
print("   - Протестировать реальную интеграцию")
