#!/usr/bin/env swift

import Foundation

// Компилируем и запускаем тест BreakEndWindow
let currentDir = FileManager.default.currentDirectoryPath
let testCommand = """
cd "\(currentDir)" && swiftc -o /tmp/RealBreakEndWindowTest \
SimplePomodoroTest/BreakEndWindow.swift \
-framework Cocoa \
-I /System/Library/Frameworks \
&& echo "🧪 Запуск теста BreakEndWindow..." \
&& /tmp/RealBreakEndWindowTest
"""

print("📋 Запуск: RealBreakEndWindowTest")
print("----------------------------------------")

let task = Process()
task.launchPath = "/bin/bash"
task.arguments = ["-c", testCommand]

let pipe = Pipe()
task.standardOutput = pipe
task.standardError = pipe

task.launch()
task.waitUntilExit()

let data = pipe.fileHandleForReading.readDataToEndOfFile()
let output = String(data: data, encoding: .utf8) ?? ""

if task.terminationStatus == 0 {
    print("✅ RealBreakEndWindowTest ПРОЙДЕН")
} else {
    print("❌ RealBreakEndWindowTest ПРОВАЛЕН")
    print("Вывод:")
    print(output)
}

// Дополнительная проверка кода
print("\n🔍 Проверка кода BreakEndWindow...")

guard let breakEndWindowCode = try? String(contentsOfFile: "SimplePomodoroTest/BreakEndWindow.swift") else {
    print("❌ Не удалось прочитать BreakEndWindow.swift")
    exit(1)
}

var testsPassed = 0
let totalTests = 7

// Тест 1: Проверяем наличие новых режимов
if breakEndWindowCode.contains("enum BreakEndMode") &&
   breakEndWindowCode.contains("userReturnPartialRest") &&
   breakEndWindowCode.contains("userReturnChoiceRest") &&
   breakEndWindowCode.contains("userReturnFullRest") {
    print("✅ Тест 1: Новые режимы BreakEndMode добавлены")
    testsPassed += 1
} else {
    print("❌ Тест 1: Новые режимы BreakEndMode не найдены")
}

// Тест 2: Проверяем функцию minuteWord
if breakEndWindowCode.contains("func minuteWord") &&
   breakEndWindowCode.contains("lastDigit == 1") &&
   breakEndWindowCode.contains("return \"минуту\"") &&
   breakEndWindowCode.contains("return \"минуты\"") &&
   breakEndWindowCode.contains("return \"минут\"") {
    print("✅ Тест 2: Функция minuteWord реализована корректно")
    testsPassed += 1
} else {
    print("❌ Тест 2: Функция minuteWord не найдена или неполная")
}

// Тест 3: Проверяем метод configureForMode
if breakEndWindowCode.contains("func configureForMode") &&
   breakEndWindowCode.contains("currentMode = mode") &&
   breakEndWindowCode.contains("updateContentForMode()") {
    print("✅ Тест 3: Метод configureForMode реализован")
    testsPassed += 1
} else {
    print("❌ Тест 3: Метод configureForMode не найден")
}

// Тест 4: Проверяем новые колбэки
if breakEndWindowCode.contains("var onContinueRest") &&
   breakEndWindowCode.contains("var onStartWork") &&
   breakEndWindowCode.contains("@objc private func continueRestClicked") &&
   breakEndWindowCode.contains("@objc private func startWorkClicked") {
    print("✅ Тест 4: Новые колбэки и action методы добавлены")
    testsPassed += 1
} else {
    print("❌ Тест 4: Новые колбэки или action методы не найдены")
}

// Тест 5: Проверяем updateContentForMode с правильной логикой кнопок
if breakEndWindowCode.contains("func updateContentForMode") &&
   breakEndWindowCode.contains("switch currentMode") &&
   breakEndWindowCode.contains("Вы отдыхали") &&
   breakEndWindowCode.contains("Keep resting") &&
   breakEndWindowCode.contains("Start session") &&
   breakEndWindowCode.contains("secondButton.isHidden = true") &&
   breakEndWindowCode.contains("secondButton.isHidden = false") {
    print("✅ Тест 5: Логика кнопок - Keep resting первая, Start session после 10+ мин")
    testsPassed += 1
} else {
    print("❌ Тест 5: Логика кнопок неправильная")
}

// Тест 6: Проверяем размер шрифта проекта
if breakEndWindowCode.contains("NSFont.systemFont(ofSize: 12, weight: .regular)") &&
   breakEndWindowCode.contains("NSColor.white.withAlphaComponent(0.7)") {
    print("✅ Тест 6: Размер шрифта проекта увеличен до 12pt")
    testsPassed += 1
} else {
    print("❌ Тест 6: Размер шрифта проекта не исправлен")
}

// Тест 7: Проверяем интеграцию с существующим дизайном
if breakEndWindowCode.contains("titleLabel") &&
   breakEndWindowCode.contains("subtitleLabel") &&
   breakEndWindowCode.contains("startButton") &&
   breakEndWindowCode.contains("secondButton") &&
   breakEndWindowCode.contains("projectLabel") {
    print("✅ Тест 7: Интеграция с существующим дизайном BreakEndWindow")
    testsPassed += 1
} else {
    print("❌ Тест 7: Интеграция с дизайном неполная")
}

print("\n🧪 Результаты теста BreakEndWindow:")
print("✅ Прошло: \(testsPassed)/\(totalTests)")

if testsPassed == totalTests {
    print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! BreakEndWindow готов к использованию")
} else {
    print("⚠️ Некоторые тесты не прошли. Требуется доработка.")
}
