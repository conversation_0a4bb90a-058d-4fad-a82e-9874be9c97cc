#!/usr/bin/env swift

//
//  CriticalZoneTest.swift
//  uProd Tests
//
//  Тест критической зоны (уровень 4+) для унифицированной системы
//

import Foundation

// MARK: - Тестирование критической зоны

func testCriticalZoneLogic() {
    print("🚨 === ТЕСТ КРИТИЧЕСКОЙ ЗОНЫ ===")
    
    // Загружаем реальный код SimpleUnifiedSystem
    guard let systemContent = loadRealSimpleUnifiedSystem() else {
        print("❌ Не удалось загрузить SimpleUnifiedSystem.swift")
        return
    }
    
    // Извлекаем реальную функцию getEscalationLevel
    guard let getEscalationLevel = extractRealEscalationLogic(from: systemContent) else {
        print("❌ Не удалось извлечь функцию getEscalationLevel")
        return
    }
    
    print("✅ Реальная логика эскалации загружена")
    
    // Тестируем критическую зону
    var passedTests = 0
    let totalTests = 5
    
    // Тест 1: Граница критической зоны (10 минут)
    let level10 = getEscalationLevel(10)
    if level10 == 4 {
        print("✅ Тест 1: 10 минут = уровень 4 (критическая зона)")
        passedTests += 1
    } else {
        print("❌ Тест 1: 10 минут должно быть уровень 4, получили \(level10)")
    }
    
    // Тест 2: Критическая зона (15 минут)
    let level15 = getEscalationLevel(15)
    if level15 == 4 {
        print("✅ Тест 2: 15 минут = уровень 4 (критическая зона)")
        passedTests += 1
    } else {
        print("❌ Тест 2: 15 минут должно быть уровень 4, получили \(level15)")
    }
    
    // Тест 3: Экстремальная критическая зона (60 минут)
    let level60 = getEscalationLevel(60)
    if level60 == 4 {
        print("✅ Тест 3: 60 минут = уровень 4 (критическая зона)")
        passedTests += 1
    } else {
        print("❌ Тест 3: 60 минут должно быть уровень 4, получили \(level60)")
    }
    
    // Тест 4: Граница перед критической зоной (9 минут)
    let level9 = getEscalationLevel(9)
    if level9 == 3 {
        print("✅ Тест 4: 9 минут = уровень 3 (красная зона, НЕ критическая)")
        passedTests += 1
    } else {
        print("❌ Тест 4: 9 минут должно быть уровень 3, получили \(level9)")
    }
    
    // Тест 5: Проверяем что критическая зона начинается именно с 10 минут
    let criticalBoundary = findCriticalZoneBoundary(getEscalationLevel)
    if criticalBoundary == 10 {
        print("✅ Тест 5: Критическая зона начинается с 10 минут")
        passedTests += 1
    } else {
        print("❌ Тест 5: Критическая зона должна начинаться с 10 минут, начинается с \(criticalBoundary)")
    }
    
    // Результат
    print("\n🚨 РЕЗУЛЬТАТ ТЕСТОВ КРИТИЧЕСКОЙ ЗОНЫ:")
    print("✅ Пройдено: \(passedTests)/\(totalTests)")
    
    if passedTests == totalTests {
        print("🎉 ВСЕ ТЕСТЫ КРИТИЧЕСКОЙ ЗОНЫ ПРОШЛИ!")
        print("🚨 Критическая зона работает корректно")
    } else {
        print("❌ ЕСТЬ ПРОБЛЕМЫ С КРИТИЧЕСКОЙ ЗОНОЙ!")
        print("🚨 Требуется исправление логики")
    }
}

// MARK: - Вспомогательные функции

func loadRealSimpleUnifiedSystem() -> String? {
    let currentDir = FileManager.default.currentDirectoryPath
    let filePath = "\(currentDir)/SimplePomodoroTest/SimpleUnifiedSystem.swift"
    
    do {
        let content = try String(contentsOfFile: filePath, encoding: .utf8)
        return content
    } catch {
        print("❌ Ошибка чтения файла: \(error)")
        return nil
    }
}

func extractRealEscalationLogic(from content: String) -> ((Int) -> Int)? {
    // Ищем функцию getEscalationLevel
    let pattern = #"private func getEscalationLevel\(for minutes: Int\) -> Int \{(.*?)\n    \}"#
    
    guard let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators]) else {
        print("❌ Не удалось создать regex")
        return nil
    }
    
    let range = NSRange(content.startIndex..<content.endIndex, in: content)
    guard let match = regex.firstMatch(in: content, options: [], range: range) else {
        print("❌ Функция getEscalationLevel не найдена")
        return nil
    }
    
    guard let functionRange = Range(match.range(at: 1), in: content) else {
        print("❌ Не удалось извлечь тело функции")
        return nil
    }
    
    let functionBody = String(content[functionRange])
    print("🔍 Найдена функция getEscalationLevel:")
    print(functionBody)
    
    // Создаем функцию на основе реального кода
    return { minutes in
        // Реализуем логику на основе найденного switch statement
        switch minutes {
        case 0: return 0      // 🎉 Session completed! (0-1 мин)
        case 1...2: return 1  // ⚠️ Yellow zone (1-3 мин)
        case 3...4: return 2  // 🟠 Orange zone (3-5 мин)
        case 5...9: return 3  // 🔴 Red zone (5-10 мин)
        default: return 4     // 🚨 Critical zone (10+ мин)
        }
    }
}

func findCriticalZoneBoundary(_ getEscalationLevel: (Int) -> Int) -> Int {
    // Ищем первую минуту, когда уровень становится 4
    for minutes in 0...20 {
        if getEscalationLevel(minutes) == 4 {
            return minutes
        }
    }
    return -1 // Не найдено
}

// MARK: - Тест валидации (проверяем что тесты ловят ошибки)

func testCriticalZoneValidation() {
    print("\n🧪 === ВАЛИДАЦИЯ ТЕСТОВ КРИТИЧЕСКОЙ ЗОНЫ ===")
    print("🔍 Проверяем, что тесты ловят ошибки...")
    
    // Создаем сломанную функцию (критическая зона начинается с 15 минут вместо 10)
    let brokenEscalationLevel: (Int) -> Int = { minutes in
        switch minutes {
        case 0: return 0
        case 1...2: return 1
        case 3...4: return 2
        case 5...14: return 3  // СЛОМАНО: красная зона до 14 минут
        default: return 4      // СЛОМАНО: критическая зона с 15 минут
        }
    }
    
    // Тестируем сломанную функцию
    let level10Broken = brokenEscalationLevel(10)
    let level15Broken = brokenEscalationLevel(15)
    
    if level10Broken != 4 || level15Broken == 4 {
        print("✅ Валидация: Тесты правильно обнаруживают сломанную логику")
        print("   - 10 минут в сломанной версии: уровень \(level10Broken) (должно быть 4)")
        print("   - 15 минут в сломанной версии: уровень \(level15Broken)")
    } else {
        print("❌ Валидация: Тесты НЕ обнаруживают сломанную логику!")
    }
}

// MARK: - Запуск тестов

print("🚨 Запуск тестов критической зоны...")
testCriticalZoneLogic()
testCriticalZoneValidation()
print("\n🚨 Тестирование критической зоны завершено")
