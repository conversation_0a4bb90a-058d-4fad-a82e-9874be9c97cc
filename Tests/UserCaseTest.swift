import Foundation

/// Простой тест для проверки конкретного случая пользователя
struct UserCaseTest {
    static func main() {
        print("🎯 ТЕСТ СЛУЧАЯ ПОЛЬЗОВАТЕЛЯ")
        print("Проблема: 30 мин планка, ожидается 35 мин, показывалось 40 мин")
        print(String(repeating: "=", count: 60))
        
        // Тестируем случай пользователя: 30 мин планка, 0 дней без работы
        print("\n📋 ИСХОДНЫЕ ДАННЫЕ:")
        print("   • Изначальная планка: 30 минут")
        print("   • Дни без работы: 0 (работал вчера)")
        print("   • Время дня: утро (1-е сообщение)")
        print("   • Ожидаемый результат: 35 минут (30 × 1.15 = 34.5 ≈ 35)")
        
        // Симулируем расчет
        let initialBar = 30
        
        // Расчет через GradualGrowthSystem
        let grownBar = applyGradualGrowth(to: TimeInterval(initialBar * 60))
        let result = Int(grownBar / 60)
        
        print("\n📊 РАСЧЕТ:")
        print("   • Изначальная планка: \(initialBar) мин")
        print("   • Применяем GradualGrowthSystem (26-40 мин → ×1.15): \(initialBar) × 1.15 = \(Double(initialBar) * 1.15)")
        print("   • Результат: \(result) мин")
        
        print("\n🎯 ПРОВЕРКА:")
        if result == 35 {
            print("   ✅ УСПЕХ! Получили ожидаемые 35 минут")
            print("   ✅ Проблема исправлена - отладочное и реальное окно теперь синхронизированы")
        } else {
            print("   ❌ ОШИБКА! Получили \(result) мин вместо 35 мин")
        }
        
        print("\n📝 ТЕХНИЧЕСКОЕ ОБЪЯСНЕНИЕ:")
        print("   Проблема была в том, что:")
        print("   • Отладочное окно использовало выбранные дни без работы")
        print("   • Реальное окно использовало реальные дни пользователя")
        print("   • Это приводило к разным результатам")
        print("")
        print("   Исправление:")
        print("   • Добавлена переменная debugDaysWithoutWork в EarlyEngagementSystem")
        print("   • createEngagementMessage() теперь проверяет debugDaysWithoutWork")
        print("   • Отладочное окно устанавливает debugDaysWithoutWork при показе реального окна")
    }
    
    static func applyGradualGrowth(to duration: TimeInterval) -> TimeInterval {
        let minutes = Int(duration / 60)
        
        switch minutes {
        case 3...7:
            return duration * 1.60
        case 8...15:
            return duration * 1.40
        case 16...25:
            return duration * 1.25
        case 26...40:
            return duration * 1.15  // Наш случай: 30 мин
        case 41...52:
            return duration * 1.10
        default:
            return duration
        }
    }
}

// Запуск теста
UserCaseTest.main()
