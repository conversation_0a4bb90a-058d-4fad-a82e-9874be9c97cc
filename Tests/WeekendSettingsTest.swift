import Foundation

/// Тест для проверки исправлений в настройках системы выходных
@main struct WeekendSettingsTest {
    static func main() {
        print("🧪 Запуск теста настроек системы выходных...")
        
        // Тест 1: Проверка доступности системы выходных
        testWeekendSystemAvailability()
        
        // Тест 2: Проверка стратегий выходных
        testWeekendStrategies()
        
        // Тест 3: Проверка логики рабочих дней
        testWorkdaysLogic()
        
        print("✅ Все тесты настроек системы выходных прошли успешно!")
    }
    
    /// Тест доступности системы выходных для разных уровней
    static func testWeekendSystemAvailability() {
        print("🔍 Тестирование доступности системы выходных...")
        
        // Проверяем что система доступна для всех уровней начиная с Regular
        let weekendManager = WeekendManager.shared
        let isAvailable = weekendManager.isWeekendSystemAvailable()
        
        print("📊 Система выходных доступна: \(isAvailable)")
        
        // Проверяем все стратегии
        for strategy in WeekendManager.WorkStrategy.allCases {
            print("📋 Стратегия '\(strategy.displayName)': \(strategy.rawValue)")
        }
        
        print("✅ Тест доступности системы выходных завершен")
    }
    
    /// Тест стратегий выходных
    static func testWeekendStrategies() {
        print("🔍 Тестирование стратегий выходных...")
        
        let weekendManager = WeekendManager.shared
        let originalStrategy = weekendManager.currentStrategy
        
        // Тестируем каждую стратегию
        for strategy in WeekendManager.WorkStrategy.allCases {
            weekendManager.currentStrategy = strategy
            
            let isWeekend = weekendManager.isWeekend()
            print("📅 Стратегия '\(strategy.displayName)': сегодня выходной = \(isWeekend)")
            
            // Для стратегии 3/1 проверяем смещающиеся выходные
            if strategy == .threeOne {
                let daysWithoutRest = weekendManager.getDaysWithoutRest()
                print("🔄 Дней без отдыха: \(daysWithoutRest)")
            }
        }
        
        // Восстанавливаем исходную стратегию
        weekendManager.currentStrategy = originalStrategy
        
        print("✅ Тест стратегий выходных завершен")
    }
    
    /// Тест логики рабочих дней
    static func testWorkdaysLogic() {
        print("🔍 Тестирование логики рабочих дней...")
        
        let weekendManager = WeekendManager.shared
        
        // Тестируем разные стратегии
        let strategies: [WeekendManager.WorkStrategy] = [.threeOne, .fiveTwo, .custom]
        
        for strategy in strategies {
            print("📋 Тестирование стратегии: \(strategy.displayName)")
            
            switch strategy {
            case .threeOne:
                print("   ⚪ Рабочие дни должны быть неактивными (управляются автоматически)")
            case .fiveTwo:
                print("   ⚪ Рабочие дни должны быть неактивными (фиксированные Пн-Пт)")
            case .custom:
                print("   🟢 Рабочие дни должны быть активными (пользователь выбирает)")
            }
        }
        
        // Проверяем текущую стратегию
        let currentStrategy = weekendManager.currentStrategy
        print("📊 Текущая стратегия: \(currentStrategy.displayName)")
        
        print("✅ Тест логики рабочих дней завершен")
    }
}
