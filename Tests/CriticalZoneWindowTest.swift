//
//  CriticalZoneWindowTest.swift
//  uProd
//
//  Тест для критического полноэкранного окна
//

import Cocoa

/// Тестирование критического окна
class CriticalZoneWindowTest {

    static func runAllTests() {
        print("🧪 Запуск теста CriticalZoneWindow...")

        // Тест 1: Создание окна
        testWindowCreation()

        // Тест 2: Проверка UI элементов
        testUIElements()

        // Тест 3: Проверка кнопки (главная проблема)
        testButtonStyling()

        print("✅ Все тесты CriticalZoneWindow пройдены!")
    }
    
    static func testWindowCreation() {
        print("🧪 Тест создания критического окна...")
        
        let window = CriticalZoneWindow(minutes: 10, intervalType: "informal")
        
        // Проверяем базовые свойства окна
        assert(window.level == .screenSaver, "Окно должно быть на уровне screenSaver")
        assert(window.backgroundColor?.alphaComponent == 0.95, "Фон должен быть почти черным")
        assert(window.styleMask.contains(.borderless), "Окно должно быть без рамки")
        
        print("✅ Создание окна работает корректно")
    }
    
    static func testUIElements() {
        print("🧪 Тест UI элементов...")
        
        let window = CriticalZoneWindow(minutes: 15, intervalType: "formal")
        
        // Проверяем что contentView существует
        assert(window.contentView != nil, "ContentView должен существовать")
        
        // Проверяем что это наш кастомный LayoutObservingView
        assert(window.contentView is CriticalZoneWindow.LayoutObservingView, 
               "ContentView должен быть LayoutObservingView")
        
        print("✅ UI элементы созданы корректно")
    }
    
    static func testButtonStyling() {
        print("🧪 Тест стилизации кнопки (основная проблема)...")
        
        let window = CriticalZoneWindow(minutes: 20, intervalType: "informal")
        
        // Находим кнопку в иерархии view
        guard let containerView = window.contentView else {
            fatalError("ContentView не найден")
        }
        
        var takeBreakButton: NSButton?
        
        // Ищем кнопку в подвидах
        func findButton(in view: NSView) {
            for subview in view.subviews {
                if let button = subview as? NSButton,
                   button.title.contains("Take a break now") {
                    takeBreakButton = button
                    return
                }
                findButton(in: subview)
            }
        }
        
        findButton(in: containerView)
        
        guard let button = takeBreakButton else {
            fatalError("Кнопка 'Take a break now' не найдена")
        }
        
        // Проверяем что кнопка настроена правильно
        assert(!button.isBordered, "Кнопка должна быть без стандартной рамки")
        assert(button.wantsLayer, "Кнопка должна использовать слои")
        
        // Проверяем что у кнопки есть слой
        assert(button.layer != nil, "У кнопки должен быть слой")
        
        // Проверяем что градиентный слой добавлен как подслой
        if let layer = button.layer {
            assert(layer.sublayers?.count ?? 0 > 0, "У кнопки должны быть подслои")
            
            // Проверяем что есть градиентный слой
            let hasGradientLayer = layer.sublayers?.contains { sublayer in
                sublayer is CAGradientLayer
            } ?? false
            
            assert(hasGradientLayer, "Должен быть градиентный подслой")
            
            // Проверяем что градиентный слой сохранен для обновления
            let savedGradient = layer.value(forKey: "gradientLayer") as? CAGradientLayer
            assert(savedGradient != nil, "Градиентный слой должен быть сохранен")
        }
        
        print("✅ Кнопка стилизована корректно - проблема с двойной кнопкой исправлена!")
    }
}
