import Foundation

// Enum для состояний активности (локальная копия для теста)
enum ActivityState: String, Codable, CaseIterable {
    case working = "working"
    case awayShort = "awayShort"
    case awayMedium = "awayMedium"
    case awayLong = "awayLong"
    case awayVeryLong = "awayVeryLong"
    case formalRest = "formalRest"
}

// Структура для записи активности (локальная копия для теста)
struct ActivityRecord: Codable {
    let date: Date
    let state: ActivityState
    let duration: TimeInterval
    let bandData: [Bool]
    
    init(date: Date, state: ActivityState, duration: TimeInterval, bandData: [Bool] = []) {
        self.date = date
        self.state = state
        self.duration = duration
        self.bandData = bandData
    }
}

// Упрощенная версия StatisticsManager только для активности
class SimpleActivityStatistics {
    private let userDefaults = UserDefaults.standard
    private let activityRecordsKey = "testActivityRecords"
    
    func recordActivityState(state: ActivityState, duration: TimeInterval, bandData: [Bool] = []) {
        let now = Date()
        let record = ActivityRecord(
            date: now,
            state: state,
            duration: duration,
            bandData: bandData
        )

        var records = getAllStoredActivityRecords()
        records.append(record)

        saveActivityRecords(records)
        
        let bandInfo = bandData.isEmpty ? "" : " (банды: \(bandData))"
        print("🎯 SimpleActivityStatistics: Записано состояние активности \(state), продолжительность: \(Int(duration)) сек\(bandInfo)")
    }
    
    func getActivityStatsForDateRange(from startDate: Date, to endDate: Date) -> [ActivityState: TimeInterval] {
        let records = getAllStoredActivityRecords().filter { record in
            record.date >= startDate && record.date < endDate
        }
        
        var stats: [ActivityState: TimeInterval] = [:]
        
        for record in records {
            stats[record.state, default: 0] += record.duration
        }
        
        return stats
    }
    
    func getActivityQualityForDateRange(from startDate: Date, to endDate: Date) -> Double {
        let stats = getActivityStatsForDateRange(from: startDate, to: endDate)
        
        let totalTime = stats.values.reduce(0, +)
        let workingTime = stats[.working] ?? 0
        
        guard totalTime > 0 else { return -1.0 }
        
        return workingTime / totalTime
    }
    
    func getAllStoredActivityRecords() -> [ActivityRecord] {
        guard let data = userDefaults.data(forKey: activityRecordsKey) else {
            return []
        }
        
        do {
            let records = try JSONDecoder().decode([ActivityRecord].self, from: data)
            return records.sorted { $0.date < $1.date }
        } catch {
            print("❌ Ошибка декодирования записей активности: \(error)")
            return []
        }
    }
    
    func saveActivityRecords(_ records: [ActivityRecord]) {
        do {
            let data = try JSONEncoder().encode(records)
            userDefaults.set(data, forKey: activityRecordsKey)
        } catch {
            print("❌ Ошибка сохранения записей активности: \(error)")
        }
    }
    
    func clearAllActivityRecords() {
        userDefaults.removeObject(forKey: activityRecordsKey)
        print("🎯 Все данные активности очищены")
    }
}

// Запуск теста
func runStatisticsActivityTest() {
        print("🧪 Запуск теста системы статистики активности")
        
        let stats = SimpleActivityStatistics()
        var passedTests = 0
        let totalTests = 5
        
        // Очищаем данные перед тестом
        stats.clearAllActivityRecords()
        
        // Тест 1: Запись состояний активности
        print("\n🧪 Тест 1: Запись состояний активности")
        let bandData = [true, false, true, false]
        
        stats.recordActivityState(state: .working, duration: 1800, bandData: bandData) // 30 мин
        stats.recordActivityState(state: .awayShort, duration: 300, bandData: [false, false, false, false]) // 5 мин
        stats.recordActivityState(state: .working, duration: 1200, bandData: [true, true, false, true]) // 20 мин
        
        let records = stats.getAllStoredActivityRecords()
        if records.count == 3 {
            print("✅ Записано 3 состояния активности")
            passedTests += 1
        } else {
            print("❌ Ожидалось 3 записи, получено: \(records.count)")
        }
        
        // Тест 2: Получение статистики за период
        print("\n🧪 Тест 2: Получение статистики за период")
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        let activityStats = stats.getActivityStatsForDateRange(from: today, to: tomorrow)
        
        let expectedWorkingTime: TimeInterval = 3000 // 30 + 20 мин = 50 мин = 3000 сек
        let expectedAwayTime: TimeInterval = 300 // 5 мин = 300 сек
        
        if let workingTime = activityStats[.working], let awayTime = activityStats[.awayShort] {
            if abs(workingTime - expectedWorkingTime) < 1 && abs(awayTime - expectedAwayTime) < 1 {
                print("✅ Статистика корректна: работа \(Int(workingTime)) сек, отдых \(Int(awayTime)) сек")
                passedTests += 1
            } else {
                print("❌ Неверная статистика: работа \(Int(workingTime)) сек (ожидалось \(Int(expectedWorkingTime))), отдых \(Int(awayTime)) сек (ожидалось \(Int(expectedAwayTime)))")
            }
        } else {
            print("❌ Не найдены ожидаемые состояния в статистике")
        }
        
        // Тест 3: Качество активности
        print("\n🧪 Тест 3: Качество активности")
        let quality = stats.getActivityQualityForDateRange(from: today, to: tomorrow)
        let expectedQuality = 3000.0 / 3300.0 // 3000 сек работы из 3300 сек общего времени
        
        if abs(quality - expectedQuality) < 0.01 {
            print("✅ Качество активности корректно: \(Int(quality * 100))%")
            passedTests += 1
        } else {
            print("❌ Неверное качество: \(Int(quality * 100))% (ожидалось \(Int(expectedQuality * 100))%)")
        }
        
        // Тест 4: Данные бандов
        print("\n🧪 Тест 4: Данные бандов")
        let firstRecord = records.first
        if let record = firstRecord, record.bandData == bandData {
            print("✅ Данные бандов сохранены корректно: \(record.bandData)")
            passedTests += 1
        } else {
            print("❌ Данные бандов не сохранены или неверны")
        }
        
        // Тест 5: Очистка данных
        print("\n🧪 Тест 5: Очистка данных")
        stats.clearAllActivityRecords()
        let recordsAfterClear = stats.getAllStoredActivityRecords()
        
        if recordsAfterClear.isEmpty {
            print("✅ Данные успешно очищены")
            passedTests += 1
        } else {
            print("❌ Данные не очищены: осталось \(recordsAfterClear.count) записей")
        }
        
        // Итоги
        print("\n==================================================")
        print("🧪 Результаты теста системы статистики активности:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Система статистики активности работает")
        } else {
            print("❌ Есть проблемы с системой статистики активности")
        }
        
        print("\n💡 Для проверки что тест ловит ошибки:")
        print("1. Сломайте метод recordActivityState() в SimpleActivityStatistics")
        print("2. Запустите тест - он должен упасть")
        print("3. Восстановите код и убедитесь что тест снова проходит")
        
        print("\n🔧 Этот тест проверяет основную логику статистики активности")
        print("🔧 Интеграция с полным StatisticsManager будет проверена отдельно")
}

// Запуск теста
runStatisticsActivityTest()
