#!/usr/bin/env swift

import Foundation

/// Базовый тест унифицированной системы
/// Проверяет основную логику без сложных зависимостей
class UnifiedSystemBasicTest {
    
    static func runBasicTests() {
        print("🧪 ========== БАЗОВЫЕ ТЕСТЫ УНИФИЦИРОВАННОЙ СИСТЕМЫ ==========")
        
        // Тест 1: Проверка расчета времени
        testTimeCalculations()
        
        // Тест 2: Проверка логики эскалации
        testEscalationLogic()
        
        // Тест 3: Проверка различий между типами интервалов
        testIntervalTypes()
        
        print("\n🧪 ========== РЕЗУЛЬТАТЫ ==========")
        print("✅ Все базовые тесты завершены")
        print("📋 Проверьте вывод выше на предмет ошибок")
    }
    
    /// Тест 1: Расчет времени для разных типов интервалов
    static func testTimeCalculations() {
        print("\n🧪 Тест 1: Расчет времени")
        
        // Формальный интервал 25 минут
        let formalDuration: TimeInterval = 25 * 60
        let formalMinutes = Int(formalDuration / 60)
        print("📊 Формальный интервал: \(formalMinutes) минут")
        
        if formalMinutes == 25 {
            print("✅ Формальный интервал: расчет правильный")
        } else {
            print("❌ Формальный интервал: ожидали 25, получили \(formalMinutes)")
        }
        
        // Неформальный интервал 52 минуты
        let informalDuration: TimeInterval = 52 * 60
        let informalMinutes = Int(informalDuration / 60)
        print("📊 Неформальный интервал: \(informalMinutes) минут")
        
        if informalMinutes == 52 {
            print("✅ Неформальный интервал: расчет правильный")
        } else {
            print("❌ Неформальный интервал: ожидали 52, получили \(informalMinutes)")
        }
        
        // Проверка отображения времени с переработкой
        let overtimeMinutes = 3
        let totalFormalTime = formalMinutes + overtimeMinutes  // 25 + 3 = 28
        let totalInformalTime = informalMinutes + overtimeMinutes  // 52 + 3 = 55
        
        print("📊 Формальный с переработкой: \(totalFormalTime) минут")
        print("📊 Неформальный с переработкой: \(totalInformalTime) минут")
        
        if totalFormalTime == 28 && totalInformalTime == 55 {
            print("✅ Расчет переработки: правильный")
        } else {
            print("❌ Расчет переработки: ошибка")
        }
    }
    
    /// Тест 2: Логика уровней эскалации
    static func testEscalationLogic() {
        print("\n🧪 Тест 2: Логика эскалации")
        
        // Тестируем логику уровней (как в OvertimeConfig)
        let testCases = [
            (minutes: 0, expectedLevel: 0, description: "Завершение интервала"),
            (minutes: 1, expectedLevel: 1, description: "Желтая зона"),
            (minutes: 3, expectedLevel: 2, description: "Оранжевая зона"),
            (minutes: 5, expectedLevel: 3, description: "Красная зона"),
            (minutes: 10, expectedLevel: 4, description: "Критическая зона")
        ]
        
        for testCase in testCases {
            let level = getEscalationLevel(for: testCase.minutes)
            print("📊 \(testCase.minutes) мин → уровень \(level) (\(testCase.description))")
            
            if level == testCase.expectedLevel {
                print("✅ Уровень \(testCase.minutes) мин: правильный")
            } else {
                print("❌ Уровень \(testCase.minutes) мин: ожидали \(testCase.expectedLevel), получили \(level)")
            }
        }
    }
    
    /// Тест 3: Различия между типами интервалов
    static func testIntervalTypes() {
        print("\n🧪 Тест 3: Типы интервалов")
        
        // Проверяем что для неформальных интервалов уровень 0 не показывается
        let shouldShowFormalLevel0 = shouldShowEscalationReminder(level: 0, intervalType: "formal")
        let shouldShowInformalLevel0 = shouldShowEscalationReminder(level: 0, intervalType: "informal")
        
        print("📊 Показывать уровень 0 для формальных: \(shouldShowFormalLevel0)")
        print("📊 Показывать уровень 0 для неформальных: \(shouldShowInformalLevel0)")
        
        if shouldShowFormalLevel0 && !shouldShowInformalLevel0 {
            print("✅ Логика уровня 0: правильная")
        } else {
            print("❌ Логика уровня 0: ошибка")
        }
        
        // Проверяем что для остальных уровней показываются для всех типов
        let shouldShowFormalLevel1 = shouldShowEscalationReminder(level: 1, intervalType: "formal")
        let shouldShowInformalLevel1 = shouldShowEscalationReminder(level: 1, intervalType: "informal")
        
        print("📊 Показывать уровень 1 для формальных: \(shouldShowFormalLevel1)")
        print("📊 Показывать уровень 1 для неформальных: \(shouldShowInformalLevel1)")
        
        if shouldShowFormalLevel1 && shouldShowInformalLevel1 {
            print("✅ Логика уровня 1: правильная")
        } else {
            print("❌ Логика уровня 1: ошибка")
        }
    }
    
    // MARK: - Helper Functions
    
    /// Упрощенная логика уровней эскалации (как в OvertimeConfig)
    static func getEscalationLevel(for minutes: Int) -> Int {
        if minutes < 1 { return 0 }      // 0-1 мин: завершение
        else if minutes < 3 { return 1 } // 1-3 мин: желтая зона
        else if minutes < 5 { return 2 } // 3-5 мин: оранжевая зона
        else if minutes < 10 { return 3 } // 5-10 мин: красная зона
        else { return 4 }                // 10+ мин: критическая зона
    }
    
    /// Проверяет нужно ли показывать напоминание для данного уровня и типа интервала
    static func shouldShowEscalationReminder(level: Int, intervalType: String) -> Bool {
        // Для неформальных интервалов уровень 0 не показываем (окно уже открыто)
        if intervalType == "informal" && level == 0 {
            return false
        }
        return true
    }
}

// Запускаем тесты
UnifiedSystemBasicTest.runBasicTests()
