import Foundation

struct SleepIntegrationTest {
    static func main() {
        print("🚀 ТЕСТ Интеграция с системой сна")

        var passed = 0
        var total = 0

        // Сценарий 1: Проверяем что файлы существуют
        total += 1
        print("\n📋 Сценарий 1: Проверка существования файлов системы сна")

        let fileManager = FileManager.default
        let sleepDetectorPath = "SimplePomodoroTest/SleepWakeDetector.swift"
        let activityManagerPath = "SimplePomodoroTest/ActivityStateManager.swift"

        if fileManager.fileExists(atPath: sleepDetectorPath) {
            print("✅ SleepWakeDetector.swift существует")
        } else {
            print("❌ SleepWakeDetector.swift не найден")
        }

        if fileManager.fileExists(atPath: activityManagerPath) {
            print("✅ ActivityStateManager.swift существует")
        } else {
            print("❌ ActivityStateManager.swift не найден")
        }

        print("✅ Файлы системы сна найдены")
        passed += 1

        // Сценарий 2: Проверяем интеграцию в AppDelegate
        total += 1
        print("\n📋 Сценарий 2: Проверка интеграции в AppDelegate")

        let appDelegatePath = "SimplePomodoroTest/AppDelegate.swift"
        if let content = try? String(contentsOfFile: appDelegatePath) {
            // Проверяем что добавлен ActivityStateManager
            if content.contains("var activityStateManager: ActivityStateManager!") {
                print("✅ ActivityStateManager объявлен в AppDelegate")
            } else {
                print("❌ ActivityStateManager не найден в AppDelegate")
            }

            // Проверяем что добавлена функция setupActivityStateManager
            if content.contains("setupActivityStateManager()") {
                print("✅ setupActivityStateManager() вызывается в AppDelegate")
            } else {
                print("❌ setupActivityStateManager() не найден в AppDelegate")
            }

            // Проверяем интеграцию с resetAfterSleep
            if content.contains("activityStateManager?.resetAfterSleep()") {
                print("✅ Интеграция resetAfterSleep() найдена")
            } else {
                print("❌ Интеграция resetAfterSleep() не найдена")
            }

            // Проверяем интеграцию с handleReturnAfterInactivity
            if content.contains("activityStateManager?.handleReturnAfterInactivity") {
                print("✅ Интеграция handleReturnAfterInactivity() найдена")
            } else {
                print("❌ Интеграция handleReturnAfterInactivity() не найдена")
            }

            print("✅ Интеграция в AppDelegate проверена")
            passed += 1
        } else {
            print("❌ Не удалось прочитать AppDelegate.swift")
        }
        
        // Сценарий 3: Проверяем методы ActivityStateManager
        total += 1
        print("\n📋 Сценарий 3: Проверка методов ActivityStateManager")

        if let content = try? String(contentsOfFile: activityManagerPath) {
            // Проверяем что есть метод resetAfterSleep
            if content.contains("func resetAfterSleep()") {
                print("✅ Метод resetAfterSleep() найден")
            } else {
                print("❌ Метод resetAfterSleep() не найден")
            }

            // Проверяем что есть метод handleReturnAfterInactivity
            if content.contains("func handleReturnAfterInactivity(duration: TimeInterval)") {
                print("✅ Метод handleReturnAfterInactivity() найден")
            } else {
                print("❌ Метод handleReturnAfterInactivity() не найден")
            }

            // Проверяем что есть устаревший метод с предупреждением
            if content.contains("@available(*, deprecated") && content.contains("resetAfterLongSleep") {
                print("✅ Устаревший метод resetAfterLongSleep() помечен как deprecated")
            } else {
                print("❌ Устаревший метод не найден или не помечен как deprecated")
            }

            print("✅ Методы ActivityStateManager проверены")
            passed += 1
        } else {
            print("❌ Не удалось прочитать ActivityStateManager.swift")
        }

        // Сценарий 4: Проверяем SleepWakeDetector
        total += 1
        print("\n📋 Сценарий 4: Проверка SleepWakeDetector")

        if let content = try? String(contentsOfFile: sleepDetectorPath) {
            // Проверяем что это singleton
            if content.contains("static let shared") {
                print("✅ SleepWakeDetector реализован как singleton")
            } else {
                print("❌ SleepWakeDetector не является singleton")
            }

            // Проверяем что есть колбэк для событий
            if content.contains("onSleepWakeEvent") {
                print("✅ Колбэк onSleepWakeEvent найден")
            } else {
                print("❌ Колбэк onSleepWakeEvent не найден")
            }

            print("✅ SleepWakeDetector проверен")
            passed += 1
        } else {
            print("❌ Не удалось прочитать SleepWakeDetector.swift")
        }
        
        // Сценарий 5: Проверяем TODO комментарии удалены
        total += 1
        print("\n📋 Сценарий 5: Проверка что TODO комментарии удалены")

        if let content = try? String(contentsOfFile: appDelegatePath) {
            // Проверяем что старые TODO комментарии удалены
            let todoCount = content.components(separatedBy: "TODO: Интеграция с ActivityStateManager").count - 1

            if todoCount == 0 {
                print("✅ Все TODO комментарии для интеграции удалены")
                passed += 1
            } else {
                print("❌ Найдено \(todoCount) TODO комментариев - интеграция не завершена")
            }
        } else {
            print("❌ Не удалось прочитать AppDelegate.swift")
        }

        // Сценарий 6: Проверяем логику различения сна vs неактивности
        total += 1
        print("\n📋 Сценарий 6: Логика различения сна vs неактивности")

        // Проверяем что в AppDelegate есть разные функции для разных случаев
        if let content = try? String(contentsOfFile: appDelegatePath) {
            if content.contains("handleRealSleep()") && content.contains("handleLongInactivity()") {
                print("✅ Разные функции для сна и неактивности найдены")
            } else {
                print("❌ Функции для различения сна и неактивности не найдены")
            }

            // Проверяем что вызываются разные методы ActivityStateManager
            let resetAfterSleepCount = content.components(separatedBy: "resetAfterSleep()").count - 1
            let handleReturnCount = content.components(separatedBy: "handleReturnAfterInactivity").count - 1

            if resetAfterSleepCount > 0 && handleReturnCount > 0 {
                print("✅ Разные методы ActivityStateManager вызываются для разных сценариев")
                passed += 1
            } else {
                print("❌ Не все методы ActivityStateManager интегрированы")
            }
        } else {
            print("❌ Не удалось прочитать AppDelegate.swift")
        }
        
        print("\n" + String(repeating: "=", count: 50))
        print("📊 РЕЗУЛЬТАТ ТЕСТА ИНТЕГРАЦИИ С СИСТЕМОЙ СНА")
        print(String(repeating: "=", count: 50))
        print("✅ Пройдено: \(passed)/\(total)")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ИНТЕГРАЦИИ ПРОШЛИ!")
            print("✅ Система сна интегрирована корректно")
        } else {
            print("❌ ЕСТЬ ПРОБЛЕМЫ С ИНТЕГРАЦИЕЙ")
            print("🔧 Требуется доработка")
        }
    }
}

// Запускаем тест
SleepIntegrationTest.main()
