import Foundation

/// Тест интеграции ButtonMatrix с EarlyEngagementWindow
/// Проверяет что кнопки создаются правильно и соответствуют ButtonMatrix
@main struct EarlyEngagementButtonMatrixTest {
    static func main() {
        print("🧪 Тест интеграции ButtonMatrix с EarlyEngagementWindow")
        
        // Тест 1: Проверяем что ButtonMatrix генерирует правильные кнопки
        testButtonMatrixGeneration()
        
        // Тест 2: Проверяем что EarlyEngagementWindow использует кнопки от ButtonMatrix
        testEarlyEngagementWindowIntegration()
        
        // Тест 3: Проверяем что отладочное окно передает кнопки правильно
        testDebugWindowIntegration()
        
        print("✅ Все тесты пройдены!")
    }
    
    /// Тест генерации кнопок ButtonMatrix
    static func testButtonMatrixGeneration() {
        print("\n🔍 Тест 1: Генерация кнопок ButtonMatrix")
        
        // Тестируем разные сценарии
        let scenarios = [
            (daysWithoutWork: 0, messageIndex: 0, baseBarMinutes: 3, expected: "Начать работу (3 мин)"),
            (daysWithoutWork: 1, messageIndex: 1, baseBarMinutes: 5, expected: "Начать работу (5 мин)"),
            (daysWithoutWork: 2, messageIndex: 2, baseBarMinutes: 10, expected: "Начать работу (10 мин)")
        ]
        
        for scenario in scenarios {
            let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
                daysWithoutWork: scenario.daysWithoutWork,
                messageIndex: scenario.messageIndex,
                baseBarMinutes: scenario.baseBarMinutes
            )
            
            // Проверяем что есть хотя бы одна кнопка
            assert(!buttons.isEmpty, "ButtonMatrix должен генерировать хотя бы одну кнопку")
            
            // Проверяем что первая кнопка - primary
            let primaryButton = buttons.first!
            assert(primaryButton.type == .primary, "Первая кнопка должна быть primary")
            
            // Проверяем текст кнопки
            assert(primaryButton.text.contains("Начать работу"), "Текст должен содержать 'Начать работу'")
            assert(primaryButton.text.contains("\(scenario.baseBarMinutes) мин"), "Текст должен содержать время")
            
            // Проверяем что есть кнопка "Позже"
            let laterButton = buttons.first { $0.type == .later }
            assert(laterButton != nil, "Должна быть кнопка 'Позже'")
            assert(laterButton!.text == "Позже", "Текст кнопки 'Позже' должен быть правильным")
            
            print("✓ Сценарий \(scenario.daysWithoutWork) дней: \(buttons.count) кнопок, primary: '\(primaryButton.text)'")
        }
    }
    
    /// Тест интеграции с EarlyEngagementWindow
    static func testEarlyEngagementWindowIntegration() {
        print("\n🔍 Тест 2: Интеграция с EarlyEngagementWindow")
        
        // Создаем тестовое сообщение с ButtonMatrix
        let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
            daysWithoutWork: 1,
            messageIndex: 0,
            baseBarMinutes: 5
        )
        
        var message = EngagementMessage(
            title: "Тестовое сообщение",
            subtitle: "Проверка интеграции",
            proposedDuration: 300,
            buttonText: "Старый текст",
            level: 0,
            timeOfDay: 0
        )
        
        // Добавляем кнопки от ButtonMatrix
        message.buttonMatrix = buttons
        
        // Проверяем что сообщение содержит правильные кнопки
        assert(message.buttonMatrix != nil, "Сообщение должно содержать кнопки от ButtonMatrix")
        assert(message.buttonMatrix!.count >= 2, "Должно быть минимум 2 кнопки")
        
        let primaryButton = message.buttonMatrix!.first { $0.type == .primary }
        assert(primaryButton != nil, "Должна быть primary кнопка")
        assert(primaryButton!.text.contains("Начать работу"), "Primary кнопка должна содержать 'Начать работу'")
        
        let laterButton = message.buttonMatrix!.first { $0.type == .later }
        assert(laterButton != nil, "Должна быть кнопка 'Позже'")
        
        print("✓ Сообщение содержит \(message.buttonMatrix!.count) кнопок от ButtonMatrix")
        print("✓ Primary кнопка: '\(primaryButton!.text)'")
        print("✓ Later кнопка: '\(laterButton!.text)'")
    }
    
    /// Тест интеграции отладочного окна
    static func testDebugWindowIntegration() {
        print("\n🔍 Тест 3: Интеграция отладочного окна")
        
        // Симулируем создание сообщения в отладочном окне
        let realDaysWithoutWork = 1
        let messageIndex = 0
        let calculatedBarMinutes = 5
        
        // Генерируем кнопки как в отладочном окне
        let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
            daysWithoutWork: realDaysWithoutWork,
            messageIndex: messageIndex,
            baseBarMinutes: calculatedBarMinutes
        )
        
        // Создаем сообщение как в отладочном окне
        var processedMessage = EngagementMessage(
            title: "Отладочное сообщение",
            subtitle: "Тест отладочного окна",
            proposedDuration: TimeInterval(calculatedBarMinutes * 60),
            buttonText: "Старый текст",
            level: messageIndex,
            timeOfDay: 0
        )
        
        // Обновляем текст основной кнопки (как в отладочном окне)
        if let primaryButton = buttons.first {
            processedMessage.buttonText = primaryButton.text
        }
        
        // Передаем кнопки от ButtonMatrix в сообщение
        processedMessage.buttonMatrix = buttons
        
        // Проверяем результат
        assert(processedMessage.buttonMatrix != nil, "Отладочное сообщение должно содержать кнопки от ButtonMatrix")
        assert(processedMessage.buttonText.contains("Начать работу"), "Текст кнопки должен быть обновлен")
        assert(processedMessage.buttonText.contains("\(calculatedBarMinutes) мин"), "Текст должен содержать правильное время")
        
        print("✓ Отладочное окно правильно интегрирует ButtonMatrix")
        print("✓ Обновленный текст кнопки: '\(processedMessage.buttonText)'")
        print("✓ Количество кнопок: \(processedMessage.buttonMatrix!.count)")
    }
}
