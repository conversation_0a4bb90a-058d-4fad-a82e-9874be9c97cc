import Foundation

/// Тест системы матрицы кнопок для раннего вовлечения
/// Проверяет правильность генерации кнопок для всех позиций матрицы
struct ButtonMatrixTest {

    static func runTests() {
        print("🚀 ТЕСТ СИСТЕМЫ МАТРИЦЫ КНОПОК")
        print(String(repeating: "=", count: 60))
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Базовая генерация кнопок
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Базовая генерация кнопок")
        
        let buttons = ButtonMatrix.generateButtons(vertical: 0, horizontal: 0, baseBar: 30*60)
        
        if !buttons.isEmpty && buttons.first?.type == .primary {
            print("✅ ПРОЙДЕН: Кнопки генерируются корректно")
            print("   Количество кнопок: \(buttons.count)")
            print("   Первая кнопка: \(buttons.first?.text ?? "НЕТ")")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Кнопки НЕ генерируются")
            print("   Количество: \(buttons.count), первая: \(buttons.first?.text ?? "НЕТ")")
        }
        
        // СЦЕНАРИЙ 2: Проверка горизонтальной дескалации
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Горизонтальная дескалация")
        
        let buttons1 = ButtonMatrix.generateButtons(vertical: 0, horizontal: 0, baseBar: 30*60) // 100%
        let buttons2 = ButtonMatrix.generateButtons(vertical: 0, horizontal: 1, baseBar: 30*60) // 50%
        
        let duration1 = buttons1.first?.duration ?? 0
        let duration2 = buttons2.first?.duration ?? 0
        
        if duration2 < duration1 && duration2 >= 15*60 {
            print("✅ ПРОЙДЕН: Горизонтальная дескалация работает")
            print("   1-е сообщение: \(Int(duration1/60)) мин")
            print("   2-е сообщение: \(Int(duration2/60)) мин")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Дескалация НЕ работает")
            print("   1-е: \(Int(duration1/60)) мин, 2-е: \(Int(duration2/60)) мин")
        }
        
        // СЦЕНАРИЙ 3: Фиксированные уровни (3-е и 4-е сообщения)
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Фиксированные уровни")
        
        let buttons3 = ButtonMatrix.generateButtons(vertical: 0, horizontal: 2, baseBar: 30*60) // 15 мин
        let buttons4 = ButtonMatrix.generateButtons(vertical: 0, horizontal: 3, baseBar: 30*60) // 3 мин
        
        let duration3 = buttons3.first?.duration ?? 0
        let duration4 = buttons4.first?.duration ?? 0
        
        if duration3 == 15*60 && duration4 == 3*60 {
            print("✅ ПРОЙДЕН: Фиксированные уровни работают")
            print("   3-е сообщение: \(Int(duration3/60)) мин (должно быть 15)")
            print("   4-е сообщение: \(Int(duration4/60)) мин (должно быть 3)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Фиксированные уровни НЕ работают")
            print("   3-е: \(Int(duration3/60)) мин, 4-е: \(Int(duration4/60)) мин")
        }
        
        // СЦЕНАРИЙ 4: Вертикальная адаптация (7+ дней)
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Вертикальная адаптация (7+ дней)")
        
        let buttonsLevel7 = ButtonMatrix.generateButtons(vertical: 7, horizontal: 0, baseBar: 30*60)
        let durationLevel7 = buttonsLevel7.first?.duration ?? 0
        
        if durationLevel7 == 3*60 {
            print("✅ ПРОЙДЕН: Вертикальная адаптация работает")
            print("   7+ дней: \(Int(durationLevel7/60)) мин (должно быть 3 - план-минимум)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Вертикальная адаптация НЕ работает")
            print("   7+ дней: \(Int(durationLevel7/60)) мин (должно быть 3)")
        }
        
        // СЦЕНАРИЙ 5: Полная сессия (показывается когда планка < 52)
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Кнопка полной сессии")
        
        let buttonsSmall = ButtonMatrix.generateButtons(vertical: 1, horizontal: 0, baseBar: 30*60)
        let buttonsBig = ButtonMatrix.generateButtons(vertical: 0, horizontal: 0, baseBar: 52*60)
        
        let hasFullSessionSmall = buttonsSmall.contains { $0.type == .fullSession }
        let hasFullSessionBig = buttonsBig.contains { $0.type == .fullSession }
        
        if hasFullSessionSmall && !hasFullSessionBig {
            print("✅ ПРОЙДЕН: Полная сессия показывается правильно")
            print("   При планке < 52: показывается")
            print("   При планке >= 52: НЕ показывается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Полная сессия работает неправильно")
            print("   При планке < 52: \(hasFullSessionSmall ? "показывается" : "НЕ показывается")")
            print("   При планке >= 52: \(hasFullSessionBig ? "показывается" : "НЕ показывается")")
        }
        
        // СЦЕНАРИЙ 6: Полная планка для горизонтали 1+
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Кнопка полной планки для горизонтали")
        
        let buttonsH0 = ButtonMatrix.generateButtons(vertical: 0, horizontal: 0, baseBar: 30*60)
        let buttonsH1 = ButtonMatrix.generateButtons(vertical: 0, horizontal: 1, baseBar: 30*60)
        
        let hasFullBarH0 = buttonsH0.contains { $0.type == .fullBar }
        let hasFullBarH1 = buttonsH1.contains { $0.type == .fullBar }
        
        if !hasFullBarH0 && hasFullBarH1 {
            print("✅ ПРОЙДЕН: Полная планка показывается правильно")
            print("   1-е сообщение: НЕ показывается")
            print("   2-е сообщение: показывается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Полная планка работает неправильно")
            print("   1-е сообщение: \(hasFullBarH0 ? "показывается" : "НЕ показывается")")
            print("   2-е сообщение: \(hasFullBarH1 ? "показывается" : "НЕ показывается")")
        }
        
        // СЦЕНАРИЙ 7: Тестирование всех комбинаций матрицы 5x4
        total += 1
        print("\n📋 СЦЕНАРИЙ 7: Полная матрица 5x4")
        
        var allCombinationsWork = true
        var combinationCount = 0
        
        for vertical in 0..<5 {
            for horizontal in 0..<4 {
                let buttons = ButtonMatrix.generateButtons(vertical: vertical, horizontal: horizontal, baseBar: 30*60)
                combinationCount += 1
                
                if buttons.isEmpty || buttons.first?.type != .primary {
                    allCombinationsWork = false
                    print("❌ Проблема с комбинацией v\(vertical)h\(horizontal)")
                }
            }
        }
        
        if allCombinationsWork && combinationCount == 20 {
            print("✅ ПРОЙДЕН: Все 20 комбинаций матрицы работают")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Найдены проблемы в матрице")
            print("   Всего комбинаций: \(combinationCount), все работают: \(allCombinationsWork)")
        }
        
        // СЦЕНАРИЙ 8: Форматирование для отладки
        total += 1
        print("\n📋 СЦЕНАРИЙ 8: Форматирование для отладки")
        
        let testButtons = ButtonMatrix.generateButtons(vertical: 0, horizontal: 1, baseBar: 30*60)
        let formatted = ButtonMatrix.formatButtonsForDebug(testButtons)
        let detailed = ButtonMatrix.createDetailedButtonDescription(testButtons)
        
        if formatted.contains("[") && detailed.contains("тип:") {
            print("✅ ПРОЙДЕН: Форматирование работает")
            print("   Краткое: \(formatted)")
            print("   Детальное содержит типы: ДА")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Форматирование НЕ работает")
            print("   Краткое: \(formatted)")
            print("   Детальное содержит типы: НЕТ")
        }
        
        // ИТОГОВЫЙ ОТЧЕТ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 ИТОГОВЫЙ ОТЧЕТ")
        print("✅ Пройдено: \(passed)/\(total)")
        print("❌ Провалено: \(total - passed)/\(total)")
        print("📈 Процент успеха: \(Int(Double(passed)/Double(total)*100))%")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Система ButtonMatrix работает корректно.")
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ! Нужно исправить \(total - passed) тест(ов).")
        }
        
        // Демонстрация работы системы
        print("\n🎪 ДЕМОНСТРАЦИЯ РАБОТЫ СИСТЕМЫ:")
        demonstrateButtonMatrix()
    }
    
    /// Демонстрирует работу системы на примерах
    static func demonstrateButtonMatrix() {
        let examples = [
            (vertical: 0, horizontal: 0, baseBar: 30, description: "0 дней, 1-е сообщение, планка 30 мин"),
            (vertical: 0, horizontal: 1, baseBar: 30, description: "0 дней, 2-е сообщение, планка 30 мин"),
            (vertical: 4, horizontal: 0, baseBar: 30, description: "7+ дней, 1-е сообщение, планка 30 мин"),
            (vertical: 0, horizontal: 3, baseBar: 30, description: "0 дней, 4-е сообщение, планка 30 мин")
        ]
        
        for example in examples {
            print("\n📍 \(example.description):")
            let buttons = ButtonMatrix.generateButtons(
                vertical: example.vertical,
                horizontal: example.horizontal,
                baseBar: TimeInterval(example.baseBar * 60)
            )
            let formatted = ButtonMatrix.formatButtonsForDebug(buttons)
            print("   Кнопки: \(formatted)")
        }
    }
}

// Запуск тестов
ButtonMatrixTest.runTests()

// MARK: - ButtonMatrix для тестирования (копия основной логики)

/// Копия ButtonMatrix для тестирования (чтобы тесты были независимы)
struct ButtonMatrix {
    
    struct ButtonComponent {
        let text: String
        let duration: TimeInterval
        let type: ButtonType
        let context: String
        
        var debugDescription: String {
            let minutes = Int(duration / 60)
            return "[\(text)] - тип: \(type.rawValue), время: \(minutes) мин, контекст: \(context)"
        }
    }
    
    enum ButtonType: String, CaseIterable {
        case primary = "primary"
        case fullBar = "fullBar"
        case fullSession = "fullSession"
        case planMinimum = "planMinimum"
        case later = "later"
        case snooze = "snooze"
    }
    
    static func generateButtons(vertical: Int, horizontal: Int, baseBar: TimeInterval) -> [ButtonComponent] {
        var buttons: [ButtonComponent] = []
        
        let calculatedBar = calculateFinalBar(vertical: vertical, horizontal: horizontal, baseBar: baseBar)
        
        buttons.append(createPrimaryButton(duration: calculatedBar))
        
        if horizontal >= 1 {
            let fullBar = calculateVerticalBar(vertical: vertical, baseBar: baseBar)
            if fullBar > calculatedBar {
                buttons.append(createFullBarButton(duration: fullBar))
            }
        }
        
        if calculatedBar < 52 * 60 {
            buttons.append(createFullSessionButton())
        }
        
        buttons.append(createLaterButton())
        
        return buttons
    }
    
    private static func calculateFinalBar(vertical: Int, horizontal: Int, baseBar: TimeInterval) -> TimeInterval {
        let verticalBar = calculateVerticalBar(vertical: vertical, baseBar: baseBar)
        return calculateHorizontalBar(horizontal: horizontal, verticalBar: verticalBar)
    }
    
    private static func calculateVerticalBar(vertical: Int, baseBar: TimeInterval) -> TimeInterval {
        switch vertical {
        case 0: return baseBar * 1.14
        case 1: return baseBar * 0.77
        case 2...3: return baseBar * 0.48
        case 4...6: return baseBar * 0.29
        default: return 3 * 60
        }
    }
    
    private static func calculateHorizontalBar(horizontal: Int, verticalBar: TimeInterval) -> TimeInterval {
        switch horizontal {
        case 0: return max(verticalBar, 3 * 60)
        case 1: return max(verticalBar * 0.5, 3 * 60)
        case 2: return 15 * 60
        case 3: return 3 * 60
        default: return max(verticalBar, 3 * 60)
        }
    }
    
    private static func createPrimaryButton(duration: TimeInterval) -> ButtonComponent {
        let minutes = Int(duration / 60)
        return ButtonComponent(text: "Начать работу (\(minutes) мин)", duration: duration, type: .primary, context: "calculated_bar")
    }
    
    private static func createFullBarButton(duration: TimeInterval) -> ButtonComponent {
        let minutes = Int(duration / 60)
        return ButtonComponent(text: "Полная планка (\(minutes) мин)", duration: duration, type: .fullBar, context: "full_bar_before_horizontal")
    }
    
    private static func createFullSessionButton() -> ButtonComponent {
        return ButtonComponent(text: "Полная сессия (52 мин)", duration: 52 * 60, type: .fullSession, context: "full_session_52min")
    }
    
    private static func createLaterButton() -> ButtonComponent {
        return ButtonComponent(text: "Позже", duration: 0, type: .later, context: "postpone")
    }
    
    static func formatButtonsForDebug(_ buttons: [ButtonComponent]) -> String {
        return buttons.map { "[\($0.text)]" }.joined(separator: " | ")
    }
    
    static func createDetailedButtonDescription(_ buttons: [ButtonComponent]) -> String {
        var description = "🔘 Детальная информация о кнопках:\n"
        for (index, button) in buttons.enumerated() {
            description += "   Кнопка \(index + 1): \(button.debugDescription)\n"
        }
        return description
    }
    
    static func generateButtonsForEarlyEngagement(daysWithoutWork: Int, messageIndex: Int, baseBarMinutes: Int) -> [ButtonComponent] {
        let vertical = min(4, max(0, daysWithoutWork))
        let horizontal = min(3, max(0, messageIndex))
        let baseBar = TimeInterval(baseBarMinutes * 60)
        return generateButtons(vertical: vertical, horizontal: horizontal, baseBar: baseBar)
    }
}
