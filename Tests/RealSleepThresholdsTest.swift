import Foundation

/// РЕАЛЬНЫЙ ТЕСТ ПОРОГОВ СНА И НЕАКТИВНОСТИ
/// Читает пороги из реального кода и проверяет логику

struct RealSleepThresholdsTest {
    static func main() {
        print("🚀 РЕАЛЬНЫЙ ТЕСТ ПОРОГОВ СНА И НЕАКТИВНОСТИ")
        print(String(repeating: "=", count: 60))
        
        var passed = 0
        var total = 0
        
        let appDelegatePath = "SimplePomodoroTest/AppDelegate.swift"
        
        guard let content = try? String(contentsOfFile: appDelegatePath, encoding: .utf8) else {
            print("❌ Не удалось прочитать AppDelegate.swift")
            return
        }
        
        // ТЕСТ 1: Извлечение реального порога сна
        total += 1
        print("\n📋 ТЕСТ 1: Извлечение порога реального сна из кода")
        
        var realSleepThreshold: TimeInterval = 0
        
        // Ищем строку с realSleepThreshold
        let lines = content.components(separatedBy: .newlines)
        for line in lines {
            if line.contains("realSleepThreshold") && line.contains("=") && line.contains("*") {
                // Извлекаем число перед * 60
                let components = line.components(separatedBy: "*")
                if components.count >= 2 {
                    let numberPart = components[0].trimmingCharacters(in: .whitespaces)
                    if let number = Double(numberPart.components(separatedBy: " ").last ?? "") {
                        realSleepThreshold = number * 60
                        print("✅ Найден порог реального сна: \(Int(number)) минут (\(Int(realSleepThreshold)) секунд)")
                        passed += 1
                        break
                    }
                }
            }
        }
        
        if realSleepThreshold == 0 {
            print("❌ Порог реального сна не найден в коде")
        }
        
        // ТЕСТ 2: Извлечение реального порога неактивности
        total += 1
        print("\n📋 ТЕСТ 2: Извлечение порога неактивности из кода")
        
        var inactivityThreshold: TimeInterval = 0
        
        for line in lines {
            if line.contains("inactivityThreshold") && line.contains("=") && line.contains("*") {
                let components = line.components(separatedBy: "*")
                if components.count >= 2 {
                    let numberPart = components[0].trimmingCharacters(in: .whitespaces)
                    if let number = Double(numberPart.components(separatedBy: " ").last ?? "") {
                        inactivityThreshold = number * 60
                        print("✅ Найден порог неактивности: \(Int(number)) минут (\(Int(inactivityThreshold)) секунд)")
                        passed += 1
                        break
                    }
                }
            }
        }
        
        if inactivityThreshold == 0 {
            print("❌ Порог неактивности не найден в коде")
        }
        
        // ТЕСТ 3: Проверка логики порогов
        total += 1
        print("\n📋 ТЕСТ 3: Проверка логики порогов")
        
        if realSleepThreshold > 0 && inactivityThreshold > 0 {
            if inactivityThreshold > realSleepThreshold {
                print("✅ Логика корректна: порог неактивности (\(Int(inactivityThreshold/60)) мин) > порога сна (\(Int(realSleepThreshold/60)) мин)")
                print("✅ Это означает что система различает короткий сон от длительной неактивности")
                passed += 1
            } else {
                print("❌ Логика некорректна: порог неактивности (\(Int(inactivityThreshold/60)) мин) <= порога сна (\(Int(realSleepThreshold/60)) мин)")
                print("❌ Система не сможет корректно различать сон и неактивность")
            }
        } else {
            print("❌ Не удалось извлечь пороги для проверки логики")
        }
        
        // ТЕСТ 4: Проверка использования порогов в коде
        total += 1
        print("\n📋 ТЕСТ 4: Проверка использования порогов в handleRealSleep")
        
        let hasRealSleepUsage = content.contains("duration > realSleepThreshold")
        if hasRealSleepUsage {
            print("✅ Порог realSleepThreshold используется в условии")
            passed += 1
        } else {
            print("❌ Порог realSleepThreshold НЕ используется в условии")
        }
        
        // ТЕСТ 5: Проверка использования порогов в handleLongInactivity
        total += 1
        print("\n📋 ТЕСТ 5: Проверка использования порогов в handleLongInactivity")
        
        let hasInactivityUsage = content.contains("duration > inactivityThreshold")
        if hasInactivityUsage {
            print("✅ Порог inactivityThreshold используется в условии")
            passed += 1
        } else {
            print("❌ Порог inactivityThreshold НЕ используется в условии")
        }
        
        // ТЕСТ 6: Проверка разных методов для разных сценариев
        total += 1
        print("\n📋 ТЕСТ 6: Проверка вызовов разных методов ActivityStateManager")
        
        let resetAfterSleepCount = content.components(separatedBy: "resetAfterSleep()").count - 1
        let handleReturnCount = content.components(separatedBy: "handleReturnAfterInactivity").count - 1
        
        if resetAfterSleepCount > 0 && handleReturnCount > 0 {
            print("✅ resetAfterSleep() вызывается \(resetAfterSleepCount) раз")
            print("✅ handleReturnAfterInactivity() вызывается \(handleReturnCount) раз")
            print("✅ Разные методы используются для разных сценариев")
            passed += 1
        } else {
            print("❌ Не все методы ActivityStateManager интегрированы")
            print("❌ resetAfterSleep: \(resetAfterSleepCount), handleReturn: \(handleReturnCount)")
        }
        
        print("\n" + String(repeating: "=", count: 50))
        print("📊 РЕЗУЛЬТАТ РЕАЛЬНОГО ТЕСТИРОВАНИЯ ПОРОГОВ")
        print(String(repeating: "=", count: 50))
        print("✅ Пройдено: \(passed)/\(total)")
        
        if passed == total {
            print("🎉 ВСЕ РЕАЛЬНЫЕ ПОРОГИ ПРОТЕСТИРОВАНЫ!")
            print("✅ Система корректно читает пороги из кода")
            print("✅ Логика различения сна и неактивности работает")
            print("✅ Интеграция с ActivityStateManager корректна")
        } else {
            print("❌ ЕСТЬ ПРОБЛЕМЫ С РЕАЛЬНЫМИ ПОРОГАМИ")
            print("🔧 Требуется проверка логики в AppDelegate")
        }
    }
}

// Запускаем тест
RealSleepThresholdsTest.main()
