import Foundation

/// Тест проверки блокировки неформальных предложений во время активного формального интервала
@main struct InformalBlockingTest {
    static func main() {
        print("🧪 ===== ТЕСТ БЛОКИРОВКИ НЕФОРМАЛЬНЫХ ПРЕДЛОЖЕНИЙ =====")
        print("🎯 Проверяем что неформальные предложения НЕ показываются во время активного интервала")
        
        // Создаем компоненты
        let activityManager = ActivityStateManager()
        let pomodoroTimer = PomodoroTimer()
        
        // Устанавливаем связь
        activityManager.pomodoroTimer = pomodoroTimer
        
        var informalSuggestionTriggered = false
        activityManager.onInformalRestSuggestion = {
            informalSuggestionTriggered = true
            print("🔔 НЕФОРМАЛЬНОЕ ПРЕДЛОЖЕНИЕ СРАБОТАЛО!")
        }
        
        print("\n📋 Тест 1: Неформальное предложение во время IDLE состояния")
        print("   Состояние таймера: \(pomodoroTimer.state)")
        
        // Заполняем историю активности (52 минуты с 42+ активными)
        for i in 0..<52 {
            let isActive = i < 45 // 45 активных минут из 52
            activityManager.recordMinuteActivity(isActive: isActive)
        }
        
        if informalSuggestionTriggered {
            print("   ✅ Неформальное предложение показано в IDLE состоянии")
        } else {
            print("   ❌ Неформальное предложение НЕ показано в IDLE состоянии")
        }
        
        print("\n📋 Тест 2: Неформальное предложение во время АКТИВНОГО интервала")
        
        // Сбрасываем флаг
        informalSuggestionTriggered = false
        
        // Запускаем формальный интервал
        pomodoroTimer.startInterval()
        print("   Состояние таймера: \(pomodoroTimer.state)")
        
        // Добавляем еще одну активную минуту (должно сработать условие, но быть заблокировано)
        activityManager.recordMinuteActivity(isActive: true)
        
        if informalSuggestionTriggered {
            print("   ❌ ОШИБКА: Неформальное предложение показано во время активного интервала!")
            print("   🚨 ЭТО БАГИ! Неформальные предложения должны блокироваться")
        } else {
            print("   ✅ ПРАВИЛЬНО: Неформальное предложение заблокировано во время активного интервала")
        }
        
        print("\n📋 Тест 3: Неформальное предложение во время ПЕРЕРАБОТКИ")
        
        // Сбрасываем флаг
        informalSuggestionTriggered = false
        
        // Переводим в переработку (симулируем завершение интервала)
        pomodoroTimer.stopInterval()
        // Устанавливаем состояние переработки вручную для теста
        // (в реальности это происходит автоматически)
        print("   Состояние таймера: \(pomodoroTimer.state)")
        
        // Добавляем активную минуту
        activityManager.recordMinuteActivity(isActive: true)
        
        if informalSuggestionTriggered {
            print("   ✅ Неформальное предложение показано во время переработки (это правильно)")
        } else {
            print("   ⚠️  Неформальное предложение НЕ показано во время переработки")
            print("      (возможно cooldown или другие условия)")
        }
        
        print("\n🎯 ===== РЕЗУЛЬТАТ ТЕСТА =====")
        print("✅ Тест завершен - проверьте логи на наличие сообщений:")
        print("   'БЛОКИРОВКА: Активный формальный интервал'")
        print("🔍 Если такие сообщения есть - исправление работает!")
    }
}
