import Foundation

/// БАЗОВЫЙ ТЕСТ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Проверяем основную логику без внешних зависимостей

struct EarlyEngagementBasicTest {
    static func main() {
        print("🚀 БАЗОВЫЙ ТЕСТ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ")
        print(String(repeating: "=", count: 60))
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Тест адаптации планки
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Тест адаптации планки")
        
        var userBar: TimeInterval = 25 * 60 // 25 минут
        let initialBar = userBar
        
        // Симулируем успех - планка должна увеличиться на 10%
        userBar = adaptUserBarOnSuccess(userBar)
        
        if userBar > initialBar {
            print("✅ ПРОЙДЕН: Планка увеличивается при успехе")
            print("   Было: \(Int(initialBar/60))мин, стало: \(Int(userBar/60))мин")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Планка НЕ увеличивается при успехе")
            print("   Было: \(Int(initialBar/60))мин, стало: \(Int(userBar/60))мин")
        }
        
        // СЦЕНАРИЙ 2: Тест уменьшения планки при отказе
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Тест уменьшения планки при отказе")
        
        let beforeRefusal = userBar
        userBar = adaptUserBarOnFailure(userBar)
        
        if userBar < beforeRefusal {
            print("✅ ПРОЙДЕН: Планка уменьшается при отказе")
            print("   Было: \(Int(beforeRefusal/60))мин, стало: \(Int(userBar/60))мин")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Планка НЕ уменьшается при отказе")
            print("   Было: \(Int(beforeRefusal/60))мин, стало: \(Int(userBar/60))мин")
        }
        
        // СЦЕНАРИЙ 3: Тест границ планки
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Тест границ планки")
        
        // Многократно уменьшаем планку
        var minBar = userBar
        for _ in 0..<50 {
            minBar = adaptUserBarOnFailure(minBar)
        }
        
        // Многократно увеличиваем планку
        var maxBar = userBar
        for _ in 0..<50 {
            maxBar = adaptUserBarOnSuccess(maxBar)
        }
        
        if minBar >= 2 * 60 && maxBar <= 2 * 60 * 60 {
            print("✅ ПРОЙДЕН: Планка соблюдает границы")
            print("   Минимум: \(Int(minBar/60))мин, максимум: \(Int(maxBar/60))мин")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Планка НЕ соблюдает границы")
            print("   Минимум: \(Int(minBar/60))мин, максимум: \(Int(maxBar/60))мин")
        }
        
        // СЦЕНАРИЙ 4: Тест матричной логики
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Тест матричной логики")
        
        let (v1, h1) = getMatrixPosition(daysWithoutWork: 0, timeOfDay: 8.0)  // Утро, 0 дней
        let (v2, h2) = getMatrixPosition(daysWithoutWork: 3, timeOfDay: 20.0) // Вечер, 3 дня
        
        if v1 == 0 && h1 == 0 && v2 == 3 && h2 == 3 {
            print("✅ ПРОЙДЕН: Матричная логика работает корректно")
            print("   Утро 0 дней: v\(v1)h\(h1), Вечер 3 дня: v\(v2)h\(h2)")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Матричная логика НЕ работает")
            print("   Утро 0 дней: v\(v1)h\(h1), Вечер 3 дня: v\(v2)h\(h2)")
        }
        
        // СЦЕНАРИЙ 5: Тест расчета интервалов
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Тест расчета интервалов")
        
        let intervals = calculateAdaptiveIntervals(userBar: 30 * 60) // 30 минут
        
        if intervals.count == 4 && intervals[0] > intervals[1] && intervals[1] > intervals[2] && intervals[2] > intervals[3] {
            print("✅ ПРОЙДЕН: Интервалы рассчитываются корректно")
            print("   Интервалы: \(intervals.map { Int($0/60) })мин")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Интервалы НЕ рассчитываются корректно")
            print("   Интервалы: \(intervals.map { Int($0/60) })мин")
        }
        
        // ТЕСТ ПОЛОМКИ: Намеренно ломаем функционал
        print("\n🔧 ТЕСТ ПОЛОМКИ: Проверяем что тесты ловят реальные проблемы")
        print(String(repeating: "-", count: 60))
        
        testBrokenFunctionality()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 ИТОГИ ТЕСТИРОВАНИЯ:")
        print("   Пройдено: \(passed)/\(total)")
        print("   Процент успеха: \(Int(Double(passed)/Double(total) * 100))%")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        } else {
            print("⚠️  ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!")
        }
    }
    
    // MARK: - Тестируемые функции (копии логики из EarlyEngagementSystem)
    
    static func adaptUserBarOnSuccess(_ currentBar: TimeInterval) -> TimeInterval {
        let newBar = currentBar * 1.1 // Увеличиваем на 10%
        return min(newBar, 2 * 60 * 60) // Максимум 2 часа
    }
    
    static func adaptUserBarOnFailure(_ currentBar: TimeInterval) -> TimeInterval {
        let newBar = currentBar * 0.85 // Уменьшаем на 15%
        return max(newBar, 2 * 60) // Минимум 2 минуты
    }
    
    static func getMatrixPosition(daysWithoutWork: Int, timeOfDay: Double) -> (vertical: Int, horizontal: Int) {
        let vertical = min(daysWithoutWork, 4) // 0-4 дня
        
        let horizontal: Int
        if timeOfDay < 12 {
            horizontal = 0 // Утро
        } else if timeOfDay < 16 {
            horizontal = 1 // День
        } else if timeOfDay < 20 {
            horizontal = 2 // Вечер
        } else {
            horizontal = 3 // Ночь
        }
        
        return (vertical, horizontal)
    }
    
    static func calculateAdaptiveIntervals(userBar: TimeInterval) -> [TimeInterval] {
        return [
            userBar,           // 100% планки
            userBar * 0.75,    // 75% планки
            userBar * 0.5,     // 50% планки
            userBar * 0.25     // 25% планки
        ]
    }
    
    /// Тестирует что наши тесты действительно ловят поломки
    static func testBrokenFunctionality() {
        print("🔧 Тестируем поломку адаптации планки...")
        
        let testBar: TimeInterval = 25 * 60
        let successBar = adaptUserBarOnSuccess(testBar)
        let failureBar = adaptUserBarOnFailure(testBar)
        
        if successBar <= testBar {
            print("🚨 КРИТИЧЕСКАЯ ОШИБКА: Планка НЕ увеличивается при успехе!")
        } else {
            print("✅ Тест поломки: Логика успеха работает корректно")
        }
        
        if failureBar >= testBar {
            print("🚨 КРИТИЧЕСКАЯ ОШИБКА: Планка НЕ уменьшается при отказе!")
        } else {
            print("✅ Тест поломки: Логика отказа работает корректно")
        }
        
        // Тест границ
        let minTest = adaptUserBarOnFailure(1 * 60) // 1 минута
        let maxTest = adaptUserBarOnSuccess(3 * 60 * 60) // 3 часа
        
        if minTest < 2 * 60 {
            print("🚨 КРИТИЧЕСКАЯ ОШИБКА: Планка может стать меньше минимума!")
        } else {
            print("✅ Тест поломки: Минимальная граница работает")
        }
        
        if maxTest > 2 * 60 * 60 {
            print("🚨 КРИТИЧЕСКАЯ ОШИБКА: Планка может стать больше максимума!")
        } else {
            print("✅ Тест поломки: Максимальная граница работает")
        }
        
        print("🔧 Тест поломки завершен - основная логика работает корректно")
    }
}

// Запускаем тест
EarlyEngagementBasicTest.main()
