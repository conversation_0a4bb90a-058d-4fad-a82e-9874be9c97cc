import Foundation

/// Тест трассирует весь путь данных от отладочного окна до реального
/// Помогает найти точное место где происходит расхождение
struct EarlyEngagementDebugTraceTest {
    static func main() {
        print("🔍 ТРАССИРОВКА: Путь данных от отладочного окна до реального")
        print(String(repeating: "=", count: 80))
        
        // Тестируем конкретный случай пользователя: 30 мин → должно быть 35, а показывает 40
        traceUserCase()
        
        // Тестируем различные сценарии
        traceVariousScenarios()
        
        print("\n🎯 АНАЛИЗ ЗАВЕРШЕН")
    }
    
    static func traceUserCase() {
        print("\n📋 ТРАССИРОВКА СЛУЧАЯ ПОЛЬЗОВАТЕЛЯ")
        print("   Исходные данные: 30 мин планка, ожидается 35 мин, показывает 40 мин")

        // ТЕСТИРУЕМ ОБА СЛУЧАЯ: 0 дней (35 мин) и 1 день (23 мин)
        print("\n🔍 СЛУЧАЙ 1: 0 дней без работы (работал вчера)")
        traceSpecificCase(initialBar: 30, daysLevel: 0, messageIndex: 0, expectedDebug: 35)

        print("\n🔍 СЛУЧАЙ 2: 1 день без работы")
        traceSpecificCase(initialBar: 30, daysLevel: 1, messageIndex: 0, expectedDebug: 23)
    }

    static func traceSpecificCase(initialBar: Int, daysLevel: Int, messageIndex: Int, expectedDebug: Int) {
        print("   Входные данные: initialBar=\(initialBar), daysLevel=\(daysLevel), messageIndex=\(messageIndex)")
        print("   Ожидаемый результат в отладочном окне: \(expectedDebug) мин")

        // 1. Расчет в отладочном окне (debugCalculateBarForScenario)
        let debugResult = simulateDebugCalculation(initialBar: initialBar, daysLevel: daysLevel, messageIndex: messageIndex)
        print("   debugCalculateBarForScenario: \(debugResult) мин")

        // 2. Расчет ButtonMatrix в отладочном окне
        let realDaysWithoutWork = convertIndexToRealDays(index: daysLevel)
        let buttonMatrixDebugResult = simulateButtonMatrix(baseBar: initialBar, daysWithoutWork: realDaysWithoutWork, messageIndex: messageIndex)
        print("   ButtonMatrix (отладка): \(buttonMatrixDebugResult) мин")
        print("   Преобразование индекса: \(daysLevel) → \(realDaysWithoutWork) дней")

        print("\n🎯 РЕАЛЬНОЕ ОКНО:")

        // 3. Симулируем что происходит в createEngagementMessage
        print("   debugInitialBar установлена: \(initialBar) мин (галочка включена)")

        // 4. Расчет в реальном окне (calculateBarForRealWindow)
        let realWindowResult = simulateRealWindowCalculation(initialBar: initialBar, daysWithoutWork: realDaysWithoutWork, messageIndex: messageIndex)
        print("   calculateBarForRealWindow: \(realWindowResult) мин")

        // 5. Расчет ButtonMatrix в реальном окне
        let buttonMatrixRealResult = simulateButtonMatrix(baseBar: initialBar, daysWithoutWork: realDaysWithoutWork, messageIndex: messageIndex)
        print("   ButtonMatrix (реальное): \(buttonMatrixRealResult) мин")

        print("\n📊 СРАВНЕНИЕ:")
        print("   Ожидалось: \(expectedDebug) мин")
        print("   Отладочное окно: \(debugResult) мин")
        print("   Реальное окно: \(realWindowResult) мин")
        print("   ButtonMatrix: \(buttonMatrixRealResult) мин")

        if debugResult == expectedDebug {
            print("   ✅ Отладочное окно показывает ожидаемое значение")
        } else {
            print("   ❌ Отладочное окно: ожидали \(expectedDebug), получили \(debugResult)")
        }

        if debugResult == realWindowResult && realWindowResult == buttonMatrixRealResult {
            print("   ✅ ВСЕ СОВПАДАЕТ!")
        } else {
            print("   ❌ ЕСТЬ РАСХОЖДЕНИЯ!")
            if debugResult != realWindowResult {
                print("      🔴 Отладочное ≠ Реальное: \(debugResult) ≠ \(realWindowResult)")
            }
            if realWindowResult != buttonMatrixRealResult {
                print("      🔴 Реальное ≠ ButtonMatrix: \(realWindowResult) ≠ \(buttonMatrixRealResult)")
            }
        }
    }
    
    static func traceVariousScenarios() {
        print("\n📋 ТРАССИРОВКА РАЗЛИЧНЫХ СЦЕНАРИЕВ")
        
        let scenarios = [
            (bar: 30, days: 1, msg: 0, desc: "30мин, 1 день, утро"),
            (bar: 52, days: 1, msg: 0, desc: "52мин, 1 день, утро"),
            (bar: 40, days: 2, msg: 1, desc: "40мин, 2 дня, день"),
            (bar: 25, days: 3, msg: 2, desc: "25мин, 3 дня, вечер")
        ]
        
        for scenario in scenarios {
            print("\n   🔍 \(scenario.desc)")
            
            let debugResult = simulateDebugCalculation(initialBar: scenario.bar, daysLevel: scenario.days, messageIndex: scenario.msg)
            let realDays = convertIndexToRealDays(index: scenario.days)
            let realResult = simulateRealWindowCalculation(initialBar: scenario.bar, daysWithoutWork: realDays, messageIndex: scenario.msg)
            let buttonResult = simulateButtonMatrix(baseBar: scenario.bar, daysWithoutWork: realDays, messageIndex: scenario.msg)
            
            print("      Отладка: \(debugResult)мин, Реальное: \(realResult)мин, Кнопки: \(buttonResult)мин")
            
            if debugResult == realResult && realResult == buttonResult {
                print("      ✅ Совпадает")
            } else {
                print("      ❌ Расхождение!")
            }
        }
    }
    
    /// Симулирует debugCalculateBarForScenario
    static func simulateDebugCalculation(initialBar: Int, daysLevel: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)
        
        // Преобразуем индекс в реальные дни
        let realDaysWithoutWork = convertIndexToRealDays(index: daysLevel)
        
        // Вертикальная адаптация
        let verticalBarTime: TimeInterval
        switch realDaysWithoutWork {
        case 0:
            verticalBarTime = simulateGradualGrowth(currentBar: initialBarTime)
        case 1:
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            verticalBarTime = initialBarTime * 0.29
        default:
            verticalBarTime = 3 * 60
        }
        
        // Горизонтальная адаптация
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
        case 3:
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }
        
        let limitedBarTime = min(finalBarTime, 52 * 60)
        return Int(limitedBarTime / 60)
    }
    
    /// Симулирует calculateBarForRealWindow
    static func simulateRealWindowCalculation(initialBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)
        
        // Вертикальная адаптация (БЕЗ преобразования индексов!)
        let verticalBarTime: TimeInterval
        switch daysWithoutWork {
        case 0:
            verticalBarTime = simulateGradualGrowth(currentBar: initialBarTime)
        case 1:
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            verticalBarTime = initialBarTime * 0.29
        default:
            verticalBarTime = 3 * 60
        }
        
        // Горизонтальная адаптация
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
        case 3:
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }
        
        let limitedBarTime = min(finalBarTime, 52 * 60)
        return Int(limitedBarTime / 60)
    }
    
    /// Симулирует ButtonMatrix
    static func simulateButtonMatrix(baseBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let baseBarTime = TimeInterval(baseBar * 60)
        
        // Вертикальная адаптация
        let verticalBar: TimeInterval
        switch daysWithoutWork {
        case 0:
            verticalBar = simulateGradualGrowth(currentBar: baseBarTime)
        case 1:
            verticalBar = baseBarTime * 0.77
        case 2...3:
            verticalBar = baseBarTime * 0.48
        case 4...6:
            verticalBar = baseBarTime * 0.29
        default:
            verticalBar = 3 * 60
        }
        
        // Горизонтальная адаптация
        let finalBar: TimeInterval
        switch messageIndex {
        case 0:
            finalBar = max(verticalBar, 3 * 60)
        case 1:
            finalBar = max(verticalBar * 0.5, 3 * 60)
        case 2:
            finalBar = max(verticalBar * 0.25, 3 * 60)
        case 3:
            finalBar = 3 * 60
        default:
            finalBar = max(verticalBar, 3 * 60)
        }
        
        return Int(finalBar / 60)
    }
    
    /// Преобразует индекс в реальные дни (как в convertIndexToRealDays)
    static func convertIndexToRealDays(index: Int) -> Int {
        switch index {
        case 0: return 0
        case 1: return 1
        case 2: return 2
        case 3: return 4
        case 4: return 7
        default: return 7
        }
    }
    
    /// Симулирует градационный рост
    static func simulateGradualGrowth(currentBar: TimeInterval) -> TimeInterval {
        let currentMinutes = Int(currentBar / 60)
        let multiplier: Double
        
        switch currentMinutes {
        case 3...7: multiplier = 1.60
        case 8...15: multiplier = 1.40
        case 16...25: multiplier = 1.25
        case 26...40: multiplier = 1.15
        case 41...52: multiplier = 1.10
        default: multiplier = 1.05
        }
        
        let newMinutes = Int(round(Double(currentMinutes) * multiplier))
        return TimeInterval(min(newMinutes, 52) * 60)
    }
}

// Запуск теста
EarlyEngagementDebugTraceTest.main()
