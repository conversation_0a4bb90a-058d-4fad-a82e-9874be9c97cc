#!/usr/bin/env swift

import Foundation
import Cocoa

// Подключаем файлы проекта
let projectPath = "/Users/<USER>/EVO/uProd/SimplePomodoroTest"

// Загружаем исходники
let gradualGrowthContent = try String(contentsOfFile: "\(projectPath)/GradualGrowthSystem.swift", encoding: .utf8)
let earlyEngagementContent = try String(contentsOfFile: "\(projectPath)/EarlyEngagementSystem.swift", encoding: .utf8)
let loggerContent = try String(contentsOfFile: "\(projectPath)/Logger.swift", encoding: .utf8)

// Создаем минимальный тест
@main
struct SimpleGrowthTest {
    static func main() {
        print("🧪 ПРОСТОЙ ТЕСТ РОСТА ПЛАНКИ")
        print(String(repeating: "=", count: 40))
        
        // Создаем экземпляр системы
        let system = EarlyEngagementSystem.shared
        
        // Устанавливаем отладочную планку 30 минут
        system.debugSetInitialBar(30)
        system.debugSetDaysWithoutWork(0) // 0 дней без работы
        
        print("📊 Тестируем: 30 минут, 0 дней без работы")
        
        // Получаем сообщение через createEngagementMessage
        let message = system.createEngagementMessage()
        
        print("🎯 Результат:")
        print("   Заголовок: \(message.title)")
        print("   Подзаголовок: \(message.subtitle)")
        print("   Кнопка: \(message.buttonText)")
        print("   Длительность: \(Int(message.proposedDuration / 60)) минут")
        
        // Очищаем отладочные настройки
        system.debugClearInitialBar()
        system.debugClearDaysWithoutWork()
        
        print("\n✅ Тест завершен")
    }
}
