import Cocoa

// Заглушка для SleepWakeDetector (упрощенная версия для тестирования)
class SleepWakeDetector {
    static let shared = SleepWakeDetector()

    enum SleepWakeEvent {
        case willSleep
        case didWake(duration: TimeInterval, wasRealSleep: Bool)
    }

    var onSleepWakeEvent: ((SleepWakeEvent) -> Void)?
    private var sleepStartTime: Date?
    private var receivedSleepNotification = false
    private var isMonitoring = false

    private init() {}

    func startMonitoring() {
        isMonitoring = true
    }

    func stopMonitoring() {
        isMonitoring = false
    }

    func getDebugInfo() -> String {
        return isMonitoring ? "Мониторинг: активен" : "Мониторинг: неактивен"
    }

    func simulateSystemSleep() {
        sleepStartTime = Date()
        receivedSleepNotification = true
        onSleepWakeEvent?(.willSleep)
    }

    func simulateSystemWake(afterDuration duration: TimeInterval = 0) {
        if duration > 0 {
            sleepStartTime = Date().addingTimeInterval(-duration)
        }

        let wasRealSleep = receivedSleepNotification && duration >= 5 * 60
        onSleepWakeEvent?(.didWake(duration: duration, wasRealSleep: wasRealSleep))

        sleepStartTime = nil
        receivedSleepNotification = false
    }

    func simulateLongInactivity(duration: TimeInterval) {
        sleepStartTime = Date().addingTimeInterval(-duration)
        receivedSleepNotification = false // НЕ получили уведомление о сне

        // Определяем тип: очень длинные периоды (> 2 часа) считаем сном
        let wasRealSleep = duration > 2 * 60 * 60
        onSleepWakeEvent?(.didWake(duration: duration, wasRealSleep: wasRealSleep))

        sleepStartTime = nil
    }
}

// Реальный тест SleepWakeDetector - проверяет различение сна и неактивности
print("🧪 ТЕСТ SLEEPWAKEDETECTOR")
print(String(repeating: "=", count: 50))

var passedTests = 0
var totalTests = 0

// Тест 1: Базовая функциональность
totalTests += 1
print("\n1️⃣ Тест базовой функциональности")

let detector = SleepWakeDetector.shared
var receivedEvents: [SleepWakeDetector.SleepWakeEvent] = []

// Устанавливаем колбэк для получения событий
detector.onSleepWakeEvent = { event in
    receivedEvents.append(event)
}

// Запускаем мониторинг
detector.startMonitoring()

// Проверяем что мониторинг запустился
let debugInfo = detector.getDebugInfo()
if debugInfo.contains("активен") {
    print("✅ Мониторинг успешно запущен")
    passedTests += 1
} else {
    print("❌ Мониторинг НЕ запустился")
    print("   Debug: \(debugInfo)")
}

// Тест 2: Симуляция реального сна
totalTests += 1
print("\n2️⃣ Тест симуляции реального сна")

receivedEvents.removeAll()

// Симулируем засыпание
detector.simulateSystemSleep()

// Проверяем что получили событие willSleep
var gotWillSleep = false
for event in receivedEvents {
    switch event {
    case .willSleep:
        gotWillSleep = true
    default:
        break
    }
}

if gotWillSleep {
    print("✅ Событие willSleep получено")
} else {
    print("❌ Событие willSleep НЕ получено")
}

// Симулируем пробуждение после 30 минут
receivedEvents.removeAll()
detector.simulateSystemWake(afterDuration: 30 * 60)

// Проверяем что получили событие didWake с правильным типом
var gotDidWakeRealSleep = false
for event in receivedEvents {
    switch event {
    case .didWake(let duration, let wasRealSleep):
        if wasRealSleep && duration >= 30 * 60 - 5 && duration <= 30 * 60 + 5 {
            gotDidWakeRealSleep = true
        }
    default:
        break
    }
}

if gotDidWakeRealSleep {
    print("✅ Событие didWake (реальный сон) получено правильно")
    passedTests += 1
} else {
    print("❌ Событие didWake (реальный сон) НЕ получено или неправильное")
}

// Тест 3: Симуляция длительной неактивности
totalTests += 1
print("\n3️⃣ Тест симуляции длительной неактивности")

receivedEvents.removeAll()

// Симулируем длительную неактивность (20 минут)
detector.simulateLongInactivity(duration: 20 * 60)

// Проверяем что получили событие didWake с wasRealSleep = false
var gotDidWakeInactivity = false
for event in receivedEvents {
    switch event {
    case .didWake(let duration, let wasRealSleep):
        if !wasRealSleep && duration >= 20 * 60 - 5 && duration <= 20 * 60 + 5 {
            gotDidWakeInactivity = true
        }
    default:
        break
    }
}

if gotDidWakeInactivity {
    print("✅ Событие didWake (неактивность) получено правильно")
    passedTests += 1
} else {
    print("❌ Событие didWake (неактивность) НЕ получено или неправильное")
}

// Тест 4: Короткий перерыв
totalTests += 1
print("\n4️⃣ Тест короткого перерыва")

receivedEvents.removeAll()

// Симулируем короткий перерыв (3 минуты)
detector.simulateLongInactivity(duration: 3 * 60)

// Проверяем что получили событие didWake с wasRealSleep = false
var gotDidWakeShortBreak = false
for event in receivedEvents {
    switch event {
    case .didWake(let duration, let wasRealSleep):
        if !wasRealSleep && duration >= 3 * 60 - 5 && duration <= 3 * 60 + 5 {
            gotDidWakeShortBreak = true
        }
    default:
        break
    }
}

if gotDidWakeShortBreak {
    print("✅ Событие didWake (короткий перерыв) получено правильно")
    passedTests += 1
} else {
    print("❌ Событие didWake (короткий перерыв) НЕ получено или неправильное")
}

// Тест 5: Очень длинная неактивность (должна определиться как сон)
totalTests += 1
print("\n5️⃣ Тест очень длинной неактивности")

receivedEvents.removeAll()

// Симулируем очень длинную неактивность (3 часа)
detector.simulateLongInactivity(duration: 3 * 60 * 60)

// Проверяем что получили событие didWake с wasRealSleep = true (определилось как сон)
var gotDidWakeVeryLong = false
for event in receivedEvents {
    switch event {
    case .didWake(let duration, let wasRealSleep):
        if wasRealSleep && duration >= 3 * 60 * 60 - 5 && duration <= 3 * 60 * 60 + 5 {
            gotDidWakeVeryLong = true
        }
    default:
        break
    }
}

if gotDidWakeVeryLong {
    print("✅ Очень длинная неактивность правильно определена как сон")
    passedTests += 1
} else {
    print("❌ Очень длинная неактивность НЕ определена как сон")
}

// Тест 6: Остановка мониторинга
totalTests += 1
print("\n6️⃣ Тест остановки мониторинга")

detector.stopMonitoring()

let debugInfoAfterStop = detector.getDebugInfo()
if debugInfoAfterStop.contains("неактивен") {
    print("✅ Мониторинг успешно остановлен")
    passedTests += 1
} else {
    print("❌ Мониторинг НЕ остановлен")
    print("   Debug: \(debugInfoAfterStop)")
}

// Итоги
print("\n" + String(repeating: "=", count: 50))
print("📊 ИТОГИ ТЕСТИРОВАНИЯ SLEEPWAKEDETECTOR:")
print("   Пройдено: \(passedTests)/\(totalTests)")

if passedTests == totalTests {
    print("🎉 ВСЕ ТЕСТЫ SLEEPWAKEDETECTOR ПРОЙДЕНЫ!")
} else {
    print("⚠️  ЕСТЬ ПРОБЛЕМЫ В SLEEPWAKEDETECTOR")
}

exit(passedTests == totalTests ? 0 : 1)
