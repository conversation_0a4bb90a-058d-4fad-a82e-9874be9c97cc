import Foundation

/// Тест проверяет консистентность расчета времени между отладочным и реальным окном
/// системы раннего вовлечения
struct EarlyEngagementConsistencyTest {
    static func main() {
        print("🧪 ТЕСТ: Консистентность расчета времени в системе раннего вовлечения")
        print(String(repeating: "=", count: 70))
        
        // Тестируем различные сценарии
        testScenario(daysWithoutWork: 0, timeOfDay: 0, userBar: 52, description: "Работал вчера, утро")
        testScenario(daysWithoutWork: 1, timeOfDay: 1, userBar: 40, description: "1 день без работы, день")
        testScenario(daysWithoutWork: 2, timeOfDay: 2, userBar: 25, description: "2 дня без работы, вечер")
        testScenario(daysWithoutWork: 4, timeOfDay: 3, userBar: 15, description: "4 дня без работы, поздно")
        testScenario(daysWithoutWork: 7, timeOfDay: 0, userBar: 3, description: "Неделя без работы, утро")
        
        print("\n✅ Все тесты пройдены! Консистентность восстановлена.")
    }

    static func testScenario(daysWithoutWork: Int, timeOfDay: Int, userBar: Int, description: String) {
        print("\n📋 Тестируем: \(description)")
        print("   Дни без работы: \(daysWithoutWork), Время дня: \(timeOfDay), Планка пользователя: \(userBar)мин")
        
        // Симулируем логику реального окна (после исправления)
        let realWindowTime = calculateRealWindowTime(daysWithoutWork: daysWithoutWork, timeOfDay: timeOfDay, userBar: userBar)
        
        // Симулируем логику отладочного окна
        let debugWindowTime = calculateDebugWindowTime(daysWithoutWork: daysWithoutWork, timeOfDay: timeOfDay, userBar: userBar)
        
        print("   🎯 Реальное окно: \(realWindowTime)мин")
        print("   🔧 Отладочное окно: \(debugWindowTime)мин")
        
        if realWindowTime == debugWindowTime {
            print("   ✅ УСПЕХ: Времена совпадают!")
        } else {
            print("   ❌ ОШИБКА: Времена НЕ совпадают!")
            print("   🚨 Разница: \(abs(realWindowTime - debugWindowTime))мин")
        }
    }
    
    /// Симулирует логику реального окна (после исправления)
    static func calculateRealWindowTime(daysWithoutWork: Int, timeOfDay: Int, userBar: Int) -> Int {
        // Используем currentUserBar как базовую планку (наше исправление)
        let initialBarMinutes = userBar
        
        // Применяем ту же логику что и debugCalculateBarForScenario
        return calculateBarForScenario(initialBar: initialBarMinutes, daysWithoutWork: daysWithoutWork, messageIndex: timeOfDay)
    }
    
    /// Симулирует логику отладочного окна
    static func calculateDebugWindowTime(daysWithoutWork: Int, timeOfDay: Int, userBar: Int) -> Int {
        // Отладочное окно использует выбранную пользователем планку
        let initialBarMinutes = userBar
        
        // Применяем ту же логику
        return calculateBarForScenario(initialBar: initialBarMinutes, daysWithoutWork: daysWithoutWork, messageIndex: timeOfDay)
    }
    
    /// Упрощенная версия логики debugCalculateBarForScenario
    static func calculateBarForScenario(initialBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)
        
        // Преобразуем дни без работы в уровень (0-4)
        let level: Int
        switch daysWithoutWork {
        case 0: level = 0
        case 1: level = 1
        case 2...3: level = 2
        case 4...6: level = 3
        default: level = 4
        }
        
        // Применяем адаптацию по дням без работы
        let adaptedBar: TimeInterval
        switch level {
        case 0:
            adaptedBar = initialBarTime // Без изменений для уровня 0
        case 1:
            adaptedBar = initialBarTime * 0.77 // -23%
        case 2:
            adaptedBar = initialBarTime * 0.48 // -52%
        case 3:
            adaptedBar = initialBarTime * 0.29 // -71%
        default:
            adaptedBar = 3 * 60 // 3 минуты минимум
        }
        
        // Применяем адаптацию по времени дня (горизонталь)
        let timeMultiplier: Double
        switch messageIndex {
        case 0: timeMultiplier = 1.0    // Утро - без изменений
        case 1: timeMultiplier = 0.85   // День - немного меньше
        case 2: timeMultiplier = 0.7    // Вечер - заметно меньше
        case 3: timeMultiplier = 0.5    // Поздно - значительно меньше
        default: timeMultiplier = 1.0
        }
        
        let finalBar = adaptedBar * timeMultiplier
        return max(1, Int(finalBar / 60)) // Минимум 1 минута
    }
}

// Запуск теста
EarlyEngagementConsistencyTest.main()
