import Foundation

/// КОМПЛЕКСНЫЙ ТЕСТ ВСЕЙ УНИФИЦИРОВАННОЙ СИСТЕМЫ АКТИВНОСТИ
/// Проверяет интеграцию всех компонентов: формальные/неформальные интервалы, эскалация, отдых

struct ComprehensiveSystemTest {
    static func main() {
        print("🚀 КОМПЛЕКСНЫЙ ТЕСТ УНИФИЦИРОВАННОЙ СИСТЕМЫ АКТИВНОСТИ")
        print(String(repeating: "=", count: 70))
        
        var passed = 0
        var total = 0
        
        // ТЕСТ 1: Проверка существования всех ключевых компонентов
        total += 1
        print("\n📋 ТЕСТ 1: Проверка существования ключевых файлов системы")
        
        let keyFiles = [
            "SimplePomodoroTest/MinuteActivityTracker.swift",
            "SimplePomodoroTest/ActivityStateManager.swift",
            "SimplePomodoroTest/ParallelRestTracker.swift",
            "SimplePomodoroTest/ReturnMessageGenerator.swift",
            "SimplePomodoroTest/StatisticsManager.swift",
            "SimplePomodoroTest/InformalSessionDetector.swift",
            "SimplePomodoroTest/UnifiedReminderSystem.swift",
            "SimplePomodoroTest/BreakTimer.swift",
            "SimplePomodoroTest/ComputerTimeTracker.swift"
        ]
        
        var missingFiles: [String] = []
        for file in keyFiles {
            if !FileManager.default.fileExists(atPath: file) {
                missingFiles.append(file)
            }
        }
        
        if missingFiles.isEmpty {
            print("✅ Все ключевые файлы системы найдены (\(keyFiles.count) файлов)")
            passed += 1
        } else {
            print("❌ Отсутствуют файлы: \(missingFiles.joined(separator: ", "))")
        }
        
        // ТЕСТ 2: Проверка интеграции с InformalSessionDetector
        total += 1
        print("\n📋 ТЕСТ 2: Интеграция с InformalSessionDetector")
        
        if let content = try? String(contentsOfFile: "SimplePomodoroTest/InformalSessionDetector.swift", encoding: .utf8) {
            let hasUnifiedChecker = content.contains("UnifiedActivityChecker")
            let hasMinuteTracker = content.contains("MinuteActivityTracker")
            let hasActivityState = content.contains("ActivityStateManager")
            
            if hasUnifiedChecker || hasMinuteTracker || hasActivityState {
                print("✅ InformalSessionDetector интегрирован с новой системой активности")
                passed += 1
            } else {
                print("❌ InformalSessionDetector НЕ интегрирован с новой системой")
            }
        } else {
            print("❌ Не удалось прочитать InformalSessionDetector.swift")
        }
        
        // ТЕСТ 3: Проверка интеграции с UnifiedReminderSystem
        total += 1
        print("\n📋 ТЕСТ 3: Интеграция с UnifiedReminderSystem")
        
        if let content = try? String(contentsOfFile: "SimplePomodoroTest/UnifiedReminderSystem.swift", encoding: .utf8) {
            let hasUnifiedChecker = content.contains("UnifiedActivityChecker")
            let hasActivityState = content.contains("ActivityStateManager")
            let hasParallelRest = content.contains("ParallelRestTracker")
            
            if hasUnifiedChecker || hasActivityState || hasParallelRest {
                print("✅ UnifiedReminderSystem интегрирован с новой системой")
                passed += 1
            } else {
                print("❌ UnifiedReminderSystem НЕ интегрирован с новой системой")
            }
        } else {
            print("❌ Не удалось прочитать UnifiedReminderSystem.swift")
        }
        
        // ТЕСТ 4: Проверка интеграции с BreakTimer
        total += 1
        print("\n📋 ТЕСТ 4: Интеграция с BreakTimer")
        
        if let content = try? String(contentsOfFile: "SimplePomodoroTest/BreakTimer.swift", encoding: .utf8) {
            let hasUnifiedChecker = content.contains("UnifiedActivityChecker")
            let hasMinuteTracker = content.contains("MinuteActivityTracker")
            
            if hasUnifiedChecker || hasMinuteTracker {
                print("✅ BreakTimer интегрирован с 4-бандовой системой")
                passed += 1
            } else {
                print("❌ BreakTimer НЕ интегрирован с новой системой")
            }
        } else {
            print("❌ Не удалось прочитать BreakTimer.swift")
        }
        
        // ТЕСТ 5: Проверка интеграции с ComputerTimeTracker
        total += 1
        print("\n📋 ТЕСТ 5: Интеграция с ComputerTimeTracker")
        
        if let content = try? String(contentsOfFile: "SimplePomodoroTest/ComputerTimeTracker.swift", encoding: .utf8) {
            let hasUnifiedChecker = content.contains("UnifiedActivityChecker")
            let hasActivityRecord = content.contains("ActivityRecord")
            
            if hasUnifiedChecker || hasActivityRecord {
                print("✅ ComputerTimeTracker интегрирован с новой системой статистики")
                passed += 1
            } else {
                print("❌ ComputerTimeTracker НЕ интегрирован с новой системой")
            }
        } else {
            print("❌ Не удалось прочитать ComputerTimeTracker.swift")
        }
        
        // ТЕСТ 6: Проверка системы отдыха
        total += 1
        print("\n📋 ТЕСТ 6: Система параллельного отслеживания отдыха")
        
        if let parallelContent = try? String(contentsOfFile: "SimplePomodoroTest/ParallelRestTracker.swift", encoding: .utf8),
           let messageContent = try? String(contentsOfFile: "SimplePomodoroTest/ReturnMessageGenerator.swift", encoding: .utf8) {

            let hasAutoRestThreshold = parallelContent.contains("autoRestThreshold") && parallelContent.contains("17 * 60")
            let hasInactivityTracking = parallelContent.contains("startInactivityTracking") && parallelContent.contains("stopInactivityTracking")
            let hasRestRecording = parallelContent.contains("recordAutomaticRest") && parallelContent.contains("recordRestStatistics")
            let hasSmartMessages = messageContent.contains("generateMessage") && messageContent.contains("ReturnMessage")

            if hasAutoRestThreshold && hasInactivityTracking && hasRestRecording && hasSmartMessages {
                print("✅ Система параллельного отдыха полностью интегрирована")
                passed += 1
            } else {
                print("❌ Система отдыха неполная:")
                print("  - Порог 17 мин: \(hasAutoRestThreshold)")
                print("  - Отслеживание: \(hasInactivityTracking)")
                print("  - Запись отдыха: \(hasRestRecording)")
                print("  - Умные сообщения: \(hasSmartMessages)")
            }
        } else {
            print("❌ Не удалось прочитать файлы системы отдыха")
        }
        
        // ТЕСТ 7: Проверка системы статистики
        total += 1
        print("\n📋 ТЕСТ 7: Система статистики активности")
        
        if let activityContent = try? String(contentsOfFile: "SimplePomodoroTest/ActivityStateManager.swift", encoding: .utf8),
           let statsContent = try? String(contentsOfFile: "SimplePomodoroTest/StatisticsManager.swift", encoding: .utf8) {

            let hasRestStatistics = statsContent.contains("RestStatistics") && statsContent.contains("RestType") && statsContent.contains("RestQuality")
            let hasSmartRestRecording = statsContent.contains("recordSmartRestStatistics") && statsContent.contains("determineRestTypeAndQuality")
            let hasActivityIntegration = activityContent.contains("onUserReturned") && activityContent.contains("ReturnMessage")

            if hasRestStatistics && hasSmartRestRecording && hasActivityIntegration {
                print("✅ Система статистики полностью интегрирована")
                passed += 1
            } else {
                print("❌ Система статистики неполная:")
                print("  - RestStatistics: \(hasRestStatistics)")
                print("  - Умная запись: \(hasSmartRestRecording)")
                print("  - Интеграция активности: \(hasActivityIntegration)")
            }
        } else {
            print("❌ Не удалось прочитать файлы статистики")
        }
        
        // ТЕСТ 8: Проверка интеграции с AppDelegate
        total += 1
        print("\n📋 ТЕСТ 8: Интеграция с основным приложением (AppDelegate)")
        
        if let content = try? String(contentsOfFile: "SimplePomodoroTest/AppDelegate.swift", encoding: .utf8) {
            let hasActivityStateManager = content.contains("ActivityStateManager")
            let hasSleepIntegration = content.contains("resetAfterSleep") || content.contains("handleReturnAfterInactivity")
            
            if hasActivityStateManager && hasSleepIntegration {
                print("✅ Основное приложение интегрировано с новой системой")
                print("✅ Интеграция со сном и неактивностью работает")
                passed += 1
            } else {
                print("❌ Интеграция с AppDelegate неполная")
                print("❌ ActivityStateManager: \(hasActivityStateManager), Sleep: \(hasSleepIntegration)")
            }
        } else {
            print("❌ Не удалось прочитать AppDelegate.swift")
        }
        
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТ КОМПЛЕКСНОГО ТЕСТИРОВАНИЯ СИСТЕМЫ")
        print(String(repeating: "=", count: 60))
        print("✅ Пройдено: \(passed)/\(total)")
        
        if passed == total {
            print("🎉 ВСЯ СИСТЕМА ИНТЕГРИРОВАНА КОРРЕКТНО!")
            print("✅ Все компоненты работают с новой архитектурой")
            print("✅ Формальные и неформальные интервалы поддерживаются")
            print("✅ Система эскалации и отдыха функционирует")
            print("✅ Статистика и интеграция с приложением работают")
            print("✅ Система готова к продолжению тестирования")
        } else {
            print("❌ ЕСТЬ ПРОБЛЕМЫ С ИНТЕГРАЦИЕЙ СИСТЕМЫ")
            print("🔧 Требуется доработка компонентов")
            print("⚠️  Не все части системы интегрированы корректно")
        }
    }
}

// Запускаем тест
ComprehensiveSystemTest.main()
