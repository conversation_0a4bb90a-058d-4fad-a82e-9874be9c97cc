import Foundation

/// Тест для воспроизведения конкретной проблемы пользователя

func runUserProblemTest() {
    print("🔍 ТЕСТ ПРОБЛЕМЫ ПОЛЬЗОВАТЕЛЯ: 7+ дней → 11 мин вместо 3 мин")
    print(String(repeating: "=", count: 70))
    
    // Воспроизводим ТОЧНО тот же случай что описал пользователь
    print("\n📋 СЛУЧАЙ ИЗ ОТЛАДОЧНОГО ОКНА:")
    print("   Позиция: 7+ дней не работал × 1-е сообщение")
    print("   Изначальная планка: 40 мин")
    print("   Расчет: 40 × 0.29 × 1.0 (100%) = 11.6 мин")
    print("   Рассчитанная планка: 3 мин (план-минимум)")
    print("   Кнопка показывает: 11 мин ❌")
    
    // Тестируем разные варианты daysWithoutWork для 7+
    let testCases = [7, 8, 9, 10, 15, 20, 30]
    
    for days in testCases {
        print("\n🔍 ТЕСТ: \(days) дней без работы")
        
        // Используем реальную функцию ButtonMatrix
        let buttons = simulateButtonMatrixGeneration(
            daysWithoutWork: days,
            messageIndex: 0,
            baseBarMinutes: 40
        )
        
        // Ищем кнопку "Начать работу"
        if let primaryButton = buttons.first(where: { $0.type == .primary }) {
            let buttonMinutes = Int(primaryButton.duration / 60)
            print("   ButtonMatrix результат: \(buttonMinutes) мин")
            
            if buttonMinutes == 3 {
                print("   ✅ КОРРЕКТНО: План-минимум применен")
            } else {
                print("   ❌ ОШИБКА: Должно быть 3 мин, получили \(buttonMinutes) мин")
                
                // Дополнительная диагностика
                print("   🔍 ДИАГНОСТИКА:")
                let vertical = max(0, days)
                print("      vertical = \(vertical)")
                
                if vertical >= 7 {
                    print("      Должен попасть в case default (3 мин)")
                } else {
                    print("      Попадает в case 4...6 (× 0.29)")
                }
            }
        }
    }
    
    // Специальный тест: проверяем что происходит с формулой 40 × 0.29
    print("\n" + String(repeating: "-", count: 70))
    print("🔍 СПЕЦИАЛЬНЫЙ ТЕСТ: Откуда берется 11 мин?")
    
    let formula_result = 40.0 * 0.29
    print("   Формула 40 × 0.29 = \(formula_result) мин")
    print("   Округленно = \(Int(round(formula_result))) мин")
    
    // Проверяем что происходит если vertical попадает в case 4...6
    print("\n   Если vertical попадает в case 4...6:")
    let baseBar = TimeInterval(40 * 60)
    let verticalResult = baseBar * 0.29
    let horizontalResult = max(verticalResult, 3 * 60)
    print("   Вертикальная: \(Int(verticalResult/60)) мин")
    print("   Горизонтальная: \(Int(horizontalResult/60)) мин")
    
    // Проверяем что происходит если vertical попадает в default
    print("\n   Если vertical попадает в case default:")
    let verticalDefault = 3 * 60
    let horizontalDefault = max(verticalDefault, 3 * 60)
    print("   Вертикальная: \(Int(verticalDefault/60)) мин")
    print("   Горизонтальная: \(Int(horizontalDefault/60)) мин")
}

/// Симулирует генерацию кнопок ButtonMatrix
func simulateButtonMatrixGeneration(
    daysWithoutWork: Int,
    messageIndex: Int,
    baseBarMinutes: Int
) -> [ButtonComponent] {
    
    // ИСПОЛЬЗУЕМ ТУ ЖЕ ЛОГИКУ что в реальном ButtonMatrix
    let vertical = max(0, daysWithoutWork)
    let horizontal = min(3, max(0, messageIndex))
    let baseBar = TimeInterval(baseBarMinutes * 60)
    
    // Вертикальная адаптация
    let verticalBar: TimeInterval
    switch vertical {
    case 0:
        verticalBar = simulateGradualGrowth(currentBar: baseBar)
    case 1:
        verticalBar = baseBar * 0.77
    case 2...3:
        verticalBar = baseBar * 0.48
    case 4...6:
        verticalBar = baseBar * 0.29
    default:
        verticalBar = 3 * 60
    }
    
    // Горизонтальная адаптация
    let finalBar: TimeInterval
    switch horizontal {
    case 0:
        finalBar = max(verticalBar, 3 * 60)
    case 1:
        finalBar = max(verticalBar * 0.5, 3 * 60)
    case 2:
        finalBar = max(verticalBar * 0.25, 3 * 60)
    case 3:
        finalBar = 3 * 60
    default:
        finalBar = max(verticalBar, 3 * 60)
    }
    
    // Создаем кнопки (упрощенно)
    let primaryButton = ButtonComponent(
        text: "Начать работу (\(Int(finalBar/60)) мин)",
        duration: finalBar,
        type: .primary,
        context: "work"
    )
    
    return [primaryButton]
}

/// Структура ButtonComponent для теста
struct ButtonComponent {
    let text: String
    let duration: TimeInterval
    let type: ButtonType
    let context: String
}

enum ButtonType {
    case primary
    case fullBar
    case fullSession
    case later
}

/// Симулирует GradualGrowthSystem
func simulateGradualGrowth(currentBar: TimeInterval) -> TimeInterval {
    let currentMinutes = Int(currentBar / 60)
    let multiplier: Double
    
    switch currentMinutes {
    case 3...7: multiplier = 1.60
    case 8...15: multiplier = 1.40
    case 16...25: multiplier = 1.25
    case 26...40: multiplier = 1.15
    case 41...52: multiplier = 1.10
    default: multiplier = 1.05
    }
    
    let newMinutes = Int(round(Double(currentMinutes) * multiplier))
    return TimeInterval(min(newMinutes, 52) * 60)
}

// Запускаем тест
runUserProblemTest()
