import Foundation

/// РУЧНОЙ ТЕСТ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Простой тест для проверки основной логики без зависимостей
struct EarlyEngagementManualTest {
    
    static func main() {
        print("🌅 РУЧНОЙ ТЕСТ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ")
        print("=" * 50)
        
        // Тест 1: Проверка матрицы сообщений
        testMessageMatrix()
        
        // Тест 2: Проверка адаптации планки
        testUserBarAdaptation()
        
        // Тест 3: Проверка расчета дней без работы
        testDaysWithoutWork()
        
        // Тест 4: Проверка времени дня
        testTimeOfDay()
        
        print("\n✅ Все ручные тесты завершены!")
        print("📝 Результаты показывают основную логику системы")
    }
    
    static func testMessageMatrix() {
        print("\n🎯 ТЕСТ 1: Матрица сообщений")
        print("-" * 30)
        
        // Тестируем все комбинации матрицы 5x4
        for vertical in 0...4 {
            for horizontal in 0...3 {
                let message = getMessage(vertical: vertical, horizontal: horizontal)
                print("[\(vertical),\(horizontal)]: \(message.title)")
            }
        }
        
        print("✅ Матрица сообщений работает корректно")
    }
    
    static func testUserBarAdaptation() {
        print("\n⏱️ ТЕСТ 2: Адаптация планки пользователя")
        print("-" * 30)
        
        var currentBar: TimeInterval = 25 * 60 // 25 минут
        print("Начальная планка: \(Int(currentBar / 60)) минут")
        
        // Симуляция успеха
        currentBar = adaptUserBar(currentBar: currentBar, success: true, duration: 25*60)
        print("После успеха: \(Int(currentBar / 60)) минут")
        
        // Симуляция неудачи
        currentBar = adaptUserBar(currentBar: currentBar, success: false, duration: 10*60)
        print("После неудачи: \(Int(currentBar / 60)) минут")
        
        // Проверка границ
        var minBar: TimeInterval = 1 * 60 // 1 минута
        minBar = adaptUserBar(currentBar: minBar, success: false, duration: 30)
        print("Минимальная планка после неудачи: \(Int(minBar / 60)) минут")
        
        var maxBar: TimeInterval = 120 * 60 // 2 часа
        maxBar = adaptUserBar(currentBar: maxBar, success: true, duration: 120*60)
        print("Максимальная планка после успеха: \(Int(maxBar / 60)) минут")
        
        print("✅ Адаптация планки работает корректно")
    }
    
    static func testDaysWithoutWork() {
        print("\n📅 ТЕСТ 3: Расчет дней без работы")
        print("-" * 30)
        
        let now = Date()
        
        // Тест: работал сегодня
        let todayWork = now
        let daysToday = calculateDaysWithoutWork(lastWork: todayWork, currentTime: now)
        print("Работал сегодня: \(daysToday) дней")
        
        // Тест: работал вчера
        let yesterdayWork = Calendar.current.date(byAdding: .day, value: -1, to: now)!
        let daysYesterday = calculateDaysWithoutWork(lastWork: yesterdayWork, currentTime: now)
        print("Работал вчера: \(daysYesterday) дней")
        
        // Тест: работал 3 дня назад
        let threeDaysAgo = Calendar.current.date(byAdding: .day, value: -3, to: now)!
        let daysThree = calculateDaysWithoutWork(lastWork: threeDaysAgo, currentTime: now)
        print("Работал 3 дня назад: \(daysThree) дней")
        
        // Тест: работал неделю назад
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: now)!
        let daysWeek = calculateDaysWithoutWork(lastWork: weekAgo, currentTime: now)
        print("Работал неделю назад: \(daysWeek) дней")
        
        print("✅ Расчет дней без работы работает корректно")
    }
    
    static func testTimeOfDay() {
        print("\n🕐 ТЕСТ 4: Определение времени дня")
        print("-" * 30)
        
        let calendar = Calendar.current
        let now = Date()
        
        // Тестируем разное время
        let times = [
            (8, 0, "Утро"),
            (12, 0, "День"),
            (16, 0, "Вечер"),
            (22, 0, "Поздно")
        ]
        
        for (hour, minute, description) in times {
            let testTime = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: now)!
            let timeOfDay = getCurrentTimeOfDay(for: testTime)
            print("\(description) (\(hour):\(String(format: "%02d", minute))): уровень \(timeOfDay)")
        }
        
        print("✅ Определение времени дня работает корректно")
    }
    
    // MARK: - Вспомогательные функции (упрощенные версии)
    
    static func getMessage(vertical: Int, horizontal: Int) -> (title: String, duration: TimeInterval) {
        let titles = [
            // Уровень 0 (сегодня работал)
            ["Продолжим?", "Еще немного?", "Добавим интервал?", "Финальный рывок?"],
            // Уровень 1 (1 день без работы)
            ["Вернемся к работе?", "Начнем день?", "Время работать!", "Не откладываем!"],
            // Уровень 2 (2 дня без работы)
            ["Пора возвращаться!", "Хватит откладывать!", "Начинаем сейчас!", "Время действовать!"],
            // Уровень 3 (3 дня без работы)
            ["Серьезно, пора работать!", "Уже 3 дня!", "Начинаем немедленно!", "Хватит прокрастинировать!"],
            // Уровень 4 (4+ дней без работы)
            ["КРИТИЧНО! Начинаем!", "Уже слишком долго!", "СЕЙЧАС ИЛИ НИКОГДА!", "Экстренный старт!"]
        ]
        
        let durations: [TimeInterval] = [15*60, 20*60, 25*60, 30*60] // 15, 20, 25, 30 минут
        
        let safeVertical = min(max(vertical, 0), 4)
        let safeHorizontal = min(max(horizontal, 0), 3)
        
        return (
            title: titles[safeVertical][safeHorizontal],
            duration: durations[safeHorizontal]
        )
    }
    
    static func adaptUserBar(currentBar: TimeInterval, success: Bool, duration: TimeInterval) -> TimeInterval {
        let minBar: TimeInterval = 2 * 60 // 2 минуты
        let maxBar: TimeInterval = 2 * 60 * 60 // 2 часа
        
        var newBar: TimeInterval
        
        if success {
            // Увеличиваем на 10%
            newBar = currentBar * 1.1
        } else {
            // Уменьшаем на 15%
            newBar = currentBar * 0.85
        }
        
        // Применяем границы
        return min(max(newBar, minBar), maxBar)
    }
    
    static func calculateDaysWithoutWork(lastWork: Date?, currentTime: Date) -> Int {
        guard let lastWork = lastWork else { return 4 } // Максимум если нет данных
        
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: lastWork, to: currentTime).day ?? 0
        
        return min(max(days, 0), 4) // Ограничиваем 0-4
    }
    
    static func getCurrentTimeOfDay(for date: Date = Date()) -> Int {
        let hour = Calendar.current.component(.hour, from: date)
        
        switch hour {
        case 6..<12:
            return 0 // Утро
        case 12..<16:
            return 1 // День
        case 16..<20:
            return 2 // Вечер
        default:
            return 3 // Поздно
        }
    }
}

// Расширение для повторения строки
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// Запуск тестов
EarlyEngagementManualTest.main()
