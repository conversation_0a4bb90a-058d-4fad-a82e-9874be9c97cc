#!/usr/bin/env swift

//
//  InformalBlockingDuringBreakTest.swift
//  uProd Tests
//
//  Тест для проверки блокировки неформальных интервалов во время отдыха в ActivityStateManager
//  Исправляет проблему: неформальные интервалы срабатывают во время отдыха
//  ВАЖНО: Тестирует РЕАЛЬНУЮ систему ActivityStateManager, а не устаревший InformalSessionDetector
//

import Foundation

// MARK: - Test Framework

struct TestResult {
    let name: String
    let passed: Bool
    let message: String
    let duration: TimeInterval
}

class TestRunner {
    private var results: [TestResult] = []
    
    func test(_ name: String, _ testFunc: () throws -> Void) {
        let startTime = Date()
        do {
            try testFunc()
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: true, message: "✅ ПРОШЕЛ", duration: duration))
            print("✅ \(name)")
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: false, message: "❌ ПРОВАЛЕН: \(error)", duration: duration))
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String = "") throws {
        if actual != expected {
            throw TestError.assertionFailed("Ожидалось: \(expected), получено: \(actual). \(message)")
        }
    }
    
    func printSummary() -> Bool {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        let totalTime = results.reduce(0) { $0 + $1.duration }
        
        print("\n" + String(repeating: "=", count: 60))
        print("🧪 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ БЛОКИРОВКИ НЕФОРМАЛЬНЫХ ИНТЕРВАЛОВ")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(total)")
        print("✅ Прошло: \(passed)")
        print("❌ Провалено: \(total - passed)")
        print("⏱️ Время выполнения: \(String(format: "%.3f", totalTime))с")
        print("📈 Успешность: \(Int((Double(passed) / Double(total)) * 100))%")
        print(String(repeating: "=", count: 60))
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Блокировка работает корректно.")
            return true
        } else {
            print("⚠️ ЕСТЬ ПРОБЛЕМЫ! Требуется исправление.")
            print("\nПровалившиеся тесты:")
            for result in results where !result.passed {
                print("  • \(result.name): \(result.message)")
            }
            return false
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Mock PomodoroTimer для тестирования

enum MockPomodoroState: String {
    case idle = "idle"
    case working = "working"
    case overtime = "overtime"
    case onBreak = "onBreak"
}

class MockPomodoroTimer {
    var state: MockPomodoroState = .idle
    
    func setState(_ newState: MockPomodoroState) {
        state = newState
        print("🍅 MockPomodoroTimer: состояние изменено на \(state)")
    }
}

// MARK: - Тестовая версия InformalSessionDetector

class TestInformalSessionDetector {
    private var minuteActivityLog: [Bool] = []
    private let maxLogSize = 60
    private let checkWindowSize = 52
    private let minActiveMinutes = 42
    
    // Mock зависимости
    weak var pomodoroTimer: MockPomodoroTimer?
    
    func recordMinuteActivity(isActive: Bool) {
        // Проверяем состояние таймера перед записью активности
        if let timer = pomodoroTimer, timer.state != .idle {
            if timer.state == .onBreak {
                print("🔍 recordMinuteActivity: Пользователь на отдыхе (.onBreak), пропускаем запись активности")
            } else {
                print("🔍 recordMinuteActivity: Формальный таймер активен (\(timer.state)), пропускаем запись активности")
            }
            return
        }
        
        minuteActivityLog.append(isActive)
        
        if minuteActivityLog.count > maxLogSize {
            minuteActivityLog.removeFirst()
        }
        
        print("🔍 Записана активность: \(isActive), всего в логе: \(minuteActivityLog.count)")
    }
    
    func isPomodoroTimerIdle() -> Bool {
        if let timer = pomodoroTimer {
            print("🔍 Состояние Pomodoro таймера: \(timer.state)")
            guard timer.state == .idle else {
                if timer.state == .onBreak {
                    print("🔍 Пользователь на отдыхе (.onBreak) - НЕ показываем неформальные интервалы")
                } else {
                    print("🔍 Pomodoro таймер активен (\(timer.state)) - НЕ показываем неформальные интервалы")
                }
                return false
            }
            return true
        } else {
            print("🔍 ⚠️ Pomodoro таймер недоступен!")
            return false
        }
    }
    
    func shouldTriggerRestSuggestion() -> Bool {
        guard minuteActivityLog.count >= checkWindowSize else {
            return false
        }
        
        // Проверяем состояние таймера
        guard isPomodoroTimerIdle() else {
            return false
        }
        
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count
        
        return activeCount >= minActiveMinutes
    }
    
    func getActiveMinutesCount() -> Int {
        guard minuteActivityLog.count >= checkWindowSize else {
            return 0
        }
        
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        return recentActivity.filter { $0 }.count
    }
    
    func getTotalMinutesCount() -> Int {
        return min(minuteActivityLog.count, checkWindowSize)
    }
    
    func resetActivityHistory() {
        minuteActivityLog.removeAll()
    }
}

// MARK: - Tests

func runInformalBlockingTests() {
    let runner = TestRunner()
    
    // Тест 1: Неформальные интервалы НЕ срабатывают во время отдыха
    runner.test("🚫 Блокировка во время отдыха (.onBreak)") {
        let detector = TestInformalSessionDetector()
        let timer = MockPomodoroTimer()
        detector.pomodoroTimer = timer
        
        // Накапливаем достаточно активности для срабатывания
        timer.setState(.idle)
        for _ in 1...52 {
            detector.recordMinuteActivity(isActive: true)
        }
        
        // Проверяем что в idle состоянии срабатывает
        try runner.assert(detector.shouldTriggerRestSuggestion(),
                         "В состоянии idle должно срабатывать")
        
        // Переводим в состояние отдыха
        timer.setState(.onBreak)
        
        // Проверяем что во время отдыха НЕ срабатывает
        try runner.assert(!detector.shouldTriggerRestSuggestion(),
                         "Во время отдыха (.onBreak) НЕ должно срабатывать")
        
        // Проверяем что isPomodoroTimerIdle() возвращает false
        try runner.assert(!detector.isPomodoroTimerIdle(),
                         "isPomodoroTimerIdle() должен возвращать false во время отдыха")
    }
    
    // Тест 2: Активность НЕ записывается во время отдыха
    runner.test("🚫 Активность НЕ записывается во время отдыха") {
        let detector = TestInformalSessionDetector()
        let timer = MockPomodoroTimer()
        detector.pomodoroTimer = timer
        
        // Записываем активность в idle состоянии
        timer.setState(.idle)
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: true)
        
        try runner.assertEqual(detector.getTotalMinutesCount(), 2,
                              "В idle состоянии активность должна записываться")
        
        // Переводим в состояние отдыха
        timer.setState(.onBreak)
        
        // Пытаемся записать активность во время отдыха
        detector.recordMinuteActivity(isActive: true)
        detector.recordMinuteActivity(isActive: true)
        
        // Проверяем что активность НЕ записалась
        try runner.assertEqual(detector.getTotalMinutesCount(), 2,
                              "Во время отдыха активность НЕ должна записываться")
    }
    
    // Тест 3: Блокировка во время работы (.working)
    runner.test("🚫 Блокировка во время работы (.working)") {
        let detector = TestInformalSessionDetector()
        let timer = MockPomodoroTimer()
        detector.pomodoroTimer = timer
        
        // Накапливаем активность в idle
        timer.setState(.idle)
        for _ in 1...52 {
            detector.recordMinuteActivity(isActive: true)
        }
        
        // Переводим в состояние работы
        timer.setState(.working)
        
        // Проверяем что во время работы НЕ срабатывает
        try runner.assert(!detector.shouldTriggerRestSuggestion(),
                         "Во время работы (.working) НЕ должно срабатывать")
        
        // Проверяем что активность НЕ записывается
        let countBefore = detector.getTotalMinutesCount()
        detector.recordMinuteActivity(isActive: true)
        let countAfter = detector.getTotalMinutesCount()
        
        try runner.assertEqual(countBefore, countAfter,
                              "Во время работы активность НЕ должна записываться")
    }
    
    // Тест 4: Блокировка во время переработки (.overtime)
    runner.test("🚫 Блокировка во время переработки (.overtime)") {
        let detector = TestInformalSessionDetector()
        let timer = MockPomodoroTimer()
        detector.pomodoroTimer = timer
        
        // Накапливаем активность в idle
        timer.setState(.idle)
        for _ in 1...52 {
            detector.recordMinuteActivity(isActive: true)
        }
        
        // Переводим в состояние переработки
        timer.setState(.overtime)
        
        // Проверяем что во время переработки НЕ срабатывает
        try runner.assert(!detector.shouldTriggerRestSuggestion(),
                         "Во время переработки (.overtime) НЕ должно срабатывать")
    }
    
    // Тест 5: Возобновление работы после отдыха
    runner.test("✅ Возобновление после отдыха") {
        let detector = TestInformalSessionDetector()
        let timer = MockPomodoroTimer()
        detector.pomodoroTimer = timer
        
        // Накапливаем активность в idle
        timer.setState(.idle)
        for _ in 1...52 {
            detector.recordMinuteActivity(isActive: true)
        }
        
        // Проверяем что срабатывает
        try runner.assert(detector.shouldTriggerRestSuggestion(),
                         "В idle должно срабатывать")
        
        // Переводим в отдых
        timer.setState(.onBreak)
        try runner.assert(!detector.shouldTriggerRestSuggestion(),
                         "Во время отдыха НЕ должно срабатывать")
        
        // Возвращаемся в idle
        timer.setState(.idle)
        try runner.assert(detector.shouldTriggerRestSuggestion(),
                         "После возврата в idle должно снова срабатывать")
    }
    
    let success = runner.printSummary()
    exit(success ? 0 : 1)
}

// MARK: - Main

print("🚀 Запуск тестов блокировки неформальных интервалов во время отдыха...")
print("📅 \(Date())")
print("")

runInformalBlockingTests()
