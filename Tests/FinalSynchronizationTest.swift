#!/usr/bin/env swift

import Foundation
import Cocoa

print("🎯 ФИНАЛЬНЫЙ ТЕСТ СИНХРОНИЗАЦИИ")
print(String(repeating: "=", count: 50))

// Создаем экземпляр системы
let system = EarlyEngagementSystem.shared

print("📋 ТЕСТИРУЕМ СЦЕНАРИЙ ПОЛЬЗОВАТЕЛЯ:")
print("   • Изначальная планка: 30 минут")
print("   • Дни без работы: 0 (работал вчера)")
print("   • Ожидаемый результат: 35 минут")

// Устанавливаем отладочные параметры
system.debugSetInitialBar(30)
system.debugSetDaysWithoutWork(0)

print("\n🔧 ОТЛАДОЧНЫЕ ПАРАМЕТРЫ УСТАНОВЛЕНЫ")

// Получаем сообщение через createEngagementMessage (как в реальном окне)
let realMessage = system.createEngagementMessage()

print("\n📱 РЕЗУЛЬТАТ РЕАЛЬНОГО ОКНА:")
print("   Заголовок: \(realMessage.title)")
print("   Подзаголовок: \(realMessage.subtitle)")
print("   Кнопка: \(realMessage.buttonText)")
print("   Длительность: \(Int(realMessage.proposedDuration / 60)) минут")

// Проверяем результат
let resultMinutes = Int(realMessage.proposedDuration / 60)
if resultMinutes == 35 {
    print("\n✅ УСПЕХ! Реальное окно показывает 35 минут (как ожидается)")
} else {
    print("\n❌ ПРОБЛЕМА! Реальное окно показывает \(resultMinutes) минут вместо 35")
}

// Очищаем отладочные настройки
system.debugClearInitialBar()
system.debugClearDaysWithoutWork()

print("\n🧹 ОТЛАДОЧНЫЕ ПАРАМЕТРЫ ОЧИЩЕНЫ")
print("\n🎉 ТЕСТ ЗАВЕРШЕН")
