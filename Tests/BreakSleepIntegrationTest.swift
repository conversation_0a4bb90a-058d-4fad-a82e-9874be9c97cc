import Foundation

/// Тест интеграции BreakTimer с унифицированной системой сна
/// Проверяет что время отдыха корректно учитывает сон

@main struct BreakSleepIntegrationTest {
    
    // Простая заглушка для BreakTimer для тестирования
    class TestBreakTimer {
        private var breakEndTime: Date?
        private var timeRemaining: TimeInterval = 0
        private var isActive: Bool = false
        
        func startBreak(duration: TimeInterval) {
            timeRemaining = duration
            breakEndTime = Date().addingTimeInterval(duration)
            isActive = true
            print("🌿 Отдых начат: \(Int(duration/60)) мин")
        }
        
        func handleSystemWake() {
            print("🌅 BreakTimer: Система проснулась (унифицированная система)")

            // Если таймер активен, немедленно обновляем оставшееся время
            if isActive {
                updateBreakTimer()
                print("🌅 BreakTimer: Время скорректировано после пробуждения. Осталось: \(formatTime(timeRemaining))")
            }
        }
        
        private func updateBreakTimer() {
            // Вычисляем оставшееся время на основе абсолютного времени окончания
            guard let endTime = breakEndTime else { return }

            let now = Date()
            timeRemaining = max(0, endTime.timeIntervalSince(now))
        }
        
        func getTimeRemaining() -> TimeInterval {
            return timeRemaining
        }
        
        private func formatTime(_ time: TimeInterval) -> String {
            let minutes = Int(time) / 60
            let seconds = Int(time) % 60
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
    
    static func main() {
        print("🧪 ========================================")
        print("🧪 ТЕСТ: Интеграция отдыха и сна")
        print("🧪 ========================================")
        
        testBreakWithSleep()
        testBreakWithoutSleep()
        
        print("🧪 ========================================")
        print("🧪 ВСЕ ТЕСТЫ ЗАВЕРШЕНЫ")
        print("🧪 ========================================")
    }
    
    /// Тест: отдых с сном должен учитывать время сна
    static func testBreakWithSleep() {
        print("\n🧪 ТЕСТ 1: Отдых с сном")
        
        let breakTimer = TestBreakTimer()
        
        // Начинаем отдых на 10 секунд (для быстрого теста)
        breakTimer.startBreak(duration: 10.0)

        // Ждем 3 секунды
        Thread.sleep(forTimeInterval: 3.0)

        // Симулируем сон на 5 секунд
        let sleepDuration: TimeInterval = 5.0
        print("💤 Симулируем сон на \(Int(sleepDuration)) секунд...")

        // Ждем реальное время чтобы симулировать прохождение времени
        Thread.sleep(forTimeInterval: sleepDuration)

        // Симулируем пробуждение
        breakTimer.handleSystemWake()

        let remainingTime = breakTimer.getTimeRemaining()
        let expectedTime = 10.0 - 3.0 - sleepDuration // 10 - 3 - 5 = 2 секунды
        
        print("🔍 Ожидаемое время: \(String(format: "%.1f", expectedTime)) сек")
        print("🔍 Фактическое время: \(String(format: "%.1f", remainingTime)) сек")

        // Проверяем что время корректно уменьшилось
        if abs(remainingTime - expectedTime) < 1.0 { // Допуск в 1 секунду
            print("✅ ТЕСТ 1 ПРОЙДЕН: Время отдыха корректно учитывает сон")
        } else {
            print("❌ ТЕСТ 1 ПРОВАЛЕН: Время не соответствует ожиданиям")
        }
    }
    
    /// Тест: отдых без сна работает как обычно
    static func testBreakWithoutSleep() {
        print("\n🧪 ТЕСТ 2: Отдых без сна")
        
        let breakTimer = TestBreakTimer()
        
        // Начинаем отдых на 5 секунд
        breakTimer.startBreak(duration: 5.0)
        
        // Небольшая задержка
        Thread.sleep(forTimeInterval: 0.1)
        
        // Проверяем время без сна
        let remainingTime = breakTimer.getTimeRemaining()
        let expectedTime: TimeInterval = 5.0 // Должно остаться примерно 5 секунд

        print("🔍 Ожидаемое время: ~\(String(format: "%.1f", expectedTime)) сек")
        print("🔍 Фактическое время: \(String(format: "%.1f", remainingTime)) сек")
        
        // Проверяем что время почти не изменилось
        if abs(remainingTime - expectedTime) < 1.0 { // Допуск в 1 секунду
            print("✅ ТЕСТ 2 ПРОЙДЕН: Отдых без сна работает корректно")
        } else {
            print("❌ ТЕСТ 2 ПРОВАЛЕН: Время изменилось неожиданно")
        }
    }
}
