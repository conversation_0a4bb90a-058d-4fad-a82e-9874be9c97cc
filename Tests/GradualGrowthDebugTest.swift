#!/usr/bin/env swift

import Foundation

print("🧪 ТЕСТ ГРАДАЦИОННОЙ СИСТЕМЫ РОСТА")
print(String(repeating: "=", count: 50))

// Тестируем 30 минут
let testBar = 30
print("📊 Тестируем планку: \(testBar) минут")

// Проверяем диапазон
if testBar >= 26 && testBar <= 40 {
    print("✅ Попадает в диапазон 26-40 (множитель 1.15)")
    let result = Double(testBar) * 1.15
    print("🔢 Расчет: \(testBar) × 1.15 = \(result)")
    print("🔢 Округление: \(Int(round(result)))")
} else {
    print("❌ Не попадает в ожидаемый диапазон")
}

print("\n🎯 ПРОВЕРКА РАЗНЫХ ЗНАЧЕНИЙ:")
let testValues = [25, 26, 30, 35, 40, 41]
for value in testValues {
    let result = Double(value) * 1.15
    let rounded = Int(round(result))
    print("   \(value) мин → \(result) → \(rounded) мин")
}

print("\n📝 ВЫВОД:")
print("   30 минут × 1.15 = 34.5 → 34 минуты (не 35)")
print("   Возможно, пользователь ожидает другую логику?")
