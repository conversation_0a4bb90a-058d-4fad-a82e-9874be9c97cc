import Foundation

// Локальные копии структур для теста (избегаем сложных зависимостей)
enum RestType: String, Codable, CaseIterable {
    case formal = "formal"
    case automatic = "automatic"
    case partial = "partial"
    case micro = "micro"
}

enum RestQuality: String, Codable, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    case interrupted = "interrupted"
}

enum RestContext: String, Codable, CaseIterable {
    case formalInterval = "formalInterval"
    case informalSession = "informalSession"
    case betweenSessions = "betweenSessions"
    case systemDetected = "systemDetected"
}

struct RestStatistics: Codable {
    let date: Date
    let duration: TimeInterval
    let type: RestType
    let quality: RestQuality
    let context: RestContext
    let wasComputerActive: Bool
    let computerActiveTime: TimeInterval
    let bandData: [Bool]
    
    init(date: Date, duration: TimeInterval, type: RestType, quality: RestQuality, context: RestContext, wasComputerActive: Bool = false, computerActiveTime: TimeInterval = 0, bandData: [Bool] = []) {
        self.date = date
        self.duration = duration
        self.type = type
        self.quality = quality
        self.context = context
        self.wasComputerActive = wasComputerActive
        self.computerActiveTime = computerActiveTime
        self.bandData = bandData
    }
}

// Упрощенная версия StatisticsManager для тестирования RestStatistics
class RestStatisticsManager {
    private let userDefaults = UserDefaults.standard
    private let restStatisticsKey = "testRestStatistics"
    
    func recordRestStatistics(duration: TimeInterval, type: RestType, quality: RestQuality, context: RestContext, wasComputerActive: Bool = false, computerActiveTime: TimeInterval = 0, bandData: [Bool] = []) {
        let now = Date()
        let restStats = RestStatistics(
            date: now,
            duration: duration,
            type: type,
            quality: quality,
            context: context,
            wasComputerActive: wasComputerActive,
            computerActiveTime: computerActiveTime,
            bandData: bandData
        )
        
        var allRestStats = getAllStoredRestStatistics()
        allRestStats.append(restStats)
        saveRestStatistics(allRestStats)
    }
    
    func recordSmartRestStatistics(inactivityDuration: TimeInterval, context: RestContext, userChoice: String? = nil) {
        let (type, quality) = determineRestTypeAndQuality(duration: inactivityDuration, userChoice: userChoice)
        recordRestStatistics(duration: inactivityDuration, type: type, quality: quality, context: context)
    }
    
    private func determineRestTypeAndQuality(duration: TimeInterval, userChoice: String?) -> (RestType, RestQuality) {
        let minutes = duration / 60.0

        switch minutes {
        case 0..<2:
            return (.micro, .good)
        case 2..<10:
            return userChoice == "continue_break" ? (.partial, .good) : (.partial, .poor)
        case 10..<17:
            if userChoice == "continue_break" {
                return (.partial, .good)
            } else if userChoice == "start_interval" {
                return (.partial, .fair)
            } else {
                return (.partial, .good)
            }
        default:
            return (.automatic, .excellent)
        }
    }
    
    func getRestStatsByType(from startDate: Date, to endDate: Date) -> [RestType: (count: Int, totalDuration: TimeInterval)] {
        let restStats = getAllStoredRestStatistics().filter { stat in
            stat.date >= startDate && stat.date < endDate
        }
        
        var typeStats: [RestType: (count: Int, totalDuration: TimeInterval)] = [:]
        
        for stat in restStats {
            let current = typeStats[stat.type] ?? (count: 0, totalDuration: 0)
            typeStats[stat.type] = (count: current.count + 1, totalDuration: current.totalDuration + stat.duration)
        }
        
        return typeStats
    }
    
    func getOverallRestQuality(from startDate: Date, to endDate: Date) -> Double {
        let restStats = getAllStoredRestStatistics().filter { stat in
            stat.date >= startDate && stat.date < endDate
        }
        
        let totalRests = restStats.count
        guard totalRests > 0 else { return -1.0 }
        
        let goodRests = restStats.filter { stat in
            stat.quality == .excellent || stat.quality == .good
        }.count
        
        return Double(goodRests) / Double(totalRests)
    }
    
    func getAllStoredRestStatistics() -> [RestStatistics] {
        guard let data = userDefaults.data(forKey: restStatisticsKey) else {
            return []
        }
        
        do {
            return try JSONDecoder().decode([RestStatistics].self, from: data)
        } catch {
            return []
        }
    }
    
    func saveRestStatistics(_ restStats: [RestStatistics]) {
        do {
            let data = try JSONEncoder().encode(restStats)
            userDefaults.set(data, forKey: restStatisticsKey)
        } catch {
            print("Ошибка сохранения: \(error)")
        }
    }
    
    func clearAllRestStatistics() {
        userDefaults.removeObject(forKey: restStatisticsKey)
    }
}

// ТЕСТ: Проверка функциональности расширенной статистики отдыха
@main
struct RealRestStatisticsTest {
    static func main() {
        print("🧪 Тест расширенной статистики отдыха (RestStatistics)...")
        
        let manager = RestStatisticsManager()
        var passedTests = 0
        let totalTests = 6
        
        // Очищаем данные перед тестом
        manager.clearAllRestStatistics()
        
        // Тест 1: Запись различных типов отдыха
        print("\n1️⃣ Тест записи различных типов отдыха...")
        manager.recordRestStatistics(duration: 90, type: .micro, quality: .good, context: .informalSession)
        manager.recordRestStatistics(duration: 480, type: .partial, quality: .good, context: .informalSession)
        manager.recordRestStatistics(duration: 1200, type: .automatic, quality: .excellent, context: .systemDetected)
        
        let allStats = manager.getAllStoredRestStatistics()
        if allStats.count == 3 {
            print("✅ Записано 3 типа отдыха")
            passedTests += 1
        } else {
            print("❌ Ожидалось 3 записи, получено \(allStats.count)")
        }
        
        // Тест 2: Умная запись отдыха с выбором пользователя
        print("\n2️⃣ Тест умной записи отдыха...")
        manager.recordSmartRestStatistics(inactivityDuration: 360, context: .informalSession, userChoice: "continue_break")
        manager.recordSmartRestStatistics(inactivityDuration: 600, context: .informalSession, userChoice: "start_interval")
        
        let smartStats = manager.getAllStoredRestStatistics()
        if smartStats.count == 5 {
            let lastTwo = Array(smartStats.suffix(2))
            let firstIsGood = lastTwo[0].quality == .good  // continue_break
            let secondIsFair = lastTwo[1].quality == .fair  // start_interval
            
            if firstIsGood && secondIsFair {
                print("✅ Умная логика качества работает корректно")
                passedTests += 1
            } else {
                print("❌ Неправильное определение качества: \(lastTwo[0].quality), \(lastTwo[1].quality)")
            }
        } else {
            print("❌ Неправильное количество записей после умной записи")
        }
        
        // Тест 3: Статистика по типам отдыха
        print("\n3️⃣ Тест статистики по типам...")
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        
        let typeStats = manager.getRestStatsByType(from: today, to: tomorrow)
        
        if typeStats[.micro]?.count == 1 && typeStats[.partial]?.count == 3 && typeStats[.automatic]?.count == 1 {
            print("✅ Статистика по типам корректна")
            passedTests += 1
        } else {
            print("❌ Неправильная статистика по типам: \(typeStats)")
        }
        
        // Тест 4: Расчет общего качества отдыха
        print("\n4️⃣ Тест расчета качества отдыха...")
        let quality = manager.getOverallRestQuality(from: today, to: tomorrow)
        
        // У нас: 1 micro.good + 1 partial.good + 1 automatic.excellent + 1 partial.good + 1 partial.fair = 4 хороших из 5
        let expectedQuality = 4.0 / 5.0  // 0.8
        
        if abs(quality - expectedQuality) < 0.01 {
            print("✅ Качество отдыха рассчитано правильно: \(Int(quality * 100))%")
            passedTests += 1
        } else {
            print("❌ Неправильное качество: \(quality), ожидалось \(expectedQuality)")
        }
        
        // Тест 5: Сохранение и загрузка данных
        print("\n5️⃣ Тест сохранения/загрузки...")
        let beforeClear = manager.getAllStoredRestStatistics().count
        manager.clearAllRestStatistics()
        let afterClear = manager.getAllStoredRestStatistics().count
        
        if beforeClear == 5 && afterClear == 0 {
            print("✅ Сохранение и очистка данных работают")
            passedTests += 1
        } else {
            print("❌ Проблемы с сохранением: до очистки \(beforeClear), после \(afterClear)")
        }
        
        // Тест 6: Данные бандов и активности за компьютером
        print("\n6️⃣ Тест данных бандов и активности...")
        let bandData = [true, false, true, false]
        manager.recordRestStatistics(
            duration: 300,
            type: .formal,
            quality: .good,
            context: .formalInterval,
            wasComputerActive: true,
            computerActiveTime: 120,
            bandData: bandData
        )
        
        let statsWithBands = manager.getAllStoredRestStatistics()
        if let lastStat = statsWithBands.last {
            if lastStat.bandData == bandData && lastStat.wasComputerActive && lastStat.computerActiveTime == 120 {
                print("✅ Данные бандов и активности сохраняются корректно")
                passedTests += 1
            } else {
                print("❌ Неправильные данные бандов или активности")
            }
        } else {
            print("❌ Не удалось получить последнюю запись")
        }
        
        // Итоги
        print("\n🧪 Результаты теста расширенной статистики отдыха:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Расширенная статистика отдыха работает")
        } else {
            print("❌ Некоторые тесты провалились")
        }
        
        // Очищаем после теста
        manager.clearAllRestStatistics()
    }
}
