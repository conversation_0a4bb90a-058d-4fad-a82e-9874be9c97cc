import Foundation

/// РЕАЛЬНЫЙ ИНТЕГРАЦИОННЫЙ ТЕСТ УНИФИЦИРОВАННОЙ СИСТЕМЫ
/// Проверяем что MinuteActivityTracker + ActivityStateManager работают вместе

@main
struct RealUnifiedIntegrationTest {
    static func main() {
        print("🚀 РЕАЛЬНЫЙ ИНТЕГРАЦИОННЫЙ ТЕСТ УНИФИЦИРОВАННОЙ СИСТЕМЫ")
        print(String(repeating: "=", count: 70))
        
        // Создаем РЕАЛЬНЫЕ объекты
        let tracker = MinuteActivityTracker()
        let stateManager = ActivityStateManager()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Запуск обеих систем
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Запуск обеих систем")
        
        tracker.startTracking()
        stateManager.start()
        
        let trackerState = tracker.getCurrentBandsState()
        let managerState = stateManager.getCurrentStateInfo()
        
        if trackerState.isActive && managerState.state == .working {
            print("✅ ПРОЙДЕН: Обе системы запускаются корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы при запуске систем")
            print("   Трекер активен: \(trackerState.isActive), Менеджер: \(managerState.state)")
        }
        
        // СЦЕНАРИЙ 2: Симуляция активности в обеих системах
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Симуляция активности в обеих системах")
        
        tracker.simulateActivityInCurrentBand()
        stateManager.simulateActivity()
        
        let trackerActive = tracker.wasCurrentMinuteActive()
        let managerActive = stateManager.getCurrentStateInfo().state == .working
        
        if trackerActive && managerActive {
            print("✅ ПРОЙДЕН: Активность корректно обрабатывается в обеих системах")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с обработкой активности")
            print("   Трекер активен: \(trackerActive), Менеджер в работе: \(managerActive)")
        }
        
        // СЦЕНАРИЙ 3: Принудительное завершение минуты + переход состояния
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Принудительное завершение минуты + переход состояния")

        let completedWithActivity = tracker.forceCompleteCurrentMinute()
        stateManager.simulateInactivity(duration: 90) // 1.5 минуты

        let managerAfterInactivity = stateManager.getCurrentStateInfo()

        // Принудительное завершение может вернуть true или false в зависимости от реальной активности
        // Главное что переход состояния работает корректно
        if managerAfterInactivity.state == .awayShort {
            print("✅ ПРОЙДЕН: Переход состояния работает корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с переходом состояния")
            print("   Завершено с активностью: \(completedWithActivity), Состояние: \(managerAfterInactivity.state)")
        }
        
        // СЦЕНАРИЙ 4: Возврат пользователя в обеих системах
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Возврат пользователя в обеих системах")
        
        tracker.simulateActivityInCurrentBand()
        stateManager.simulateActivity()
        
        let trackerAfterReturn = tracker.wasCurrentMinuteActive()
        let managerAfterReturn = stateManager.getCurrentStateInfo().state
        
        if trackerAfterReturn && managerAfterReturn == .working {
            print("✅ ПРОЙДЕН: Возврат пользователя обрабатывается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с возвратом пользователя")
            print("   Трекер активен: \(trackerAfterReturn), Менеджер: \(managerAfterReturn)")
        }
        
        // СЦЕНАРИЙ 5: Длительная неактивность в обеих системах
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Длительная неактивность в обеих системах")
        
        stateManager.simulateInactivity(duration: 18 * 60) // 18 минут
        let trackerAfterLongInactivity = tracker.getCurrentBandsState()
        let managerAfterLongInactivity = stateManager.getCurrentStateInfo()
        
        if managerAfterLongInactivity.state == .awayVeryLong {
            print("✅ ПРОЙДЕН: Длительная неактивность обрабатывается корректно")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с длительной неактивностью")
            print("   Менеджер: \(managerAfterLongInactivity.state)")
        }
        
        // СЦЕНАРИЙ 6: Остановка обеих систем
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Остановка обеих систем")
        
        tracker.stopTracking()
        stateManager.stop()
        
        let trackerStopped = !tracker.getCurrentBandsState().isActive
        let managerStopped = stateManager.getDebugInfo().contains("Активен: false")
        
        if trackerStopped && managerStopped {
            print("✅ ПРОЙДЕН: Обе системы корректно останавливаются")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с остановкой систем")
            print("   Трекер остановлен: \(trackerStopped), Менеджер остановлен: \(managerStopped)")
        }
        
        // СЦЕНАРИЙ 7: Проверка независимости систем
        total += 1
        print("\n📋 СЦЕНАРИЙ 7: Проверка независимости систем")
        
        // Запускаем только трекер
        tracker.startTracking()
        tracker.simulateActivityInCurrentBand()
        
        let independentTracker = tracker.wasCurrentMinuteActive()
        let independentManager = stateManager.getDebugInfo().contains("Активен: false")
        
        if independentTracker && independentManager {
            print("✅ ПРОЙДЕН: Системы работают независимо")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Проблемы с независимостью систем")
            print("   Трекер работает: \(independentTracker), Менеджер остановлен: \(independentManager)")
        }
        
        // Финальная очистка
        tracker.stopTracking()
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 70))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ ИНТЕГРАЦИОННЫЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ Унифицированная система активности работает корректно")
            
            // Проверка обнаружения поломок
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В ИНТЕГРАЦИИ УНИФИЦИРОВАННОЙ СИСТЕМЫ!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Сломайте MinuteActivityTracker.simulateActivityInCurrentBand():")
        print("   Закомментируйте: currentMinuteBands[currentBand] = true")
        print("2. Запустите тест - СЦЕНАРИИ 2, 4, 7 провалятся!")
        print("\nИЛИ:")
        print("1. Сломайте ActivityStateManager.simulateActivity():")
        print("   Закомментируйте: handleUserActivity()")
        print("2. Запустите тест - СЦЕНАРИИ 2, 4 провалятся!")
        print("\nИЛИ:")
        print("1. Сломайте интеграцию - в ActivityStateManager.start():")
        print("   Закомментируйте: minuteTracker.startTracking()")
        print("2. Запустите тест - СЦЕНАРИЙ 1 провалится!")
        print("\nЭто покажет что интеграционный тест ловит поломки в реальной логике.")
        
        exit(0)
    }
}
