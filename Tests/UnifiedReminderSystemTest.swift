#!/usr/bin/env swift

//
//  UnifiedReminderSystemTest.swift
//  uProd Tests
//
//  Тесты для проверки унификации логики между формальными и неформальными окнами
//  Проверяет что обе системы используют одинаковую логику эскалации, тряски и таймеров
//

import Foundation

// MARK: - Test Framework

struct TestResult {
    let name: String
    let passed: Bool
    let message: String
    let duration: TimeInterval
}

class TestRunner {
    private var results: [TestResult] = []
    
    func test(_ name: String, _ testFunc: () throws -> Void) {
        let startTime = Date()
        do {
            try testFunc()
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: true, message: "✅ ПРОШЕЛ", duration: duration))
            print("✅ \(name)")
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: false, message: "❌ ПРОВАЛЕН: \(error)", duration: duration))
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String = "") throws {
        if actual != expected {
            throw TestError.assertionFailed("Ожидалось: \(expected), получено: \(actual). \(message)")
        }
    }
    
    func printSummary() {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        let totalTime = results.reduce(0) { $0 + $1.duration }
        
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ ТЕСТОВ УНИФИЦИРОВАННОЙ СИСТЕМЫ")
        print(String(repeating: "=", count: 60))
        print("✅ Прошло: \(passed)/\(total)")
        print("⏱️  Время: \(String(format: "%.3f", totalTime))с")
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ!")
        } else {
            print("❌ ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ:")
            for result in results where !result.passed {
                print("   • \(result.name): \(result.message)")
            }
        }
        print(String(repeating: "=", count: 60))
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Mock Classes для изоляции тестов

// Простая версия OvertimeConfig для тестов
struct MockOvertimeConfig {
    static func getLevelNumber(for minutes: Int) -> Int {
        switch minutes {
        case 0..<1: return 0
        case 1..<3: return 1  // Yellow
        case 3..<5: return 2  // Orange  
        case 5..<10: return 3 // Red
        case 10..<15: return 4 // Dark Red
        default: return 5     // Critical
        }
    }
    
    static func getColor(for minutes: Int) -> String {
        switch getLevelNumber(for: minutes) {
        case 0: return "normal"
        case 1: return "yellow"
        case 2: return "orange"
        case 3: return "red"
        case 4: return "darkRed"
        default: return "critical"
        }
    }
}

// Тестовая версия системы напоминаний
class MockReminderSystem {
    var currentLevel: Int = -1
    var isTimerRunning: Bool = false
    var reminderHistory: [(minutes: Int, level: Int)] = []
    var shakeHistory: [Bool] = [] // true = с тряской, false = без тряски
    var statusBarUpdates: [String] = []
    
    func startReminder(minutes: Int) {
        isTimerRunning = true
        updateLevel(minutes: minutes)
    }
    
    func updateLevel(minutes: Int) {
        let newLevel = MockOvertimeConfig.getLevelNumber(for: minutes)
        
        // Записываем историю только при изменении уровня
        if newLevel != currentLevel {
            currentLevel = newLevel
            reminderHistory.append((minutes: minutes, level: newLevel))
            
            // Симулируем обновление статус-бара
            let color = MockOvertimeConfig.getColor(for: minutes)
            statusBarUpdates.append("\(color)_\(minutes)min")
        }
    }
    
    func showWindow(isExisting: Bool) {
        // Записываем логику тряски: существующие окна - с тряской, новые - без
        shakeHistory.append(isExisting)
    }
    
    func stopReminder() {
        isTimerRunning = false
        currentLevel = -1
    }
    
    func reset() {
        currentLevel = -1
        isTimerRunning = false
        reminderHistory.removeAll()
        shakeHistory.removeAll()
        statusBarUpdates.removeAll()
    }
}

// MARK: - Тесты

let runner = TestRunner()

// Тест 1: Проверка унификации эскалации
runner.test("Формальные и неформальные интервалы используют одинаковую эскалацию") {
    let formalSystem = MockReminderSystem()
    let informalSystem = MockReminderSystem()
    
    // Симулируем одинаковую последовательность для обеих систем
    let testMinutes = [1, 2, 3, 4, 5, 8, 12, 16]
    
    for minutes in testMinutes {
        formalSystem.updateLevel(minutes: minutes)
        informalSystem.updateLevel(minutes: minutes)
    }
    
    // Проверяем что история эскалации одинаковая
    try runner.assertEqual(formalSystem.reminderHistory.count, informalSystem.reminderHistory.count, 
                          "Количество уровней эскалации должно быть одинаковым")
    
    for i in 0..<formalSystem.reminderHistory.count {
        let formal = formalSystem.reminderHistory[i]
        let informal = informalSystem.reminderHistory[i]
        
        try runner.assertEqual(formal.level, informal.level, 
                              "Уровень \(i): формальный=\(formal.level), неформальный=\(informal.level)")
        try runner.assertEqual(formal.minutes, informal.minutes,
                              "Минуты \(i): формальный=\(formal.minutes), неформальный=\(informal.minutes)")
    }
}

// Тест 2: Проверка логики тряски
runner.test("Логика тряски одинакова для формальных и неформальных окон") {
    let system = MockReminderSystem()
    
    // Симулируем последовательность: новое окно -> обновление -> новое окно -> обновление
    system.showWindow(isExisting: false) // Новое окно - без тряски
    system.showWindow(isExisting: true)  // Обновление существующего - с тряской
    system.showWindow(isExisting: false) // Новое окно после закрытия - без тряски
    system.showWindow(isExisting: true)  // Обновление существующего - с тряской
    
    let expected = [false, true, false, true]
    try runner.assertEqual(system.shakeHistory, expected, 
                          "Логика тряски: новые окна без тряски, обновления с тряской")
}

// Тест 3: Проверка цветовой схемы статус-бара
runner.test("Цветовая схема статус-бара одинакова для всех типов интервалов") {
    let system = MockReminderSystem()
    
    // Тестируем все уровни эскалации
    let testCases = [
        (minutes: 0, expectedColor: "normal"),
        (minutes: 1, expectedColor: "yellow"),
        (minutes: 3, expectedColor: "orange"),
        (minutes: 5, expectedColor: "red"),
        (minutes: 10, expectedColor: "darkRed"),
        (minutes: 20, expectedColor: "critical")
    ]
    
    for testCase in testCases {
        system.updateLevel(minutes: testCase.minutes)
        let actualColor = MockOvertimeConfig.getColor(for: testCase.minutes)
        try runner.assertEqual(actualColor, testCase.expectedColor,
                              "Цвет для \(testCase.minutes) минут")
    }
}

// Тест 4: Проверка непрерывности эскалации
runner.test("Эскалация работает непрерывно без остановок") {
    let system = MockReminderSystem()
    
    system.startReminder(minutes: 0)
    try runner.assert(system.isTimerRunning, "Таймер должен быть запущен")
    
    // Симулируем непрерывную работу в течение 15 минут
    for minute in 1...15 {
        system.updateLevel(minutes: minute)
        try runner.assert(system.isTimerRunning, "Таймер должен продолжать работать на \(minute) минуте")
    }
    
    // Проверяем что все ключевые уровни были зафиксированы
    let levels = system.reminderHistory.map { $0.level }
    try runner.assert(levels.contains(1), "Должен быть уровень 1 (yellow)")
    try runner.assert(levels.contains(2), "Должен быть уровень 2 (orange)")
    try runner.assert(levels.contains(3), "Должен быть уровень 3 (red)")
}

// Тест 5: Проверка обновления статус-бара
runner.test("Статус-бар обновляется при каждом изменении уровня") {
    let system = MockReminderSystem()
    
    // Симулируем изменения уровней
    system.updateLevel(minutes: 1)  // yellow
    system.updateLevel(minutes: 1)  // тот же уровень - не должно обновляться
    system.updateLevel(minutes: 3)  // orange
    system.updateLevel(minutes: 5)  // red
    
    // Должно быть 3 обновления (1, 3, 5 минут), но не для повторного 1
    try runner.assertEqual(system.statusBarUpdates.count, 3, 
                          "Статус-бар должен обновляться только при изменении уровня")
    
    try runner.assertEqual(system.statusBarUpdates[0], "yellow_1min", "Первое обновление")
    try runner.assertEqual(system.statusBarUpdates[1], "orange_3min", "Второе обновление")
    try runner.assertEqual(system.statusBarUpdates[2], "red_5min", "Третье обновление")
}

// Запускаем все тесты
print("🧪 Запуск тестов унифицированной системы напоминаний...")
print("📋 Проверяем что формальные и неформальные интервалы используют одинаковую логику")
print("")

runner.printSummary()
