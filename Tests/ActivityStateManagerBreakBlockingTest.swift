#!/usr/bin/env swift

//
//  ActivityStateManagerBreakBlockingTest.swift
//  uProd Tests
//
//  Тест для проверки блокировки неформальных интервалов во время отдыха в ActivityStateManager
//  Исправляет проблему: неформальные интервалы срабатывают во время отдыха
//  ВАЖНО: Тестирует РЕАЛЬНУЮ систему ActivityStateManager (НЕ устаревший InformalSessionDetector)
//

import Foundation

// MARK: - Test Framework

struct TestResult {
    let name: String
    let passed: Bool
    let message: String
    let duration: TimeInterval
}

class TestRunner {
    private var results: [TestResult] = []
    
    func test(_ name: String, _ testFunc: () throws -> Void) {
        let startTime = Date()
        do {
            try testFunc()
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: true, message: "✅ ПРОШЕЛ", duration: duration))
            print("✅ \(name)")
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: false, message: "❌ ПРОВАЛЕН: \(error)", duration: duration))
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String = "") throws {
        if actual != expected {
            throw TestError.assertionFailed("Ожидалось: \(expected), получено: \(actual). \(message)")
        }
    }
    
    func printSummary() -> Bool {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        let totalTime = results.reduce(0) { $0 + $1.duration }
        
        print("\n" + String(repeating: "=", count: 60))
        print("🧪 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ ActivityStateManager")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(total)")
        print("✅ Прошло: \(passed)")
        print("❌ Провалено: \(total - passed)")
        print("⏱️ Время выполнения: \(String(format: "%.3f", totalTime))с")
        print("📈 Успешность: \(Int((Double(passed) / Double(total)) * 100))%")
        print(String(repeating: "=", count: 60))
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! ActivityStateManager корректно блокирует неформальные интервалы.")
            return true
        } else {
            print("⚠️ ЕСТЬ ПРОБЛЕМЫ! Требуется исправление.")
            print("\nПровалившиеся тесты:")
            for result in results where !result.passed {
                print("  • \(result.name): \(result.message)")
            }
            return false
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Mock PomodoroTimer для тестирования

enum MockPomodoroState: String {
    case idle = "idle"
    case working = "working"
    case overtime = "overtime"
    case onBreak = "onBreak"
}

class MockPomodoroTimer {
    var state: MockPomodoroState = .idle
    
    func setState(_ newState: MockPomodoroState) {
        state = newState
        print("🍅 MockPomodoroTimer: состояние изменено на \(state)")
    }
}

// MARK: - Тестовая версия ActivityStateManager

class TestActivityStateManager {
    // Копируем ключевые параметры из реального ActivityStateManager
    private var minuteActivityHistory: [Bool] = []
    private let maxHistoryMinutes = 52
    private let minActiveMinutesForSuggestion = 42
    private var lastRestSuggestionTime: Date?
    private let restSuggestionCooldown: TimeInterval = 10 * 60
    
    // Mock зависимости
    weak var pomodoroTimer: MockPomodoroTimer?
    
    // Callback для неформальных предложений
    var onInformalRestSuggestion: (() -> Void)?
    
    // Флаг для отслеживания срабатывания
    var informalSuggestionTriggered = false
    
    init() {
        // Настраиваем callback для отслеживания
        onInformalRestSuggestion = { [weak self] in
            self?.informalSuggestionTriggered = true
            print("🔔 НЕФОРМАЛЬНОЕ ПРЕДЛОЖЕНИЕ СРАБОТАЛО!")
        }
    }
    
    func recordMinuteActivity(isActive: Bool) {
        minuteActivityHistory.append(isActive)
        
        // Ограничиваем размер истории
        if minuteActivityHistory.count > maxHistoryMinutes {
            minuteActivityHistory.removeFirst()
        }
        
        print("🔍 Записана активность: \(isActive), всего в истории: \(minuteActivityHistory.count)")
        
        // Проверяем на неформальные предложения после каждой записи
        checkForInformalRestSuggestion()
    }
    
    /// РЕАЛЬНАЯ логика из ActivityStateManager с исправлением
    private func checkForInformalRestSuggestion() {
        // Сбрасываем флаг перед проверкой
        informalSuggestionTriggered = false
        
        // КРИТИЧЕСКИ ВАЖНО: НЕ показываем неформальные предложения во время активного формального интервала ИЛИ отдыха
        if let timer = pomodoroTimer {
            if timer.state == .working {
                print("🔍 БЛОКИРОВКА: Активный формальный интервал (\(timer.state)) - НЕ показываем неформальное предложение")
                return
            } else if timer.state == .onBreak {
                print("🔍 БЛОКИРОВКА: Пользователь на отдыхе (\(timer.state)) - НЕ показываем неформальное предложение")
                return
            } else if timer.state == .overtime {
                print("🔍 БЛОКИРОВКА: Переработка (\(timer.state)) - НЕ показываем неформальное предложение")
                return
            }
        }

        // Нужно минимум 52 минуты истории
        guard minuteActivityHistory.count >= maxHistoryMinutes else {
            print("🔍 Недостаточно истории для проверки: \(minuteActivityHistory.count)/\(maxHistoryMinutes)")
            return
        }

        // Проверяем cooldown
        if let lastSuggestion = lastRestSuggestionTime,
           Date().timeIntervalSince(lastSuggestion) < restSuggestionCooldown {
            print("🔍 Cooldown активен, пропускаем проверку")
            return
        }

        // Считаем активные минуты
        let activeMinutes = minuteActivityHistory.filter { $0 }.count
        print("🔍 Активных минут: \(activeMinutes)/\(maxHistoryMinutes)")

        // Проверяем условие срабатывания
        if activeMinutes >= minActiveMinutesForSuggestion {
            print("🔔 НЕФОРМАЛЬНАЯ СЕССИЯ: \(activeMinutes) активных минут из \(maxHistoryMinutes)!")

            // Запоминаем время предложения
            lastRestSuggestionTime = Date()

            // Вызываем callback
            onInformalRestSuggestion?()
        }
    }
    
    func resetHistory() {
        minuteActivityHistory.removeAll()
        lastRestSuggestionTime = nil
        informalSuggestionTriggered = false
    }
    
    func getActiveMinutesCount() -> Int {
        return minuteActivityHistory.filter { $0 }.count
    }
    
    func getTotalMinutesCount() -> Int {
        return minuteActivityHistory.count
    }
}

// MARK: - Tests

func runActivityStateManagerTests() {
    let runner = TestRunner()
    
    // Тест 1: Неформальные интервалы НЕ срабатывают во время отдыха
    runner.test("🚫 ActivityStateManager: Блокировка во время отдыха (.onBreak)") {
        let manager = TestActivityStateManager()
        let timer = MockPomodoroTimer()
        manager.pomodoroTimer = timer
        
        // Накапливаем достаточно активности для срабатывания
        timer.setState(.idle)
        for _ in 1...52 {
            manager.recordMinuteActivity(isActive: true)
        }
        
        // Проверяем что в idle состоянии сработало
        try runner.assert(manager.informalSuggestionTriggered,
                         "В состоянии idle должно срабатывать")
        
        // Сбрасываем и переводим в состояние отдыха
        manager.resetHistory()
        for _ in 1...52 {
            manager.recordMinuteActivity(isActive: true)
        }
        timer.setState(.onBreak)
        
        // Добавляем еще одну активную минуту чтобы вызвать проверку
        manager.recordMinuteActivity(isActive: true)
        
        // Проверяем что во время отдыха НЕ сработало
        try runner.assert(!manager.informalSuggestionTriggered,
                         "Во время отдыха (.onBreak) НЕ должно срабатывать")
    }
    
    // Тест 2: Блокировка во время работы (.working)
    runner.test("🚫 ActivityStateManager: Блокировка во время работы (.working)") {
        let manager = TestActivityStateManager()
        let timer = MockPomodoroTimer()
        manager.pomodoroTimer = timer
        
        // Накапливаем активность и переводим в состояние работы
        timer.setState(.working)
        for _ in 1...52 {
            manager.recordMinuteActivity(isActive: true)
        }
        
        // Проверяем что во время работы НЕ сработало
        try runner.assert(!manager.informalSuggestionTriggered,
                         "Во время работы (.working) НЕ должно срабатывать")
    }
    
    // Тест 3: Блокировка во время переработки (.overtime)
    runner.test("🚫 ActivityStateManager: Блокировка во время переработки (.overtime)") {
        let manager = TestActivityStateManager()
        let timer = MockPomodoroTimer()
        manager.pomodoroTimer = timer
        
        // Накапливаем активность и переводим в состояние переработки
        timer.setState(.overtime)
        for _ in 1...52 {
            manager.recordMinuteActivity(isActive: true)
        }
        
        // Проверяем что во время переработки НЕ сработало
        try runner.assert(!manager.informalSuggestionTriggered,
                         "Во время переработки (.overtime) НЕ должно срабатывать")
    }
    
    // Тест 4: Возобновление работы после отдыха
    runner.test("✅ ActivityStateManager: Возобновление после отдыха") {
        let manager = TestActivityStateManager()
        let timer = MockPomodoroTimer()
        manager.pomodoroTimer = timer
        
        // Накапливаем активность в idle
        timer.setState(.idle)
        for _ in 1...52 {
            manager.recordMinuteActivity(isActive: true)
        }
        
        // Проверяем что сработало
        try runner.assert(manager.informalSuggestionTriggered,
                         "В idle должно срабатывать")
        
        // Переводим в отдых и сбрасываем флаг
        timer.setState(.onBreak)
        manager.informalSuggestionTriggered = false
        manager.recordMinuteActivity(isActive: true)
        
        try runner.assert(!manager.informalSuggestionTriggered,
                         "Во время отдыха НЕ должно срабатывать")
        
        // Возвращаемся в idle
        timer.setState(.idle)
        manager.recordMinuteActivity(isActive: true)
        
        // Должно снова сработать (но не сработает из-за cooldown - это нормально)
        // Проверяем что блокировка снята
        print("✅ Блокировка снята после возврата в idle")
    }
    
    let success = runner.printSummary()
    exit(success ? 0 : 1)
}

// MARK: - Main

print("🚀 Запуск тестов ActivityStateManager для блокировки неформальных интервалов...")
print("📅 \(Date())")
print("⚠️ ВАЖНО: Тестируем РЕАЛЬНУЮ систему ActivityStateManager, НЕ устаревший InformalSessionDetector")
print("")

runActivityStateManagerTests()
