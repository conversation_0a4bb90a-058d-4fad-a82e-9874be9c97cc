import Foundation

@main
struct RealBreakQualityTest {
    static func main() {
        print("🧪 Запуск теста интеграции BreakQualityTracker с 4-бандовой системой")
        print("================================================================")
        
        var passedTests = 0
        let totalTests = 6
        
        // Тест 1: Создание BreakQualityTracker без MinuteActivityTracker (старая система)
        print("\n🧪 Тест 1: Создание со старой системой")
        let oldTracker = BreakQualityTracker()
        let oldDebugInfo = oldTracker.getDebugInfo()
        if oldDebugInfo.contains("старая") && oldDebugInfo.contains("нет") {
            print("✅ Старая система инициализирована корректно")
            passedTests += 1
        } else {
            print("❌ Ошибка инициализации старой системы: \(oldDebugInfo)")
        }
        
        // Тест 2: Создание BreakQualityTracker с MinuteActivityTracker (новая система)
        print("\n🧪 Тест 2: Создание с 4-бандовой системой")
        let minuteTracker = MinuteActivityTracker()
        let newTracker = BreakQualityTracker(minuteActivityTracker: minuteTracker)
        let newDebugInfo = newTracker.getDebugInfo()
        if newDebugInfo.contains("4-бандовая") && newDebugInfo.contains("есть") {
            print("✅ 4-бандовая система инициализирована корректно")
            passedTests += 1
        } else {
            print("❌ Ошибка инициализации 4-бандовой системы: \(newDebugInfo)")
        }
        
        // Тест 3: Переключение систем
        print("\n🧪 Тест 3: Переключение между системами")
        let switchableTracker = BreakQualityTracker(minuteActivityTracker: minuteTracker)
        
        // Переключаем на старую систему
        switchableTracker.setUseNewActivitySystem(false)
        let switchedToOld = switchableTracker.getDebugInfo()
        
        // Переключаем обратно на новую систему
        switchableTracker.setUseNewActivitySystem(true)
        let switchedToNew = switchableTracker.getDebugInfo()
        
        if switchedToOld.contains("старая") && switchedToNew.contains("4-бандовая") {
            print("✅ Переключение между системами работает")
            passedTests += 1
        } else {
            print("❌ Ошибка переключения систем")
            print("   Старая: \(switchedToOld)")
            print("   Новая: \(switchedToNew)")
        }
        
        // Тест 4: Получение статистики
        print("\n🧪 Тест 4: Получение статистики отдыха")
        let statsTracker = BreakQualityTracker(minuteActivityTracker: minuteTracker)
        let initialStats = statsTracker.getCurrentStats()
        
        if initialStats.totalMinutes == 0 && initialStats.activeMinutes == 0 && initialStats.qualityPercentage == 100 {
            print("✅ Начальная статистика корректна: \(initialStats)")
            passedTests += 1
        } else {
            print("❌ Ошибка начальной статистики: \(initialStats)")
        }
        
        // Тест 5: Запуск и остановка отслеживания
        print("\n🧪 Тест 5: Запуск и остановка отслеживания")
        let trackingTracker = BreakQualityTracker(minuteActivityTracker: minuteTracker)
        
        // Проверяем начальное состояние
        let beforeStart = trackingTracker.getDebugInfo()
        if !beforeStart.contains("неактивно") {
            print("❌ Неправильное начальное состояние: \(beforeStart)")
        } else {
            // Запускаем отслеживание
            trackingTracker.startTracking()
            let afterStart = trackingTracker.getDebugInfo()
            
            // Останавливаем отслеживание
            let quality = trackingTracker.stopTracking()
            let afterStop = trackingTracker.getDebugInfo()
            
            if afterStart.contains("активно") && afterStop.contains("неактивно") && quality >= 0 && quality <= 100 {
                print("✅ Запуск и остановка отслеживания работают, качество: \(quality)%")
                passedTests += 1
            } else {
                print("❌ Ошибка запуска/остановки отслеживания")
                print("   После запуска: \(afterStart)")
                print("   После остановки: \(afterStop)")
                print("   Качество: \(quality)")
            }
        }
        
        // Тест 6: Принудительная остановка
        print("\n🧪 Тест 6: Принудительная остановка")
        let forceStopTracker = BreakQualityTracker(minuteActivityTracker: minuteTracker)
        forceStopTracker.startTracking()
        forceStopTracker.forceStop()
        let afterForceStop = forceStopTracker.getDebugInfo()
        
        if afterForceStop.contains("неактивно") {
            print("✅ Принудительная остановка работает")
            passedTests += 1
        } else {
            print("❌ Ошибка принудительной остановки: \(afterForceStop)")
        }
        
        // Итоги
        print("\n==================================================")
        print("🧪 Результаты теста интеграции BreakQualityTracker:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Интеграция BreakQualityTracker работает")
            print("")
            print("💡 Для проверки что тест ловит ошибки:")
            print("1. Сломайте метод setUseNewActivitySystem() в BreakQualityTracker")
            print("2. Запустите тест - он должен упасть")
            print("3. Восстановите код и убедитесь что тест снова проходит")
            print("")
            print("🔧 Этот тест проверяет интеграцию с MinuteActivityTracker")
            print("🔧 Полная функциональность требует запуска отслеживания")
            print("🔧 Но основная логика интеграции работает корректно!")
        } else {
            print("❌ Есть проблемы с интеграцией BreakQualityTracker")
        }
    }
}
