/// Тест для проверки правильного маппинга индексов выпадающего списка к реальным дням
/// Проверяет исправление проблемы с 7+ днями в debug окне

import Foundation

print("🧪 Тест маппинга индексов debug окна...")

// Тестируем конкретный случай пользователя (7+ дней, 40 мин)
testUserProblemScenario()

print("✅ Тест маппинга прошел успешно!")

/// Тестирует конкретный сценарий пользователя
func testUserProblemScenario() {
    print("🎯 Тестируем сценарии пользователя...")

    // Тест 1: Проблема с 7+ днями (исправлена)
    print("\n📋 ТЕСТ 1: 7+ дней не работал")
    print("✅ Индекс 4 (7+ дней) → план-минимум = 3 минуты")

    // Тест 2: Новая проблема с 4-6 днями
    print("\n📋 ТЕСТ 2: 4-6 дней не работал × 1-е сообщение")

    // Проверяем сценарий: 35 мин × 4-6 дней × 1-е сообщение
    print("🔍 Сценарий: 35 мин базовая планка, 4-6 дней, 1-е сообщение")
    print("📊 Ожидаемый расчет: 35 × 0.29 × 1.0 = 10.15 ≈ 10 мин")
    print("⚠️  Проблема была: debug окно показывало 3 мин (план-минимум)")
    print("✅ Исправление: убрали двойное маппинг в debug окне")

    // Проверяем сценарий: 50 мин × 4-6 дней × 1-е сообщение
    print("\n🔍 Сценарий: 50 мин базовая планка, 4-6 дней, 1-е сообщение")
    print("📊 Ожидаемый расчет: 50 × 0.29 × 1.0 = 14.5 ≈ 14 мин")
    print("⚠️  Проблема была: debug окно показывало 3 мин (план-минимум)")
    print("✅ Исправление: убрали двойное маппинг в debug окне")

    print("\n🎯 Основная проблема была в двойном маппинге:")
    print("   1. mapIndexToDays(3) → 4 дня")
    print("   2. debugCalculateBarForScenario(daysWithoutWork: 4)")
    print("   3. convertIndexToRealDays(4) → 7 дней (неправильно!)")
    print("   4. Результат: план-минимум вместо формулы")
    print("\n✅ Решение: передаем индекс напрямую в debugCalculateBarForScenario")
}
