import Foundation

// Локальные копии для теста (избегаем сложных зависимостей)
enum ActivityState {
    case working, awayShort, awayMedium, awayLong, awayVeryLong, formalRest
}

enum ReturnMessage {
    case resumeSilently, partialRest, chooseRestOrWork, fullRest
}

// Упрощенная версия ReturnMessageGenerator для тестирования
class TestReturnMessageGenerator {
    
    struct ReturnMessageData {
        let title: String
        let message: String
        let primaryButton: String
        let secondaryButton: String?
        let messageType: MessageType
    }
    
    enum MessageType {
        case silent, gentle, choice, positive
    }
    
    static func generateMessage(for returnMessage: ReturnMessage, awayTimeMinutes: Int) -> ReturnMessageData? {
        
        switch returnMessage {
        case .resumeSilently:
            return nil
            
        case .partialRest:
            return ReturnMessageData(
                title: "Частичный отдых",
                message: "Вы отдыхали \(awayTimeMinutes) \(minuteWord(awayTimeMinutes)). Хотите продолжить отдых или вернуться к работе?",
                primaryButton: "Вернуться к работе",
                secondaryButton: "Продолжить отдых",
                messageType: .choice
            )
            
        case .chooseRestOrWork:
            return ReturnMessageData(
                title: "Хорошее время для отдыха",
                message: "Вы отдыхали \(awayTimeMinutes) \(minuteWord(awayTimeMinutes)). Это отличное время для восстановления! Что выберете?",
                primaryButton: "Начать работу",
                secondaryButton: "Продолжить отдых",
                messageType: .choice
            )
            
        case .fullRest:
            return ReturnMessageData(
                title: "Полноценный отдых",
                message: "Отлично! Вы отдыхали \(awayTimeMinutes) \(minuteWord(awayTimeMinutes)). Теперь вы готовы к продуктивной работе!",
                primaryButton: "Начать работу",
                secondaryButton: nil,
                messageType: .positive
            )
        }
    }
    
    static func generateStatusBarMessage(for returnMessage: ReturnMessage, awayTimeMinutes: Int) -> String {
        switch returnMessage {
        case .resumeSilently:
            return "🟢 Работа продолжена"
        case .partialRest:
            return "🟡 Частичный отдых (\(awayTimeMinutes) мин)"
        case .chooseRestOrWork:
            return "🟠 Время выбора (\(awayTimeMinutes) мин)"
        case .fullRest:
            return "🟢 Полный отдых (\(awayTimeMinutes) мин)"
        }
    }
    
    private static func minuteWord(_ minutes: Int) -> String {
        let lastDigit = minutes % 10
        let lastTwoDigits = minutes % 100
        
        if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
            return "минут"
        }
        
        switch lastDigit {
        case 1:
            return "минуту"
        case 2, 3, 4:
            return "минуты"
        default:
            return "минут"
        }
    }
}

// ТЕСТ: Проверка генерации сообщений при возвращении пользователя
@main
struct RealReturnMessageTest {
    static func main() {
        print("🧪 Тест генерации сообщений при возвращении пользователя...")
        
        var passedTests = 0
        let totalTests = 6
        
        // Тест 1: resumeSilently (0-2 мин) - никаких сообщений
        print("\n1️⃣ Тест молчаливого продолжения (0-2 мин)...")
        let silentMessage = TestReturnMessageGenerator.generateMessage(for: .resumeSilently, awayTimeMinutes: 1)
        if silentMessage == nil {
            print("✅ Молчаливое продолжение: сообщение не генерируется")
            passedTests += 1
        } else {
            print("❌ Ошибка: для молчаливого продолжения не должно быть сообщения")
        }
        
        // Тест 2: partialRest (2-10 мин) - сообщение с выбором
        print("\n2️⃣ Тест частичного отдыха (2-10 мин)...")
        let partialMessage = TestReturnMessageGenerator.generateMessage(for: .partialRest, awayTimeMinutes: 5)
        if let msg = partialMessage {
            let hasCorrectTitle = msg.title == "Частичный отдых"
            let hasCorrectButtons = msg.primaryButton == "Вернуться к работе" && msg.secondaryButton == "Продолжить отдых"
            let hasCorrectType = msg.messageType == .choice
            let hasCorrectTime = msg.message.contains("5 минут")
            
            if hasCorrectTitle && hasCorrectButtons && hasCorrectType && hasCorrectTime {
                print("✅ Частичный отдых: корректное сообщение с двумя кнопками")
                passedTests += 1
            } else {
                print("❌ Ошибка в сообщении частичного отдыха: \(msg)")
            }
        } else {
            print("❌ Ошибка: сообщение частичного отдыха не сгенерировано")
        }
        
        // Тест 3: chooseRestOrWork (10-17 мин) - выбор между отдыхом и работой
        print("\n3️⃣ Тест выбора отдых/работа (10-17 мин)...")
        let choiceMessage = TestReturnMessageGenerator.generateMessage(for: .chooseRestOrWork, awayTimeMinutes: 12)
        if let msg = choiceMessage {
            let hasCorrectTitle = msg.title == "Хорошее время для отдыха"
            let hasCorrectButtons = msg.primaryButton == "Начать работу" && msg.secondaryButton == "Продолжить отдых"
            let hasCorrectType = msg.messageType == .choice
            let hasCorrectTime = msg.message.contains("12 минут")
            
            if hasCorrectTitle && hasCorrectButtons && hasCorrectType && hasCorrectTime {
                print("✅ Выбор отдых/работа: корректное сообщение с выбором")
                passedTests += 1
            } else {
                print("❌ Ошибка в сообщении выбора: \(msg)")
            }
        } else {
            print("❌ Ошибка: сообщение выбора не сгенерировано")
        }
        
        // Тест 4: fullRest (17+ мин) - полноценный отдых
        print("\n4️⃣ Тест полноценного отдыха (17+ мин)...")
        let fullMessage = TestReturnMessageGenerator.generateMessage(for: .fullRest, awayTimeMinutes: 25)
        if let msg = fullMessage {
            let hasCorrectTitle = msg.title == "Полноценный отдых"
            let hasOneButton = msg.primaryButton == "Начать работу" && msg.secondaryButton == nil
            let hasCorrectType = msg.messageType == .positive
            let hasCorrectTime = msg.message.contains("25 минут")
            
            if hasCorrectTitle && hasOneButton && hasCorrectType && hasCorrectTime {
                print("✅ Полноценный отдых: корректное позитивное сообщение")
                passedTests += 1
            } else {
                print("❌ Ошибка в сообщении полноценного отдыха: \(msg)")
            }
        } else {
            print("❌ Ошибка: сообщение полноценного отдыха не сгенерировано")
        }
        
        // Тест 5: Правильные формы слова "минута"
        print("\n5️⃣ Тест правильных форм слова 'минута'...")
        let msg1 = TestReturnMessageGenerator.generateMessage(for: .partialRest, awayTimeMinutes: 1)?.message ?? ""
        let msg2 = TestReturnMessageGenerator.generateMessage(for: .partialRest, awayTimeMinutes: 2)?.message ?? ""
        let msg5 = TestReturnMessageGenerator.generateMessage(for: .partialRest, awayTimeMinutes: 5)?.message ?? ""
        let msg11 = TestReturnMessageGenerator.generateMessage(for: .partialRest, awayTimeMinutes: 11)?.message ?? ""
        
        let correct1 = msg1.contains("1 минуту")
        let correct2 = msg2.contains("2 минуты")
        let correct5 = msg5.contains("5 минут")
        let correct11 = msg11.contains("11 минут")
        
        if correct1 && correct2 && correct5 && correct11 {
            print("✅ Формы слова 'минута' корректны: 1 минуту, 2 минуты, 5 минут, 11 минут")
            passedTests += 1
        } else {
            print("❌ Ошибки в формах слова: 1-'\(msg1.contains("минуту"))', 2-'\(msg2.contains("минуты"))', 5-'\(msg5.contains("минут"))', 11-'\(msg11.contains("минут"))'")
        }
        
        // Тест 6: Сообщения для статус-бара
        print("\n6️⃣ Тест сообщений для статус-бара...")
        let statusSilent = TestReturnMessageGenerator.generateStatusBarMessage(for: .resumeSilently, awayTimeMinutes: 1)
        let statusPartial = TestReturnMessageGenerator.generateStatusBarMessage(for: .partialRest, awayTimeMinutes: 5)
        let statusChoice = TestReturnMessageGenerator.generateStatusBarMessage(for: .chooseRestOrWork, awayTimeMinutes: 12)
        let statusFull = TestReturnMessageGenerator.generateStatusBarMessage(for: .fullRest, awayTimeMinutes: 25)
        
        let correctStatuses = statusSilent.contains("🟢") && statusSilent.contains("продолжена") &&
                             statusPartial.contains("🟡") && statusPartial.contains("5 мин") &&
                             statusChoice.contains("🟠") && statusChoice.contains("12 мин") &&
                             statusFull.contains("🟢") && statusFull.contains("25 мин")
        
        if correctStatuses {
            print("✅ Сообщения для статус-бара корректны")
            passedTests += 1
        } else {
            print("❌ Ошибки в сообщениях статус-бара")
        }
        
        // Итоги
        print("\n🧪 Результаты теста генерации сообщений:")
        print("✅ Прошло: \(passedTests)/\(totalTests)")
        
        if passedTests == totalTests {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Генерация сообщений работает корректно")
        } else {
            print("❌ Некоторые тесты провалились")
        }
    }
}
