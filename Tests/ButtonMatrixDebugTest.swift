import Foundation

/// Отладочный тест для выявления проблемы с 7+ днями

func runDebugTest() {
    print("🔍 ОТЛАДОЧНЫЙ ТЕСТ: Проблема с 7+ днями")
    print(String(repeating: "=", count: 60))
    
    // Тестируем конкретный случай: 7+ дней, 1-е сообщение, 40 мин
    let daysWithoutWork = 7
    let messageIndex = 0
    let baseBarMinutes = 40
    
    print("\n📋 ВХОДНЫЕ ДАННЫЕ:")
    print("   daysWithoutWork: \(daysWithoutWork)")
    print("   messageIndex: \(messageIndex)")
    print("   baseBarMinutes: \(baseBarMinutes)")
    
    // Проверяем маппинг
    let vertical = max(0, daysWithoutWork)
    let horizontal = min(3, max(0, messageIndex))
    let baseBar = TimeInterval(baseBarMinutes * 60)
    
    print("\n📋 МАППИНГ:")
    print("   vertical: \(vertical)")
    print("   horizontal: \(horizontal)")
    print("   baseBar: \(Int(baseBar/60)) мин")
    
    // Проверяем вертикальную адаптацию
    let verticalBar: TimeInterval
    print("\n🔍 ВЕРТИКАЛЬНАЯ АДАПТАЦИЯ:")
    print("   Проверяем case для vertical=\(vertical)")
    
    switch vertical {
    case 0:
        print("   → Попал в case 0 (GradualGrowthSystem)")
        verticalBar = simulateGradualGrowth(currentBar: baseBar)
    case 1:
        print("   → Попал в case 1 (× 0.77)")
        verticalBar = baseBar * 0.77
    case 2...3:
        print("   → Попал в case 2...3 (× 0.48)")
        verticalBar = baseBar * 0.48
    case 4...6:
        print("   → Попал в case 4...6 (× 0.29)")
        verticalBar = baseBar * 0.29
    default:
        print("   → Попал в case default (план-минимум 3 мин)")
        verticalBar = 3 * 60
    }
    
    print("   Результат вертикальной адаптации: \(Int(verticalBar/60)) мин")
    
    // Проверяем горизонтальную адаптацию
    let finalBar: TimeInterval
    print("\n🔍 ГОРИЗОНТАЛЬНАЯ АДАПТАЦИЯ:")
    print("   Проверяем case для horizontal=\(horizontal)")
    
    switch horizontal {
    case 0:
        print("   → Попал в case 0 (100%, но не меньше 3 мин)")
        finalBar = max(verticalBar, 3 * 60)
    case 1:
        print("   → Попал в case 1 (50%, но не меньше 3 мин)")
        finalBar = max(verticalBar * 0.5, 3 * 60)
    case 2:
        print("   → Попал в case 2 (25%, но не меньше 3 мин)")
        finalBar = max(verticalBar * 0.25, 3 * 60)
    case 3:
        print("   → Попал в case 3 (план-минимум 3 мин)")
        finalBar = 3 * 60
    default:
        print("   → Попал в case default (100%, но не меньше 3 мин)")
        finalBar = max(verticalBar, 3 * 60)
    }
    
    print("   Результат горизонтальной адаптации: \(Int(finalBar/60)) мин")
    
    // Итоговый результат
    let finalMinutes = Int(finalBar / 60)
    print("\n📊 ИТОГОВЫЙ РЕЗУЛЬТАТ:")
    print("   ButtonMatrix: \(finalMinutes) мин")
    print("   Ожидается: 3 мин (план-минимум)")
    
    if finalMinutes == 3 {
        print("   ✅ КОРРЕКТНО: Результат соответствует ожиданиям")
    } else {
        print("   ❌ ОШИБКА: Результат НЕ соответствует ожиданиям!")
        print("   🔍 ПРОБЛЕМА: Где-то в логике есть ошибка")
    }
    
    // Дополнительная проверка: тестируем граничные случаи
    print("\n" + String(repeating: "-", count: 60))
    print("🔍 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Граничные случаи")
    
    for days in [4, 5, 6, 7, 8, 10] {
        let v = max(0, days)
        let result: String
        
        switch v {
        case 4...6:
            result = "case 4...6 (× 0.29)"
        default:
            result = "case default (3 мин)"
        }
        
        print("   \(days) дней → vertical=\(v) → \(result)")
    }
}

/// Симулирует GradualGrowthSystem
func simulateGradualGrowth(currentBar: TimeInterval) -> TimeInterval {
    let currentMinutes = Int(currentBar / 60)
    let multiplier: Double
    
    switch currentMinutes {
    case 3...7: multiplier = 1.60
    case 8...15: multiplier = 1.40
    case 16...25: multiplier = 1.25
    case 26...40: multiplier = 1.15
    case 41...52: multiplier = 1.10
    default: multiplier = 1.05
    }
    
    let newMinutes = Int(round(Double(currentMinutes) * multiplier))
    return TimeInterval(min(newMinutes, 52) * 60)
}

// Запускаем отладочный тест
runDebugTest()
