import Foundation

/// Тест логики WeekendManager на соответствие примерам пользователя
/// Проверяет корректность определения выходных дней для стратегии 3/1
struct WeekendLogicTest {
    static func runTests() {
        print("🏖️ Запуск тестов логики выходных...")
        
        // Сохраняем исходное состояние
        let originalIntervals = getAllIntervals()
        let originalStrategy = UserDefaults.standard.string(forKey: "weekendStrategy")
        let originalLastWeekend = UserDefaults.standard.object(forKey: "lastWeekendDate") as? Date
        
        do {
            // Устанавливаем стратегию 3/1
            UserDefaults.standard.set("3/1 (рекомендуемая)", forKey: "weekendStrategy")
            
            try testScenario1()
            try testScenario2() 
            try testScenario3()
            try testScenario4()
            
            print("✅ Все тесты логики выходных прошли успешно!")
        } catch {
            print("❌ Тест провален: \(error)")
            exit(1)
        }
        
        // Восстанавливаем исходное состояние
        restoreState(originalIntervals, originalStrategy, originalLastWeekend)
    }
    
    // MARK: - Тесты сценариев
    
    static func testScenario1() throws {
        print("🔍 Сценарий 1: Работал только сегодня")
        
        clearAllData()
        
        // Добавляем сессию только за сегодня
        addInterval(daysAgo: 0, duration: 25)
        
        let workingDays = calculateWorkingDaysAfterLastWeekend()
        let shouldShow = shouldShowWeekendChoice()
        let isWeekend = isWeekendDay()
        
        print("   Рабочих дней: \(workingDays)")
        print("   Показать выходной: \(shouldShow)")
        print("   Сегодня выходной: \(isWeekend)")
        
        // Ожидаем: 1 рабочий день, выходной не предлагается (нужно 3 дня)
        guard workingDays == 1 else {
            throw TestError("Сценарий 1: ожидался 1 рабочий день, получено: \(workingDays)")
        }
        
        guard !shouldShow else {
            throw TestError("Сценарий 1: выходной не должен предлагаться после 1 дня работы")
        }
        
        print("✅ Сценарий 1 корректен")
    }
    
    static func testScenario2() throws {
        print("🔍 Сценарий 2: Работал вчера + сегодня")
        
        clearAllData()
        
        // Добавляем сессии за вчера и сегодня
        addInterval(daysAgo: 1, duration: 25) // вчера
        addInterval(daysAgo: 0, duration: 25) // сегодня
        
        let workingDays = calculateWorkingDaysAfterLastWeekend()
        let shouldShow = shouldShowWeekendChoice()
        let isWeekend = isWeekendDay()
        
        print("   Рабочих дней: \(workingDays)")
        print("   Показать выходной: \(shouldShow)")
        print("   Сегодня выходной: \(isWeekend)")
        
        // Ожидаем: 2 рабочих дня, выходной не предлагается (нужно 3 дня)
        guard workingDays == 2 else {
            throw TestError("Сценарий 2: ожидалось 2 рабочих дня, получено: \(workingDays)")
        }
        
        guard !shouldShow else {
            throw TestError("Сценарий 2: выходной не должен предлагаться после 2 дней работы")
        }
        
        print("✅ Сценарий 2 корректен")
    }
    
    static func testScenario3() throws {
        print("🔍 Сценарий 3: Работал ЧТ+ПТ+СБ, сегодня НЕ работал")
        
        clearAllData()
        
        // Добавляем сессии за ЧТ, ПТ, СБ (3, 2, 1 день назад)
        addInterval(daysAgo: 3, duration: 25) // четверг
        addInterval(daysAgo: 2, duration: 25) // пятница
        addInterval(daysAgo: 1, duration: 25) // суббота
        // Сегодня (воскресенье) НЕ работал
        
        let workingDays = calculateWorkingDaysAfterLastWeekend()
        let shouldShow = shouldShowWeekendChoice()
        let isWeekend = isWeekendDay()
        
        print("   Рабочих дней: \(workingDays)")
        print("   Показать выходной: \(shouldShow)")
        print("   Сегодня выходной: \(isWeekend)")
        
        // Ожидаем: 3 рабочих дня, выходной предлагается СЕГОДНЯ
        guard workingDays == 3 else {
            throw TestError("Сценарий 3: ожидалось 3 рабочих дня, получено: \(workingDays)")
        }
        
        guard shouldShow else {
            throw TestError("Сценарий 3: выходной должен предлагаться после 3 дней работы")
        }
        
        guard isWeekend else {
            throw TestError("Сценарий 3: сегодня должен быть выходной день")
        }
        
        print("✅ Сценарий 3 корректен")
    }
    
    static func testScenario4() throws {
        print("🔍 Сценарий 4: Работал ЧТ+ПТ+СБ+ВСК (уже поработал сегодня)")
        
        clearAllData()
        
        // Добавляем сессии за ЧТ, ПТ, СБ, ВСК
        addInterval(daysAgo: 3, duration: 25) // четверг
        addInterval(daysAgo: 2, duration: 25) // пятница
        addInterval(daysAgo: 1, duration: 25) // суббота
        addInterval(daysAgo: 0, duration: 25) // воскресенье (сегодня)
        
        let workingDays = calculateWorkingDaysAfterLastWeekend()
        let shouldShow = shouldShowWeekendChoice()
        let isWeekend = isWeekendDay()
        
        print("   Рабочих дней: \(workingDays)")
        print("   Показать выходной: \(shouldShow)")
        print("   Сегодня выходной: \(isWeekend)")
        
        // Ожидаем: 4 рабочих дня, выходной предлагается ЗАВТРА
        guard workingDays == 4 else {
            throw TestError("Сценарий 4: ожидалось 4 рабочих дня, получено: \(workingDays)")
        }
        
        guard shouldShow else {
            throw TestError("Сценарий 4: выходной должен предлагаться после 4 дней работы")
        }
        
        // В этом сценарии сегодня НЕ выходной (уже поработал), выходной завтра
        guard !isWeekend else {
            throw TestError("Сценарий 4: сегодня НЕ должен быть выходной (уже поработал)")
        }
        
        print("✅ Сценарий 4 корректен")
    }
    
    // MARK: - Вспомогательные методы
    
    static func addInterval(daysAgo: Int, duration: Int) {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDate = calendar.date(byAdding: .day, value: -daysAgo, to: today) ?? today

        // Для сегодняшнего дня используем текущее время, для прошлых дней - 14:00
        let sessionDate: Date
        if daysAgo == 0 {
            sessionDate = Date() // текущее время
        } else {
            sessionDate = calendar.date(bySettingHour: 14, minute: 0, second: 0, of: targetDate) ?? targetDate
        }

        let interval = CompletedInterval(
            date: sessionDate,
            duration: TimeInterval(duration * 60),
            projectId: nil,
            intervalType: "test"
        )

        var intervals = getAllIntervals()
        intervals.append(interval)
        saveIntervals(intervals)
    }
    
    static func clearAllData() {
        // Очищаем все интервалы
        saveIntervals([])
        
        // Сбрасываем дату последнего выходного
        UserDefaults.standard.removeObject(forKey: "lastWeekendDate")
    }
    
    // MARK: - Интеграция с WeekendManager
    
    static func calculateWorkingDaysAfterLastWeekend() -> Int {
        let intervals = getAllIntervals()
        let lastWeekend = UserDefaults.standard.object(forKey: "lastWeekendDate") as? Date

        let calendar = Calendar.current
        let startDate: Date

        if let lastWeekend = lastWeekend {
            startDate = calendar.date(byAdding: .day, value: 1, to: lastWeekend) ?? Date()
        } else {
            // Если нет истории выходных, считаем все интервалы
            startDate = Date.distantPast
        }

        let endDate = Date()

        // Получаем все дни с интервалами
        var workingDays = Set<String>()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        for interval in intervals {
            if interval.date >= startDate && interval.date <= endDate {
                let dayKey = dateFormatter.string(from: calendar.startOfDay(for: interval.date))
                workingDays.insert(dayKey)
            }
        }

        return workingDays.count
    }
    
    static func shouldShowWeekendChoice() -> Bool {
        let workingDays = calculateWorkingDaysAfterLastWeekend()
        return workingDays >= 3
    }
    
    static func isWeekendDay() -> Bool {
        let workingDays = calculateWorkingDaysAfterLastWeekend()
        
        // Проверяем, работал ли пользователь сегодня
        let calendar = Calendar.current
        let today = Date()
        let todayIntervals = getAllIntervals().filter { interval in
            calendar.isDate(interval.date, inSameDayAs: today)
        }
        
        // Если работал сегодня, то сегодня не выходной
        if !todayIntervals.isEmpty {
            return false
        }
        
        // Если не работал сегодня и накопилось 3+ рабочих дня, то сегодня выходной
        return workingDays >= 3
    }
    
    // MARK: - Работа с данными
    
    static func getAllIntervals() -> [CompletedInterval] {
        let data = UserDefaults.standard.data(forKey: "completedIntervals") ?? Data()
        return (try? JSONDecoder().decode([CompletedInterval].self, from: data)) ?? []
    }
    
    static func saveIntervals(_ intervals: [CompletedInterval]) {
        let data = (try? JSONEncoder().encode(intervals)) ?? Data()
        UserDefaults.standard.set(data, forKey: "completedIntervals")
    }
    
    static func restoreState(_ intervals: [CompletedInterval], _ strategy: String?, _ lastWeekend: Date?) {
        saveIntervals(intervals)
        
        if let strategy = strategy {
            UserDefaults.standard.set(strategy, forKey: "weekendStrategy")
        } else {
            UserDefaults.standard.removeObject(forKey: "weekendStrategy")
        }
        
        if let lastWeekend = lastWeekend {
            UserDefaults.standard.set(lastWeekend, forKey: "lastWeekendDate")
        } else {
            UserDefaults.standard.removeObject(forKey: "lastWeekendDate")
        }
    }
}

// MARK: - Структуры данных

struct CompletedInterval: Codable {
    let date: Date
    let duration: TimeInterval
    let projectId: UUID?
    let intervalType: String
}

struct TestError: Error {
    let message: String
    
    init(_ message: String) {
        self.message = message
    }
}

extension TestError: LocalizedError {
    var errorDescription: String? {
        return message
    }
}

// Запуск тестов
WeekendLogicTest.runTests()
