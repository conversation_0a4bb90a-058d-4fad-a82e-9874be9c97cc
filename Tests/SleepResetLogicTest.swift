#!/usr/bin/env swift

import Foundation

// Простой тестовый фреймворк
class TestRunner {
    private var testCount = 0
    private var passedCount = 0
    
    func test(_ name: String, _ testBlock: () throws -> Void) {
        testCount += 1
        print("\n🧪 Тест \(testCount): \(name)")
        
        do {
            try testBlock()
            passedCount += 1
            print("✅ ПРОШЕЛ")
        } catch {
            print("❌ ПРОВАЛЕН: \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String) throws {
        if actual != expected {
            throw TestError.assertionFailed("\(message). Ожидалось: \(expected), получено: \(actual)")
        }
    }
    
    func printSummary() -> Bo<PERSON> {
        print("\n" + String(repeating: "=", count: 60))
        print("🧪 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ ЛОГИКИ СБРОСА")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(testCount)")
        print("✅ Прошло: \(passedCount)")
        print("❌ Провалено: \(testCount - passedCount)")
        print("📈 Успешность: \(passedCount * 100 / max(testCount, 1))%")
        print(String(repeating: "=", count: 60))
        
        if passedCount == testCount {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Логика сброса корректна.")
        } else {
            print("🚨 ЕСТЬ ПРОБЛЕМЫ! Нужно исправить логику.")
        }
        
        return passedCount == testCount
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// Тестируем логику сброса
func runSleepResetLogicTests() {
    let runner = TestRunner()
    
    print("🚀 Запуск тестов логики сброса при сне...")
    print("📅 \(Date())")
    
    // Тест 1: Проверяем пороги времени
    runner.test("🧪 Пороги времени для сброса") {
        let shortSleep = 25 * 60  // 25 минут
        let longSleep = 35 * 60   // 35 минут
        let resetThreshold = 30 * 60  // 30 минут
        
        try runner.assert(shortSleep < resetThreshold, "25 минут < 30 минут - НЕ должно сбрасываться")
        try runner.assert(longSleep >= resetThreshold, "35 минут >= 30 минут - ДОЛЖНО сбрасываться")
        
        print("✅ Пороги времени корректны:")
        print("   Короткий сон (25 мин): запись неактивности")
        print("   Длительный сон (35 мин): полный сброс")
    }
    
    // Тест 2: Проверяем логику интервала
    runner.test("🧪 Логика сброса интервала при неактивности") {
        let pauseThreshold = 10 * 60   // 10 минут - приостановка
        let resetThreshold = 15 * 60   // 15 минут - сброс
        
        let shortInactivity = 8 * 60   // 8 минут
        let mediumInactivity = 12 * 60 // 12 минут
        let longInactivity = 18 * 60   // 18 минут
        
        try runner.assert(shortInactivity < pauseThreshold, "8 мин < 10 мин - интервал продолжается")
        try runner.assert(mediumInactivity >= pauseThreshold && mediumInactivity < resetThreshold, "12 мин - интервал приостанавливается")
        try runner.assert(longInactivity >= resetThreshold, "18 мин >= 15 мин - интервал сбрасывается")
        
        print("✅ Логика интервала корректна:")
        print("   0-10 мин: интервал продолжается")
        print("   10-15 мин: интервал приостанавливается")
        print("   15+ мин: интервал сбрасывается")
    }
    
    // Тест 3: Проверяем комбинированную логику
    runner.test("🧪 Комбинированная логика: сон + интервал") {
        // Сценарий: работа 20 мин → сон 35 мин
        let workTime = 20
        let sleepTime = 35
        
        // Что должно произойти:
        // 1. Через 10 мин сна: интервал приостановлен
        // 2. Через 15 мин сна: интервал сброшен
        // 3. Через 30 мин сна: лог активности сброшен
        
        let intervalPauseTime = 10
        let intervalResetTime = 15
        let logResetTime = 30
        
        try runner.assert(sleepTime > intervalPauseTime, "35 мин > 10 мин - интервал должен приостановиться")
        try runner.assert(sleepTime > intervalResetTime, "35 мин > 15 мин - интервал должен сброситься")
        try runner.assert(sleepTime > logResetTime, "35 мин > 30 мин - лог должен сброситься")
        
        print("✅ Комбинированная логика корректна:")
        print("   После 35 мин сна: интервал сброшен + лог сброшен")
    }
    
    // Тест 4: Проверяем граничные случаи
    runner.test("🧪 Граничные случаи") {
        let exactly30min = 30 * 60
        let exactly15min = 15 * 60
        let exactly10min = 10 * 60
        
        // Проверяем что границы включительные
        try runner.assert(exactly30min >= 30 * 60, "Ровно 30 мин - должен сброситься лог")
        try runner.assert(exactly15min >= 15 * 60, "Ровно 15 мин - должен сброситься интервал")
        try runner.assert(exactly10min >= 10 * 60, "Ровно 10 мин - должен приостановиться интервал")
        
        print("✅ Граничные случаи обработаны корректно")
    }
    
    let success = runner.printSummary()
    
    if success {
        print("\n💡 РЕКОМЕНДАЦИИ ДЛЯ РЕАЛЬНОГО ТЕСТИРОВАНИЯ:")
        print("1. 🧪 Запустите uProd")
        print("2. ⏰ Начните интервал")
        print("3. 💤 Закройте ноутбук на 16+ минут")
        print("4. 🌅 Откройте ноутбук")
        print("5. 🔍 Проверьте:")
        print("   - Интервал должен быть сброшен (не работает)")
        print("   - Лог активности должен быть пустым (0 кружочков)")
        print("   - В логах должно быть: 'ИНТЕРВАЛ СБРОШЕН'")
        print("\n📋 ОЖИДАЕМОЕ ПОВЕДЕНИЕ:")
        print("✅ Короткий сон (до 30 мин): время записывается как неактивность")
        print("✅ Длительный сон (30+ мин): полный сброс всех данных")
        print("✅ Неактивность 10+ мин: приостановка интервала")
        print("✅ Неактивность 15+ мин: сброс интервала")
    }
    
    exit(success ? 0 : 1)
}

// Запускаем тесты
runSleepResetLogicTests()
