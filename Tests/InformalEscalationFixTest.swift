#!/usr/bin/env swift

//
// InformalEscalationFixTest.swift
// Тест для проверки исправления бага с повторным запуском эскалации
//

import Foundation

// Мок для тестирования исправленной системы
class MockEscalationSystemFixed {
    var startCallCount = 0
    var stopCallCount = 0
    var isActive = false
    
    func startEscalation() {
        startCallCount += 1
        isActive = true
        print("🚀 Эскалация запущена (вызов #\(startCallCount))")
    }
    
    func stopEscalation() {
        stopCallCount += 1
        isActive = false
        print("🛑 Эскалация остановлена (вызов #\(stopCallCount))")
    }
}

// Исправленная версия ActivityStateManager с отслеживанием эскалации
class FixedActivityStateManager {
    private var minuteActivityHistory: [Bool] = []
    private let maxHistoryMinutes = 52
    private let minActiveMinutesForSuggestion = 42
    private var lastRestSuggestionTime: Date?
    
    // ИСПРАВЛЕНИЕ: Добавлено отслеживание состояния эскалации
    private var isEscalationActive: Bool = false
    private var currentIntervalId: UUID?
    
    var onInformalRestSuggestion: (() -> Void)?
    
    func recordMinuteActivity(isActive: Bool) {
        minuteActivityHistory.append(isActive)
        
        if minuteActivityHistory.count > maxHistoryMinutes {
            minuteActivityHistory.removeFirst()
        }
        
        print("📝 Записана активность: \(isActive), история: \(minuteActivityHistory.count) мин")
        checkForInformalRestSuggestion()
    }
    
    private func checkForInformalRestSuggestion() {
        guard minuteActivityHistory.count >= maxHistoryMinutes else { return }
        
        // КРИТИЧЕСКИ ВАЖНО: Проверяем активна ли уже эскалация
        if isEscalationActive {
            print("🔍 Эскалация уже активна для интервала \(currentIntervalId?.uuidString.prefix(8) ?? "unknown"), пропускаем проверку")
            return
        }
        
        let activeMinutes = minuteActivityHistory.filter { $0 }.count
        print("🔍 Активных минут: \(activeMinutes)/\(maxHistoryMinutes)")
        
        if activeMinutes >= minActiveMinutesForSuggestion {
            print("🔔 НЕФОРМАЛЬНАЯ СЕССИЯ: \(activeMinutes) активных минут из \(maxHistoryMinutes)!")
            lastRestSuggestionTime = Date()
            
            // Отмечаем что эскалация запущена
            notifyEscalationStarted()
            
            onInformalRestSuggestion?()
        }
    }
    
    // Методы управления эскалацией
    func notifyEscalationStarted() {
        isEscalationActive = true
        currentIntervalId = UUID()
        print("🚀 Эскалация запущена для интервала \(currentIntervalId?.uuidString.prefix(8) ?? "unknown")")
    }
    
    func notifyEscalationEnded() {
        isEscalationActive = false
        let intervalId = currentIntervalId?.uuidString.prefix(8) ?? "unknown"
        currentIntervalId = nil
        print("🛑 Эскалация завершена для интервала \(intervalId)")
    }
    
    func resetEscalationState() {
        if isEscalationActive {
            print("🔄 Сброс состояния эскалации")
        }
        isEscalationActive = false
        currentIntervalId = nil
    }
    
    // Симулирует заполнение истории активностью
    func simulateActiveWork() {
        for i in 0..<maxHistoryMinutes {
            let isActive = i < 42 // Первые 42 минуты активны, остальные 10 - нет
            recordMinuteActivity(isActive: isActive)
        }
    }
    
    // Симулирует продолжение работы (добавляет еще активные минуты)
    func simulateContinuedWork(minutes: Int) {
        for _ in 0..<minutes {
            recordMinuteActivity(isActive: true)
        }
    }
}

// Основная функция теста
func runFixTest() {
    print("🧪 Тест исправления бага с повторным запуском эскалации")
    print(String(repeating: "=", count: 60))
    
    let mockEscalation = MockEscalationSystemFixed()
    let activityManager = FixedActivityStateManager()
    
    // Подключаем callback
    activityManager.onInformalRestSuggestion = {
        mockEscalation.startEscalation()
    }
    
    print("\n1️⃣ Симулируем активную работу (42+ минут из 52)")
    activityManager.simulateActiveWork()
    
    print("\n2️⃣ Проверяем состояние после первого срабатывания:")
    print("   Запусков эскалации: \(mockEscalation.startCallCount)")
    print("   Эскалация активна: \(mockEscalation.isActive)")
    
    print("\n3️⃣ Симулируем продолжение работы (еще 5 минут)")
    print("   ✅ ИСПРАВЛЕНИЕ: Эскалация НЕ должна перезапускаться!")
    
    for minute in 1...5 {
        print("\n   Минута \(minute):")
        activityManager.simulateContinuedWork(minutes: 1)
        print("     Запусков эскалации: \(mockEscalation.startCallCount)")
    }
    
    print("\n4️⃣ Симулируем окончание эскалации (пользователь пошел на отдых)")
    activityManager.notifyEscalationEnded()
    mockEscalation.stopEscalation()
    
    print("\n5️⃣ Симулируем новый интервал работы")
    activityManager.resetEscalationState()
    print("   Добавляем еще 5 активных минут...")
    activityManager.simulateContinuedWork(minutes: 5)
    print("   Запусков эскалации: \(mockEscalation.startCallCount)")
    
    print("\n🚨 РЕЗУЛЬТАТ ТЕСТА:")
    print("   Ожидалось: 1 запуск эскалации (повторные вызовы заблокированы)")
    print("   Фактически: \(mockEscalation.startCallCount) запусков")
    
    if mockEscalation.startCallCount == 1 {
        print("   ✅ ИСПРАВЛЕНИЕ РАБОТАЕТ! Эскалация НЕ перезапускается!")
    } else {
        print("   ❌ Исправление не работает")
    }
    
    print("\n💡 ОБЪЯСНЕНИЕ ИСПРАВЛЕНИЯ:")
    print("   1. Добавлен флаг isEscalationActive в ActivityStateManager")
    print("   2. Проверка этого флага в checkForInformalRestSuggestion()")
    print("   3. Методы notifyEscalationStarted/Ended для управления состоянием")
    print("   4. Интеграция с AppDelegate для уведомлений об окончании эскалации")
}

// Запускаем тест
runFixTest()
