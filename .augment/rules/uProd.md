---
type: "always_apply"
---

# Команда Сборки
./build.sh

# Логи
'/Users/<USER>/Library/Containers/com.local.uProd/Data/Library/Application Support/uProd/Logs'

# Тестирование - КРИТИЧЕСКИ ВАЖНО!

## 🎯 Принципы тестирования uProd
- **ВСЕГДА используй тесты** при любых изменениях кода
- **Пиши новые тесты** для каждого нового компонента
- **Только реальная логика** - никаких упрощенных версий
- **Тесты должны находить реальные проблемы**

## 🧪 Когда писать тесты
- При добавлении новой функциональности
- При исправлении багов (сначала тест, потом исправление)
- При рефакторинге существующего кода
- При изменении критической логики

## 📋 Ключевые правила
1. **Тестируй РЕАЛЬНЫЕ классы** (InformalSessionDetector, PomodoroTimer, etc.)
2. **Изолируй только внешние зависимости** (UI, файлы, сеть)
3. **НЕ создавай TestableXXX** версии основной логики
4. **Каждый тест <1 секунды** выполнения
5. **Четкие сообщения об ошибках**

## 📚 Документация и обновления
- **Детальная документация:** `TESTING.md`
- **Структура тестов:** `Tests/` папка
- **Запуск:** `./test.sh` или автоматически при `./build.sh`
- **Обновляй TESTING.md** при добавлении новых тестов

## 🚀 Быстрый старт
```bash
# Запустить все тесты
./test.sh

# Запустить конкретный модуль
swift Tests/InformalSessionTest.swift

# Создать новый тест (скопируй шаблон из существующего)
cp Tests/InformalSessionTest.swift Tests/NewTest.swift
```

## ⚠️ ПОМНИ
- Тесты - это **страховка от регрессий**
- Если тест проходит, но реальный код сломан - тест бесполезен
- Лучше **меньше тестов, но качественных**
- **Система тестирования уже нашла 2 реальных бага!**

# UI и Позиционирование

## Правила позиционирования окон в отладке
- **ВСЕГДА** получать statusItemFrame из AppDelegate.statusItem.button
- **ВСЕГДА** передавать statusItemFrame в метод showXXX() окна
- **НИКОГДА** не создавать окна напрямую в отладочных окнах без позиционирования
- Использовать паттерн: получить statusFrame → передать в окно → окно само позиционируется
- Добавлять логирование позиционирования для отладки

## Система стилей
- Унифицированная система стилей через UIStyles.swift
- Два типа градиентов (сессия/отдых), три типа кнопок (зеленая/серая/фиолетовая с обводкой)
- Extensions для NSWindow/NSButton, каждое окно - отдельный файл
