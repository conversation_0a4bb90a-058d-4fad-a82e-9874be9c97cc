#!/usr/bin/env swift

import Cocoa
import Foundation

/// ВИЗУАЛЬНАЯ ДЕМОНСТРАЦИЯ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Показывает как будет выглядеть окно утром при пробуждении
class VisualTestApp: NSObject, NSApplicationDelegate {
    
    var window: NSWindow!
    var statusLabel: NSTextField!
    var messageLabel: NSTextField!
    var durationLabel: NSTextField!
    var acceptButton: NSButton!
    var declineButton: NSButton!
    var snoozeButton: NSButton!
    var changeScenarioButton: NSButton!
    
    var currentScenario = 0
    let scenarios = [
        (days: 0, time: 0, description: "Сегодня работал, утром"),
        (days: 1, time: 2, description: "Вчера не работал, вечером"),
        (days: 3, time: 1, description: "3 дня назад работал, днем"),
        (days: 4, time: 3, description: "Неделю назад работал, поздно"),
        (days: 2, time: 0, description: "2 дня назад работал, утром")
    ]
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        setupWindow()
        updateDisplay()
    }
    
    func setupWindow() {
        // Создаем окно
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 500, height: 350),
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "🌅 Визуальный тест системы раннего вовлечения"
        window.center()
        window.makeKeyAndOrderFront(nil)
        
        let contentView = NSView(frame: window.contentView!.bounds)
        contentView.autoresizingMask = [.width, .height]
        window.contentView = contentView
        
        var yPosition: CGFloat = 300
        let margin: CGFloat = 20
        let spacing: CGFloat = 15
        
        // Заголовок сценария
        statusLabel = createLabel(text: "Загрузка...", fontSize: 14, bold: true)
        statusLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: 20)
        contentView.addSubview(statusLabel)
        yPosition -= 30
        
        // Сообщение системы
        messageLabel = createLabel(text: "Загрузка сообщения...", fontSize: 18, bold: true)
        messageLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: 25)
        messageLabel.textColor = NSColor.systemBlue
        contentView.addSubview(messageLabel)
        yPosition -= 35
        
        // Длительность
        durationLabel = createLabel(text: "Длительность: загрузка...", fontSize: 14)
        durationLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: 20)
        contentView.addSubview(durationLabel)
        yPosition -= 40
        
        // Кнопки действий (как в реальном окне)
        let buttonWidth: CGFloat = 120
        let buttonHeight: CGFloat = 35
        let buttonSpacing: CGFloat = 20
        
        acceptButton = createButton(title: "✅ Принять", color: NSColor.systemGreen)
        acceptButton.frame = NSRect(x: margin, y: yPosition, width: buttonWidth, height: buttonHeight)
        acceptButton.target = self
        acceptButton.action = #selector(acceptAction)
        contentView.addSubview(acceptButton)
        
        declineButton = createButton(title: "❌ Отклонить", color: NSColor.systemRed)
        declineButton.frame = NSRect(x: margin + buttonWidth + buttonSpacing, y: yPosition, width: buttonWidth, height: buttonHeight)
        declineButton.target = self
        declineButton.action = #selector(declineAction)
        contentView.addSubview(declineButton)
        
        snoozeButton = createButton(title: "⏰ Отложить", color: NSColor.systemOrange)
        snoozeButton.frame = NSRect(x: margin + 2 * (buttonWidth + buttonSpacing), y: yPosition, width: buttonWidth, height: buttonHeight)
        snoozeButton.target = self
        snoozeButton.action = #selector(snoozeAction)
        contentView.addSubview(snoozeButton)
        yPosition -= 60
        
        // Кнопка смены сценария
        changeScenarioButton = createButton(title: "🔄 Следующий сценарий", color: NSColor.systemBlue)
        changeScenarioButton.frame = NSRect(x: margin, y: yPosition, width: 200, height: 30)
        changeScenarioButton.target = self
        changeScenarioButton.action = #selector(changeScenario)
        contentView.addSubview(changeScenarioButton)
        yPosition -= 50
        
        // Информационный текст
        let infoLabel = createLabel(text: "ℹ️ Это демонстрация того, как будет выглядеть окно системы раннего вовлечения.\nВы можете переключать сценарии и видеть разные сообщения.", fontSize: 12)
        infoLabel.frame = NSRect(x: margin, y: yPosition - 30, width: 460, height: 40)
        infoLabel.maximumNumberOfLines = 3
        contentView.addSubview(infoLabel)
    }
    
    func createLabel(text: String, fontSize: CGFloat, bold: Bool = false) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.isEditable = false
        label.isSelectable = true
        label.backgroundColor = NSColor.clear
        label.textColor = NSColor.labelColor
        label.alignment = .center
        
        if bold {
            label.font = NSFont.boldSystemFont(ofSize: fontSize)
        } else {
            label.font = NSFont.systemFont(ofSize: fontSize)
        }
        
        return label
    }
    
    func createButton(title: String, color: NSColor) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.bezelStyle = .rounded
        button.contentTintColor = color
        return button
    }
    
    @objc func changeScenario() {
        currentScenario = (currentScenario + 1) % scenarios.count
        updateDisplay()
    }
    
    @objc func acceptAction() {
        showResult("✅ ПРИНЯТО", "Пользователь согласился работать. Планка увеличится на 10%.")
    }
    
    @objc func declineAction() {
        showResult("❌ ОТКЛОНЕНО", "Пользователь отклонил предложение. Планка уменьшится на 15%.")
    }
    
    @objc func snoozeAction() {
        showResult("⏰ ОТЛОЖЕНО", "Система покажет сообщение снова через некоторое время.")
    }
    
    func showResult(_ title: String, _ description: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = description
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    func updateDisplay() {
        let scenario = scenarios[currentScenario]
        
        // Обновляем статус
        let timeNames = ["утром", "днем", "вечером", "поздно"]
        statusLabel.stringValue = "📊 Сценарий: \(scenario.description)"
        
        // Получаем сообщение для текущего сценария
        let message = getMessage(vertical: scenario.days, horizontal: scenario.time)
        messageLabel.stringValue = "\"\(message.title)\""
        
        let durationMinutes = Int(message.duration / 60)
        durationLabel.stringValue = "⏰ Предлагаемая длительность: \(durationMinutes) минут"
        
        print("🔄 Сценарий изменен: \(scenario.description)")
        print("💬 Сообщение: \(message.title)")
        print("⏰ Длительность: \(durationMinutes) минут")
    }
    
    func getMessage(vertical: Int, horizontal: Int) -> (title: String, duration: TimeInterval) {
        let titles = [
            // Уровень 0 (сегодня работал)
            ["Продолжим?", "Еще немного?", "Добавим интервал?", "Финальный рывок?"],
            // Уровень 1 (1 день без работы)
            ["Вернемся к работе?", "Начнем день?", "Время работать!", "Не откладываем!"],
            // Уровень 2 (2 дня без работы)
            ["Пора возвращаться!", "Хватит откладывать!", "Начинаем сейчас!", "Время действовать!"],
            // Уровень 3 (3 дня без работы)
            ["Серьезно, пора работать!", "Уже 3 дня!", "Начинаем немедленно!", "Хватит прокрастинировать!"],
            // Уровень 4 (4+ дней без работы)
            ["КРИТИЧНО! Начинаем!", "Уже слишком долго!", "СЕЙЧАС ИЛИ НИКОГДА!", "Экстренный старт!"]
        ]
        
        let durations: [TimeInterval] = [15*60, 20*60, 25*60, 30*60] // 15, 20, 25, 30 минут
        
        let safeVertical = min(max(vertical, 0), 4)
        let safeHorizontal = min(max(horizontal, 0), 3)
        
        return (
            title: titles[safeVertical][safeHorizontal],
            duration: durations[safeHorizontal]
        )
    }
}

// Запуск приложения
let app = NSApplication.shared
let delegate = VisualTestApp()
app.delegate = delegate
app.setActivationPolicy(.regular)
app.run()
