import Cocoa

/// Окно предложения работы после микросессий с обработкой отказов
class MicroSessionOfferWindow: NSWindow {
    
    // MARK: - UI Elements
    private var containerView: NSView!
    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var yesButton: NSButton!
    private var noButton: NSButton!
    private var projectLabel: NSTextField!
    
    // MARK: - Callbacks
    var onAccept: ((UUID?) -> Void)?
    var onDecline: (() -> Void)?
    
    // MARK: - Properties
    private var currentProjectId: UUID?
    
    // MARK: - Initialization
    
    init() {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 380, height: 160),
                   styleMask: [.borderless],
                   backing: .buffered,
                   defer: false)

        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true
        // НЕ центрируем - позиционируем возле status item

        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // Создаем основной контейнер
        containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Настраиваем унифицированный фон (тип "сессия")
        self.setupUnifiedBackground(type: .session, for: containerView)

        self.contentView = containerView

        // Создаем элементы UI
        createUIElements()
        setupConstraints()

        Logger.shared.log(.info, "MicroSessionOffer", "🪟 Окно создано")
    }
    
    private func createUIElements() {
        // Заголовок
        titleLabel = NSTextField.createUnifiedTitle(text: "🚀 Хотите еще поработать?")

        // Подзаголовок
        subtitleLabel = NSTextField.createUnifiedSubtitle(text: "Может быть, еще одну короткую сессию?")

        // Кнопки
        yesButton = NSButton.createUnifiedButton(title: "Да, поработаем", type: UIButtonType.green, isSmall: false)
        yesButton.target = self
        yesButton.action = #selector(acceptButtonClicked)

        noButton = NSButton.createUnifiedButton(title: "Нет, не сейчас", type: UIButtonType.gray, isSmall: false)
        noButton.target = self
        noButton.action = #selector(declineButtonClicked)
        
        // Информация о проекте
        let projectInfo = getCurrentProject()
        projectLabel = NSTextField.createUnifiedSubtitle(text: "\(projectInfo.emoji) \(projectInfo.name)")
        projectLabel.font = NSFont.systemFont(ofSize: 11, weight: .regular)
        projectLabel.textColor = NSColor(white: 0.7, alpha: 1.0)
        
        // Добавляем элементы в контейнер
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(yesButton)
        containerView.addSubview(noButton)
        containerView.addSubview(projectLabel)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            titleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            // Подзаголовок
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            subtitleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            subtitleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            subtitleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            // Кнопки
            yesButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 20),
            yesButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            yesButton.heightAnchor.constraint(equalToConstant: 32),
            yesButton.widthAnchor.constraint(equalToConstant: 140),
            
            noButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 20),
            noButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            noButton.heightAnchor.constraint(equalToConstant: 32),
            noButton.widthAnchor.constraint(equalToConstant: 140),
            
            // Информация о проекте
            projectLabel.topAnchor.constraint(equalTo: yesButton.bottomAnchor, constant: 8),
            projectLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            projectLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15)
        ])
    }
    
    // MARK: - Public Methods
    
    /// Показывает окно с предложением работы
    func showOffer(projectId: UUID? = nil) {
        self.currentProjectId = projectId
        updateProjectInfo()
        self.showWithUnifiedAnimation()

        Logger.shared.log(.info, "MicroSessionOffer", "💬 Показано предложение работы")
    }
    
    /// Обновляет информацию о проекте
    private func updateProjectInfo() {
        let projectInfo = getCurrentProject()
        projectLabel.stringValue = "\(projectInfo.emoji) \(projectInfo.name)"
    }

    /// Позиционирует окно под иконкой приложения в status bar (как все остальные окна)
    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        Logger.shared.log(.info, "MicroSessionOffer", "📍 Позиционирование под status item")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом (как ModernCompletionWindow)
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        self.setFrameOrigin(NSPoint(x: x, y: y))
        Logger.shared.log(.info, "MicroSessionOffer", "📍 Позиция установлена: x=\(Int(x)), y=\(Int(y))")
    }
    
    // MARK: - Actions
    
    @objc private func acceptButtonClicked() {
        Logger.shared.log(.info, "MicroSessionOffer", "✅ Пользователь согласился на работу")

        self.hideWithUnifiedAnimation { [weak self] in
            self?.onAccept?(self?.currentProjectId)
        }
    }

    @objc private func declineButtonClicked() {
        Logger.shared.log(.info, "MicroSessionOffer", "❌ Пользователь отказался от работы")

        // Уведомляем DailyWorkloadManager об отказе
        DailyWorkloadManager.shared.handleMicroSessionRefusal()

        self.hideWithUnifiedAnimation { [weak self] in
            self?.onDecline?()
        }
    }
    
    // MARK: - Helper Methods
    
    private func getCurrentProject() -> (name: String, emoji: String) {
        // Получаем текущий проект из AppDelegate
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate,
           let projectId = currentProjectId ?? appDelegate.currentProjectId,
           let project = appDelegate.projectManager.getProject(by: projectId) {
            return (name: project.name, emoji: project.effectiveEmoji)
        }

        // Возвращаем проект по умолчанию
        return (name: "Общая работа", emoji: "💼")
    }
    
    // MARK: - Keyboard Support
    
    override func keyDown(with event: NSEvent) {
        switch event.keyCode {
        case 36: // Enter
            acceptButtonClicked()
        case 53: // Escape
            declineButtonClicked()
        default:
            super.keyDown(with: event)
        }
    }
    
    // MARK: - Window Lifecycle
    
    override func awakeFromNib() {
        super.awakeFromNib()
        self.acceptsMouseMovedEvents = true
    }
    
    override func mouseDown(with event: NSEvent) {
        // Позволяем перетаскивать окно
        self.performDrag(with: event)
    }
}

// MARK: - Static Factory Method

extension MicroSessionOfferWindow {
    
    /// Создает и показывает окно предложения микросессии
    static func show(projectId: UUID? = nil, 
                     onAccept: @escaping (UUID?) -> Void,
                     onDecline: @escaping () -> Void) {
        let window = MicroSessionOfferWindow()
        window.onAccept = onAccept
        window.onDecline = onDecline
        window.showOffer(projectId: projectId)
    }
}
