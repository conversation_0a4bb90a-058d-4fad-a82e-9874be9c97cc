import Cocoa

/// Окно взращивания сессий - показывает предложения продолжить работу
class SessionGrowthWindow: NSWindow {
    
    // MARK: - Types
    
    enum GrowthWindowType {
        case continueGrowth(nextMinutes: Int)     // "Еще +20 мин"
        case reachTarget(remainingMinutes: Int)   // "Добить до планки +18 мин"  
        case reachMaximum(remainingMinutes: Int)  // "Добить до максимума +22 мин"
    }
    
    // MARK: - Properties
    
    private var windowType: GrowthWindowType
    private var titleLabel: NSTextField!
    private var messageLabel: NSTextField!
    private var primaryButton: NSButton!
    private var secondaryButton: NSButton!
    private var buttonContainer: NSView!
    
    // Callbacks
    var onAccept: (() -> Void)?
    var onDecline: (() -> Void)?
    
    // MARK: - Initialization
    
    init(type: GrowthWindowType) {
        self.windowType = type
        
        super.init(
            contentRect: NSRect(x: 0, y: 0, width: 380, height: 160),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
        setupUI()
        updateContent()
    }
    
    // MARK: - Setup
    
    private func setupWindow() {
        self.level = .floating
        self.isReleasedWhenClosed = false
        self.backgroundColor = NSColor.clear
        self.isOpaque = false
        self.hasShadow = true
        // НЕ центрируем - позиционируем возле status item (как обычное окно завершения)

        // Делаем окно всегда поверх других
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
    }
    
    private func setupUI() {
        guard let contentView = self.contentView else { return }
        
        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        // Создаем сложный фон (копируем из ModernCompletionWindow)
        setupComplexBackground(for: containerView)
        
        // Заголовок
        titleLabel = NSTextField(labelWithString: "")
        titleLabel.font = NSFont.systemFont(ofSize: 15, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.backgroundColor = NSColor.clear
        titleLabel.isBordered = false
        titleLabel.isEditable = false
        
        // Сообщение
        messageLabel = NSTextField(labelWithString: "")
        messageLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)
        messageLabel.textColor = NSColor.white.withAlphaComponent(0.9)
        messageLabel.alignment = .center
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.backgroundColor = NSColor.clear
        messageLabel.isBordered = false
        messageLabel.isEditable = false
        
        // Контейнер для кнопок
        buttonContainer = NSView()
        buttonContainer.translatesAutoresizingMaskIntoConstraints = false
        
        // Добавляем элементы
        containerView.addSubview(titleLabel)
        containerView.addSubview(messageLabel)
        containerView.addSubview(buttonContainer)
        contentView.addSubview(containerView)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Контейнер
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // Сообщение
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // Контейнер кнопок
            buttonContainer.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
            buttonContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            buttonContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            buttonContainer.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20),
            buttonContainer.heightAnchor.constraint(equalToConstant: 36)
        ])
    }
    
    private func setupComplexBackground(for view: NSView) {
        // Основной градиентный слой (копируем из ModernCompletionWindow)
        let mainGradient = CAGradientLayer()
        mainGradient.colors = [
            NSColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 0.95).cgColor,
            NSColor(red: 0.15, green: 0.15, blue: 0.2, alpha: 0.95).cgColor
        ]
        mainGradient.startPoint = CGPoint(x: 0, y: 0)
        mainGradient.endPoint = CGPoint(x: 1, y: 1)
        mainGradient.cornerRadius = 12
        
        // Тень
        mainGradient.shadowColor = NSColor.black.cgColor
        mainGradient.shadowOpacity = 0.3
        mainGradient.shadowOffset = CGSize(width: 0, height: 4)
        mainGradient.shadowRadius = 12
        
        view.layer = mainGradient
        
        // Добавляем радиальные градиенты для красоты
        DispatchQueue.main.async {
            self.addRadialGradients(to: view)
        }
    }
    
    private func addRadialGradients(to view: NSView) {
        guard let layer = view.layer else { return }
        
        // Первый радиальный градиент (левый верхний)
        let radial1 = CAGradientLayer()
        radial1.type = .radial
        radial1.colors = [
            NSColor(red: 0.3, green: 0.4, blue: 0.8, alpha: 0.3).cgColor,
            NSColor.clear.cgColor
        ]
        radial1.startPoint = CGPoint(x: 0.2, y: 0.8)
        radial1.endPoint = CGPoint(x: 0.8, y: 0.2)
        radial1.frame = layer.bounds
        layer.addSublayer(radial1)
        
        // Второй радиальный градиент (правый нижний)
        let radial2 = CAGradientLayer()
        radial2.type = .radial
        radial2.colors = [
            NSColor(red: 0.8, green: 0.3, blue: 0.6, alpha: 0.2).cgColor,
            NSColor.clear.cgColor
        ]
        radial2.startPoint = CGPoint(x: 0.8, y: 0.2)
        radial2.endPoint = CGPoint(x: 0.2, y: 0.8)
        radial2.frame = layer.bounds
        layer.addSublayer(radial2)
    }
    
    private func updateContent() {
        switch windowType {
        case .continueGrowth(let nextMinutes):
            titleLabel.stringValue = "🌱 ОТЛИЧНОЕ НАЧАЛО!"
            messageLabel.stringValue = "Хорошо идет! Давайте еще немного"
            setupButtons(
                primaryTitle: "Еще +\(nextMinutes) мин",
                secondaryTitle: "Хватит",
                primaryAction: #selector(acceptClicked),
                secondaryAction: #selector(declineClicked)
            )
            
        case .reachTarget(let remainingMinutes):
            titleLabel.stringValue = "💪 ПОЧТИ У ЦЕЛИ!"
            messageLabel.stringValue = "Давайте доведем до планки"
            setupButtons(
                primaryTitle: "Добить до планки +\(remainingMinutes) мин",
                secondaryTitle: "Хватит",
                primaryAction: #selector(acceptClicked),
                secondaryAction: #selector(declineClicked)
            )
            
        case .reachMaximum(let remainingMinutes):
            titleLabel.stringValue = "🎉 ПЛАНКА ДОСТИГНУТА!"
            messageLabel.stringValue = "Отлично! Вы выполнили свою цель"
            setupButtons(
                primaryTitle: "Завершить сессию",
                secondaryTitle: "Добить +\(remainingMinutes) мин",
                primaryAction: #selector(acceptClicked),
                secondaryAction: #selector(declineClicked)
            )
        }
    }
    
    private func setupButtons(primaryTitle: String, secondaryTitle: String, primaryAction: Selector, secondaryAction: Selector) {
        // Удаляем старые кнопки
        buttonContainer.subviews.forEach { $0.removeFromSuperview() }
        
        // Создаем кнопки (копируем логику из ModernCompletionWindow)
        primaryButton = createGradientButton(title: primaryTitle, color: .green)
        primaryButton.target = self
        primaryButton.action = primaryAction
        
        secondaryButton = createGradientButton(title: secondaryTitle, color: .darkGray)
        secondaryButton.target = self
        secondaryButton.action = secondaryAction
        
        // Добавляем кнопки
        buttonContainer.addSubview(primaryButton)
        buttonContainer.addSubview(secondaryButton)
        
        // Constraints для кнопок
        NSLayoutConstraint.activate([
            // Основная кнопка (слева)
            primaryButton.leadingAnchor.constraint(equalTo: buttonContainer.leadingAnchor),
            primaryButton.topAnchor.constraint(equalTo: buttonContainer.topAnchor),
            primaryButton.bottomAnchor.constraint(equalTo: buttonContainer.bottomAnchor),
            primaryButton.widthAnchor.constraint(equalTo: buttonContainer.widthAnchor, multiplier: 0.48),
            
            // Дополнительная кнопка (справа)
            secondaryButton.trailingAnchor.constraint(equalTo: buttonContainer.trailingAnchor),
            secondaryButton.topAnchor.constraint(equalTo: buttonContainer.topAnchor),
            secondaryButton.bottomAnchor.constraint(equalTo: buttonContainer.bottomAnchor),
            secondaryButton.widthAnchor.constraint(equalTo: buttonContainer.widthAnchor, multiplier: 0.48)
        ])
    }

    private func createGradientButton(title: String, color: ButtonColor) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Создаем градиентный слой (копируем из ModernCompletionWindow)
        let gradientLayer = CAGradientLayer()

        switch color {
        case .green:
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.3, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.2, alpha: 1.0).cgColor
            ]
        case .darkGray:
            gradientLayer.colors = [
                NSColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0).cgColor
            ]
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = 8

        // Тень для кнопки
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.2
        gradientLayer.shadowOffset = CGSize(width: 0, height: 1)
        gradientLayer.shadowRadius = 3

        button.layer = gradientLayer

        // Белый текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 12, weight: .semibold)
            ]
        )

        return button
    }

    enum ButtonColor {
        case green, darkGray
    }

    // MARK: - Actions

    @objc private func acceptClicked() {
        Logger.shared.log(.info, "SessionGrowth", "✅ Пользователь принял предложение взращивания")
        hideWithAnimation {
            self.onAccept?()
        }
    }

    @objc private func declineClicked() {
        Logger.shared.log(.info, "SessionGrowth", "❌ Пользователь отклонил предложение взращивания")
        hideWithAnimation {
            self.onDecline?()
        }
    }

    // MARK: - Animation

    private func hideWithAnimation(completion: @escaping () -> Void) {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
            self.animator().alphaValue = 0
        }) {
            self.orderOut(nil)
            completion()
        }
    }

    func showWithAnimation() {
        self.alphaValue = 0
        self.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
            self.animator().alphaValue = 1
        })
    }

    // MARK: - Keyboard Support

    override func keyDown(with event: NSEvent) {
        if event.keyCode == 53 { // Escape key
            declineClicked()
        } else if event.keyCode == 36 { // Enter key
            acceptClicked()
        } else {
            super.keyDown(with: event)
        }
    }

    /// Позиционирует окно возле status item (как обычное окно завершения)
    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        print("🌱 SessionGrowthWindow: Позиционирование возле status item")
        print("🌱 SessionGrowthWindow: statusItemFrame = \(statusItemFrame)")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом (как ModernCompletionWindow)
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        let newPosition = NSPoint(x: x, y: y)
        print("🌱 SessionGrowthWindow: Новая позиция = \(newPosition)")
        self.setFrameOrigin(newPosition)
    }
}
