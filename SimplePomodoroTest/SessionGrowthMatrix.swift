import Foundation

/// Матрица взращивания сессий - рассчитывает последовательность интервалов
/// для достижения планки пользователя в рамках одной сессии
class SessionGrowthMatrix {
    
    /// Режим взращивания (единый оптимальный режим)
    enum GrowthMode: String {
        case standard = "стандартный"
    }
    
    /// Параметры алгоритма для разных режимов
    private struct GrowthParams {
        let smallStepPercent: Int    // Процент для остатка 11-20 мин
        let mediumStepPercent: Int   // Процент для остатка 21-35 мин  
        let largeStepPercent: Int    // Процент для остатка 36+ мин
        let maxFirstStep: Int        // Максимальный первый шаг
        let minInterval: Int         // Минимальный интервал
    }
    
    /// Параметры для стандартного режима (бывший уверенный)
    private static let standardParams = GrowthParams(
        smallStepPercent: 60,    // Оптимальный рост
        mediumStepPercent: 35,
        largeStepPercent: 30,
        maxFirstStep: 15,        // Разумные шаги
        minInterval: 8
    )
    
    /// Специальные случаи (переопределяют алгоритм)
    private static let specialCases: [String: [GrowthMode: [Int]]] = [:]
        // Можно добавлять кастомные последовательности для проблемных случаев:
        // "3->52": [.standard: [3, 15, 34]]
    
    /// Основной метод расчета последовательности взращивания
    /// - Parameters:
    ///   - startMinutes: Стартовый интервал в минутах
    ///   - targetTotalMinutes: Планка пользователя в минутах
    ///   - mode: Режим взращивания (щадящий/уверенный)
    /// - Returns: Последовательность интервалов, сумма которых равна планке
    static func calculateGrowthSequence(
        startMinutes: Int,
        targetTotalMinutes: Int,
        mode: GrowthMode
    ) -> [Int] {
        
        let remaining = targetTotalMinutes - startMinutes
        
        logInfo("SessionGrowth", "🌱 Расчет взращивания: старт=\(startMinutes)мин, планка=\(targetTotalMinutes)мин, режим=\(mode.rawValue)")
        logInfo("SessionGrowth", "🌱 Остается до планки: \(remaining) минут")
        
        // Проверяем специальные случаи
        let key = "\(startMinutes)->\(targetTotalMinutes)"
        if let specialCase = specialCases[key]?[mode] {
            logInfo("SessionGrowth", "🎯 Используем специальный случай для \(key)")
            logInfo("SessionGrowth", "🌱 Результат (спец.случай): \(specialCase) (сумма: \(specialCase.reduce(0, +))мин)")
            return specialCase
        }
        
        // Если остается слишком мало - не предлагаем взращивание
        if remaining <= 1 { 
            logInfo("SessionGrowth", "🌱 Взращивание не предлагается: остается \(remaining) мин (≤1)")
            return [startMinutes] 
        }
        
        // Если остается мало - один шаг
        if remaining <= 10 {
            logInfo("SessionGrowth", "🌱 Простое взращивание: один шаг +\(remaining) мин")
            let result = [startMinutes, remaining]
            logInfo("SessionGrowth", "🌱 Результат (простое): \(result) (сумма: \(result.reduce(0, +))мин)")
            return result
        }
        
        // Используем алгоритм
        let result = calculateOptimalSequence(
            start: startMinutes,
            remaining: remaining,
            mode: mode
        )
        
        let sum = result.reduce(0, +)
        logInfo("SessionGrowth", "🌱 Результат (алгоритм): \(result) (сумма: \(sum)мин)")
        
        // Проверка корректности
        if sum != targetTotalMinutes {
            logError("SessionGrowth", "❌ ОШИБКА: сумма \(sum) ≠ планка \(targetTotalMinutes)")
        }
        
        return result
    }
    
    /// Рассчитывает оптимальную последовательность с помощью алгоритма
    private static func calculateOptimalSequence(
        start: Int,
        remaining: Int,
        mode: GrowthMode
    ) -> [Int] {
        
        let params = getParams(for: mode)
        let strategy = determineStrategy(remaining: remaining, mode: mode, startInterval: start)

        var sequence = [start]

        // Обрабатываем специальные стратегии
        if strategy.count == 1 {
            if strategy[0] == -1 {
                // Равномерное деление пополам
                let firstStep = remaining / 2
                let secondStep = remaining - firstStep
                sequence.append(firstStep)
                sequence.append(secondStep)
                logInfo("SessionGrowth", "🌱 Равномерное деление: +\(firstStep)мин, +\(secondStep)мин")
                return sequence
            } else if strategy[0] == -2 {
                // Ограниченные сбалансированные шаги
                let maxFirstStep = min(remaining / 2, 20)
                let firstStep = max(maxFirstStep, params.minInterval)
                let secondStep = remaining - firstStep
                sequence.append(firstStep)
                sequence.append(secondStep)
                logInfo("SessionGrowth", "🌱 Сбалансированные шаги: +\(firstStep)мин, +\(secondStep)мин")
                return sequence
            }
        }

        // Обычная логика с процентами
        logInfo("SessionGrowth", "🌱 Стратегия: \(strategy.count) шагов с процентами \(strategy)")

        var leftToDistribute = remaining

        for (index, percentage) in strategy.enumerated() {
            let isLastStep = (index == strategy.count - 1)

            let nextInterval = if isLastStep {
                leftToDistribute // Последний шаг - весь остаток
            } else {
                calculateIntervalSize(
                    remaining: leftToDistribute,
                    percentage: getAdjustedPercentage(
                        basePercentage: percentage,
                        remaining: remaining,
                        params: params
                    ),
                    params: params,
                    previousInterval: sequence.last
                )
            }

            let finalInterval = max(nextInterval, params.minInterval)
            let safeInterval = min(finalInterval, leftToDistribute)

            if safeInterval > 0 {
                sequence.append(safeInterval)
                leftToDistribute -= safeInterval

                logInfo("SessionGrowth", "🌱 Шаг \(index + 1): +\(safeInterval)мин (остается: \(leftToDistribute)мин)")
            }
        }
        
        return sequence
    }
    
    /// Возвращает параметры для режима
    private static func getParams(for mode: GrowthMode) -> GrowthParams {
        return standardParams
    }
    
    /// Определяет стратегию (количество шагов и базовые проценты)
    private static func determineStrategy(remaining: Int, mode: GrowthMode, startInterval: Int? = nil) -> [Int] {
        // Проверяем, не будет ли слишком большой скачок при 1 шаге
        if let start = startInterval, remaining <= 30 {
            let singleStepSize = remaining * 70 / 100
            if singleStepSize > start * 3 {
                // Слишком большой скачок - принудительно 2 шага
                return [-1] // Специальный маркер для равномерного деления
            }
        }

        switch remaining {
        case (11...30): return [70]           // 1 шаг
        case (31...40): return [-1]          // Равномерное деление пополам
        case (41...):   return [-2]          // Ограниченные сбалансированные шаги
        default:        return [70]           // Fallback - 1 шаг
        }
    }
    
    /// Корректирует процент в зависимости от остатка и параметров
    private static func getAdjustedPercentage(
        basePercentage: Int,
        remaining: Int,
        params: GrowthParams
    ) -> Int {
        switch remaining {
        case 11...30:  return params.smallStepPercent  // Расширили с 20 до 30
        case 31...:    return params.largeStepPercent  // Было 36+, теперь 31+
        default:       return basePercentage
        }
    }
    
    /// Рассчитывает размер интервала с учетом ограничений
    private static func calculateIntervalSize(
        remaining: Int,
        percentage: Int,
        params: GrowthParams,
        previousInterval: Int? = nil
    ) -> Int {
        let baseSize = remaining * percentage / 100
        var result = max(params.minInterval, min(baseSize, params.maxFirstStep))

        // Ограничиваем максимальный скачок (не больше чем в 3 раза)
        if let prev = previousInterval {
            let maxJump = prev * 3
            result = min(result, maxJump)
        }

        return result
    }
}

// MARK: - Расширения для отладки

extension SessionGrowthMatrix {
    
    /// Получает отладочную информацию о расчете
    static func getDebugInfo(
        startMinutes: Int,
        targetTotalMinutes: Int,
        mode: GrowthMode
    ) -> String {
        
        let sequence = calculateGrowthSequence(
            startMinutes: startMinutes,
            targetTotalMinutes: targetTotalMinutes,
            mode: mode
        )
        
        let remaining = targetTotalMinutes - startMinutes
        let sum = sequence.reduce(0, +)
        let steps = sequence.count - 1
        
        var result = """
        📊 Отладка взращивания сессии:
        • Старт: \(startMinutes) мин
        • Планка: \(targetTotalMinutes) мин
        • Остается: \(remaining) мин
        • Режим: \(mode.rawValue)
        • Результат: \(sequence.map(String.init).joined(separator: "→"))
        • Сумма: \(sum) мин
        • Шагов: \(steps)
        """
        
        if sum != targetTotalMinutes {
            result += "\n❌ ОШИБКА: сумма не равна планке!"
        }
        
        return result
    }
    
    /// Тестирует алгоритм на наборе случаев
    static func runTests() {
        logInfo("SessionGrowth", "🧪 Запуск тестов матрицы взращивания...")
        
        let testCases = [
            (3, 52), (3, 30), (5, 25), (8, 40), (10, 52),
            (15, 45), (20, 35), (25, 30), (45, 52), (50, 52)
        ]
        
        var errors = 0
        
        for (start, target) in testCases {
            let sequence = calculateGrowthSequence(
                startMinutes: start,
                targetTotalMinutes: target,
                mode: .standard
            )

            let sum = sequence.reduce(0, +)
            if sum != target {
                errors += 1
                logError("SessionGrowth", "❌ Тест \(start)→\(target): сумма \(sum) ≠ \(target)")
            }
        }
        
        if errors == 0 {
            logInfo("SessionGrowth", "✅ Все тесты пройдены успешно!")
        } else {
            logError("SessionGrowth", "❌ Найдено \(errors) ошибок в тестах")
        }
    }
}
