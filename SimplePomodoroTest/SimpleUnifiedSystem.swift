import Foundation
import Cocoa

/// Простая унифицированная система напоминаний - версия 2.0
/// Создана для тестирования и отладки проблем с оригинальной системой
class SimpleUnifiedSystem {
    
    // MARK: - Singleton
    static let shared = SimpleUnifiedSystem()
    private init() {
        print("🆕 SimpleUnifiedSystem создана")
    }
    
    // MARK: - Properties
    private var escalationTimer: Timer?
    private var escalationStartTime: Date?
    private var lastEscalationLevel: Int = -1
    private var currentIntervalType = ""
    private var isTestMode = false
    private var intervalDuration: TimeInterval = 0  // Длительность завершенного интервала

    // Для отслеживания паузы при неактивности
    private var isPausedDueToInactivity = false
    private var pauseStartTime: Date?
    private var totalPausedTime: TimeInterval = 0

    // Для защиты от "дрочки" системы
    private var shortPauseCount = 0  // Счетчик коротких пауз подряд
    private var lastWorkStartTime: Date?  // Время начала последней работы
    private let maxShortPauses = 3  // Максимум коротких пауз подряд
    private let minWorkDuration: TimeInterval = 5 * 60  // Минимум 5 минут работы между паузами
    private let shortPauseThreshold: TimeInterval = 2 * 60  // Пауза < 2 минут считается короткой

    // Делегат для связи с AppDelegate
    weak var delegate: SimpleUnifiedSystemDelegate?
    
    // MARK: - Public Methods
    
    /// Расширенный метод запуска эскалации с полной функциональностью
    func startSimpleEscalation(for intervalType: String, isTest: Bool = false, intervalDuration: TimeInterval = 0) {
        logInfo("SimpleUnified", "🚀🚀🚀 РАСШИРЕННАЯ СИСТЕМА: startSimpleEscalation вызван!")
        logInfo("SimpleUnified", "📋📋📋 Параметры: intervalType=\(intervalType), isTest=\(isTest), intervalDuration=\(Int(intervalDuration/60)) мин")

        // Сохраняем параметры
        currentIntervalType = intervalType
        isTestMode = isTest
        self.intervalDuration = intervalDuration

        // Останавливаем предыдущий таймер если есть
        stopEscalation()

        // Устанавливаем время начала эскалации
        escalationStartTime = Date()
        lastEscalationLevel = -1
        lastWorkStartTime = Date()  // Начинаем отсчет времени работы

        // Сразу обновляем статус-бар чтобы таймер появился без задержки
        // Показываем общее время (интервал + переработка)
        let initialTotalMinutes = Int(intervalDuration / 60)
        DispatchQueue.main.async { [weak self] in
            self?.delegate?.updateStatusBar(overtimeMinutes: 0, totalMinutes: initialTotalMinutes, seconds: 0)
        }

        // Запускаем таймер с интервалом 1 секунда для точной проверки
        print("⏰ Запускаем таймер эскалации (проверка каждую секунду)")
        escalationTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.checkEscalation()
        }

        print("✅ Расширенная эскалация запущена успешно")
    }
    
    /// Останавливает эскалацию
    func stopEscalation() {
        print("🛑 Остановка эскалации")

        // Записываем статистику для неформальных интервалов при остановке
        if currentIntervalType == "informal" && !isTestMode {
            recordInformalIntervalStatistics()
        }

        // TODO: БУДУЩЕЕ УЛУЧШЕНИЕ - Автоматические отдыхи при долгом отсутствии
        // Если пользователь ушел на 17+ минут, автоматически засчитывать отдых:
        // - Для формальных интервалов: записать в статистику как полноценный отдых
        // - Для неформальных: просто сбросить счетчик переработки
        // - Запустить параллельный счетчик отдыха при обнаружении неактивности
        // - Отслеживать возвращение пользователя и корректно обрабатывать прерванные отдыхи

        escalationTimer?.invalidate()
        escalationTimer = nil
        escalationStartTime = nil
        lastEscalationLevel = -1
        isTestMode = false

        // Сбрасываем состояние паузы и защиты от дрочки
        isPausedDueToInactivity = false
        pauseStartTime = nil
        totalPausedTime = 0
        shortPauseCount = 0
        lastWorkStartTime = nil
    }

    /// Записывает статистику для неформальных интервалов
    private func recordInformalIntervalStatistics() {
        guard let startTime = escalationStartTime else {
            print("⚠️ Не удалось записать статистику - нет времени начала")
            return
        }

        let totalDuration = intervalDuration + Date().timeIntervalSince(startTime)
        delegate?.recordIntervalStatistics(duration: totalDuration, intervalType: "informal")
        print("📊 Записана статистика неформального интервала: \(Int(totalDuration/60)) мин")
    }


    // MARK: - Private Methods
    
    /// Проверяет нужно ли показать следующий уровень эскалации
    private func checkEscalation() {
        guard let startTime = escalationStartTime else {
            print("⚠️ escalationStartTime не установлен")
            return
        }

        // КРИТИЧЕСКИ ВАЖНО: Проверяем активность пользователя (кроме тестового режима)
        if !isTestMode {
            let isUserActive = UnifiedActivityChecker.shared.isUserCurrentlyActive()

            if !isUserActive && !isPausedDueToInactivity {
                // Пользователь стал неактивен - проверяем защиту от дрочки
                if let workStart = lastWorkStartTime {
                    let workDuration = Date().timeIntervalSince(workStart)
                    if workDuration < minWorkDuration {
                        shortPauseCount += 1
                        print("⚠️ SimpleUnifiedSystem: Короткая работа (\(Int(workDuration/60)) мин), счетчик: \(shortPauseCount)/\(maxShortPauses)")

                        if shortPauseCount >= maxShortPauses {
                            print("📊 SimpleUnifiedSystem: Много коротких пауз (\(shortPauseCount)) - возможно стоит отдохнуть")
                            // Не блокируем, просто логируем для анализа
                        }
                    } else {
                        // Нормальная работа - сбрасываем счетчик
                        shortPauseCount = 0
                        print("✅ SimpleUnifiedSystem: Нормальная работа (\(Int(workDuration/60)) мин), счетчик сброшен")
                    }
                }

                isPausedDueToInactivity = true
                pauseStartTime = Date()
                print("⏸️ SimpleUnifiedSystem: Пользователь неактивен - ставим эскалацию на паузу")
                return
            } else if isUserActive && isPausedDueToInactivity {
                // Проверяем стабильность через сегменты (2 активных сегмента подряд)
                if checkStableActivity() {
                    // Стабильная активность - возобновляем
                    if let pauseStart = pauseStartTime {
                        let pauseDuration = Date().timeIntervalSince(pauseStart)
                        print("🔄 DEBUG: Добавляем паузу \(Int(pauseDuration))с к общему времени пауз \(Int(totalPausedTime))с")
                        totalPausedTime += pauseDuration
                        print("▶️ SimpleUnifiedSystem: Стабильная активность через сегменты - возобновляем (пауза: \(Int(pauseDuration)) сек, общие паузы: \(Int(totalPausedTime))с)")
                    }
                    isPausedDueToInactivity = false
                    pauseStartTime = nil
                    lastWorkStartTime = Date()  // Начинаем отсчет времени работы
                } else {
                    // Еще ждем стабилизации
                    return
                }
            } else if isPausedDueToInactivity {
                // Все еще на паузе - ничего не делаем
                return
            }
        }

        // Вычисляем активное время (общее время минус время пауз)
        let totalElapsed = Date().timeIntervalSince(startTime)
        var currentPausedTime = totalPausedTime

        // Если сейчас на паузе, добавляем текущее время паузы
        if isPausedDueToInactivity, let pauseStart = pauseStartTime {
            let currentPauseTime = Date().timeIntervalSince(pauseStart)
            currentPausedTime += currentPauseTime
            print("⏸️ DEBUG: На паузе \(Int(currentPauseTime))с, общее время пауз: \(Int(currentPausedTime))с")
        }

        let activeElapsed = totalElapsed - currentPausedTime
        print("🕐 DEBUG: Общее время: \(Int(totalElapsed))с, паузы: \(Int(currentPausedTime))с, активное: \(Int(activeElapsed))с")
        let elapsedMinutes = Int(activeElapsed / 60)
        let remainingSeconds = Int(activeElapsed) % 60

        // Для отображения времени показываем ОБЩЕЕ время (интервал + переработка)
        let totalMinutes = Int(intervalDuration / 60) + elapsedMinutes
        let displayMinutes = totalMinutes
        let displaySeconds = remainingSeconds

        // Для логики эскалации в тестовом режиме ускоряем разумно (10 секунд = 1 минута)
        let escalationMinutes = isTestMode ? Int(activeElapsed / 10) : elapsedMinutes

        // Получаем текущий уровень эскалации (используем ускоренное время для теста)
        let currentLevel = getEscalationLevel(for: escalationMinutes)

        // Показываем напоминание только при изменении уровня
        if currentLevel != lastEscalationLevel {
            lastEscalationLevel = currentLevel
            print("📈 Изменение уровня эскалации: \(displayMinutes):\(String(format: "%02d", displaySeconds)) = уровень \(currentLevel) (эскалация: \(escalationMinutes) мин)")

            // Показываем уведомление через делегат
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                // Для неформальных интервалов показываем напоминания только с уровня 1
                // (уровень 0 - это просто "Time for rest", окно уже показано)
                if self.currentIntervalType == "informal" && currentLevel == 0 {
                    print("⏭️ Пропускаем показ напоминания для неформального уровня 0 (окно уже показано)")
                } else {
                    self.delegate?.showEscalationReminder(minutes: escalationMinutes, level: currentLevel, for: self.currentIntervalType)
                }

                // Для статус-бара передаем время переработки (для правильных цветов) и общее время (для отображения)
                self.delegate?.updateStatusBar(overtimeMinutes: elapsedMinutes, totalMinutes: displayMinutes, seconds: displaySeconds)
            }
        } else {
            // Даже если уровень не изменился, обновляем статус-бар каждую секунду с реальным временем
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.delegate?.updateStatusBar(overtimeMinutes: elapsedMinutes, totalMinutes: displayMinutes, seconds: displaySeconds)
            }
        }
    }

    /// Правильная логика определения уровня эскалации (как в OvertimeConfig)
    private func getEscalationLevel(for minutes: Int) -> Int {
        // Используем ту же логику что и в OvertimeConfig для единообразия
        switch minutes {
        case 0: return 0      // 🎉 Session completed! (0-1 мин)
        case 1...2: return 1  // ⚠️ Yellow zone (1-3 мин)
        case 3...4: return 2  // 🟠 Orange zone (3-5 мин)
        case 5...9: return 3  // 🔴 Red zone (5-10 мин)
        default: return 4     // 🚨 Critical zone (10+ мин)
        }
    }

    /// Получает название зоны эскалации
    private func getEscalationZoneName(for level: Int) -> String {
        switch level {
        case 0: return "🎉 Session completed!"
        case 1: return "⚠️ Yellow zone"
        case 2: return "🟠 Orange zone"
        case 3: return "🔴 Red zone"
        default: return "🚨 Critical zone"
        }
    }

    // MARK: - Private Methods

    /// Проверяет стабильную активность через проверку последних 6 секунд
    private func checkStableActivity() -> Bool {
        // Проверяем что активность была стабильной последние 6 секунд (2 сегмента по 3 сек)
        let isCurrentlyActive = UnifiedActivityChecker.shared.isUserCurrentlyActive()

        if isCurrentlyActive {
            // Проверяем минимальное время с последней активности
            let mouseIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .mouseMoved)
            let keyboardIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .keyDown)
            let clickIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .leftMouseDown)
            let scrollIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .scrollWheel)

            let minIdleTime = min(mouseIdleTime, keyboardIdleTime, clickIdleTime, scrollIdleTime)
            let isStable = minIdleTime <= 6.0  // Активность в последние 6 секунд (2 сегмента)

            if isStable {
                print("✅ SimpleUnifiedSystem: Стабильная активность подтверждена (последняя активность: \(String(format: "%.1f", minIdleTime))с)")
            } else {
                print("⏳ SimpleUnifiedSystem: Ждем стабилизации активности (последняя активность: \(String(format: "%.1f", minIdleTime))с)")
            }

            return isStable
        }

        return false
    }
}

// MARK: - Единая конфигурация для всех компонентов
struct UnifiedButtonConfig {
    /// Единый текст для кнопки отсрочки (для всех типов интервалов)
    static let postponeText = "I need a couple of minutes"

    /// Единый цвет для кнопки отсрочки (фиолетовый) - для NSColor
    static let postponeColor = NSColor(red: 0.7, green: 0.6, blue: 0.8, alpha: 1.0)

    /// Единый текст для кнопки отдыха
    static let breakText = "Take a break"
}

// MARK: - Протокол для связи с AppDelegate
protocol SimpleUnifiedSystemDelegate: AnyObject {
    /// Показать напоминание определенного уровня
    func showEscalationReminder(minutes: Int, level: Int, for intervalType: String)

    /// Обновить статус-бар с информацией о переработке (с секундами)
    func updateStatusBar(overtimeMinutes: Int, totalMinutes: Int, seconds: Int)

    /// Записать статистику интервала
    func recordIntervalStatistics(duration: TimeInterval, intervalType: String)
}
