import Foundation

/// Система матрицы кнопок для раннего вовлечения
/// Генерирует предсказуемый набор кнопок для каждой позиции (вертикаль × горизонталь)
struct ButtonMatrix {
    
    // MARK: - Button Component
    
    /// Компонент кнопки с типом и временем
    struct ButtonComponent {
        let text: String           // "Начать работу (35 мин)"
        let duration: TimeInterval // 35 * 60 секунд
        let type: ButtonType       // .primary, .fullSession, etc.
        let context: String        // "calculated_bar", "full_session", etc.
        
        /// Форматированное отображение для отладки
        var debugDescription: String {
            let minutes = Int(duration / 60)
            return "[\(text)] - тип: \(type.rawValue), время: \(minutes) мин, контекст: \(context)"
        }
    }
    
    /// Типы кнопок в системе
    enum ButtonType: String, CaseIterable {
        case primary = "primary"           // Основная кнопка (рассчитанная планка)
        case fullBar = "fullBar"           // Полная планка (до горизонтальной дескалации)
        case fullSession = "fullSession"   // Полная сессия (52 мин)
        case planMinimum = "planMinimum"   // План-минимум (3 мин)
        case later = "later"               // Отложить
        case snooze = "snooze"             // Через 30 мин
    }
    
    // MARK: - Main Generation Method
    
    /// Генерирует кнопки для заданной позиции матрицы
    /// - Parameters:
    ///   - vertical: Уровень по дням без работы (0-4)
    ///   - horizontal: Время дня (0-3)
    ///   - baseBar: Базовая планка в секундах (до адаптации)
    /// - Returns: Массив компонентов кнопок
    static func generateButtons(vertical: Int, horizontal: Int, baseBar: TimeInterval) -> [ButtonComponent] {
        var buttons: [ButtonComponent] = []
        
        // 1. Рассчитываем основную планку (вертикаль + горизонталь)
        let calculatedBar = calculateFinalBar(vertical: vertical, horizontal: horizontal, baseBar: baseBar)
        
        // 2. Первая кнопка - всегда рассчитанная планка
        buttons.append(createPrimaryButton(duration: calculatedBar))
        
        // 3. Всегда добавляем полную планку (если она больше рассчитанной)
        let fullBar = calculateVerticalBar(vertical: vertical, baseBar: baseBar)
        print("🔍 ButtonMatrix DEBUG: calculatedBar=\(Int(calculatedBar/60))мин, fullBar=\(Int(fullBar/60))мин, vertical=\(vertical), horizontal=\(horizontal)")
        if fullBar > calculatedBar {
            buttons.append(createFullBarButton(duration: fullBar))
            print("✅ ButtonMatrix: Добавлена кнопка fullBar (\(Int(fullBar/60))мин)")
        } else {
            print("❌ ButtonMatrix: fullBar (\(Int(fullBar/60))мин) НЕ больше calculatedBar (\(Int(calculatedBar/60))мин)")
        }
        
        // 4. Полная сессия убрана - решение проблемы 4 кнопок
        
        // 5. Служебные кнопки
        buttons.append(createLaterButton())
        
        return buttons
    }
    
    // MARK: - Bar Calculation Methods
    
    /// Рассчитывает финальную планку с учетом вертикальной и горизонтальной адаптации
    private static func calculateFinalBar(vertical: Int, horizontal: Int, baseBar: TimeInterval) -> TimeInterval {
        // Сначала применяем вертикальную адаптацию
        let verticalBar = calculateVerticalBar(vertical: vertical, baseBar: baseBar)
        
        // Затем применяем горизонтальную адаптацию
        let finalBar = calculateHorizontalBar(horizontal: horizontal, verticalBar: verticalBar)
        
        return finalBar
    }
    
    /// Применяет вертикальную адаптацию (по дням без работы)
    /// СИНХРОНИЗИРОВАНО с EarlyEngagementSystem.debugCalculateBarForPosition()
    private static func calculateVerticalBar(vertical: Int, baseBar: TimeInterval) -> TimeInterval {
        print("🔍 ButtonMatrix calculateVerticalBar: vertical=\(vertical), baseBar=\(Int(baseBar/60))мин")

        let result: TimeInterval
        switch vertical {
        case -1:
            // STREAK РЕЖИМ: планка уже рассчитана с учетом streak, не применяем дополнительных изменений
            result = baseBar
            print("🔍 ButtonMatrix case -1 (STREAK): \(Int(baseBar/60))мин → \(Int(result/60))мин (без изменений)")
        case 0:
            // Уровень 0 (работал вчера): планка растет по градационной системе
            // ИСПОЛЬЗУЕМ ТУ ЖЕ ЛОГИКУ что в EarlyEngagementSystem
            result = GradualGrowthSystem.applyLevel0Growth(currentBar: baseBar)
            print("🔍 ButtonMatrix case 0: \(Int(baseBar/60))мин → \(Int(result/60))мин (GradualGrowth)")
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%)
            result = baseBar * 0.77
            print("🔍 ButtonMatrix case 1: \(Int(baseBar/60))мин × 0.77 → \(Int(result/60))мин")
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%)
            result = baseBar * 0.48
            print("🔍 ButtonMatrix case 2-3: \(Int(baseBar/60))мин × 0.48 → \(Int(result/60))мин")
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%)
            result = baseBar * 0.29
            print("🔍 ButtonMatrix case 4-6: \(Int(baseBar/60))мин × 0.29 → \(Int(result/60))мин")
        default:
            // Уровень 7+ (неделя+): план-минимум (3 мин)
            result = 3 * 60
            print("🔍 ButtonMatrix case default (7+): → 3мин")
        }

        return result
    }
    
    /// Применяет горизонтальную адаптацию (по времени дня)
    /// СИНХРОНИЗИРОВАНО с EarlyEngagementSystem.debugCalculateBarForPosition()
    private static func calculateHorizontalBar(horizontal: Int, verticalBar: TimeInterval) -> TimeInterval {
        switch horizontal {
        case 0:
            // 1-е сообщение: 100% планки (но не меньше план-минимума)
            return max(verticalBar, 3 * 60)
        case 1:
            // 2-е сообщение: 50% планки (но не меньше план-минимума)
            return max(verticalBar * 0.5, 3 * 60)
        case 2:
            // 3-е сообщение: 25% планки (но не меньше план-минимума)
            return max(verticalBar * 0.25, 3 * 60)
        case 3:
            // 4-е сообщение: план-минимум (3 мин)
            return 3 * 60
        default:
            return max(verticalBar, 3 * 60)
        }
    }
    
    // MARK: - Button Creation Methods
    
    /// Создает основную кнопку с рассчитанным временем
    private static func createPrimaryButton(duration: TimeInterval) -> ButtonComponent {
        let minutes = Int(duration / 60)
        return ButtonComponent(
            text: "Начать работу (\(minutes) мин)",
            duration: duration,
            type: .primary,
            context: "calculated_bar"
        )
    }
    
    /// Создает кнопку полной планки (до горизонтальной дескалации)
    private static func createFullBarButton(duration: TimeInterval) -> ButtonComponent {
        let minutes = Int(duration / 60)
        return ButtonComponent(
            text: "Максимальная сессия",
            duration: duration,
            type: .fullBar,
            context: "full_bar_before_horizontal"
        )
    }
    
    /// Создает кнопку полной сессии
    private static func createFullSessionButton() -> ButtonComponent {
        return ButtonComponent(
            text: "Полная сессия (52 мин)",
            duration: 52 * 60,
            type: .fullSession,
            context: "full_session_52min"
        )
    }
    
    /// Создает кнопку "Позже"
    private static func createLaterButton() -> ButtonComponent {
        return ButtonComponent(
            text: "Позже",
            duration: 0,
            type: .later,
            context: "postpone"
        )
    }
    
    // MARK: - Utility Methods
    
    /// Форматирует кнопки для отображения в отладочном окне
    static func formatButtonsForDebug(_ buttons: [ButtonComponent]) -> String {
        let buttonTexts = buttons.map { "[\($0.text)]" }.joined(separator: " | ")
        return buttonTexts
    }
    
    /// Создает детальное описание кнопок для отладки
    static func createDetailedButtonDescription(_ buttons: [ButtonComponent]) -> String {
        var description = "🔘 Детальная информация о кнопках:\n"
        for (index, button) in buttons.enumerated() {
            description += "   Кнопка \(index + 1): \(button.debugDescription)\n"
        }
        return description
    }
    
    /// Получает формулу расчета для отладки
    static func getCalculationFormula(vertical: Int, horizontal: Int, baseBar: Int) -> String {
        let verticalFormula = getVerticalFormula(vertical: vertical, baseBar: baseBar)
        let horizontalFormula = getHorizontalFormula(horizontal: horizontal)
        
        return "\(verticalFormula) × \(horizontalFormula)"
    }
    
    private static func getVerticalFormula(vertical: Int, baseBar: Int) -> String {
        switch vertical {
        case 0:
            let growthDesc = GradualGrowthSystem.getGrowthDescription(currentBarMinutes: baseBar)
            return "\(baseBar) \(growthDesc)"
        case 1: return "\(baseBar) × 0.77"
        case 2...3: return "\(baseBar) × 0.48"
        case 4...6: return "\(baseBar) × 0.29"
        default: return "план-минимум (3 мин)"
        }
    }

    private static func getHorizontalFormula(horizontal: Int) -> String {
        switch horizontal {
        case 0: return "1.0 (100%)"
        case 1: return "0.5 (50%)"
        case 2: return "0.25 (25%)"
        case 3: return "план-минимум (3 мин)"
        default: return "1.0 (100%)"
        }
    }
}

// MARK: - Extensions for Integration

extension ButtonMatrix {
    
    /// Интеграция с существующей системой EarlyEngagementSystem
    static func generateButtonsForEarlyEngagement(
        daysWithoutWork: Int,
        messageIndex: Int,
        baseBarMinutes: Int,
        streakDays: Int? = nil
    ) -> [ButtonComponent] {
        // ИСПРАВЛЕНО: правильный маппинг дней без работы
        // 0-6 дней → 0-6, 7+ дней → 7 (для case default)
        let vertical = max(0, daysWithoutWork)
        let horizontal = min(3, max(0, messageIndex))

        // ✅ STREAK ЛОГИКА: Если передан streak, предварительно рассчитываем планку
        let adjustedBaseBarMinutes: Int
        let adjustedVertical: Int

        if let streak = streakDays, daysWithoutWork == 0 {
            // Для streak режима применяем GradualGrowth несколько раз
            var currentBarTime = TimeInterval(baseBarMinutes * 60)
            for _ in 0..<streak {
                currentBarTime = GradualGrowthSystem.applyLevel0Growth(currentBar: currentBarTime)
                currentBarTime = min(currentBarTime, 52 * 60) // Ограничиваем на каждом шаге
            }
            adjustedBaseBarMinutes = Int(currentBarTime / 60)
            adjustedVertical = -1  // ✅ СПЕЦИАЛЬНЫЙ УРОВЕНЬ для streak (избегаем повторной обработки)
            print("🔍 ButtonMatrix STREAK \(streak): \(baseBarMinutes)мин → \(adjustedBaseBarMinutes)мин (vertical=-1)")
        } else {
            adjustedBaseBarMinutes = baseBarMinutes
            adjustedVertical = max(0, daysWithoutWork)
        }

        let baseBar = TimeInterval(adjustedBaseBarMinutes * 60)

        print("🔍 ButtonMatrix ВХОД: daysWithoutWork=\(daysWithoutWork), messageIndex=\(messageIndex), baseBarMinutes=\(baseBarMinutes), streak=\(streakDays?.description ?? "nil")")
        print("🔍 ButtonMatrix МАППИНГ: adjustedVertical=\(adjustedVertical), horizontal=\(horizontal), adjustedBaseBar=\(Int(baseBar/60))мин")

        return generateButtons(vertical: adjustedVertical, horizontal: horizontal, baseBar: baseBar)
    }
}
