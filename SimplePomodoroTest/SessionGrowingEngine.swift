import Foundation

/// Система взращивания сессий
/// Предлагает дополнительные интервалы после завершения основного, используя психологический момент успеха
class SessionGrowingEngine {
    static let shared = SessionGrowingEngine()
    
    // Текущая сессия
    private var currentSession: GrowingSession?
    
    // Колбэки
    var onGrowthOffered: ((Int, @escaping (Bool) -> Void) -> Void)? // (минуты, колбэк с ответом)
    var onSessionCompleted: ((GrowingSession) -> Void)?
    var onWindowCreated: ((SessionGrowthWindow) -> Void)? // Колбэк для позиционирования окна
    var onGrowthAccepted: ((Int) -> Void)? // Колбэк когда пользователь принял взращивание (минуты)
    var onGrowthDeclined: ((TimeInterval) -> Void)? // Колбэк когда пользователь отклонил взращивание (originalDuration)
    
    private init() {}
    
    // MARK: - Основные методы
    
    /// Начинает новую сессию взращивания
    /// - Parameter initialDuration: Начальная длительность в секундах
    func startSession(initialDuration: TimeInterval) {
        let session = GrowingSession(initialDuration: initialDuration)
        currentSession = session
        
        logInfo("SessionGrowing", "🌱 Начата сессия взращивания: \(Int(initialDuration/60)) мин")
    }
    
    /// Добавляет сегмент роста к текущей сессии
    /// - Parameter duration: Длительность сегмента в секундах
    func addGrowthSegment(duration: TimeInterval) {
        guard let session = currentSession else {
            logWarning("SessionGrowing", "Попытка добавить сегмент без активной сессии")
            return
        }
        
        session.addSegment(duration: duration)
        logInfo("SessionGrowing", "🌱 Добавлен сегмент: \(Int(duration/60)) мин (всего: \(Int(session.totalDuration/60)) мин)")
    }
    
    /// Завершает текущую сессию и возвращает новую планку
    /// - Parameter oldBar: Старая планка пользователя в секундах
    /// - Returns: Новая планка в секундах
    func finishSession(oldBar: TimeInterval) -> TimeInterval {
        guard let session = currentSession else {
            logWarning("SessionGrowing", "Попытка завершить сессию без активной сессии")
            return oldBar
        }
        
        // Применяем адаптивную формулу роста
        let newBar = calculateAdaptiveGrowth(totalCompleted: session.totalDuration, oldBar: oldBar)
        
        logInfo("SessionGrowing", "🌱 Сессия завершена: \(Int(session.totalDuration/60)) мин → планка \(Int(oldBar/60))→\(Int(newBar/60)) мин")
        
        // Уведомляем о завершении
        onSessionCompleted?(session)
        
        // Очищаем текущую сессию
        currentSession = nil
        
        return newBar
    }
    
    /// Проверяет, можно ли предложить взращивание для данного интервала
    /// - Parameter duration: Длительность интервала в секундах
    /// - Returns: true, если можно предложить взращивание
    func canOfferGrowth(for duration: TimeInterval) -> Bool {
        let minutes = Int(duration / 60)
        return minutes < 52 // Взращивание только для интервалов < 52 мин
    }
    
    /// Предлагает взращивание после завершения интервала
    /// - Parameter completedDuration: Завершенная длительность в секундах
    /// - Parameter userBar: Планка пользователя в секундах
    func offerGrowth(for completedDuration: TimeInterval, userBar: TimeInterval) {
        let completedMinutes = Int(completedDuration / 60)
        let userBarMinutes = Int(userBar / 60)

        guard canOfferGrowth(for: completedDuration) else {
            logInfo("SessionGrowing", "🌱 Взращивание не предлагается для \(completedMinutes) мин (≥52)")
            return
        }

        // Определяем целевую планку для взращивания
        let growthTargetMinutes = determineGrowthTarget(userBarMinutes: userBarMinutes)

        // Рассчитываем последовательность взращивания с помощью матрицы
        let currentSessionTotal = getCurrentSessionTotalMinutes()
        let startMinutes = currentSessionTotal > 0 ? currentSessionTotal : completedMinutes

        let growthSequence = SessionGrowthMatrix.calculateGrowthSequence(
            startMinutes: startMinutes,
            targetTotalMinutes: growthTargetMinutes,
            mode: .standard
        )

        // Если взращивание не предлагается (последовательность содержит только стартовый интервал)
        guard growthSequence.count > 1 else {
            logInfo("SessionGrowing", "🌱 Взращивание не предлагается: слишком близко к планке")
            return
        }

        // Определяем следующий интервал для предложения
        let nextIntervalIndex = getCurrentSessionSegmentCount() + 1
        guard nextIntervalIndex < growthSequence.count else {
            logInfo("SessionGrowing", "🌱 Взращивание завершено: достигнута планка")
            return
        }

        let nextIntervalMinutes = growthSequence[nextIntervalIndex]

        logInfo("SessionGrowing", "🌱 Предлагаем взращивание: \(nextIntervalMinutes) мин")
        logInfo("SessionGrowing", "🌱 Целевая планка: \(growthTargetMinutes) мин (пользователь: \(userBarMinutes) мин)")
        logInfo("SessionGrowing", "🌱 Полная последовательность: \(growthSequence)")

        // Определяем тип окна взращивания
        let windowType = determineWindowType(
            nextIntervalMinutes: nextIntervalMinutes,
            currentTotal: getCurrentSessionTotalMinutes(),
            growthTarget: growthTargetMinutes,
            userBar: userBarMinutes
        )

        // Показываем окно взращивания
        showGrowthWindow(type: windowType, nextIntervalMinutes: nextIntervalMinutes, originalDuration: completedDuration)
    }
    
    // MARK: - Приватные методы
    


    /// Возвращает общее время текущей сессии в минутах
    private func getCurrentSessionTotalMinutes() -> Int {
        guard let session = currentSession else { return 0 }
        return Int(session.totalDuration / 60)
    }

    /// Возвращает общую длительность текущей сессии в секундах (для PomodoroTimer)
    func getCurrentSessionTotalDuration() -> TimeInterval? {
        guard let session = currentSession else { return nil }
        return session.totalDuration
    }

    /// Возвращает количество сегментов в текущей сессии
    private func getCurrentSessionSegmentCount() -> Int {
        guard let session = currentSession else { return 0 }
        return session.segments.count
    }

    /// Определяет целевую планку для взращивания
    /// - Parameter userBarMinutes: Реальная планка пользователя
    /// - Returns: Целевая планка для взращивания
    private func determineGrowthTarget(userBarMinutes: Int) -> Int {
        if userBarMinutes <= 15 {
            // Новички: взращиваем до неформальной планки 30 мин
            logInfo("SessionGrowing", "🌱 Новичок (планка ≤15 мин): целевая планка 30 мин")
            return 30
        } else {
            // Опытные: взращиваем до реальной планки
            logInfo("SessionGrowing", "🌱 Опытный (планка >15 мин): целевая планка \(userBarMinutes) мин")
            return userBarMinutes
        }
    }

    /// Определяет тип окна взращивания
    private func determineWindowType(nextIntervalMinutes: Int, currentTotal: Int, growthTarget: Int, userBar: Int) -> SessionGrowthWindow.GrowthWindowType {
        let afterNextTotal = currentTotal + nextIntervalMinutes

        if afterNextTotal >= growthTarget {
            // Достигнем целевой планки - проверяем нужно ли предложить добить до максимума
            if growthTarget < 52 {
                let remainingToMax = 52 - afterNextTotal
                return .reachMaximum(remainingMinutes: remainingToMax)
            } else {
                // Уже максимум - просто завершаем
                return .reachTarget(remainingMinutes: nextIntervalMinutes)
            }
        } else {
            // Промежуточное взращивание
            return .continueGrowth(nextMinutes: nextIntervalMinutes)
        }
    }

    /// Показывает окно взращивания
    private func showGrowthWindow(type: SessionGrowthWindow.GrowthWindowType, nextIntervalMinutes: Int, originalDuration: TimeInterval) {
        let window = SessionGrowthWindow(type: type)

        window.onAccept = { [weak self] in
            self?.handleGrowthAccepted(growthMinutes: nextIntervalMinutes, originalDuration: originalDuration)
        }

        window.onDecline = { [weak self] in
            self?.handleGrowthDeclined(originalDuration: originalDuration)
        }

        // Вызываем колбэк для позиционирования окна
        onWindowCreated?(window)

        window.showWithAnimation()
    }
    
    /// Обрабатывает принятие предложения роста
    private func handleGrowthAccepted(growthMinutes: Int, originalDuration: TimeInterval) {
        // Если сессия еще не начата, начинаем ее
        if currentSession == nil {
            startSession(initialDuration: originalDuration)
        }

        // Добавляем сегмент роста
        let growthDuration = TimeInterval(growthMinutes * 60)
        addGrowthSegment(duration: growthDuration)

        logInfo("SessionGrowing", "🌱 Рост принят: +\(growthMinutes) мин")

        // Вызываем колбэк для интеграции с PomodoroTimer
        onGrowthAccepted?(growthMinutes)
    }
    
    /// Обрабатывает отклонение предложения роста
    private func handleGrowthDeclined(originalDuration: TimeInterval) {
        logInfo("SessionGrowing", "🌱 Рост отклонен для \(Int(originalDuration/60)) мин")

        // Передаем originalDuration напрямую (простое решение)
        logInfo("SessionGrowing", "🌱 Передаем originalDuration в колбэк: \(Int(originalDuration/60)) мин")

        // Вызываем колбэк для интеграции с PomodoroTimer
        // ВАЖНО: НЕ очищаем currentSession здесь, это сделает finishSession()
        onGrowthDeclined?(originalDuration)
    }
    
    /// Применяет адаптивную формулу роста планки
    /// - Parameters:
    ///   - totalCompleted: Общее выполненное время в секундах
    ///   - oldBar: Старая планка в секундах
    /// - Returns: Новая планка в секундах
    private func calculateAdaptiveGrowth(totalCompleted: TimeInterval, oldBar: TimeInterval) -> TimeInterval {
        let completedMinutes = Int(totalCompleted / 60)
        let oldBarMinutes = Int(oldBar / 60)

        // Определяем коэффициент роста в зависимости от длительности сессии
        let growthMultiplier: Double

        if completedMinutes <= oldBarMinutes {
            // МИКРО-СЕССИЯ (работал = текущая планка): +30%
            growthMultiplier = 1.30
            logInfo("SessionGrowing", "🌱 Микро-сессия (\(completedMinutes) мин): рост +30%")

        } else if completedMinutes < 30 {
            // СРЕДНЯЯ СЕССИЯ (15-29 мин): +50%
            growthMultiplier = 1.50
            logInfo("SessionGrowing", "🌱 Средняя сессия (\(completedMinutes) мин): рост +50%")

        } else if completedMinutes < 52 {
            // НЕФОРМАЛЬНАЯ СЕССИЯ (30+ мин): +80%
            growthMultiplier = 1.80
            logInfo("SessionGrowing", "🌱 Неформальная сессия (\(completedMinutes) мин): рост +80%")

        } else {
            // ПОЛНАЯ СЕССИЯ (52+ мин): +100%
            growthMultiplier = 2.00
            logInfo("SessionGrowing", "🌱 Полная сессия (\(completedMinutes) мин): рост +100%")
        }

        // Применяем рост к старой планке
        let newBarMinutes = Int(round(Double(oldBarMinutes) * growthMultiplier))
        let newBar = TimeInterval(newBarMinutes * 60)

        // Ограничиваем максимумом 52 минуты
        let finalBar = min(newBar, 52 * 60)

        logInfo("SessionGrowing", "🌱 Планка: \(oldBarMinutes) → \(Int(finalBar/60)) мин (×\(String(format: "%.1f", growthMultiplier)))")

        return finalBar
    }
    
    // MARK: - Отладочные методы
    
    /// Возвращает информацию о текущей сессии
    func getCurrentSessionInfo() -> String {
        guard let session = currentSession else {
            return "Нет активной сессии"
        }
        
        return """
        🌱 Активная сессия взращивания:
        • Начальная длительность: \(Int(session.initialDuration/60)) мин
        • Сегментов роста: \(session.segments.count)
        • Общая длительность: \(Int(session.totalDuration/60)) мин
        • Сегменты: \(session.segments.map { "\(Int($0/60))мин" }.joined(separator: ", "))
        """
    }
    
    /// Сбрасывает текущую сессию (для отладки)
    func resetCurrentSession() {
        currentSession = nil
        logInfo("SessionGrowing", "🌱 Сессия сброшена")
    }
}

// MARK: - Модель сессии

/// Модель растущей сессии
class GrowingSession {
    let initialDuration: TimeInterval
    private(set) var segments: [TimeInterval] = []
    
    var totalDuration: TimeInterval {
        return initialDuration + segments.reduce(0, +)
    }
    
    init(initialDuration: TimeInterval) {
        self.initialDuration = initialDuration
    }
    
    func addSegment(duration: TimeInterval) {
        segments.append(duration)
    }
}

// MARK: - Интеграция с Logger

extension SessionGrowingEngine {
    private func logInfo(_ category: String, _ message: String) {
        Logger.shared.log(.info, category, message)
    }
    
    private func logWarning(_ category: String, _ message: String) {
        Logger.shared.log(.warning, category, message)
    }
}
