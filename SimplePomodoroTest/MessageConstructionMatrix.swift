import Foundation

/// Матрица конструирования сообщений 5x4 для системы раннего вовлечения
/// ВЕРТИКАЛЬ (0-4): Дни без работы
/// ГОРИЗОНТАЛЬ (0-3): Время дня (утро, день, вечер, ночь)
struct MessageConstructionMatrix {
    
    // MARK: - Matrix Data
    
    /// Основная матрица сообщений 5x4 = 20 вариантов
    private static let messageMatrix: [[EngagementMessage]] = [
        // Уровень 0: 0 дней без работы (сегодня уже работал)
        [
            // Утро (0)
            EngagementMessage(
                title: "Продолжаем работу с [focused_project]?",
                subtitle: "Вчера был продуктивный день. Начнем с [current_bar] минут?",
                proposedDuration: 0, // Будет заменено на текущую планку
                buttonText: "Начать работу",
                level: 0,
                timeOfDay: 0
            ),
            // День (1)
            EngagementMessage(
                title: "Время для [focused_project]",
                subtitle: "Хороший момент для продуктивной работы. [current_bar] минут?",
                proposedDuration: 0,
                buttonText: "Приступить",
                level: 0,
                timeOfDay: 1
            ),
            // Вечер (2)
            EngagementMessage(
                title: "Завершим день работой над [focused_project]",
                subtitle: "Последний рывок на [current_bar] минут?",
                proposedDuration: 0,
                buttonText: "Завершить день",
                level: 0,
                timeOfDay: 2
            ),
            // Ночь (3)
            EngagementMessage(
                title: "Поздняя работа с [focused_project]?",
                subtitle: "Если есть вдохновение - [current_bar] минут",
                proposedDuration: 0,
                buttonText: "Поработать",
                level: 0,
                timeOfDay: 3
            )
        ],
        
        // Уровень 1: 1 день без работы
        [
            // Утро (0)
            EngagementMessage(
                title: "Новый день с [focused_project]!",
                subtitle: "Вчера отдыхали, сегодня начнем с [current_bar] минут",
                proposedDuration: 0,
                buttonText: "Начать день",
                level: 1,
                timeOfDay: 0
            ),
            // День (1)
            EngagementMessage(
                title: "Время вернуться к [focused_project]",
                subtitle: "День без работы позади. Начнем с [current_bar] минут?",
                proposedDuration: 0,
                buttonText: "Вернуться к работе",
                level: 1,
                timeOfDay: 1
            ),
            // Вечер (2)
            EngagementMessage(
                title: "Хотя бы немного [focused_project] сегодня?",
                subtitle: "Лучше [current_bar] минут, чем ничего",
                proposedDuration: 0,
                buttonText: "Хотя бы немного",
                level: 1,
                timeOfDay: 2
            ),
            // Ночь (3)
            EngagementMessage(
                title: "Не упустим день совсем",
                subtitle: "[current_bar] минут на [focused_project] перед сном?",
                proposedDuration: 0,
                buttonText: "Не упустить день",
                level: 1,
                timeOfDay: 3
            )
        ],
        
        // Уровень 2: 2-3 дня без работы
        [
            // Утро (0)
            EngagementMessage(
                title: "Пора возвращаться к [focused_project]",
                subtitle: "Два дня отдыха достаточно. Начнем с [current_bar] минут",
                proposedDuration: 0,
                buttonText: "Возвращаемся",
                level: 2,
                timeOfDay: 0
            ),
            // День (1)
            EngagementMessage(
                title: "[focused_project] ждет вас",
                subtitle: "Уже второй день без работы. [current_bar] минут?",
                proposedDuration: 0,
                buttonText: "К работе",
                level: 2,
                timeOfDay: 1
            ),
            // Вечер (2)
            EngagementMessage(
                title: "Не дадим дню пропасть зря",
                subtitle: "Хотя бы [current_bar] минут на [focused_project]",
                proposedDuration: 0,
                buttonText: "Спасти день",
                level: 2,
                timeOfDay: 2
            ),
            // Ночь (3)
            EngagementMessage(
                title: "Последний шанс сегодня",
                subtitle: "[current_bar] минут на [focused_project] - лучше чем ничего",
                proposedDuration: 0,
                buttonText: "Последний шанс",
                level: 2,
                timeOfDay: 3
            )
        ],
        
        // Уровень 3: 4-6 дней без работы
        [
            // Утро (0)
            EngagementMessage(
                title: "Три дня без [focused_project] - много!",
                subtitle: "Пора прервать паузу. Начнем с [current_bar] минут",
                proposedDuration: 0,
                buttonText: "Прервать паузу",
                level: 3,
                timeOfDay: 0
            ),
            // День (1)
            EngagementMessage(
                title: "Возвращаемся к [focused_project]",
                subtitle: "Три дня перерыва - достаточно. [current_bar] минут?",
                proposedDuration: 0,
                buttonText: "Возвращаемся",
                level: 3,
                timeOfDay: 1
            ),
            // Вечер (2)
            EngagementMessage(
                title: "Хотя бы символически",
                subtitle: "[current_bar] минут на [focused_project] - для поддержания ритма",
                proposedDuration: 0,
                buttonText: "Символически",
                level: 3,
                timeOfDay: 2
            ),
            // Ночь (3)
            EngagementMessage(
                title: "Не дадим привычке умереть",
                subtitle: "[current_bar] минут на [focused_project] - поддержим связь",
                proposedDuration: 0,
                buttonText: "Поддержать связь",
                level: 3,
                timeOfDay: 3
            )
        ],
        
        // Уровень 4: 7+ дней без работы (критическая эскалация)
        [
            // Утро (0)
            EngagementMessage(
                title: "Пора возвращаться к жизни!",
                subtitle: "Слишком долго без [focused_project]. Начнем с [current_bar] минут",
                proposedDuration: 0,
                buttonText: "К жизни!",
                level: 4,
                timeOfDay: 0
            ),
            // День (1)
            EngagementMessage(
                title: "Длинная пауза закончена",
                subtitle: "Возвращаемся к [focused_project]. [current_bar] минут для начала",
                proposedDuration: 0,
                buttonText: "Конец паузы",
                level: 4,
                timeOfDay: 1
            ),
            // Вечер (2)
            EngagementMessage(
                title: "Лучше поздно, чем никогда",
                subtitle: "[current_bar] минут на [focused_project] - первый шаг назад",
                proposedDuration: 0,
                buttonText: "Первый шаг",
                level: 4,
                timeOfDay: 2
            ),
            // Ночь (3)
            EngagementMessage(
                title: "Начнем возвращение",
                subtitle: "Даже [current_bar] минут на [focused_project] - уже прогресс",
                proposedDuration: 0,
                buttonText: "Начать возвращение",
                level: 4,
                timeOfDay: 3
            )
        ]
    ]
    
    // MARK: - Public Methods
    
    /// Получает сообщение из матрицы по координатам
    /// - Parameters:
    ///   - vertical: Уровень по дням без работы (0-4)
    ///   - horizontal: Время дня (0-3)
    /// - Returns: Сообщение для показа пользователю
    static func getMessage(vertical: Int, horizontal: Int) -> EngagementMessage {
        let safeVertical = max(0, min(4, vertical))
        let safeHorizontal = max(0, min(3, horizontal))
        
        return messageMatrix[safeVertical][safeHorizontal]
    }
    
    /// Получает все сообщения для определенного уровня дней без работы
    /// - Parameter daysWithoutWork: Количество дней без работы (0-4+)
    /// - Returns: Массив сообщений для всех времен дня
    static func getMessagesForLevel(_ daysWithoutWork: Int) -> [EngagementMessage] {
        let safeLevel = max(0, min(4, daysWithoutWork))
        return messageMatrix[safeLevel]
    }
    
    /// Получает все сообщения для определенного времени дня
    /// - Parameter timeOfDay: Время дня (0-3)
    /// - Returns: Массив сообщений для всех уровней дней без работы
    static func getMessagesForTimeOfDay(_ timeOfDay: Int) -> [EngagementMessage] {
        let safeTimeOfDay = max(0, min(3, timeOfDay))
        return messageMatrix.map { $0[safeTimeOfDay] }
    }
    
    /// Возвращает общее количество сообщений в матрице
    static func getTotalMessageCount() -> Int {
        return messageMatrix.flatMap { $0 }.count
    }
    
    /// Возвращает размеры матрицы
    static func getMatrixDimensions() -> (vertical: Int, horizontal: Int) {
        return (vertical: messageMatrix.count, horizontal: messageMatrix.first?.count ?? 0)
    }
}
