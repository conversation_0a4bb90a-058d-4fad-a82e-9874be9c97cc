//
//  ParallelRestTracker.swift
//  uProd
//
//  Отслеживает параллельный отдых в фоне при длительной неактивности
//  Автоматически засчитывает отдых через 17 минут непрерывной неактивности
//

import Foundation

/// Трекер параллельного отдыха - отслеживает отдых в фоне при неактивности
class ParallelRestTracker {
    
    // MARK: - Constants
    
    /// Порог для автоматического засчитывания отдыха (17 минут)
    private let autoRestThreshold: TimeInterval = 17 * 60
    
    // MARK: - State
    
    /// Время начала текущего периода неактивности
    private var inactivityStartTime: Date?
    
    /// Отслеживается ли сейчас неактивность
    private var isTrackingInactivity = false
    
    /// Был ли уже засчитан отдых для текущего периода
    private var restAlreadyRecorded = false
    
    /// Таймер для проверки достижения порога отдыха
    private var restCheckTimer: Timer?
    
    // MARK: - Dependencies
    
    /// Менеджер статистики для записи автоматического отдыха
    private let statisticsManager: StatisticsManager
    
    // MARK: - Callbacks
    
    /// Вызывается когда автоматический отдых засчитан
    var onAutoRestRecorded: ((TimeInterval) -> Void)?
    
    /// Вызывается при начале отслеживания неактивности
    var onInactivityStarted: (() -> Void)?
    
    /// Вызывается при возвращении активности
    var onActivityResumed: ((TimeInterval) -> Void)?
    
    // MARK: - Initialization
    
    init(statisticsManager: StatisticsManager) {
        self.statisticsManager = statisticsManager
        print("🌿 ParallelRestTracker: Инициализирован")
    }
    
    deinit {
        stopTracking()
    }
    
    // MARK: - Public Methods
    
    /// Начинает отслеживание неактивности (вызывается при первой неактивной минуте)
    func startInactivityTracking() {
        guard !isTrackingInactivity else {
            print("🌿 ParallelRestTracker: Отслеживание уже активно")
            return
        }
        
        let now = Date()
        inactivityStartTime = now
        isTrackingInactivity = true
        restAlreadyRecorded = false
        
        print("🌿 ParallelRestTracker: Начато отслеживание неактивности в \(now)")
        onInactivityStarted?()
        
        // Запускаем таймер проверки каждую минуту
        startRestCheckTimer()
    }
    
    /// Останавливает отслеживание (вызывается при возвращении активности)
    func stopInactivityTracking() {
        guard isTrackingInactivity else { return }
        
        let _ = Date()  // now
        let inactivityDuration = calculateInactivityDuration()
        
        print("🌿 ParallelRestTracker: Остановлено отслеживание, длительность: \(Int(inactivityDuration/60)) мин")
        
        // Если отдых еще не был записан, но длительность достаточная - записываем
        if !restAlreadyRecorded && inactivityDuration >= autoRestThreshold {
            recordAutomaticRest(duration: inactivityDuration)
        }
        
        // Уведомляем о возвращении активности
        onActivityResumed?(inactivityDuration)
        
        // Сбрасываем состояние
        resetState()
    }
    
    /// Полная остановка трекера
    func stopTracking() {
        stopRestCheckTimer()
        resetState()
        print("🌿 ParallelRestTracker: Полная остановка")
    }
    
    /// Возвращает текущую длительность неактивности
    func getCurrentInactivityDuration() -> TimeInterval {
        return calculateInactivityDuration()
    }
    
    /// Проверяет, отслеживается ли сейчас неактивность
    func isCurrentlyTracking() -> Bool {
        return isTrackingInactivity
    }
    
    // MARK: - Private Methods
    
    /// Запускает таймер проверки достижения порога отдыха
    private func startRestCheckTimer() {
        stopRestCheckTimer() // Останавливаем предыдущий таймер
        
        restCheckTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            self?.checkForAutoRest()
        }
    }
    
    /// Останавливает таймер проверки
    private func stopRestCheckTimer() {
        restCheckTimer?.invalidate()
        restCheckTimer = nil
    }
    
    /// Проверяет, достигнут ли порог для автоматического отдыха
    private func checkForAutoRest() {
        guard isTrackingInactivity && !restAlreadyRecorded else { return }
        
        let inactivityDuration = calculateInactivityDuration()
        let minutes = Int(inactivityDuration / 60)
        
        print("🌿 ParallelRestTracker: Проверка отдыха, неактивность: \(minutes) мин")
        
        if inactivityDuration >= autoRestThreshold {
            recordAutomaticRest(duration: inactivityDuration)
        }
    }
    
    /// Записывает автоматический отдых в статистику
    private func recordAutomaticRest(duration: TimeInterval) {
        guard !restAlreadyRecorded else { return }
        
        let minutes = Int(duration / 60)
        print("🌿 ParallelRestTracker: 🎉 Засчитан автоматический отдых (\(minutes) мин)")
        
        // Записываем в статистику как автоматический отдых отличного качества
        statisticsManager.recordRestStatistics(
            duration: duration,
            type: .automatic,
            quality: .excellent,
            context: .systemDetected,
            wasComputerActive: false,
            computerActiveTime: 0
        )
        
        restAlreadyRecorded = true
        onAutoRestRecorded?(duration)
        
        print("🌿 ParallelRestTracker: Автоматический отдых записан в статистику")
    }
    
    /// Вычисляет текущую длительность неактивности
    private func calculateInactivityDuration() -> TimeInterval {
        guard let startTime = inactivityStartTime else { return 0 }
        return Date().timeIntervalSince(startTime)
    }
    
    /// Сбрасывает состояние трекера
    private func resetState() {
        inactivityStartTime = nil
        isTrackingInactivity = false
        restAlreadyRecorded = false
        stopRestCheckTimer()
    }
}

// MARK: - Extensions

extension ParallelRestTracker {
    
    /// Возвращает информацию о текущем состоянии для отладки
    func getDebugInfo() -> String {
        let duration = getCurrentInactivityDuration()
        let minutes = Int(duration / 60)
        
        return """
        ParallelRestTracker Debug:
        - Отслеживание: \(isTrackingInactivity)
        - Длительность: \(minutes) мин
        - Отдых записан: \(restAlreadyRecorded)
        - Порог отдыха: \(Int(autoRestThreshold/60)) мин
        """
    }
}
