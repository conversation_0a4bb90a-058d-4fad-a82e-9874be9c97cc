import Cocoa
import Foundation

/// Простое окно для показа текущего состояния системы раннего вовлечения
class EarlyEngagementStatusWindow: NSWindow {
    
    private var statusLabel: NSTextField!
    private var currentBarLabel: NSTextField!
    private var lastWorkLabel: NSTextField!
    private var daysLabel: NSTextField!
    private var timeOfDayLabel: NSTextField!
    private var messageLabel: NSTextField!
    private var showMessageButton: NSButton!
    private var refreshButton: NSButton!
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 500, height: 400), styleMask: [.titled, .closable, .resizable], backing: .buffered, defer: false)
        
        self.title = "🌅 Состояние системы раннего вовлечения"
        self.center()
        
        setupUI()
        updateStatus()
    }
    
    private func setupUI() {
        let contentView = NSView(frame: self.contentView!.bounds)
        contentView.autoresizingMask = [.width, .height]
        self.contentView = contentView
        
        var yPosition: CGFloat = 350
        let margin: CGFloat = 20
        let labelHeight: CGFloat = 20
        let spacing: CGFloat = 10
        
        // Заголовок
        statusLabel = createLabel(text: "📊 ТЕКУЩЕЕ СОСТОЯНИЕ СИСТЕМЫ", fontSize: 16, bold: true)
        statusLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: 25)
        contentView.addSubview(statusLabel)
        yPosition -= 35
        
        // Текущая планка
        currentBarLabel = createLabel(text: "⏱️ Текущая планка: загрузка...", fontSize: 14)
        currentBarLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: labelHeight)
        contentView.addSubview(currentBarLabel)
        yPosition -= (labelHeight + spacing)
        
        // Последняя работа
        lastWorkLabel = createLabel(text: "🕐 Последняя работа: загрузка...", fontSize: 14)
        lastWorkLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: labelHeight)
        contentView.addSubview(lastWorkLabel)
        yPosition -= (labelHeight + spacing)
        
        // Дни без работы
        daysLabel = createLabel(text: "📅 Дни без работы: загрузка...", fontSize: 14)
        daysLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: labelHeight)
        contentView.addSubview(daysLabel)
        yPosition -= (labelHeight + spacing)
        
        // Время дня
        timeOfDayLabel = createLabel(text: "🌅 Время дня: загрузка...", fontSize: 14)
        timeOfDayLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: labelHeight)
        contentView.addSubview(timeOfDayLabel)
        yPosition -= (labelHeight + spacing)
        
        // Текущее сообщение
        messageLabel = createLabel(text: "💬 Текущее сообщение: загрузка...", fontSize: 14)
        messageLabel.frame = NSRect(x: margin, y: yPosition, width: 460, height: 40)
        messageLabel.maximumNumberOfLines = 2
        contentView.addSubview(messageLabel)
        yPosition -= 50
        
        // Кнопка показа сообщения
        showMessageButton = NSButton(frame: NSRect(x: margin, y: yPosition, width: 200, height: 30))
        showMessageButton.title = "🌅 Показать сообщение"
        showMessageButton.bezelStyle = .rounded
        showMessageButton.target = self
        showMessageButton.action = #selector(showCurrentMessage)
        contentView.addSubview(showMessageButton)
        
        // Кнопка обновления
        refreshButton = NSButton(frame: NSRect(x: margin + 220, y: yPosition, width: 120, height: 30))
        refreshButton.title = "🔄 Обновить"
        refreshButton.bezelStyle = .rounded
        refreshButton.target = self
        refreshButton.action = #selector(refreshStatus)
        contentView.addSubview(refreshButton)
        yPosition -= 50
        
        // Информационный текст
        let infoLabel = createLabel(text: "ℹ️ Это окно показывает текущее состояние системы раннего вовлечения.\nВы можете увидеть, какое сообщение показалось бы утром при пробуждении.", fontSize: 12)
        infoLabel.frame = NSRect(x: margin, y: yPosition - 40, width: 460, height: 40)
        infoLabel.maximumNumberOfLines = 3
        contentView.addSubview(infoLabel)
    }
    
    private func createLabel(text: String, fontSize: CGFloat, bold: Bool = false) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.isEditable = false
        label.isSelectable = true
        label.backgroundColor = NSColor.clear
        label.textColor = NSColor.labelColor
        
        if bold {
            label.font = NSFont.boldSystemFont(ofSize: fontSize)
        } else {
            label.font = NSFont.systemFont(ofSize: fontSize)
        }
        
        return label
    }
    
    @objc private func refreshStatus() {
        updateStatus()
        Logger.shared.log(.info, "EarlyEngagement", "🔄 Обновлено состояние системы раннего вовлечения")
    }
    
    @objc private func showCurrentMessage() {
        let system = EarlyEngagementSystem.shared
        
        // Получаем текущее сообщение
        let message = system.getCurrentMessage()
        
        // Показываем его через систему
        system.forceShowMessage()
        
        Logger.shared.log(.info, "EarlyEngagement", "🌅 Принудительно показано сообщение: \(message.title)")
    }
    
    private func updateStatus() {
        let system = EarlyEngagementSystem.shared
        
        // Текущая планка
        let currentBar = system.debugCurrentUserBar
        let barMinutes = Int(currentBar / 60)
        currentBarLabel.stringValue = "⏱️ Текущая планка: \(barMinutes) минут"
        
        // Последняя работа
        if let lastWork = system.debugLastWorkTime {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            lastWorkLabel.stringValue = "🕐 Последняя работа: \(formatter.string(from: lastWork))"
        } else {
            lastWorkLabel.stringValue = "🕐 Последняя работа: НЕТ ДАННЫХ"
        }
        
        // Дни без работы
        let daysWithoutWork = system.debugDaysWithoutWork
        daysLabel.stringValue = "📅 Дни без работы: \(daysWithoutWork)"
        
        // Время дня
        let timeOfDay = system.debugCurrentTimeOfDay
        let timeNames = ["Утро (6-12)", "День (12-16)", "Вечер (16-20)", "Поздно (20-6)"]
        timeOfDayLabel.stringValue = "🌅 Время дня: \(timeNames[timeOfDay]) - уровень \(timeOfDay)"
        
        // Текущее сообщение
        let message = system.getCurrentMessage()
        let messageDuration = Int(message.proposedDuration / 60)
        messageLabel.stringValue = "💬 Текущее сообщение: \"\(message.title)\"\n⏰ Длительность: \(messageDuration) минут"
        
        // Обновляем заголовок с текущим временем
        let timeFormatter = DateFormatter()
        timeFormatter.timeStyle = .medium
        statusLabel.stringValue = "📊 СОСТОЯНИЕ НА \(timeFormatter.string(from: Date()).uppercased())"
    }
    
    override func makeKeyAndOrderFront(_ sender: Any?) {
        super.makeKeyAndOrderFront(sender)
        updateStatus() // Обновляем при показе окна
    }
}
