import Cocoa

// Специальный view, который пропускает клики мыши
class ClickThroughView: NSView {
    override func hitTest(_ point: NSPoint) -> NSView? {
        // Возвращаем nil, чтобы клики проходили сквозь
        return nil
    }
}

class CountdownWindow: NSWindow {
    private var countdownLabel: NSTextField!
    private var instructionLabel: NSTextField!
    private var currentCount: Int = 5
    private var timer: Timer?
    private var completion: (() -> Void)?
    private var onCancel: (() -> Void)?
    
    init(duration: Int = 5, completion: @escaping () -> Void, onCancel: (() -> Void)? = nil) {
        self.currentCount = duration
        self.completion = completion
        self.onCancel = onCancel
        
        // Получаем размер главного экрана для центрирования
        let screenFrame = NSScreen.main?.frame ?? NSRect(x: 0, y: 0, width: 1920, height: 1080)

        // Создаем компактное окно для цифр (400x300 для больших цифр)
        let windowSize = NSSize(width: 400, height: 300)
        let windowOrigin = NSPoint(
            x: screenFrame.midX - windowSize.width / 2,
            y: screenFrame.midY - windowSize.height / 2
        )
        let windowFrame = NSRect(origin: windowOrigin, size: windowSize)

        super.init(
            contentRect: windowFrame,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
        setupUI()
        startCountdown()
    }
    
    private func setupWindow() {
        // Настройки окна - компактное, прозрачное
        self.level = .screenSaver
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
        self.isOpaque = false
        self.hasShadow = false
        self.ignoresMouseEvents = false  // Нужно для ESC

        // Полностью прозрачный фон
        self.backgroundColor = NSColor.clear

        // Обработка клавиш
        self.acceptsMouseMovedEvents = false
        self.makeFirstResponder(self)
    }
    
    private func setupUI() {
        guard let contentView = self.contentView else { return }

        // Заменяем contentView на ClickThroughView для пропуска кликов
        let clickThroughView = ClickThroughView()
        self.contentView = clickThroughView

        // Создаем полупрозрачный фон для видимости цифр (только в центре)
        let backgroundView = NSView()
        backgroundView.wantsLayer = true
        backgroundView.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.7).cgColor
        backgroundView.layer?.cornerRadius = 20
        backgroundView.translatesAutoresizingMaskIntoConstraints = false

        // Основная цифра обратного отсчета
        countdownLabel = NSTextField(labelWithString: "\(currentCount)")
        countdownLabel.font = NSFont.systemFont(ofSize: 150, weight: .ultraLight)  // Увеличили для лучшей видимости
        countdownLabel.textColor = NSColor.white
        countdownLabel.alignment = .center
        countdownLabel.translatesAutoresizingMaskIntoConstraints = false
        countdownLabel.isBezeled = false
        countdownLabel.drawsBackground = false

        // Настройка layer для правильной анимации из центра
        countdownLabel.wantsLayer = true

        // Инструкция внизу
        instructionLabel = NSTextField(labelWithString: "Press ESC to postpone")
        instructionLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)  // Уменьшили размер
        instructionLabel.textColor = NSColor.white.withAlphaComponent(0.8)
        instructionLabel.alignment = .center
        instructionLabel.translatesAutoresizingMaskIntoConstraints = false
        instructionLabel.isBezeled = false
        instructionLabel.drawsBackground = false

        clickThroughView.addSubview(backgroundView)
        clickThroughView.addSubview(countdownLabel)
        clickThroughView.addSubview(instructionLabel)

        // Constraints для компактного layout
        NSLayoutConstraint.activate([
            // Фон только в центре (меньше окна)
            backgroundView.centerXAnchor.constraint(equalTo: clickThroughView.centerXAnchor),
            backgroundView.centerYAnchor.constraint(equalTo: clickThroughView.centerYAnchor),
            backgroundView.widthAnchor.constraint(equalToConstant: 250),
            backgroundView.heightAnchor.constraint(equalToConstant: 200),

            // Цифра в центре окна
            countdownLabel.centerXAnchor.constraint(equalTo: clickThroughView.centerXAnchor),
            countdownLabel.centerYAnchor.constraint(equalTo: clickThroughView.centerYAnchor, constant: -10),

            // Инструкция внизу
            instructionLabel.centerXAnchor.constraint(equalTo: clickThroughView.centerXAnchor),
            instructionLabel.bottomAnchor.constraint(equalTo: backgroundView.bottomAnchor, constant: -10)
        ])
    }
    
    private func startCountdown() {
        updateCountdownDisplay()
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.tick()
        }
    }
    
    private func tick() {
        currentCount -= 1
        
        if currentCount <= 0 {
            finishCountdown()
        } else {
            updateCountdownDisplay()
        }
    }
    
    private func updateCountdownDisplay() {
        // Обновляем текст
        countdownLabel.stringValue = "\(currentCount)"

        // Цветовая индикация (зеленый → желтый → красный)
        let color: NSColor
        switch currentCount {
        case 4...5:
            color = NSColor.systemGreen
        case 2...3:
            color = NSColor.systemYellow
        case 1:
            color = NSColor.systemRed
        default:
            color = NSColor.white
        }

        countdownLabel.textColor = color

        // Простая и надежная анимация через layer transform
        // Сначала делаем невидимым и маленьким
        countdownLabel.alphaValue = 0.0
        countdownLabel.layer?.transform = CATransform3DMakeScale(0.3, 0.3, 1.0)

        // Анимируем появление
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.5
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            // Возвращаем к нормальному состоянию
            countdownLabel.animator().alphaValue = 1.0
            countdownLabel.layer?.transform = CATransform3DIdentity
        })
        
        // Пульсация для последних секунд
        if currentCount <= 2 {
            // Добавляем пульсацию через небольшую задержку после основной анимации
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) { [weak self] in
                guard let self = self else { return }

                NSAnimationContext.runAnimationGroup({ context in
                    context.duration = 0.3
                    context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                    self.countdownLabel.layer?.transform = CATransform3DMakeScale(1.15, 1.15, 1.0)
                }) {
                    NSAnimationContext.runAnimationGroup({ context in
                        context.duration = 0.3
                        context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                        self.countdownLabel.layer?.transform = CATransform3DIdentity
                    })
                }
            }
        }
    }
    
    private func finishCountdown() {
        timer?.invalidate()
        timer = nil
        
        // Анимация исчезновения окна
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.5
            self.animator().alphaValue = 0.0
        }) {
            self.orderOut(nil)
            self.completion?()
        }
    }
    
    private func cancelCountdown() {
        timer?.invalidate()
        timer = nil
        
        // Анимация исчезновения
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            self.animator().alphaValue = 0.0
        }) {
            self.orderOut(nil)
            self.onCancel?()
        }
    }
    
    // Обработка клавиш
    override func keyDown(with event: NSEvent) {
        if event.keyCode == 53 { // ESC key
            cancelCountdown()
        } else {
            super.keyDown(with: event)
        }
    }
    
    // Принимаем статус первого респондера для обработки клавиш
    override var canBecomeKey: Bool {
        return true
    }
    
    override var acceptsFirstResponder: Bool {
        return true
    }
}
