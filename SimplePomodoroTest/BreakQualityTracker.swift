import Foundation

/// Трекер качества отдыха - отслеживает процент времени без активности
class BreakQualityTracker {

    // MARK: - Properties

    private let activityDetector = ActivityDetector()
    private var checkTimer: Timer?

    // НОВОЕ: Интеграция с 4-бандовой системой активности
    private let minuteActivityTracker: MinuteActivityTracker?
    private var useNewActivitySystem = false
    
    // Статистика текущего отдыха
    private var totalMinutes = 0
    private var activeMinutes = 0
    private var isTracking = false
    
    // Колбэк для завершения отдыха
    var onBreakCompleted: ((Int) -> Void)?

    // MARK: - Инициализация

    /// Инициализация с опциональным MinuteActivityTracker для 4-бандовой системы
    init(minuteActivityTracker: MinuteActivityTracker? = nil) {
        self.minuteActivityTracker = minuteActivityTracker
        self.useNewActivitySystem = minuteActivityTracker != nil

        if useNewActivitySystem {
            print("📊 BreakQualityTracker: Инициализация с 4-бандовой системой активности")
        } else {
            print("📊 BreakQualityTracker: Инициализация со старой системой активности")
        }
    }

    // MARK: - Public Methods
    
    /// Начинает отслеживание качества отдыха
    func startTracking() {
        guard !isTracking else { return }
        
        print("📊 BreakQualityTracker: Начинаем отслеживание качества отдыха")
        isTracking = true
        totalMinutes = 0
        activeMinutes = 0
        
        // Запускаем детектор активности
        activityDetector.resetActivity()
        activityDetector.startMonitoring()
        
        // Запускаем таймер проверки каждую минуту
        checkTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            if self?.useNewActivitySystem == true {
                self?.checkActivityForMinuteNew()
            } else {
                self?.checkActivityForMinute()
            }
        }
    }
    
    /// Останавливает отслеживание и возвращает процент качества отдыха
    func stopTracking() -> Int {
        guard isTracking else { return 100 }
        
        print("📊 BreakQualityTracker: Останавливаем отслеживание качества отдыха")
        isTracking = false
        
        // Останавливаем детектор активности
        activityDetector.stopMonitoring()
        
        // Останавливаем таймер
        checkTimer?.invalidate()
        checkTimer = nil
        
        // Рассчитываем качество отдыха
        let qualityPercentage = calculateQualityPercentage()
        
        print("📊 BreakQualityTracker: Качество отдыха: \(qualityPercentage)% (отдыхали \(totalMinutes - activeMinutes) из \(totalMinutes) минут)")
        
        // Вызываем колбэк
        onBreakCompleted?(qualityPercentage)
        
        return qualityPercentage
    }
    
    /// Принудительно останавливает отслеживание без расчета
    func forceStop() {
        guard isTracking else { return }
        
        print("📊 BreakQualityTracker: Принудительная остановка отслеживания")
        isTracking = false
        
        activityDetector.stopMonitoring()
        checkTimer?.invalidate()
        checkTimer = nil
    }
    
    /// Возвращает текущую статистику отдыха
    func getCurrentStats() -> (totalMinutes: Int, activeMinutes: Int, qualityPercentage: Int) {
        let quality = calculateQualityPercentage()
        return (totalMinutes, activeMinutes, quality)
    }

    /// Переключение между старой и новой системами активности
    func setUseNewActivitySystem(_ useNew: Bool) {
        // Можем переключать только если есть MinuteActivityTracker
        if useNew && minuteActivityTracker != nil {
            useNewActivitySystem = true
            print("📊 BreakQualityTracker: Переключение на 4-бандовую систему активности")
        } else {
            useNewActivitySystem = false
            print("📊 BreakQualityTracker: Переключение на старую систему активности")
        }
    }

    /// Возвращает отладочную информацию о текущей системе
    func getDebugInfo() -> String {
        let systemType = useNewActivitySystem ? "4-бандовая" : "старая"
        let hasTracker = minuteActivityTracker != nil ? "есть" : "нет"
        return "Система: \(systemType), MinuteActivityTracker: \(hasTracker), отслеживание: \(isTracking ? "активно" : "неактивно")"
    }
    
    // MARK: - Private Methods
    
    private func checkActivityForMinute() {
        totalMinutes += 1

        if activityDetector.wasActiveInLastMinute() {
            activeMinutes += 1
            print("📊 BreakQualityTracker: Минута \(totalMinutes) - АКТИВНОСТЬ обнаружена (старая система)")
        } else {
            print("📊 BreakQualityTracker: Минута \(totalMinutes) - отдых (старая система)")
        }

        let currentQuality = calculateQualityPercentage()
        print("📊 BreakQualityTracker: Текущее качество отдыха: \(currentQuality)%")
    }

    /// НОВЫЙ метод проверки активности через 4-бандовую систему
    private func checkActivityForMinuteNew() {
        totalMinutes += 1

        guard let tracker = minuteActivityTracker else {
            print("📊 BreakQualityTracker: ОШИБКА - нет MinuteActivityTracker для новой системы")
            return
        }

        let wasActive = tracker.wasCurrentMinuteActive()

        if wasActive {
            activeMinutes += 1
            print("📊 BreakQualityTracker: Минута \(totalMinutes) - АКТИВНОСТЬ обнаружена (4-бандовая система)")
        } else {
            print("📊 BreakQualityTracker: Минута \(totalMinutes) - отдых (4-бандовая система)")
        }

        let currentQuality = calculateQualityPercentage()
        print("📊 BreakQualityTracker: Текущее качество отдыха: \(currentQuality)% (4-бандовая)")
    }
    
    private func calculateQualityPercentage() -> Int {
        guard totalMinutes > 0 else { return 100 }
        
        let restMinutes = totalMinutes - activeMinutes
        let percentage = (restMinutes * 100) / totalMinutes
        
        return max(0, min(100, percentage))
    }
    
    deinit {
        forceStop()
    }
}
