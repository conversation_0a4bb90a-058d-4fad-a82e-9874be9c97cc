import Foundation

/// Градационная система роста планок
/// Обеспечивает оптимальный рост планки в зависимости от текущего размера
class GradualGrowthSystem {
    
    /// Рассчитывает новую планку по градационной системе роста
    /// - Parameter currentBarMinutes: Текущая планка в минутах
    /// - Returns: Новая планка в минутах (максимум 52)
    static func calculateGrowth(currentBarMinutes: Int) -> Int {
        let newBar: Int
        
        switch currentBarMinutes {
        case 3...7:
            // 🚀 Быстрый старт: +60%
            newBar = Int(round(Double(currentBarMinutes) * 1.60))
        case 8...15:
            // ⚡ Активный рост: +40%
            newBar = Int(round(Double(currentBarMinutes) * 1.40))
        case 16...25:
            // ⚡ Активный рост: +25%
            newBar = Int(round(Double(currentBarMinutes) * 1.25))
        case 26...40:
            // 🎯 Стабилизация: +15%
            newBar = Int(round(Double(currentBarMinutes) * 1.15))
        case 41...52:
            // 🎯 Финал: +10%
            newBar = Int(round(Double(currentBarMinutes) * 1.10))
        default:
            // Безопасность для краевых случаев
            newBar = currentBarMinutes + 1
        }
        
        return min(newBar, 52) // Максимум 52 минуты
    }
    
    /// Рассчитывает новую планку по градационной системе роста (TimeInterval версия)
    /// - Parameter currentBar: Текущая планка в секундах
    /// - Returns: Новая планка в секундах (максимум 52 минуты)
    static func calculateGrowth(currentBar: TimeInterval) -> TimeInterval {
        let currentMinutes = Int(currentBar / 60)
        let newMinutes = calculateGrowth(currentBarMinutes: currentMinutes)
        return TimeInterval(newMinutes * 60)
    }
    
    /// Возвращает процент роста для текущей планки
    /// - Parameter currentBarMinutes: Текущая планка в минутах
    /// - Returns: Множитель роста (например, 1.60 для +60%)
    static func getGrowthMultiplier(currentBarMinutes: Int) -> Double {
        switch currentBarMinutes {
        case 3...7:   return 1.60  // +60%
        case 8...15:  return 1.40  // +40%
        case 16...25: return 1.25  // +25%
        case 26...40: return 1.15  // +15%
        case 41...52: return 1.10  // +10%
        default:      return 1.05  // +5% для безопасности
        }
    }
    
    /// Возвращает текстовое описание роста для отладки
    /// - Parameter currentBarMinutes: Текущая планка в минутах
    /// - Returns: Строка с описанием роста (например, "+60%")
    static func getGrowthDescription(currentBarMinutes: Int) -> String {
        let multiplier = getGrowthMultiplier(currentBarMinutes: currentBarMinutes)
        let percentage = Int((multiplier - 1.0) * 100)
        return "+\(percentage)%"
    }
    
    /// Возвращает категорию роста для текущей планки
    /// - Parameter currentBarMinutes: Текущая планка в минутах
    /// - Returns: Название категории роста
    static func getGrowthCategory(currentBarMinutes: Int) -> String {
        switch currentBarMinutes {
        case 3...7:   return "🚀 Быстрый старт"
        case 8...15:  return "⚡ Активный рост"
        case 16...25: return "⚡ Активный рост"
        case 26...40: return "🎯 Стабилизация"
        case 41...52: return "🎯 Финал"
        default:      return "🔧 Особый случай"
        }
    }
    
    /// Рассчитывает время достижения 52 минут при текущей планке
    /// - Parameter currentBarMinutes: Текущая планка в минутах
    /// - Returns: Примерное количество дней до достижения 52 минут
    static func estimateDaysToMaximum(currentBarMinutes: Int) -> Int {
        var bar = currentBarMinutes
        var days = 0
        
        while bar < 52 && days < 100 { // Защита от бесконечного цикла
            bar = calculateGrowth(currentBarMinutes: bar)
            days += 1
        }
        
        return days
    }
    
    /// Отладочная информация о градационной системе
    /// - Parameter currentBarMinutes: Текущая планка в минутах
    /// - Returns: Детальная информация о росте
    static func getDebugInfo(currentBarMinutes: Int) -> String {
        let newBar = calculateGrowth(currentBarMinutes: currentBarMinutes)
        let category = getGrowthCategory(currentBarMinutes: currentBarMinutes)
        let description = getGrowthDescription(currentBarMinutes: currentBarMinutes)
        let daysToMax = estimateDaysToMaximum(currentBarMinutes: currentBarMinutes)
        
        return """
        📊 Градационная система роста:
        • Текущая планка: \(currentBarMinutes) мин
        • Категория: \(category)
        • Рост: \(description)
        • Новая планка: \(newBar) мин
        • До 52 мин: ~\(daysToMax) дней
        """
    }
}

// MARK: - Расширения для интеграции

extension GradualGrowthSystem {
    
    /// Применяет градационный рост только при успехе (для замены старого +10%)
    /// - Parameters:
    ///   - currentBar: Текущая планка
    ///   - success: Успешно ли выполнен интервал
    ///   - failureMultiplier: Множитель при неудаче (по умолчанию 0.85 = -15%)
    /// - Returns: Новая планка
    static func adaptUserBar(currentBar: TimeInterval, success: Bool, failureMultiplier: Double = 0.85) -> TimeInterval {
        if success {
            return calculateGrowth(currentBar: currentBar)
        } else {
            // При неудаче используем старую логику уменьшения
            let newBar = currentBar * failureMultiplier
            return max(newBar, 2 * 60) // Минимум 2 минуты
        }
    }
    
    /// Применяет градационный рост для уровня 0 (работал вчера)
    /// - Parameter currentBar: Текущая планка
    /// - Returns: Новая планка с учетом максимума 52 минуты
    static func applyLevel0Growth(currentBar: TimeInterval) -> TimeInterval {
        let newBar = calculateGrowth(currentBar: currentBar)
        return min(newBar, 52 * 60) // Максимум 52 минуты
    }
}
