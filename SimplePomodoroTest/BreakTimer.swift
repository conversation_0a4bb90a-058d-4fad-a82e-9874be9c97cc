import Foundation
import Cocoa
import IOKit
import IOKit.pwr_mgt

/// Класс для управления таймером отдыха и отслеживания активности пользователя
class BreakTimer: ObservableObject {
    
    // MARK: - Константы
    static let shortBreakDuration: TimeInterval = 17 * 60 // 17 минут
    static let longBreakDuration: TimeInterval = 90 * 60 // 90 минут
    static let defaultBreakDuration: TimeInterval = shortBreakDuration
    
    // MARK: - Состояние таймера отдыха
    @Published var isActive: Bool = false
    @Published var timeRemaining: TimeInterval = 0
    @Published var wasComputerActiveThisBreak: Bool = false
    @Published var computerActiveTime: TimeInterval = 0 // Время активности компьютера во время отдыха
    
    // MARK: - Приватные свойства
    private var breakTimer: Timer?
    private var activityMonitorTimer: Timer?
    private var breakStartTime: Date?
    private var breakEndTime: Date? // Абсолютное время окончания отдыха
    private var lastActivityCheck: Date?
    private var breakDuration: TimeInterval = defaultBreakDuration

    // Отслеживание полноценных отдыхов
    private var isFullBreak: Bool = false
    private var naturallyCompleted: Bool = false

    // Трекер качества отдыха
    private let qualityTracker: BreakQualityTracker

    // НОВОЕ: Интеграция с 20-сегментной системой активности
    private let minuteActivityTracker: MinuteActivityTracker
    private var useNewActivitySystem = true  // Флаг для переключения между старой и новой системой
    
    // MARK: - Колбэки
    var onBreakCompleted: (() -> Void)?
    var onTimeUpdate: ((TimeInterval) -> Void)?
    var onActivityDetected: (() -> Void)?
    var onBreakQualityCalculated: ((Int) -> Void)?  // Процент качества отдыха

    // Звуковой менеджер
    var soundManager: SoundManager?
    
    // MARK: - Инициализация
    init(minuteActivityTracker: MinuteActivityTracker? = nil) {
        // Используем переданный MinuteActivityTracker или создаем новый для обратной совместимости
        self.minuteActivityTracker = minuteActivityTracker ?? MinuteActivityTracker()
        logInfo("BreakTimer", "🌿 BreakTimer инициализирован с \(minuteActivityTracker != nil ? "переданным" : "новым") MinuteActivityTracker")

        // НОВОЕ: Инициализируем qualityTracker с MinuteActivityTracker для 20-сегментной системы
        self.qualityTracker = BreakQualityTracker(minuteActivityTracker: self.minuteActivityTracker)

        timeRemaining = breakDuration
        // ОТКЛЮЧЕНО: Используем унифицированную систему сна через AppDelegate
        // setupSleepWakeNotifications()
    }

    deinit {
        // ОТКЛЮЧЕНО: Используем унифицированную систему сна
        // removeSleepWakeNotifications()
    }
    
    // MARK: - Основные методы
    
    /// Начинает таймер отдыха
    func startBreak(duration: TimeInterval = defaultBreakDuration) {
        logInfo("BreakTimer", "🌿 startBreak вызван: duration=\(duration), isActive=\(isActive)")
        guard !isActive else {
            logWarning("BreakTimer", "🌿 startBreak отклонен - уже активен")
            return
        }

        breakDuration = duration
        timeRemaining = duration
        isActive = true
        breakStartTime = Date()
        breakEndTime = Date().addingTimeInterval(duration) // Устанавливаем абсолютное время окончания
        lastActivityCheck = Date()
        wasComputerActiveThisBreak = false
        computerActiveTime = 0
        logInfo("BreakTimer", "🌿 Параметры установлены: breakDuration=\(breakDuration), isActive=\(isActive)")

        // Отслеживание полноценных отдыхов
        // Полноценный отдых - это стандартный короткий (17 мин) или длинный (90 мин) отдых
        isFullBreak = (duration == Self.shortBreakDuration || duration == Self.longBreakDuration)
        naturallyCompleted = false

        logInfo("BreakTimer", "🌿 Запускаем startBreakTimer()")
        startBreakTimer()
        logInfo("BreakTimer", "🌿 Запускаем startActivityMonitoring()")
        startActivityMonitoring()

        // Запускаем трекер качества отдыха
        qualityTracker.startTracking()
        qualityTracker.onBreakCompleted = { [weak self] qualityPercentage in
            self?.onBreakQualityCalculated?(qualityPercentage)
        }

        let breakType = isFullBreak ? "полноценный" : "тестовый"
        logInfo("BreakTimer", "🌿 BreakTimer: Отдых начат. Продолжительность: \(Int(duration/60)) мин (\(breakType))")
        print("🌿 BreakTimer: Отдых начат. Продолжительность: \(Int(duration/60)) мин (\(breakType))")
    }
    
    /// Останавливает таймер отдыха
    func stopBreak() {
        guard isActive else { return }

        // Останавливаем трекер качества отдыха
        let qualityPercentage = qualityTracker.stopTracking()

        stopAllTimers()
        resetBreak()

        print("🌿 BreakTimer: Отдых остановлен вручную. Качество: \(qualityPercentage)%")
    }
    
    /// Завершает отдых
    func completeBreak() {
        guard isActive else { return }

        // Вычисляем фактическую продолжительность отдыха на основе времени начала
        let actualBreakDuration = breakStartTime != nil ? Date().timeIntervalSince(breakStartTime!) : breakDuration

        // Отмечаем, что отдых естественно завершился
        naturallyCompleted = true

        // Звук завершения отдыха воспроизводится в AppDelegate при показе окна завершения отдыха
        NSLog("🌿 BreakTimer: Отдых завершен. Звук будет воспроизведен при показе окна завершения отдыха")

        // Останавливаем трекер качества отдыха
        let _ = qualityTracker.stopTracking()  // qualityPercentage

        stopAllTimers()
        resetBreak()

        onBreakCompleted?()

        print("🌿 BreakTimer: Отдых завершен. Фактическая продолжительность: \(Int(actualBreakDuration/60)) мин")
        print("🌿 BreakTimer: Компьютер был активен: \(wasComputerActiveThisBreak)")
        print("🌿 BreakTimer: Время активности компьютера: \(Int(computerActiveTime)) сек")
    }
    
    // MARK: - Приватные методы
    
    private func startBreakTimer() {
        breakTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateBreakTimer()
        }
    }
    
    private func updateBreakTimer() {
        // Вычисляем оставшееся время на основе абсолютного времени окончания
        guard let endTime = breakEndTime else {
            NSLog("❌ BreakTimer: breakEndTime не установлено")
            return
        }

        let now = Date()
        timeRemaining = max(0, endTime.timeIntervalSince(now))

        NSLog("🌿 BreakTimer: Обновление таймера. Осталось: \(formatTime(timeRemaining))")
        onTimeUpdate?(timeRemaining)

        if timeRemaining <= 0 {
            NSLog("🌿 BreakTimer: Время отдыха истекло, завершаем отдых")
            completeBreak()
        }
    }
    
    private func startActivityMonitoring() {
        if useNewActivitySystem {
            // НОВОЕ: Используем 20-сегментную систему активности
            NSLog("🍃 BreakTimer: Запуск 20-сегментного мониторинга активности")
            minuteActivityTracker.startTracking()

            // Проверяем активность каждую минуту через новую систему
            activityMonitorTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
                self?.checkUserActivityNew()
            }
        } else {
            // СТАРОЕ: Запускаем мониторинг активности каждую минуту
            NSLog("🍃 BreakTimer: Запуск мониторинга активности (проверка каждую минуту)")
            activityMonitorTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
                self?.checkUserActivity()
            }
        }
    }
    
    /// НОВЫЙ метод проверки активности через 20-сегментную систему с погрешностью
    private func checkUserActivityNew() {
        guard let lastCheck = lastActivityCheck else { return }

        let now = Date()
        let timeSinceLastCheck = now.timeIntervalSince(lastCheck)

        // ВАЖНО: Используем forceCompleteCurrentMinute() для обнаружения текущей активности
        // Это нужно чтобы поймать активность в текущем сегменте (пользователь активен прямо сейчас)
        // ИСКЛЮЧЕНИЕ: BreakTimer - единственная система которая должна использовать forceCompleteCurrentMinute()
        // вместо wasCurrentMinuteActive(), потому что нужно обнаруживать активность в реальном времени
        // Все остальные системы должны использовать wasCurrentMinuteActive() для унификации
        let wasMinuteActive = minuteActivityTracker.forceCompleteCurrentMinute()

        if wasMinuteActive {
            wasComputerActiveThisBreak = true
            computerActiveTime += timeSinceLastCheck
            onActivityDetected?()

            NSLog("🔍 BreakTimer: Обнаружена активность через 20-сегментную систему (2+ активных сегмента) во время отдыха")
        } else {
            NSLog("🔍 BreakTimer: Пользователь неактивен (менее 2 активных сегментов), отдых продолжается")
        }

        lastActivityCheck = now
    }

    /// СТАРЫЙ метод проверки активности (для обратной совместимости)
    private func checkUserActivity() {
        guard let lastCheck = lastActivityCheck else { return }

        let now = Date()
        let timeSinceLastCheck = now.timeIntervalSince(lastCheck)

        // Проверяем активность пользователя через системные API
        if isUserActive() {
            wasComputerActiveThisBreak = true
            computerActiveTime += timeSinceLastCheck
            onActivityDetected?()

            NSLog("🔍 BreakTimer: Обнаружена активность пользователя во время отдыха")
        } else {
            NSLog("🔍 BreakTimer: Пользователь неактивен, отдых продолжается")
        }

        lastActivityCheck = now
    }
    
    /// Проверяет активность пользователя через macOS APIs
    private func isUserActive() -> Bool {
        // Получаем время последней активности пользователя
        let mouseIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .mouseMoved)
        let keyboardIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .keyDown)
        let clickIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .leftMouseDown)
        let scrollIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .scrollWheel)

        // Считаем пользователя активным, если последняя активность была менее 15 секунд назад
        let activityThreshold: TimeInterval = 15.0

        let isActive = mouseIdleTime < activityThreshold ||
                      keyboardIdleTime < activityThreshold ||
                      clickIdleTime < activityThreshold ||
                      scrollIdleTime < activityThreshold

        // Дополнительная проверка через системные процессы
        let isSystemActive = isSystemShowingActivity()

        // Логируем детали активности для отладки
        if isActive || isSystemActive {
            NSLog("🔍 BreakTimer: Активность обнаружена - мышь: %.1fs, клавиатура: %.1fs, клики: %.1fs, скролл: %.1fs",
                  mouseIdleTime, keyboardIdleTime, clickIdleTime, scrollIdleTime)
        }

        return isActive || isSystemActive
    }

    /// Дополнительная проверка активности через системные индикаторы
    private func isSystemShowingActivity() -> Bool {
        // Упрощенная проверка - в будущем можно добавить более сложную логику
        // Пока просто возвращаем false, чтобы не блокировать компиляцию
        return false
    }
    
    private func stopAllTimers() {
        breakTimer?.invalidate()
        breakTimer = nil

        activityMonitorTimer?.invalidate()
        activityMonitorTimer = nil

        // НОВОЕ: Останавливаем MinuteActivityTracker
        if useNewActivitySystem {
            minuteActivityTracker.stopTracking()
        }
    }
    
    private func resetBreak() {
        isActive = false
        timeRemaining = breakDuration
        breakStartTime = nil
        breakEndTime = nil
        lastActivityCheck = nil
        isFullBreak = false
        naturallyCompleted = false
    }
    
    // MARK: - Утилиты
    
    /// Форматирует время в читаемый вид
    func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    /// Возвращает статистику текущего отдыха
    func getBreakStatistics() -> BreakStatistics? {
        guard let startTime = breakStartTime else { return nil }

        let elapsed = Date().timeIntervalSince(startTime)
        let progress = elapsed / breakDuration

        return BreakStatistics(
            startTime: startTime,
            duration: breakDuration,
            elapsed: elapsed,
            progress: min(progress, 1.0),
            wasComputerActive: wasComputerActiveThisBreak,
            computerActiveTime: computerActiveTime
        )
    }

    /// Проверяет, является ли отдых полноценным и естественно завершенным
    func isFullBreakCompleted() -> Bool {
        return isFullBreak && naturallyCompleted
    }

    // MARK: - Unified Sleep/Wake Integration

    /// Обрабатывает пробуждение системы через унифицированную систему
    func handleSystemWake() {
        NSLog("🌅 BreakTimer: Система проснулась (унифицированная система)")

        // Если таймер активен, немедленно обновляем оставшееся время
        if isActive {
            updateBreakTimer()
            NSLog("🌅 BreakTimer: Время скорректировано после пробуждения. Осталось: \(formatTime(timeRemaining))")
        }
    }

    // MARK: - Sleep/Wake Notifications

    private func setupSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemWillSleep),
            name: NSWorkspace.willSleepNotification,
            object: nil
        )

        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemDidWake),
            name: NSWorkspace.didWakeNotification,
            object: nil
        )
    }

    private func removeSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.removeObserver(self)
    }

    @objc private func systemWillSleep() {
        NSLog("💤 BreakTimer: Система переходит в сон")
        // Ничего не делаем - время окончания уже сохранено в breakEndTime
    }

    @objc private func systemDidWake() {
        NSLog("🌅 BreakTimer: Система проснулась")

        // Если таймер активен, немедленно обновляем оставшееся время
        if isActive {
            updateBreakTimer()
            NSLog("🌅 BreakTimer: Время скорректировано после пробуждения. Осталось: \(formatTime(timeRemaining))")
        }
    }

    // MARK: - Новые методы для интеграции с унифицированной системой

    /// Переключает между старой и новой системой обнаружения активности
    func setUseNewActivitySystem(_ useNew: Bool) {
        useNewActivitySystem = useNew
        NSLog("🔧 BreakTimer: Переключение на \(useNew ? "новую 20-отрезковую с погрешностью" : "старую") систему активности")
    }

    /// Возвращает отладочную информацию о системе активности
    func getActivitySystemDebugInfo() -> String {
        var info = "BreakTimer Activity System Debug:\n"
        info += "- Используется новая система: \(useNewActivitySystem)\n"
        info += "- Таймер активен: \(isActive)\n"
        info += "- Была активность во время отдыха: \(wasComputerActiveThisBreak)\n"
        info += "- Время активности: \(Int(computerActiveTime))с\n"

        if useNewActivitySystem {
            info += "- MinuteActivityTracker Debug:\n"
            info += minuteActivityTracker.getDebugInfo().split(separator: "\n").map { "  \($0)" }.joined(separator: "\n")
        }

        return info
    }

    /// Принудительно проверяет активность (для тестирования)
    func forceCheckActivity() {
        if useNewActivitySystem {
            checkUserActivityNew()
        } else {
            checkUserActivity()
        }
    }
}

// MARK: - Модели данных

struct BreakStatistics {
    let startTime: Date
    let duration: TimeInterval
    let elapsed: TimeInterval
    let progress: Double // 0.0 - 1.0
    let wasComputerActive: Bool
    let computerActiveTime: TimeInterval
}
