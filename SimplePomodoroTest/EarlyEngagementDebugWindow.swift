import Cocoa
import Foundation

/// ОКНО ОТЛАДКИ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Позволяет эмулировать события, просматривать состояние и тестировать систему
class EarlyEngagementDebugWindow: NSWindow {
    
    // Убираем блок состояния - он не нужен

    // Матричный интерфейс
    private var daysPopupButton: NSPopUpButton!
    private var messageNumberPopup: NSPopUpButton!
    private var barPopupButton: NSPopUpButton!  // Новый выпадающий список для планки
    private var showMatrixMessageButton: NSButton!
    private var useRealDataCheckbox: NSButton!  // Чекбокс для выбора источника данных (реальные vs отладочные)
    private var currentMessageLabel: NSTextField!

    // Streak система
    private var daysWithoutWorkRadio: NSButton!
    private var streakDaysRadio: NSButton!
    private var streakTextField: NSTextField!
    private var streakStepper: NSStepper!

    // Система взращивания сессий удалена

    // Система полной сессии удалена

    // Упрощенный режим отладки (без тестового режима)
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 100, y: 100, width: 850, height: 600),
                  styleMask: [.titled, .closable, .resizable],
                  backing: .buffered,
                  defer: false)
        
        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        title = "🌅 Отладка системы раннего вовлечения"
        center()
        isReleasedWhenClosed = false
        
        print("🔧 Открыто окно отладки системы раннего вовлечения")
    }
    
    private func setupUI() {
        let contentView = NSView(frame: self.contentView!.bounds)
        contentView.autoresizingMask = [.width, .height]
        self.contentView = contentView

        var yPos: CGFloat = contentView.bounds.height - 30
        let margin: CGFloat = 20
        let buttonHeight: CGFloat = 30
        let spacing: CGFloat = 15

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🔧 ОТЛАДКА МАТРИЦЫ СООБЩЕНИЙ")
        titleLabel.font = NSFont.boldSystemFont(ofSize: 16)
        titleLabel.frame = NSRect(x: margin, y: yPos, width: contentView.bounds.width - 2*margin, height: 25)
        titleLabel.autoresizingMask = [.width]
        contentView.addSubview(titleLabel)
        yPos -= 40

        // === ИЗНАЧАЛЬНАЯ ПЛАНКА ===

        // Изначальная планка и чекбокс в одной строке
        let barLabel = NSTextField(labelWithString: "📊 Изначальная планка (минуты):")
        barLabel.font = NSFont.boldSystemFont(ofSize: 14)
        barLabel.frame = NSRect(x: margin, y: yPos, width: 250, height: 20)
        contentView.addSubview(barLabel)

        barPopupButton = NSPopUpButton(frame: NSRect(x: margin + 260, y: yPos - 3, width: 100, height: 25))
        barPopupButton.addItems(withTitles: ["3 мин", "5 мин", "8 мин", "10 мин", "15 мин", "20 мин", "25 мин", "30 мин", "35 мин", "40 мин", "45 мин", "50 мин", "52 мин"])
        barPopupButton.selectItem(at: 7) // По умолчанию 30 мин
        barPopupButton.target = self
        barPopupButton.action = #selector(updateMatrixMessage)
        contentView.addSubview(barPopupButton)

        // Чекбокс рядом с планкой
        useRealDataCheckbox = NSButton(checkboxWithTitle: "Использовать реальные данные пользователя", target: self, action: nil)
        useRealDataCheckbox.frame = NSRect(x: margin + 380, y: yPos - 3, width: 400, height: 25)
        useRealDataCheckbox.state = .off  // По умолчанию выключен
        contentView.addSubview(useRealDataCheckbox)
        yPos -= 40

        // === ТЕСТИРОВАНИЕ МАТРИЦЫ ===

        // 0. Выбор режима тестирования вертикали (НОВОЕ!)
        let modeLabel = NSTextField(labelWithString: "📐 Режим тестирования вертикали:")
        modeLabel.font = NSFont.boldSystemFont(ofSize: 14)
        modeLabel.frame = NSRect(x: margin, y: yPos, width: 300, height: 20)
        contentView.addSubview(modeLabel)
        yPos -= 30

        // Радиокнопки для выбора режима
        daysWithoutWorkRadio = NSButton(radioButtonWithTitle: "Дни без работы (минус):", target: self, action: #selector(modeChanged))
        daysWithoutWorkRadio.frame = NSRect(x: margin + 20, y: yPos, width: 200, height: 20)
        daysWithoutWorkRadio.state = .on  // По умолчанию выбран
        contentView.addSubview(daysWithoutWorkRadio)

        // Выпадающий список дней рядом с радиокнопкой
        daysPopupButton = NSPopUpButton(frame: NSRect(x: margin + 230, y: yPos - 2, width: 200, height: 25))
        daysPopupButton.addItems(withTitles: [
            "1 день не работал",
            "2-3 дня не работал",
            "4-6 дней не работал",
            "7+ дней не работал"
        ])
        daysPopupButton.target = self
        daysPopupButton.action = #selector(updateMatrixMessage)
        contentView.addSubview(daysPopupButton)
        yPos -= 30

        streakDaysRadio = NSButton(radioButtonWithTitle: "Streak дней (плюс):", target: self, action: #selector(modeChanged))
        streakDaysRadio.frame = NSRect(x: margin + 20, y: yPos, width: 250, height: 20)
        contentView.addSubview(streakDaysRadio)

        // Числовое поле для streak (изначально отключено)
        streakTextField = NSTextField(frame: NSRect(x: margin + 280, y: yPos - 3, width: 60, height: 25))
        streakTextField.stringValue = "1"
        streakTextField.isEnabled = false
        streakTextField.target = self
        streakTextField.action = #selector(streakValueChanged)
        contentView.addSubview(streakTextField)

        // Stepper для streak
        streakStepper = NSStepper(frame: NSRect(x: margin + 345, y: yPos - 3, width: 20, height: 25))
        streakStepper.minValue = 1
        streakStepper.maxValue = 1000
        streakStepper.integerValue = 1
        streakStepper.isEnabled = false
        streakStepper.target = self
        streakStepper.action = #selector(streakStepperChanged)
        contentView.addSubview(streakStepper)
        yPos -= 50



        // 2. Контейнер в два столбца
        let containerHeight: CGFloat = 80
        let _: CGFloat = 250  // leftColumnWidth
        let _: CGFloat = 300  // rightColumnWidth

        // Горизонталь - отдельная секция
        let horizontalSectionLabel = NSTextField(labelWithString: "📏 Горизонталь (время дня):")
        horizontalSectionLabel.font = NSFont.boldSystemFont(ofSize: 14)
        horizontalSectionLabel.frame = NSRect(x: margin, y: yPos, width: 300, height: 20)
        contentView.addSubview(horizontalSectionLabel)
        yPos -= 30

        // Выпадающий список для горизонтали
        let messageLabel = NSTextField(labelWithString: "Номер сообщения:")
        messageLabel.frame = NSRect(x: margin + 20, y: yPos, width: 200, height: 20)
        contentView.addSubview(messageLabel)

        messageNumberPopup = NSPopUpButton(frame: NSRect(x: margin + 20, y: yPos - 25, width: 400, height: 25))
        messageNumberPopup.addItems(withTitles: [
            "1-е сообщение (сразу при пробуждении)",
            "2-е сообщение (через 20 минут)",
            "3-е сообщение (через 1 час)",
            "4-е сообщение (через 2 часа или в 14:00)"
        ])
        messageNumberPopup.target = self
        messageNumberPopup.action = #selector(matrixSelectionChanged)
        contentView.addSubview(messageNumberPopup)
        yPos -= containerHeight

        // 3. Кнопка показа окна для выбранной позиции
        showMatrixMessageButton = NSButton(title: "📢 ПОКАЗАТЬ ОКНО", target: self, action: #selector(showMatrixMessage))
        showMatrixMessageButton.frame = NSRect(x: margin, y: yPos, width: 200, height: buttonHeight)
        showMatrixMessageButton.bezelStyle = .rounded
        contentView.addSubview(showMatrixMessageButton)

        // 4. Кнопка "Другой пример" для рандомизации (рядом с первой кнопкой)
        let anotherExampleButton = NSButton(title: "🎲 ДРУГОЙ ПРИМЕР", target: self, action: #selector(showAnotherExample))
        anotherExampleButton.frame = NSRect(x: margin + 210, y: yPos, width: 200, height: buttonHeight)
        anotherExampleButton.bezelStyle = .rounded
        contentView.addSubview(anotherExampleButton)



        yPos -= buttonHeight + spacing

        // 4. Отладочная информация о сообщении
        let currentMessageTitleLabel = NSTextField(labelWithString: "💬 Отладка матрицы сообщений:")
        currentMessageTitleLabel.font = NSFont.boldSystemFont(ofSize: 12)
        currentMessageTitleLabel.frame = NSRect(x: margin, y: yPos, width: 300, height: 20)
        contentView.addSubview(currentMessageTitleLabel)
        yPos -= 25

        currentMessageLabel = NSTextField(labelWithString: "Выберите позицию в матрице")
        currentMessageLabel.frame = NSRect(x: margin, y: yPos - 140, width: contentView.bounds.width - 2*margin, height: 160)
        currentMessageLabel.autoresizingMask = [.width]
        currentMessageLabel.maximumNumberOfLines = 0 // Без ограничений
        currentMessageLabel.cell?.wraps = true
        currentMessageLabel.font = NSFont.systemFont(ofSize: 11)
        currentMessageLabel.isSelectable = true // Разрешаем выделение текста
        currentMessageLabel.isEditable = false // Но запрещаем редактирование
        contentView.addSubview(currentMessageLabel)

        // Обновляем информацию при инициализации
        updateMatrixMessage()

        // Секция взращивания сессий удалена по запросу пользователя

        // Секция полной сессии удалена по запросу пользователя
    }
    
    @objc private func matrixSelectionChanged() {
        updateMatrixMessage()
    }



    @objc private func updateMatrixMessage() {
        let messageIndex = messageNumberPopup.indexOfSelectedItem

        // Получаем изначальную планку из выпадающего списка
        let barOptions = [3, 5, 8, 10, 15, 20, 25, 30, 35, 40, 45, 50, 52]
        let selectedBarIndex = barPopupButton.indexOfSelectedItem
        let initialBarMinutes = barOptions[selectedBarIndex]

        // Определяем режим и получаем соответствующие значения
        let isStreakMode = streakDaysRadio.state == .on
        let daysLevel: Int
        let calculatedBarMinutes: Int

        if isStreakMode {
            // Режим streak - используем специальный расчет для streak
            daysLevel = 0
            let streakDays = streakTextField.integerValue
            calculatedBarMinutes = EarlyEngagementSystem.shared.debugCalculateBarForStreak(
                initialBar: initialBarMinutes,
                streakDays: streakDays,
                messageIndex: messageIndex
            )
        } else {
            // Режим дней без работы
            // ИСПОЛЬЗУЕМ ТУ ЖЕ ЛОГИКУ, ЧТО И В showMatrixMessage()
            let daysMapping = [1, 2, 4, 7]  // Соответствие индексов реальным дням
            daysLevel = daysMapping[daysPopupButton.indexOfSelectedItem]
            calculatedBarMinutes = EarlyEngagementSystem.shared.debugCalculateBarForScenario(
                initialBar: initialBarMinutes,
                daysWithoutWork: daysLevel,
                messageIndex: messageIndex
            )
        }

        // Получаем сообщение из новой матрицы
        let message: EngagementMessage
        if isStreakMode {
            let streakDays = streakTextField.integerValue
            message = NewMessageConstructionMatrix.getStreakMessage(streakDays: streakDays, messageIndex: messageIndex)
            Logger.shared.log(.info, "EarlyEngagementDebugWindow", "🔥 Используем STREAK режим: \(streakDays) дней, сообщение \(messageIndex + 1)")
        } else {
            let daysMapping = [1, 2, 4, 7]  // Соответствие индексов реальным дням
            let daysWithoutWork = daysMapping[daysPopupButton.indexOfSelectedItem]
            message = NewMessageConstructionMatrix.getReturnMessage(daysWithoutWork: daysWithoutWork, messageIndex: messageIndex)
            Logger.shared.log(.info, "EarlyEngagementDebugWindow", "🔄 Используем RETURN режим: \(daysWithoutWork) дней без работы, сообщение \(messageIndex + 1)")
        }

        // Подставляем рассчитанные значения в сообщение
        let processedMessage = substituteVariablesInMessage(message, calculatedBarMinutes: calculatedBarMinutes)

        // Обновляем отображение
        let _ = ["STREAK режим", "1 день не работал", "2-3 дня не работал", "4-6 дней не работал", "7+ дней не работал"]  // daysLabels
        let timeLabels = [
            "1-е сообщение (сразу при пробуждении)",
            "2-е сообщение (через 20 минут)",
            "3-е сообщение (через 1 час)",
            "4-е сообщение (через 2 часа или в 14:00)"
        ]

        // Получаем информацию о приоритетном проекте
        let projectManager = ProjectManager()
        let priorityProjectName = projectManager.getPriorityProject()?.name ?? "НЕ ВЫБРАН"

        // Формируем текст в зависимости от режима
        let modeText: String
        if isStreakMode {
            let streakDays = streakTextField.integerValue
            modeText = "🔥 STREAK: \(streakDays) дней подряд"
        } else {
            // Для режима дней без работы используем правильный индекс
            let daysWithoutWorkLabels = ["1 день не работал", "2-3 дня не работал", "4-6 дней не работал", "7+ дней не работал"]
            let labelIndex = daysPopupButton.indexOfSelectedItem
            modeText = "📅 \(daysWithoutWorkLabels[labelIndex])"
        }

        // Используем реальную формулу из EarlyEngagementSystem
        let _ = EarlyEngagementSystem.shared.debugGetFormulaText(  // formulaText
            daysLevel: daysLevel,  // Передаем индекс, не замапленные дни
            messageIndex: messageIndex,
            initialBar: initialBarMinutes,
            calculatedBar: calculatedBarMinutes
        )

        // Генерируем кнопки через ButtonMatrix (ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ КОД!)
        let buttons: [ButtonMatrix.ButtonComponent]
        if isStreakMode {
            // ✅ ИСПРАВЛЕНИЕ: Передаем streak в ButtonMatrix для использования реальной логики
            let streakDays = streakTextField.integerValue
            buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
                daysWithoutWork: 0,  // Уровень 0 (работал сегодня)
                messageIndex: messageIndex,
                baseBarMinutes: initialBarMinutes,
                streakDays: streakDays  // ✅ Передаем streak в реальный код!
            )
        } else {
            // Для режима дней без работы используем обычную логику
            buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
                daysWithoutWork: daysLevel,
                messageIndex: messageIndex,
                baseBarMinutes: initialBarMinutes
            )
        }

        let buttonTexts = ButtonMatrix.formatButtonsForDebug(buttons)

        // Формируем детальное описание кнопок с учетом режима
        let detailedButtons: String
        if isStreakMode {
            let streakDays = streakTextField.integerValue
            detailedButtons = "🔥 Кнопки для STREAK \(streakDays) дней (используется уровень 0 - работал сегодня):\n" +
                             ButtonMatrix.createDetailedButtonDescription(buttons)
        } else {
            detailedButtons = ButtonMatrix.createDetailedButtonDescription(buttons)
        }

        // Формируем формулу расчета с учетом режима
        let calculationFormula: String
        if isStreakMode {
            let streakDays = streakTextField.integerValue
            calculationFormula = "🔥 Streak \(streakDays) дней: \(initialBarMinutes)мин → GradualGrowth×\(streakDays) → \(calculatedBarMinutes)мин"
        } else {
            calculationFormula = ButtonMatrix.getCalculationFormula(
                vertical: daysLevel,
                horizontal: messageIndex,
                baseBar: initialBarMinutes
            )
        }

        // Проверяем, есть ли кнопка "Максимальная сессия" для отладки
        let maxSessionInfo: String
        if let maxSessionButton = buttons.first(where: { $0.type == .fullBar }) {
            let maxSessionMinutes = Int(maxSessionButton.duration / 60)
            maxSessionInfo = "\n⚡ Максимальная сессия: \(maxSessionMinutes) мин"
        } else {
            maxSessionInfo = ""
        }

        currentMessageLabel.stringValue = """
        📍 Позиция: \(modeText) × \(timeLabels[messageIndex])
        🎯 Приоритетный проект: \(priorityProjectName)

        ⏱️ Изначальная планка: \(initialBarMinutes) мин
        🧮 Расчет: \(calculationFormula)
        ✅ Рассчитанная планка: \(calculatedBarMinutes) мин\(maxSessionInfo)

        📝 Заголовок: "\(processedMessage.title)"
        📄 Текст: "\(processedMessage.subtitle)"
        🔘 Кнопки: \(buttonTexts)

        \(detailedButtons)
        """
    }

    // Методы расчета теперь находятся в EarlyEngagementSystem для синхронизации

    private func substituteVariablesInMessage(_ message: EngagementMessage, calculatedBarMinutes: Int) -> EngagementMessage {
        let projectManager = ProjectManager()
        let priorityProject = projectManager.getPriorityProject()
        let projectName = priorityProject?.name ?? "приоритетный проект"

        var processedMessage = message

        // Подставляем проект (используем правильные переменные из MessageConstructionMatrix)
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[focused_project]", with: projectName)
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[focused_project]", with: projectName)
        processedMessage.buttonText = processedMessage.buttonText.replacingOccurrences(of: "[focused_project]", with: projectName)

        // Подставляем время (используем правильные переменные из MessageConstructionMatrix)
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[current_bar]", with: "\(calculatedBarMinutes)")
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[current_bar]", with: "\(calculatedBarMinutes)")
        processedMessage.buttonText = processedMessage.buttonText.replacingOccurrences(of: "[current_bar]", with: "\(calculatedBarMinutes)")

        // Обновляем предлагаемую длительность
        processedMessage.proposedDuration = TimeInterval(calculatedBarMinutes * 60)

        return processedMessage
    }

    @objc private func showMatrixMessage() {
        let messageIndex = messageNumberPopup.indexOfSelectedItem

        // Получаем изначальную планку из выпадающего списка
        let barOptions = [3, 5, 8, 10, 15, 20, 25, 30, 35, 40, 45, 50, 52]
        let selectedBarIndex = barPopupButton.indexOfSelectedItem
        let initialBarMinutes = barOptions[selectedBarIndex]

        // Определяем режим и устанавливаем соответствующие отладочные параметры
        let isStreakMode = streakDaysRadio.state == .on
        let daysLevel: Int
        let calculatedBarMinutes: Int

        if isStreakMode {
            // Режим streak - используем специальный расчет для streak
            daysLevel = 0  // Всегда используем уровень 0 для streak
            let streakDays = streakTextField.integerValue
            calculatedBarMinutes = EarlyEngagementSystem.shared.debugCalculateBarForStreak(
                initialBar: initialBarMinutes,
                streakDays: streakDays,
                messageIndex: messageIndex
            )
        } else {
            // Режим дней без работы
            // Индекс 0 = "1 день не работал" = уровень 1
            // Индекс 1 = "2-3 дня не работал" = уровень 2
            // Индекс 2 = "4-6 дней не работал" = уровень 4
            // Индекс 3 = "7+ дней не работал" = уровень 7
            let daysMapping = [1, 2, 4, 7]  // Соответствие индексов реальным дням
            daysLevel = daysMapping[daysPopupButton.indexOfSelectedItem]
            calculatedBarMinutes = EarlyEngagementSystem.shared.debugCalculateBarForScenario(
                initialBar: initialBarMinutes,
                daysWithoutWork: daysLevel,
                messageIndex: messageIndex
            )
        }

        // Для логирования получаем замапленные дни
        let realDaysWithoutWork = mapIndexToDays(daysLevel)

        // Формируем текст для логирования
        let modeLogText = isStreakMode ? "streak=\(streakTextField.integerValue)" : "дни=\(realDaysWithoutWork)"
        print("🎯 Показ окна сообщения матрицы: \(modeLogText), сообщение=\(messageIndex), изначальная планка=\(initialBarMinutes)мин, рассчитанная=\(calculatedBarMinutes)мин")

        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Используем createEngagementMessage() вместо ручного создания
        // Это обеспечит полную синхронизацию с реальным окном

        // ВАЖНО: Устанавливаем отладочные параметры в систему перед показом окна
        if useRealDataCheckbox.state == .on {
            // Используем реальные данные пользователя
            EarlyEngagementSystem.shared.debugClearInitialBar()
            EarlyEngagementSystem.shared.debugClearDaysWithoutWork()
            EarlyEngagementSystem.shared.debugClearStreakDays()
            print("🔧 ОТЛАДКА: Используем реальные данные пользователя")
        } else {
            // Используем отладочные данные
            EarlyEngagementSystem.shared.debugSetInitialBar(initialBarMinutes)

            if isStreakMode {
                // Устанавливаем streak и сбрасываем дни без работы
                EarlyEngagementSystem.shared.debugSetStreakDays(streakTextField.integerValue)
                EarlyEngagementSystem.shared.debugSetDaysWithoutWork(0)  // Всегда 0 для streak
                print("🔧 ОТЛАДКА: Используем streak=\(streakTextField.integerValue), планка=\(initialBarMinutes)мин")
            } else {
                // Устанавливаем дни без работы и сбрасываем streak
                EarlyEngagementSystem.shared.debugSetDaysWithoutWork(realDaysWithoutWork)
                EarlyEngagementSystem.shared.debugClearStreakDays()
                print("🔧 ОТЛАДКА: Используем дни=\(realDaysWithoutWork), планка=\(initialBarMinutes)мин")
            }
            print("🔧 ОТЛАДКА: Используем отладочные данные - планка: \(initialBarMinutes)мин, дни без работы: \(realDaysWithoutWork)")
        }

        // НОВОЕ: Устанавливаем отладочный горизонтальный уровень (время дня)
        EarlyEngagementSystem.shared.debugSetHorizontalLevel(messageIndex)
        print("🔧 ОТЛАДКА: Используем выбранный горизонтальный уровень \(messageIndex) в реальном окне")

        // Получаем сообщение через createEngagementMessage() для полной синхронизации
        let realMessage = EarlyEngagementSystem.shared.createEngagementMessage()

        // Показываем реальное окно сообщения с правильным позиционированием
        let engagementWindow = EarlyEngagementWindow()

        // ИСПРАВЛЕНО: Используем тот же метод позиционирования что и в реальном приложении
        // Получаем statusItem из AppDelegate для корректного позиционирования
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate,
           let statusButton = appDelegate.statusItem.button {
            let statusFrame = statusButton.window?.convertToScreen(statusButton.frame) ?? NSRect.zero
            engagementWindow.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        engagementWindow.showEngagementMessage(
            realMessage,
            onAccept: { [weak self] projectId in
                print("✅ Пользователь принял: \(projectId?.uuidString ?? "без проекта")")
                // Очищаем отладочные данные если они использовались
                if self?.useRealDataCheckbox.state == .off {
                    EarlyEngagementSystem.shared.debugClearInitialBar()
                    EarlyEngagementSystem.shared.debugClearDaysWithoutWork()
                    EarlyEngagementSystem.shared.debugClearStreakDays()
                }
                EarlyEngagementSystem.shared.debugClearHorizontalLevel()
            },
            onDecline: { [weak self] in
                print("❌ Пользователь отклонил")
                // Очищаем отладочные данные если они использовались
                if self?.useRealDataCheckbox.state == .off {
                    EarlyEngagementSystem.shared.debugClearInitialBar()
                    EarlyEngagementSystem.shared.debugClearDaysWithoutWork()
                    EarlyEngagementSystem.shared.debugClearStreakDays()
                }
                EarlyEngagementSystem.shared.debugClearHorizontalLevel()
            },
            onSnooze: { [weak self] in
                print("⏰ Пользователь отложил на 30 минут")
                // Очищаем отладочные данные если они использовались
                if self?.useRealDataCheckbox.state == .off {
                    EarlyEngagementSystem.shared.debugClearInitialBar()
                    EarlyEngagementSystem.shared.debugClearDaysWithoutWork()
                    EarlyEngagementSystem.shared.debugClearStreakDays()
                }
                EarlyEngagementSystem.shared.debugClearHorizontalLevel()
            },
            onFullSession: { [weak self] projectId in
                print("🌟 Пользователь выбрал полную сессию: \(projectId?.uuidString ?? "без проекта")")
                // Очищаем отладочные данные если они использовались
                if self?.useRealDataCheckbox.state == .off {
                    EarlyEngagementSystem.shared.debugClearInitialBar()
                    EarlyEngagementSystem.shared.debugClearDaysWithoutWork()
                    EarlyEngagementSystem.shared.debugClearStreakDays()
                }
                EarlyEngagementSystem.shared.debugClearHorizontalLevel()
            }
        )

        print("✅ Показано окно сообщения: \(realMessage.title)")
    }

    // MARK: - Session Growing System Methods (удалены)

    // MARK: - Full Session System Methods (удалены)

    /// Маппинг уровня дней без работы к реальному количеству дней для отображения
    private func mapIndexToDays(_ daysLevel: Int) -> Int {
        // daysLevel - это уровень системы (1, 2, 4, 7)
        // Возвращаем представительное значение для отображения
        switch daysLevel {
        case 0: return 0    // Streak режим (работал сегодня)
        case 1: return 1    // "1 день не работал"
        case 2: return 2    // "2-3 дня не работал" (используем 2 как представитель)
        case 4: return 4    // "4-6 дней не работал" (используем 4 как представитель)
        case 7: return 7    // "7+ дней не работал" (используем 7 для попадания в default)
        default: return daysLevel  // Возвращаем как есть для других случаев
        }
    }

    // MARK: - Streak System Methods

    @objc private func modeChanged() {
        let isStreakMode = streakDaysRadio.state == .on

        // Включаем/выключаем элементы управления
        daysPopupButton.isEnabled = !isStreakMode
        streakTextField.isEnabled = isStreakMode
        streakStepper.isEnabled = isStreakMode

        // При переключении на streak режим, устанавливаем значение по умолчанию
        if isStreakMode && streakTextField.integerValue == 0 {
            streakTextField.integerValue = 1
            streakStepper.integerValue = 1
        }

        // Принудительно обновляем внешний вид элементов
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if isStreakMode {
                // Для streak режима: принудительно активируем stepper
                self.streakStepper.needsDisplay = true
                self.streakTextField.needsDisplay = true

                // Устанавливаем фокус на текстовое поле, чтобы обновить UI
                self.makeFirstResponder(self.streakTextField)

                // Затем убираем фокус
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.makeFirstResponder(nil)
                }
            }

            // Принудительно перерисовываем окно
            self.contentView?.needsDisplay = true
        }

        // Обновляем отображение
        updateMatrixMessage()

        print("🔧 Режим изменен на: \(isStreakMode ? "Streak" : "Дни без работы")")
    }

    @objc private func streakValueChanged() {
        let value = streakTextField.integerValue
        if value >= 1 && value <= 1000 {
            streakStepper.integerValue = value
            updateMatrixMessage()
        }
    }

    @objc private func streakStepperChanged() {
        streakTextField.integerValue = streakStepper.integerValue
        updateMatrixMessage()
    }

    /// Показывает другой пример сообщения с рандомизацией
    @objc private func showAnotherExample() {
        Logger.shared.log(.info, "EarlyEngagementDebugWindow", "🎲 Генерация другого примера сообщения")

        // Просто обновляем текст в отладочном окне (рандомизируем)
        updateMatrixMessage()

        // НЕ показываем новое окно - только обновляем текст в отладочном окне
    }


}


