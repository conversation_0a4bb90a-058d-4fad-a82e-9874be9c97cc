import Foundation
import Cocoa

/// Менеджер для управления тестовыми данными
/// Позволяет добавлять реальные данные о работе за прошлые дни для тестирования системы выходных
class TestDataManager {
    static let shared = TestDataManager()
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Добавляет тестовую микросессию за указанное количество дней назад
    /// - Parameters:
    ///   - daysAgo: Количество дней назад (0 = сегодня, 1 = вчера, и т.д.)
    ///   - duration: Продолжительность в минутах (по умолчанию 25 минут)
    ///   - projectId: ID проекта (опционально)
    func addTestMicroSession(daysAgo: Int, duration: Int = 25, projectId: UUID? = nil) {
        let calendar = Calendar.current
        let targetDate = calendar.date(byAdding: .day, value: -daysAgo, to: Date()) ?? Date()
        
        // Устанавливаем время в середине рабочего дня (14:00)
        let components = calendar.dateComponents([.year, .month, .day], from: targetDate)
        let sessionDate = calendar.date(bySettingHour: 14, minute: 0, second: 0, of: calendar.date(from: components) ?? targetDate) ?? targetDate
        
        let durationInSeconds = TimeInterval(duration * 60)
        
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
            appDelegate.statisticsManager.recordTestInterval(
                date: sessionDate,
                duration: durationInSeconds,
                projectId: projectId
            )
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "dd.MM.yyyy (EEEE)"
            dateFormatter.locale = Locale(identifier: "ru_RU")
            let dateStr = dateFormatter.string(from: sessionDate)
            
            Logger.shared.log(.info, "TestDataManager", "✅ Добавлена тестовая микросессия: \(duration) мин за \(dateStr)")
        } else {
            Logger.shared.log(.error, "TestDataManager", "❌ Не удалось получить StatisticsManager")
        }
    }
    
    /// Добавляет несколько тестовых сессий для создания последовательности рабочих дней
    /// - Parameters:
    ///   - workingDays: Массив дней назад, когда нужно добавить сессии
    ///   - duration: Продолжительность каждой сессии в минутах
    func addTestWorkSequence(workingDays: [Int], duration: Int = 25) {
        for dayAgo in workingDays {
            addTestMicroSession(daysAgo: dayAgo, duration: duration)
        }
        
        Logger.shared.log(.info, "TestDataManager", "📅 Добавлена последовательность рабочих дней: \(workingDays)")
    }
    
    /// Создает тестовые сценарии из примеров пользователя
    func createTestScenarios() {
        clearAllTestData()
        
        Logger.shared.log(.info, "TestDataManager", "🎭 Создание тестовых сценариев...")
        
        // Пример 1: Работал только сегодня (воскресенье)
        // Дни работы: 1, Выходной: среда (пн-вт-ср-ВЫХОДНОЙ)
        // Для этого примера добавим сессию сегодня (будет добавлена пользователем реально)
        
        // Пример 2: Работал вчера (суббота) + сегодня (воскресенье)  
        // Дни работы: 2, Выходной: вторник (пн-вт-ВЫХОДНОЙ)
        // addTestMicroSession(daysAgo: 1) // вчера
        
        // Пример 3: Работал ЧТ+ПТ+СБ, сегодня (ВСК) еще НЕ работал
        // Дни работы: 3 (чт+пт+сб), Выходной: СЕГОДНЯ (воскресенье) ✅
        addTestWorkSequence(workingDays: [3, 2, 1]) // чт, пт, сб
        
        Logger.shared.log(.info, "TestDataManager", "✅ Тестовые сценарии созданы")
    }
    
    /// Очищает все тестовые данные
    func clearAllTestData() {
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
            let beforeCount = appDelegate.statisticsManager.getTestIntervalsCount()
            appDelegate.statisticsManager.clearTestIntervals()
            
            Logger.shared.log(.info, "TestDataManager", "🧹 Очищено \(beforeCount) тестовых интервалов")
        } else {
            Logger.shared.log(.error, "TestDataManager", "❌ Не удалось получить StatisticsManager для очистки")
        }
    }
    
    /// Получает информацию о текущих тестовых данных
    func getTestDataInfo() -> String {
        guard let appDelegate = NSApplication.shared.delegate as? AppDelegate else {
            return "❌ Ошибка получения данных"
        }
        
        let testCount = appDelegate.statisticsManager.getTestIntervalsCount()
        
        if testCount == 0 {
            return "📊 Тестовых данных нет"
        }
        
        // Получаем информацию о рабочих днях для отладки
        let shouldShowWeekend = WeekendManager.shared.shouldShowWeekendChoice()
        let isWeekendDay = WeekendManager.shared.isWeekendDay()
        
        return """
        📊 Тестовых интервалов: \(testCount)
        🏖️ Показать выходной: \(shouldShowWeekend ? "ДА" : "НЕТ")
        📅 Сегодня выходной: \(isWeekendDay ? "ДА" : "НЕТ")
        """
    }
    
    /// Создает конкретный тестовый сценарий
    /// - Parameter scenario: Номер сценария (1-4 из примеров пользователя)
    func createSpecificScenario(_ scenario: Int) {
        Logger.shared.log(.info, "TestDataManager", "🎭 НАЧАЛО: createSpecificScenario(\(scenario))")

        clearAllTestData()

        let calendar = Calendar.current
        let today = Date()
        let weekday = calendar.component(.weekday, from: today)

        Logger.shared.log(.info, "TestDataManager", "🎭 Создание сценария \(scenario), сегодня: \(weekday) (1=вск, 2=пн...)")
        
        switch scenario {
        case 1:
            // Пример 1: Работал только сегодня
            // Ничего не добавляем, пользователь сам сделает сессию сегодня
            Logger.shared.log(.info, "TestDataManager", "📝 Сценарий 1: Только сегодня работаем (добавьте сессию сами)")
            
        case 2:
            // Пример 2: Работал вчера + сегодня
            addTestMicroSession(daysAgo: 1) // вчера
            addTestMicroSession(daysAgo: 0) // сегодня
            Logger.shared.log(.info, "TestDataManager", "📝 Сценарий 2: Вчера + сегодня (2 дня работы)")
            
        case 3:
            // Сценарий 3: Работал 3 дня подряд (3 дня назад + 2 дня назад + вчера), сегодня НЕ работал
            addTestWorkSequence(workingDays: [3, 2, 1]) // 3 дня назад, 2 дня назад, вчера
            Logger.shared.log(.info, "TestDataManager", "📝 Сценарий 3: 3 дня подряд (3 дня назад + 2 дня назад + вчера), сегодня НЕ работал")

        case 4:
            // Сценарий 4: Работал 4 дня подряд, включая сегодня
            addTestWorkSequence(workingDays: [3, 2, 1]) // 3 дня назад, 2 дня назад, вчера
            addTestMicroSession(daysAgo: 0) // сегодня
            Logger.shared.log(.info, "TestDataManager", "📝 Сценарий 4: 4 дня подряд (3 дня назад + 2 дня назад + вчера + сегодня)")
            
        default:
            Logger.shared.log(.warning, "TestDataManager", "⚠️ Неизвестный сценарий: \(scenario)")
        }

        Logger.shared.log(.info, "TestDataManager", "🎭 КОНЕЦ: createSpecificScenario(\(scenario))")

        // Уведомляем AppDelegate об изменении данных для обновления меню
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: NSNotification.Name("TestDataChanged"), object: nil)
        }
    }
}
