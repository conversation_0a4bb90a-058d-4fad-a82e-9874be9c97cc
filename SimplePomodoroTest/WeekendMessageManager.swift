import Foundation

/// Структура сообщения для выходного дня
struct WeekendMessage {
    let title: String
    let message: String
    let restButtonText: String
    let workButtonText: String
    let level: Int
}

/// Менеджер сообщений для системы выходных
class WeekendMessageManager {
    
    // MARK: - Singleton
    
    static let shared = WeekendMessageManager()
    private init() {}
    
    // MARK: - Public Methods
    
    /// Возвращает сообщение в зависимости от количества дней без отдыха
    func getMessageForDaysWithoutRest(_ days: Int) -> WeekendMessage {
        switch days {
        case 0:
            return WeekendMessage(
                title: "Сегодня выходной",
                message: "Сегодня выходной день. Время восстановиться!",
                restButtonText: "Отдохнуть",
                workButtonText: "Поработать",
                level: 0
            )
            
        case 1:
            return WeekendMessage(
                title: "Сегодня выходной",
                message: "Вчера ты работал, но сегодня рекомендуется отдохнуть",
                restButtonText: "Отдохнуть",
                workButtonText: "Поработать",
                level: 1
            )
            
        case 2:
            return WeekendMessage(
                title: "Сегодня выходной",
                message: "Ты уже 2 дня работаешь без отдыха. Рекомендуется восстановиться",
                restButtonText: "Отдохнуть",
                workButtonText: "Поработать",
                level: 2
            )
            
        case 3, 4:
            return WeekendMessage(
                title: "⚠️ Сегодня выходной",
                message: "Ты работаешь уже \(days) дня без отдыха. Напряжение накапливается - настоятельно рекомендуется отдохнуть!",
                restButtonText: "Отдохнуть",
                workButtonText: "Все равно работать",
                level: 3
            )
            
        case 5, 6:
            return WeekendMessage(
                title: "🔴 Критично! Сегодня выходной",
                message: "Ты работаешь уже \(days) дней без отдыха! Срочно отдохни, чтобы избежать выгорания!",
                restButtonText: "Отдохнуть",
                workButtonText: "Игнорировать",
                level: 4
            )
            
        default: // 7+ дней
            return WeekendMessage(
                title: "🚨 ОПАСНО! Обязательный отдых",
                message: "Ты работаешь уже \(days) дней подряд! Это крайне опасно для здоровья. Немедленно отдохни!",
                restButtonText: "Отдохнуть",
                workButtonText: "Я понимаю риски",
                level: 5
            )
        }
    }
    
    /// Возвращает сообщение для уведомления "завтра выходной"
    func getTomorrowWeekendMessage() -> String {
        let strategy = WeekendManager.shared.currentStrategy
        
        switch strategy {
        case .threeOne:
            return "Завтра выходной день по стратегии 3/1. Время восстановиться!"
        case .fiveTwo:
            return "Завтра выходной день. Время восстановиться!"
        case .custom:
            return "Завтра выходной день. Время восстановиться!"
        }
    }
    
    /// Возвращает краткое описание стратегии для настроек
    func getStrategyDescription(_ strategy: WeekendManager.WorkStrategy) -> String {
        switch strategy {
        case .threeOne:
            return "3 дня работы, 1 день отдыха. Смещающиеся выходные для максимальной эффективности умственного труда. Рекомендуется для работников умственного труда."
            
        case .fiveTwo:
            return "Классическая рабочая неделя понедельник-пятница с выходными в субботу и воскресенье. Привычный режим для большинства людей."
            
        case .custom:
            return "Выберите рабочие дни самостоятельно в соответствии с вашим индивидуальным графиком работы."
        }
    }
    
    /// Возвращает преимущества стратегии 3/1 для тултипа
    func getThreeOneAdvantages() -> String {
        return """
        Преимущества стратегии 3/1:
        
        • Более частые перерывы предотвращают накопление усталости
        • Смещающиеся выходные обеспечивают разнообразие
        • Оптимально для умственного труда и творческой работы
        • Показывает в разы лучшие результаты по продуктивности
        • Снижает риск выгорания и стресса
        • Улучшает качество работы за счет регулярного восстановления
        """
    }
}
