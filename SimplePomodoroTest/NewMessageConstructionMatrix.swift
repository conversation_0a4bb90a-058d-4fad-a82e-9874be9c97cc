import Foundation

/// Новая матрица сообщений, использующая JSON данные
class NewMessageConstructionMatrix {
    
    private static let textsLoader = EarlyEngagementTextsLoader.shared
    
    // MARK: - Public Methods
    
    /// Получает сообщение для streak режима
    /// - Parameters:
    ///   - streakDays: Количество дней streak
    ///   - messageIndex: Индекс сообщения (0-3)
    /// - Returns: Сообщение с заголовком и подзаголовком
    static func getStreakMessage(streakDays: Int, messageIndex: Int) -> EngagementMessage {
        Logger.shared.log(.info, "NewMessageConstructionMatrix", "🎯 Генерация streak сообщения: дни=\(streakDays), индекс=\(messageIndex)")
        
        // Проверяем специальные дни
        if let specialMessage = getSpecialDayMessage(streakDays: streakDays, messageIndex: messageIndex) {
            return specialMessage
        }
        
        // Используем универсальные сообщения
        return getUniversalStreakMessage(streakDays: streakDays, messageIndex: messageIndex)
    }
    
    /// Получает сообщение для return режима
    /// - Parameters:
    ///   - daysWithoutWork: Дни без работы
    ///   - messageIndex: Индекс сообщения (0-3)
    /// - Returns: Сообщение с заголовком и подзаголовком
    static func getReturnMessage(daysWithoutWork: Int, messageIndex: Int) -> EngagementMessage {
        Logger.shared.log(.info, "NewMessageConstructionMatrix", "🔄 Генерация return сообщения: дни=\(daysWithoutWork), индекс=\(messageIndex)")

        guard let textsData = textsLoader.getTextsData() else {
            Logger.shared.log(.error, "NewMessageConstructionMatrix", "❌ КРИТИЧЕСКАЯ ОШИБКА: textsData = nil, используем fallback")
            return createFallbackMessage(messageIndex: messageIndex)
        }
        
        // Определяем уровень по дням без работы
        let level: ReturnLevel
        switch daysWithoutWork {
        case 1:
            level = textsData.returnMode.level1
        case 2...3:
            level = textsData.returnMode.level2
        case 4...6:
            level = textsData.returnMode.level3
        default: // 7+
            level = textsData.returnMode.level4
        }
        
        // Получаем тексты для нужного сообщения
        let messages: [String]
        switch messageIndex {
        case 0: messages = level.message1
        case 1: messages = level.message2
        case 2: messages = level.message3
        case 3: messages = level.message4
        default: messages = level.message1
        }
        
        // Выбираем случайный вариант и подставляем переменные
        let rawTitle = messages.randomElement() ?? "Время работать!"
        let title = substituteReturnVariables(in: rawTitle, daysWithoutWork: daysWithoutWork)
        let subtitle = getRandomSubheader(excludingConflicts: title, timeOfDay: messageIndex)

        return EngagementMessage(
            title: title,
            subtitle: subtitle,
            proposedDuration: 0, // Будет установлено позже
            buttonText: "Начать",
            level: min(daysWithoutWork, 4),
            timeOfDay: messageIndex
        )
    }
    
    // MARK: - Private Methods
    
    /// Получает сообщение для специальных дней
    private static func getSpecialDayMessage(streakDays: Int, messageIndex: Int) -> EngagementMessage? {
        guard let textsData = textsLoader.getTextsData() else { return nil }
        
        let specialDays = textsData.streakMode.specialDays
        let dayMessages: DayMessages?
        
        switch streakDays {
        case 1:
            dayMessages = specialDays.day1
        case 2:
            dayMessages = specialDays.day2
        case 3:
            dayMessages = specialDays.day3
        case 5:
            dayMessages = specialDays.day5
        case 7:
            dayMessages = specialDays.milestoneDays.day7
        case 14:
            dayMessages = specialDays.milestoneDays.day14
        case 30:
            dayMessages = specialDays.milestoneDays.day30
        case 50:
            dayMessages = specialDays.milestoneDays.day50
        case 100:
            dayMessages = specialDays.milestoneDays.day100
        default:
            dayMessages = nil
        }
        
        guard let messages = dayMessages else { return nil }
        
        // Получаем тексты для нужного сообщения
        let messageTexts: [String]
        switch messageIndex {
        case 0: messageTexts = messages.message1
        case 1: messageTexts = messages.message2
        case 2: messageTexts = messages.message3
        case 3: messageTexts = messages.message4
        default: messageTexts = messages.message1
        }
        
        // Выбираем случайный вариант и подставляем переменные
        let rawTitle = messageTexts.randomElement() ?? "Продолжаем работу!"
        let title = substituteStreakVariables(in: rawTitle, streakDays: streakDays)
        let subtitle = getRandomSubheader(excludingConflicts: title, timeOfDay: messageIndex)
        
        return EngagementMessage(
            title: title,
            subtitle: subtitle,
            proposedDuration: 0,
            buttonText: "Продолжить",
            level: 0, // Streak режим
            timeOfDay: messageIndex
        )
    }
    
    /// Получает универсальное streak сообщение
    private static func getUniversalStreakMessage(streakDays: Int, messageIndex: Int) -> EngagementMessage {
        guard let textsData = textsLoader.getTextsData() else {
            return createFallbackMessage(messageIndex: messageIndex)
        }
        
        // Определяем уровень зрелости
        let maturityLevel: MaturityLevel
        switch streakDays {
        case 1...14:
            maturityLevel = textsData.streakMode.universalMessages.level1
        case 15...60:
            maturityLevel = textsData.streakMode.universalMessages.level2
        default: // 61+
            maturityLevel = textsData.streakMode.universalMessages.level3
        }
        
        // Получаем тексты для нужного сообщения
        let messages: [String]
        switch messageIndex {
        case 0: messages = maturityLevel.message1
        case 1: messages = maturityLevel.message2
        case 2: messages = maturityLevel.message3
        case 3: messages = maturityLevel.message4
        default: messages = maturityLevel.message1
        }
        
        // Выбираем случайный вариант и подставляем переменные
        let rawTitle = messages.randomElement() ?? "Продолжаем работу!"
        let title = substituteStreakVariables(in: rawTitle, streakDays: streakDays)
        let subtitle = getRandomSubheader(excludingConflicts: title, timeOfDay: messageIndex)
        
        return EngagementMessage(
            title: title,
            subtitle: subtitle,
            proposedDuration: 0,
            buttonText: "Продолжить",
            level: 0, // Streak режим
            timeOfDay: messageIndex
        )
    }
    
    /// Подставляет переменные streak в текст
    private static func substituteStreakVariables(in text: String, streakDays: Int) -> String {
        let declension = getDaysDeclension(streakDays)
        return text
            .replacingOccurrences(of: "{streak_days}", with: "\(streakDays)")
            .replacingOccurrences(of: "{declension}", with: declension)
    }

    /// Подставляет переменные return в текст
    private static func substituteReturnVariables(in text: String, daysWithoutWork: Int) -> String {
        let declension = getDaysDeclension(daysWithoutWork)
        return text
            .replacingOccurrences(of: "[X] дня", with: "\(daysWithoutWork) \(declension)")
            .replacingOccurrences(of: "[X] дней", with: "\(daysWithoutWork) \(declension)")
            .replacingOccurrences(of: "[X]", with: "\(daysWithoutWork)")
    }
    
    /// Получает случайный подзаголовок, исключая конфликты
    private static func getRandomSubheader(excludingConflicts title: String, timeOfDay: Int) -> String {
        let allSubheaders = textsLoader.getSubheadersForMessage(messageIndex: timeOfDay)
        let conflictPairs = textsLoader.getConflictPairs()

        // Фильтруем подзаголовки, исключая конфликтные
        let availableSubheaders = allSubheaders.filter { subheader in
            !hasConflict(title: title, subheader: subheader, conflictPairs: conflictPairs)
        }

        return availableSubheaders.randomElement() ?? allSubheaders.first ?? "Начинаем работу"
    }
    
    /// Проверяет наличие конфликта между заголовком и подзаголовком
    private static func hasConflict(title: String, subheader: String, conflictPairs: [ConflictPair]) -> Bool {
        let titleLower = title.lowercased()
        let subheaderLower = subheader.lowercased()
        
        for pair in conflictPairs {
            // Проверяем, содержит ли заголовок ключевые слова
            let hasKeyword = pair.headerKeywords.contains { keyword in
                titleLower.contains(keyword.lowercased())
            }
            
            if hasKeyword {
                // Проверяем, содержит ли подзаголовок исключаемые фразы
                let hasExcludedPhrase = pair.excludeSubheaders.contains { phrase in
                    subheaderLower.contains(phrase.lowercased())
                }
                
                if hasExcludedPhrase {
                    return true
                }
            }
        }
        
        return false
    }
    
    /// Создает fallback сообщение при ошибке загрузки
    private static func createFallbackMessage(messageIndex: Int) -> EngagementMessage {
        Logger.shared.log(.warning, "NewMessageConstructionMatrix", "⚠️ Используется fallback сообщение")
        
        return EngagementMessage(
            title: "Время работать!",
            subtitle: "Начнем продуктивный день",
            proposedDuration: 25 * 60,
            buttonText: "Начать",
            level: 1,
            timeOfDay: messageIndex
        )
    }
}
