import Foundation
import Cocoa

/// Менеджер системы выходных дней
class WeekendManager {
    
    // MARK: - Singleton
    
    static let shared = WeekendManager()
    private init() {
        Logger.shared.log(.info, "WeekendManager", "🏖️ Инициализирован")
    }
    
    // MARK: - Types
    
    /// Стратегии работы
    enum WorkStrategy: String, CaseIterable {
        case threeOne = "3/1"
        case fiveTwo = "5/2"
        case custom = "custom"
        
        var displayName: String {
            switch self {
            case .threeOne: return "3/1 (рекомендуемая)"
            case .fiveTwo: return "5/2 (классическая)"
            case .custom: return "Своя"
            }
        }
        
        var description: String {
            switch self {
            case .threeOne: return "3 дня работы, 1 день отдыха. Смещающиеся выходные для максимальной эффективности умственного труда."
            case .fiveTwo: return "Классическая рабочая неделя с фиксированными выходными днями."
            case .custom: return "Выберите рабочие дни самостоятельно."
            }
        }
    }
    
    /// Выбор пользователя в выходной день
    enum WeekendChoice {
        case rest
        case work
    }
    
    // MARK: - Properties

    /// Форматтер для дат
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()

    /// Текущая стратегия работы
    var currentStrategy: WorkStrategy {
        get {
            let strategyString = UserDefaults.standard.string(forKey: "weekendStrategy") ?? WorkStrategy.threeOne.rawValue
            return WorkStrategy(rawValue: strategyString) ?? .threeOne
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: "weekendStrategy")
            Logger.shared.log(.info, "WeekendManager", "📋 Стратегия изменена на: \(newValue.displayName)")
        }
    }
    
    /// Дата последнего выходного дня
    private var lastWeekendDate: Date? {
        get {
            return UserDefaults.standard.object(forKey: "lastWeekendDate") as? Date
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "lastWeekendDate")
        }
    }
    
    /// Счетчик дней без отдыха
    var daysWithoutRest: Int {
        get {
            return UserDefaults.standard.integer(forKey: "daysWithoutRest")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "daysWithoutRest")
            Logger.shared.log(.info, "WeekendManager", "📊 Дней без отдыха: \(newValue)")
        }
    }
    
    // MARK: - Public Methods
    
    /// Проверяет доступна ли система выходных для текущего пользователя
    func isWeekendSystemAvailable() -> Bool {
        let userLevel = DailyWorkloadManager.shared.getCurrentUserLevel()
        let isAvailable = userLevel == .regular || userLevel == .advanced

        Logger.shared.log(.debug, "WeekendManager", "🔍 Система выходных доступна: \(isAvailable) (уровень: \(userLevel.displayName))")
        return isAvailable
    }
    
    /// Проверяет является ли указанная дата выходным днем
    func isWeekendDay(_ date: Date = Date()) -> Bool {
        guard isWeekendSystemAvailable() else { return false }
        
        switch currentStrategy {
        case .threeOne:
            return isWeekendDayForThreeOne(date)
        case .fiveTwo:
            return isWeekendDayForFiveTwo(date)
        case .custom:
            return isWeekendDayForCustom(date)
        }
    }
    
    /// Рассчитывает дату следующего выходного дня
    func calculateNextWeekendDay() -> Date? {
        guard isWeekendSystemAvailable() else { return nil }
        
        switch currentStrategy {
        case .threeOne:
            return calculateNextWeekendForThreeOne()
        case .fiveTwo:
            return calculateNextWeekendForFiveTwo()
        case .custom:
            return calculateNextWeekendForCustom()
        }
    }
    
    /// Обрабатывает выбор пользователя в выходной день
    func handleWeekendChoice(_ choice: WeekendChoice) {
        let today = Date()
        
        switch choice {
        case .rest:
            Logger.shared.log(.info, "WeekendManager", "😴 Пользователь выбрал отдых")
            lastWeekendDate = today
            daysWithoutRest = 0
            recordWeekendCompliance(date: today, complied: true)
            
        case .work:
            Logger.shared.log(.info, "WeekendManager", "💼 Пользователь выбрал работу")
            daysWithoutRest += 1
            recordWeekendCompliance(date: today, complied: false)
            
            // Для стратегии 3/1 сдвигаем следующий выходной
            if currentStrategy == .threeOne {
                // Следующий выходной будет предложен через 3 рабочих дня
                Logger.shared.log(.info, "WeekendManager", "🔄 Стратегия 3/1: выходной сдвинут на завтра")
            }
        }
    }
    
    /// Проверяет нужно ли показать выбор выходного дня
    func shouldShowWeekendChoice() -> Bool {
        return isWeekendSystemAvailable() && isWeekendDay()
    }

    /// Возвращает количество дней без отдыха (публичный метод для статистики)
    func getDaysWithoutRest() -> Int {
        // Если система выходных недоступна, рассчитываем на основе реальной активности
        if !isWeekendSystemAvailable() {
            return calculateRealDaysWithoutRest()
        }

        // Если система доступна, используем счетчик из выборов пользователя
        return daysWithoutRest
    }

    /// Проверяет работал ли сегодня (с учетом эмуляции)
    private func didWorkToday() -> Bool {
        // Сначала проверяем эмулированную галочку
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let todayStr = dateFormatter.string(from: Date())

        if UserDefaults.standard.object(forKey: "worked_today_\(todayStr)") != nil {
            let emulatedWorked = UserDefaults.standard.bool(forKey: "worked_today_\(todayStr)")
            Logger.shared.log(.info, "WeekendManager", "🧪 Используем эмулированную работу сегодня: \(emulatedWorked)")
            return emulatedWorked
        }

        // Если эмуляции нет, используем реальные данные
        return DailyWorkloadManager.shared.didWorkTodayOnPriorityProject()
    }

    /// Рассчитывает количество дней работы после последнего выходного
    func calculateWorkingDaysAfterLastWeekend() -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        Logger.shared.log(.info, "WeekendManager", "🔍 Рассчитываем дни работы после последнего выходного")

        // Проверяем есть ли эмулированные дни работы (для отладки)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        var emulatedWorkingDays = 0
        for i in 1...7 {
            if let checkDate = calendar.date(byAdding: .day, value: -i, to: today) {
                let dateStr = dateFormatter.string(from: checkDate)
                if UserDefaults.standard.bool(forKey: "worked_\(dateStr)") {
                    emulatedWorkingDays += 1
                    Logger.shared.log(.info, "WeekendManager", "🧪 \(dateStr) - эмулированный рабочий день")
                } else {
                    break // Прерываем при первом дне без работы
                }
            }
        }

        if emulatedWorkingDays > 0 {
            // Добавляем сегодня если еще не работал (подразумеваем что буду)
            let workedToday = didWorkToday()
            let totalDays = emulatedWorkingDays + (workedToday ? 0 : 1)
            Logger.shared.log(.info, "WeekendManager", "🧪 Используем эмулированные дни: \(emulatedWorkingDays) + сегодня(\(workedToday ? 0 : 1)) = \(totalDays)")
            return totalDays
        }

        // Обычная логика если нет эмуляции
        var lastWeekendFound = false
        var workingDaysCount = 0

        // Проверяем последние 14 дней
        for dayOffset in 0..<14 {
            let checkDate = calendar.date(byAdding: .day, value: -dayOffset, to: today) ?? today
            let dateStr = self.dateFormatter.string(from: checkDate)

            if dayOffset == 0 {
                // Сегодня - всегда считаем что будем работать (подразумеваем) - НУЖНО ДЛЯ СТРИКОВ!
                workingDaysCount += 1
                Logger.shared.log(.info, "WeekendManager", "📅 \(dateStr) - сегодня (подразумеваем работу), дней работы: \(workingDaysCount)")
                continue
            }

            // Проверяем был ли это выходной день
            if wasWeekendDay(checkDate) {
                Logger.shared.log(.info, "WeekendManager", "🏖️ \(dateStr) - найден выходной день, останавливаем подсчет")
                lastWeekendFound = true
                break
            } else if hadWorkActivity(checkDate) {
                workingDaysCount += 1
                Logger.shared.log(.info, "WeekendManager", "💼 \(dateStr) - рабочий день, дней работы: \(workingDaysCount)")
            } else {
                Logger.shared.log(.info, "WeekendManager", "😴 \(dateStr) - день без активности, считаем как пропущенный рабочий день")
                // Не увеличиваем счетчик, но и не прерываем поиск
            }
        }

        if !lastWeekendFound {
            Logger.shared.log(.info, "WeekendManager", "⚠️ Не найден последний выходной за 14 дней, используем консервативную оценку")
            workingDaysCount = min(workingDaysCount, 3) // Максимум 3 дня
        }

        Logger.shared.log(.info, "WeekendManager", "📊 Итого дней работы после последнего выходного: \(workingDaysCount)")
        return workingDaysCount
    }

    /// Рассчитывает количество дней работы после последнего выходного БЕЗ подразумевания (только реальная активность)
    /// Используется ТОЛЬКО для расчета выходных дней
    private func calculateRealWorkingDaysAfterLastWeekend() -> Int {
        let calendar = Calendar.current
        let today = Date()

        Logger.shared.log(.info, "WeekendManager", "🔍 Расчет РЕАЛЬНЫХ дней работы после последнего выходного (БЕЗ эмуляции)")

        // ТОЛЬКО реальная логика - НЕ используем эмуляцию WeekendManager
        var lastWeekendFound = false
        var workingDaysCount = 0
        var workedToday = false

        // Проверяем последние 14 дней
        for dayOffset in 0..<14 {
            let checkDate = calendar.date(byAdding: .day, value: -dayOffset, to: today) ?? today
            let dateStr = self.dateFormatter.string(from: checkDate)
            let isToday = dayOffset == 0

            Logger.shared.log(.info, "WeekendManager", "🔍 Проверяем день \(dayOffset): \(dateStr)")

            // Проверяем реальную активность
            if hadWorkActivity(checkDate) {
                if isToday {
                    workedToday = true
                    Logger.shared.log(.info, "WeekendManager", "💼 \(dateStr) - СЕГОДНЯ работал")
                } else {
                    workingDaysCount += 1
                    Logger.shared.log(.info, "WeekendManager", "💼 \(dateStr) - рабочий день (реальная активность), дней работы: \(workingDaysCount)")
                }
            } else if wasRestDay(checkDate) {
                Logger.shared.log(.info, "WeekendManager", "🏖️ \(dateStr) - найден выходной день, останавливаем подсчет")
                lastWeekendFound = true
                break
            } else {
                if !isToday {
                    Logger.shared.log(.info, "WeekendManager", "😴 \(dateStr) - день без активности, считаем как пропущенный рабочий день")
                }
                // Не увеличиваем счетчик, но и не прерываем поиск
            }
        }

        if !lastWeekendFound {
            Logger.shared.log(.info, "WeekendManager", "⚠️ Не найден последний выходной за 14 дней, используем консервативную оценку")
            workingDaysCount = min(workingDaysCount, 3) // Максимум 3 дня
        }

        Logger.shared.log(.info, "WeekendManager", "📊 Итого РЕАЛЬНЫХ дней работы после последнего выходного: \(workingDaysCount), работал сегодня: \(workedToday)")

        // КЛЮЧЕВАЯ ЛОГИКА: если уже работал сегодня, добавляем сегодняшний день к счетчику
        // Это означает, что выходной будет предложен ЗАВТРА
        if workedToday {
            workingDaysCount += 1
            Logger.shared.log(.info, "WeekendManager", "📊 Добавляем сегодняшний день: итого \(workingDaysCount) дней")
        }

        return workingDaysCount
    }

    /// Проверяет был ли указанный день выходным
    private func wasWeekendDay(_ date: Date) -> Bool {
        // Проверяем записи о выборе пользователя в выходные дни
        let dateStr = dateFormatter.string(from: date)
        let compliance = getWeekendComplianceHistory()

        if let userChoice = compliance[dateStr] {
            // Если пользователь выбирал в этот день - значит это был предложенный выходной
            Logger.shared.log(.info, "WeekendManager", "📋 \(dateStr) - найдена запись о выборе пользователя: \(userChoice ? "отдыхал" : "работал")")
            return userChoice // true = отдыхал, false = работал
        }

        // Если нет записи, проверяем по активности
        return wasRestDay(date)
    }

    /// Рассчитывает дни без отдыха на основе реальной активности пользователя
    private func calculateRealDaysWithoutRest() -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        Logger.shared.log(.info, "WeekendManager", "🔍 Начинаем расчет дней без отдыха для пользователей без доступа")

        // Анализируем последние 7 дней
        var consecutiveDaysWithoutRest = 0

        for dayOffset in 0..<7 {
            let checkDate = calendar.date(byAdding: .day, value: -dayOffset, to: today) ?? today
            let formatter = DateFormatter()
            formatter.dateFormat = "dd.MM"
            let dateStr = formatter.string(from: checkDate)

            Logger.shared.log(.info, "WeekendManager", "🔍 Проверяем день \(dateStr) (offset: \(dayOffset))")

            // Проверяем был ли это день отдыха
            if wasRestDay(checkDate) {
                Logger.shared.log(.info, "WeekendManager", "🏖️ \(dateStr) - день отдыха, прерываем подсчет")
                break
            } else if hadWorkActivity(checkDate) {
                consecutiveDaysWithoutRest += 1
                Logger.shared.log(.info, "WeekendManager", "💼 \(dateStr) - рабочий день, дней без отдыха: \(consecutiveDaysWithoutRest)")
            } else if dayOffset == 0 {
                Logger.shared.log(.info, "WeekendManager", "📅 \(dateStr) - сегодня без активности, но день не закончился")
                continue
            } else {
                Logger.shared.log(.info, "WeekendManager", "😴 \(dateStr) - день без активности в прошлом, считаем как отдых")
                break
            }
        }

        Logger.shared.log(.info, "WeekendManager", "📊 Итого дней без отдыха: \(consecutiveDaysWithoutRest)")
        return consecutiveDaysWithoutRest
    }

    /// Проверяет был ли указанный день днем отдыха
    private func wasRestDay(_ date: Date) -> Bool {
        // Для стратегии 3/1 используем особую логику
        if currentStrategy == .threeOne {
            let calendar = Calendar.current
            let today = Date()
            let isToday = calendar.isDate(date, inSameDayAs: today)

            let intervalsCount = getIntervalsCountForDate(date)
            let dateStr = dateFormatter.string(from: date)

            // Для сегодняшнего дня: НЕ считаем выходным, даже если нет активности
            // Это позволяет проверить предыдущие дни с тестовыми данными
            if isToday {
                Logger.shared.log(.info, "WeekendManager", "🔍 wasRestDay для 3/1 (СЕГОДНЯ): \(dateStr) - интервалов: \(intervalsCount), НЕ считаем выходным")
                return false
            }

            // Для предыдущих дней: выходной только если нет активности
            let wasRest = intervalsCount == 0
            Logger.shared.log(.info, "WeekendManager", "🔍 wasRestDay для 3/1: \(dateStr) - интервалов: \(intervalsCount), отдых: \(wasRest)")

            return wasRest
        }

        // Для других стратегий используем старую логику
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: date)

        // Простая эвристика: суббота и воскресенье считаются днями отдыха
        // если в эти дни не было значительной рабочей активности
        let isWeekend = weekday == 1 || weekday == 7 // воскресенье = 1, суббота = 7

        if isWeekend && !hadWorkActivity(date) {
            return true
        }

        // Также проверяем не было ли это днем с очень малой активностью
        let intervalsCount = getIntervalsCountForDate(date)
        return intervalsCount == 0
    }

    /// Проверяет была ли рабочая активность в указанный день
    private func hadWorkActivity(_ date: Date) -> Bool {
        let intervalsCount = getIntervalsCountForDate(date)
        return intervalsCount > 0
    }

    /// Получает количество интервалов для указанной даты
    private func getIntervalsCountForDate(_ date: Date) -> Int {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? startOfDay

        // Используем StatisticsManager из AppDelegate для получения данных
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
            let count = appDelegate.statisticsManager.getCompletedIntervalsForDateRange(from: startOfDay, to: endOfDay)
            Logger.shared.log(.info, "WeekendManager", "🔍 Интервалов за \(dateFormatter.string(from: date)): \(count)")
            return count
        } else {
            Logger.shared.log(.error, "WeekendManager", "❌ Не удалось получить StatisticsManager из AppDelegate")
            return 0
        }
    }

    /// Возвращает информацию о следующем выходном дне для отображения в меню
    func getNextWeekendInfo() -> String? {
        switch currentStrategy {
        case .threeOne:
            return getNextWeekendInfoForThreeOne()
        case .fiveTwo:
            return getNextWeekendInfoForFiveTwo()
        case .custom:
            return getNextWeekendInfoForCustom()
        }
    }

    /// Возвращает процент соблюдения выходных за период
    func getWeekendCompliancePercentage(days: Int = 30) -> Double {
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -days, to: endDate) ?? endDate
        
        let history = getWeekendComplianceHistory()
        let relevantEntries = history.filter { entry in
            let date = dateFromString(entry.key)
            return date >= startDate && date <= endDate
        }
        
        guard !relevantEntries.isEmpty else { return 0.0 }
        
        let compliedCount = relevantEntries.filter { $0.value }.count
        let percentage = Double(compliedCount) / Double(relevantEntries.count) * 100.0
        
        Logger.shared.log(.debug, "WeekendManager", "📊 Соблюдение выходных за \(days) дней: \(Int(percentage))%")
        return percentage
    }
    
    // MARK: - Private Methods - Strategy Logic
    
    private func isWeekendDayForThreeOne(_ date: Date) -> Bool {
        // Для стратегии 3/1 проверяем количество рабочих дней с последнего выходного
        let workingDaysCount = calculateRealWorkingDaysAfterLastWeekend()

        Logger.shared.log(.info, "WeekendManager", "🔍 Стратегия 3/1: рабочих дней = \(workingDaysCount)")

        // Выходной предлагается СЕГОДНЯ только если:
        // - Ровно 3 дня работы (не работал сегодня)
        // - Проверяем именно сегодняшнюю дату
        let calendar = Calendar.current
        let isToday = calendar.isDate(date, inSameDayAs: Date())

        if isToday {
            // Для сегодняшней даты: выходной только если ровно 3 дня (не работал сегодня)
            return workingDaysCount == 3
        } else {
            // Для других дат используем стандартную логику
            return workingDaysCount >= 3
        }
    }
    
    private func isWeekendDayForFiveTwo(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: date)
        
        // Суббота (7) и воскресенье (1)
        return weekday == 1 || weekday == 7
    }
    
    private func isWeekendDayForCustom(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: date)
        
        // Получаем настройки рабочих дней
        let workingDays = getCustomWorkingDays()
        
        // Конвертируем weekday (1=воскресенье) в индекс массива (0=понедельник)
        let dayIndex = weekday == 1 ? 6 : weekday - 2
        
        // Если день не рабочий, то это выходной
        return dayIndex >= 0 && dayIndex < workingDays.count && !workingDays[dayIndex]
    }
    
    private func calculateNextWeekendForThreeOne() -> Date? {
        let calendar = Calendar.current
        let today = Date()

        Logger.shared.log(.info, "WeekendManager", "🔍 Расчет следующего выходного для 3/1: daysWithoutRest=\(daysWithoutRest)")

        // ПРИОРИТЕТ 1: Если есть пропущенные выходные - отдыхать СЕГОДНЯ
        if daysWithoutRest > 0 {
            // Проверяем работал ли уже сегодня
            let workedToday = didWorkToday()

            if workedToday {
                Logger.shared.log(.info, "WeekendManager", "⚠️ \(daysWithoutRest) дней без отдыха, но сегодня уже работал → выходной ЗАВТРА")
                return calendar.date(byAdding: .day, value: 1, to: today)
            } else {
                Logger.shared.log(.info, "WeekendManager", "⚠️ \(daysWithoutRest) дней без отдыха → выходной СЕГОДНЯ")
                return today
            }
        }

        // ПРИОРИТЕТ 2: Нет пропущенных выходных - используем систему 3/1
        let workingDays = calculateRealWorkingDaysAfterLastWeekend() // Используем РЕАЛЬНУЮ активность
        Logger.shared.log(.info, "WeekendManager", "📊 РЕАЛЬНЫХ дней работы после последнего выходного: \(workingDays)")

        if workingDays >= 3 {
            // 3+ дня работы - выходной СЕГОДНЯ (уже пора отдыхать)
            Logger.shared.log(.info, "WeekendManager", "📅 3+ дня работы → выходной СЕГОДНЯ")
            return today
        } else if workingDays == 2 {
            // 2 дня работы - выходной послезавтра (на 4-й день)
            Logger.shared.log(.info, "WeekendManager", "📅 2 дня работы → выходной ПОСЛЕЗАВТРА")
            return calendar.date(byAdding: .day, value: 2, to: today)
        } else if workingDays == 1 {
            // 1 день работы - выходной через 3 дня (на 4-й день)
            Logger.shared.log(.info, "WeekendManager", "📅 1 день работы → выходной ЧЕРЕЗ 3 ДНЯ")
            return calendar.date(byAdding: .day, value: 3, to: today)
        } else {
            // 0 дней работы - выходной через 3 дня (после 3-го дня)
            Logger.shared.log(.info, "WeekendManager", "📅 0 дней работы → выходной ЧЕРЕЗ 3 ДНЯ")
            return calendar.date(byAdding: .day, value: 3, to: today)
        }
    }
    
    private func calculateNextWeekendForFiveTwo() -> Date? {
        let calendar = Calendar.current
        let today = Date()
        let currentWeekday = calendar.component(.weekday, from: today)

        Logger.shared.log(.info, "WeekendManager", "🔍 Расчет следующего выходного для 5/2 (Пн-Пт работа, Сб-Вс отдых)")

        // Воскресенье = 1, Понедельник = 2, ..., Суббота = 7
        if currentWeekday == 1 || currentWeekday == 7 {
            // Сегодня суббота или воскресенье - выходной сегодня
            Logger.shared.log(.info, "WeekendManager", "📅 Сегодня выходной день (Сб/Вс)")
            return today
        }

        // Ищем ближайшую субботу
        let daysUntilSaturday = (7 - currentWeekday + 7) % 7
        let nextSaturday = calendar.date(byAdding: .day, value: daysUntilSaturday, to: today) ?? today

        Logger.shared.log(.info, "WeekendManager", "📅 Следующий выходной: суббота через \(daysUntilSaturday) дней")
        return nextSaturday
    }
    
    private func calculateNextWeekendForCustom() -> Date? {
        let calendar = Calendar.current
        let today = Date()
        let workingDays = getCustomWorkingDays()

        Logger.shared.log(.info, "WeekendManager", "🔍 Расчет следующего выходного для Своей стратегии")

        // Проверяем сегодня
        let todayWeekday = calendar.component(.weekday, from: today)
        let todayIndex = todayWeekday == 1 ? 6 : todayWeekday - 2 // Конвертируем в индекс массива (Пн=0, Вт=1, ..., Вс=6)

        if todayIndex >= 0 && todayIndex < workingDays.count && !workingDays[todayIndex] {
            Logger.shared.log(.info, "WeekendManager", "📅 Сегодня запланированный выходной")
            return today
        }

        // Ищем ближайший запланированный выходной день
        for i in 1...7 {
            let nextDate = calendar.date(byAdding: .day, value: i, to: today) ?? today
            let nextWeekday = calendar.component(.weekday, from: nextDate)
            let nextIndex = nextWeekday == 1 ? 6 : nextWeekday - 2

            if nextIndex >= 0 && nextIndex < workingDays.count && !workingDays[nextIndex] {
                Logger.shared.log(.info, "WeekendManager", "📅 Найден запланированный выходной через \(i) дней")
                return nextDate
            }
        }

        // Если не нашли выходной в ближайшие 7 дней, возвращаем завтра
        Logger.shared.log(.info, "WeekendManager", "⚠️ Не найден запланированный выходной, возвращаем завтра")
        return calendar.date(byAdding: .day, value: 1, to: today)
    }
    
    // MARK: - Private Methods - Helpers
    
    private func getCustomWorkingDays() -> [Bool] {
        // Получаем настройки рабочих дней из UserDefaults
        // По умолчанию: Пн-Пт рабочие, Сб-Вс выходные
        let defaultWorkingDays = [true, true, true, true, true, false, false]
        
        if let data = UserDefaults.standard.data(forKey: "workingDays"),
           let workingDays = try? JSONDecoder().decode([Bool].self, from: data) {
            return workingDays
        }
        
        return defaultWorkingDays
    }
    
    private func recordWeekendCompliance(date: Date, complied: Bool) {
        let dateString = stringFromDate(date)
        var history = getWeekendComplianceHistory()
        history[dateString] = complied
        
        if let data = try? JSONEncoder().encode(history) {
            UserDefaults.standard.set(data, forKey: "weekendComplianceHistory")
        }
        
        Logger.shared.log(.debug, "WeekendManager", "📝 Записано соблюдение выходного: \(dateString) = \(complied)")
    }
    
    private func getWeekendComplianceHistory() -> [String: Bool] {
        if let data = UserDefaults.standard.data(forKey: "weekendComplianceHistory"),
           let history = try? JSONDecoder().decode([String: Bool].self, from: data) {
            return history
        }
        return [:]
    }
    
    private func stringFromDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    private func dateFromString(_ string: String) -> Date {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: string) ?? Date()
    }

    // MARK: - Next Weekend Info Methods

    private func getNextWeekendInfoForThreeOne() -> String? {
        let calendar = Calendar.current
        let today = Date()

        // ПРИОРИТЕТ 1: Если есть пропущенные выходные
        if daysWithoutRest > 0 {
            let workedToday = didWorkToday()

            if workedToday {
                // Сегодня уже работал - выходной завтра
                let nextWeekendDate = calendar.date(byAdding: .day, value: 1, to: today) ?? today
                let dayName = getDayName(for: nextWeekendDate)
                return "Next day off: \(dayName) (\(daysWithoutRest) missed)"
            } else {
                // Еще не работал - выходной сегодня
                let dayName = getDayName(for: today)
                return "Next day off: \(dayName) (\(daysWithoutRest) missed)"
            }
        }

        // ПРИОРИТЕТ 2: Нет пропущенных выходных - система 3/1
        let workingDays = calculateRealWorkingDaysAfterLastWeekend() // Используем РЕАЛЬНУЮ активность
        Logger.shared.log(.info, "WeekendManager", "🔍 ОТЛАДКА getNextWeekendInfoForThreeOne: РЕАЛЬНЫХ workingDays=\(workingDays)")

        if workingDays == 3 {
            // Ровно 3 дня работы (не работал сегодня) - выходной СЕГОДНЯ
            let nextWeekendDate = today
            let dayName = getDayName(for: nextWeekendDate)
            Logger.shared.log(.info, "WeekendManager", "🔍 ОТЛАДКА: 3 дня (не работал сегодня) → СЕГОДНЯ (\(dayName))")
            return "Next day off: \(dayName)"
        } else if workingDays >= 4 {
            // 4+ дня работы (уже работал сегодня) - выходной ЗАВТРА
            let nextWeekendDate = calendar.date(byAdding: .day, value: 1, to: today) ?? today
            let dayName = getDayName(for: nextWeekendDate)
            Logger.shared.log(.info, "WeekendManager", "🔍 ОТЛАДКА: \(workingDays) дней (работал сегодня) → ЗАВТРА (\(dayName))")
            return "Next day off: \(dayName)"
        } else if workingDays == 2 {
            // 2 дня работы - выходной послезавтра (на 4-й день)
            let nextWeekendDate = calendar.date(byAdding: .day, value: 2, to: today) ?? today
            let dayName = getDayName(for: nextWeekendDate)
            Logger.shared.log(.info, "WeekendManager", "🔍 ОТЛАДКА: 2 дня → послезавтра (\(dayName))")
            return "Next day off: \(dayName)"
        } else if workingDays == 1 {
            // 1 день работы - выходной через 3 дня (на 4-й день)
            let nextWeekendDate = calendar.date(byAdding: .day, value: 3, to: today) ?? today
            let dayName = getDayName(for: nextWeekendDate)
            Logger.shared.log(.info, "WeekendManager", "🔍 ОТЛАДКА: 1 день → через 3 дня (\(dayName))")
            return "Next day off: \(dayName)"
        } else {
            // 0 дней работы - выходной через 3 дня (после 3-го дня)
            let nextWeekendDate = calendar.date(byAdding: .day, value: 3, to: today) ?? today
            let dayName = getDayName(for: nextWeekendDate)
            Logger.shared.log(.info, "WeekendManager", "🔍 ОТЛАДКА: 0 дней → через 3 дня (\(dayName))")
            return "Next day off: \(dayName)"
        }

        // Этот код недостижим, так как все случаи обработаны выше
        // let daysSinceLastWeekend = calendar.dateComponents([.day], from: lastWeekend, to: today).day ?? 0

        // if daysSinceLastWeekend >= 4 {
        //     // Пора отдыхать - сегодня выходной
        //     let dayName = getDayName(for: today)
        //     return "Next day off: \(dayName)"
        // } else {
        //     // Рассчитываем следующий выходной
        //     let daysUntilNext = 4 - daysSinceLastWeekend
        //     let nextWeekendDate = calendar.date(byAdding: .day, value: daysUntilNext, to: today) ?? today
        //     let dayName = getDayName(for: nextWeekendDate)
        //     return "Next day off: \(dayName)"
        // }
    }

    private func getNextWeekendInfoForFiveTwo() -> String? {
        let calendar = Calendar.current
        let today = Date()
        let currentWeekday = calendar.component(.weekday, from: today)

        // Показываем информацию о пропущенных выходных (для статистики)
        let missedInfo = daysWithoutRest > 0 ? " (\(daysWithoutRest) missed)" : ""

        // Воскресенье = 1, Понедельник = 2, ..., Суббота = 7
        if currentWeekday == 1 || currentWeekday == 7 {
            return "Next day off: Today\(missedInfo)"
        }

        // Ищем ближайшую субботу
        let daysUntilSaturday = (7 - currentWeekday + 7) % 7
        let nextSaturday = calendar.date(byAdding: .day, value: daysUntilSaturday, to: today) ?? today
        let dayName = getDayName(for: nextSaturday)
        return "Next day off: \(dayName)\(missedInfo)"
    }

    private func getNextWeekendInfoForCustom() -> String? {
        let workingDays = getCustomWorkingDays()
        let calendar = Calendar.current
        let today = Date()

        // Показываем информацию о пропущенных выходных (для статистики)
        let missedInfo = daysWithoutRest > 0 ? " (\(daysWithoutRest) missed)" : ""

        // Проверяем сегодня
        let todayWeekday = calendar.component(.weekday, from: today)
        let todayIndex = todayWeekday == 1 ? 6 : todayWeekday - 2 // Конвертируем в индекс массива (Пн=0, Вт=1, ..., Вс=6)

        if todayIndex >= 0 && todayIndex < workingDays.count && !workingDays[todayIndex] {
            return "Next day off: Today\(missedInfo)"
        }

        // Ищем следующий запланированный выходной день
        for i in 1...7 {
            let nextDate = calendar.date(byAdding: .day, value: i, to: today) ?? today
            let nextWeekday = calendar.component(.weekday, from: nextDate)
            let nextIndex = nextWeekday == 1 ? 6 : nextWeekday - 2

            if nextIndex >= 0 && nextIndex < workingDays.count && !workingDays[nextIndex] {
                let dayName = getDayName(for: nextDate)
                return "Next day off: \(dayName)\(missedInfo)"
            }
        }

        return "Next day off: None set\(missedInfo)"
    }

    private func getDayName(for date: Date) -> String {
        let calendar = Calendar.current
        let today = Date()

        if calendar.isDate(date, inSameDayAs: today) {
            return "Today"
        }

        if calendar.isDate(date, inSameDayAs: calendar.date(byAdding: .day, value: 1, to: today) ?? today) {
            return "Tomorrow"
        }

        let formatter = DateFormatter()
        formatter.dateFormat = "EEE" // Mon, Tue, Wed, etc.
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: date)
    }
}
