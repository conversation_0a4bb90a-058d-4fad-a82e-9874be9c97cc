import Cocoa

/// Отладочное окно для тестирования системы взращивания сессий
class SessionGrowthDebugWindow: NSWindow {
    
    // MARK: - UI Elements
    private var startMinutesSlider: NSSlider!
    private var startMinutesLabel: NSTextField!
    private var targetMinutesSlider: NSSlider!
    private var targetMinutesLabel: NSTextField!
    private var resultLabel: NSTextField!
    private var bonusLabel: NSTextField!
    
    // MARK: - Current Values
    private var currentStartMinutes: Int = 3  // Начальный интервал
    private var currentTargetMinutes: Int = 30 // Планка пользователя
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 100, y: 100, width: 600, height: 300),
                  styleMask: [.titled, .closable, .resizable],
                  backing: .buffered,
                  defer: false)

        self.title = "🌱 Калькулятор взращивания сессий"
        self.isReleasedWhenClosed = false

        setupUI()
        updateResults()
    }
    
    private func setupUI() {
        guard let contentView = self.contentView else { return }

        // Основной контейнер
        let mainStack = NSStackView()
        mainStack.orientation = .vertical
        mainStack.spacing = 20
        mainStack.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(mainStack)

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🌱 Калькулятор взращивания сессий")
        titleLabel.font = NSFont.boldSystemFont(ofSize: 16)
        titleLabel.alignment = .center
        mainStack.addArrangedSubview(titleLabel)

        // Начальный интервал
        let startRow = createSliderRow(
            title: "Начальный интервал:",
            minValue: 3, maxValue: 50, currentValue: 3,
            slider: &startMinutesSlider,
            label: &startMinutesLabel,
            action: #selector(startMinutesChanged)
        )
        mainStack.addArrangedSubview(startRow)

        // Планка пользователя
        let targetRow = createSliderRow(
            title: "Планка пользователя:",
            minValue: 3, maxValue: 52, currentValue: 30,
            slider: &targetMinutesSlider,
            label: &targetMinutesLabel,
            action: #selector(targetMinutesChanged)
        )
        mainStack.addArrangedSubview(targetRow)

        // Результат взращивания до целевой планки
        let growthTitle = NSTextField(labelWithString: "🌱 Взращивание до целевой планки:")
        growthTitle.font = NSFont.boldSystemFont(ofSize: 14)
        growthTitle.alignment = .center
        mainStack.addArrangedSubview(growthTitle)

        resultLabel = NSTextField(labelWithString: "")
        resultLabel.font = NSFont.monospacedSystemFont(ofSize: 12, weight: .medium)
        resultLabel.alignment = .center
        resultLabel.textColor = .systemBlue
        resultLabel.backgroundColor = .controlBackgroundColor
        resultLabel.isBezeled = true
        resultLabel.isEditable = false
        mainStack.addArrangedSubview(resultLabel)

        // Дополнительно до максимума
        let bonusTitle = NSTextField(labelWithString: "🚀 Дополнительно до максимума (52 мин):")
        bonusTitle.font = NSFont.boldSystemFont(ofSize: 14)
        bonusTitle.alignment = .center
        mainStack.addArrangedSubview(bonusTitle)

        let bonusLabel = NSTextField(labelWithString: "")
        bonusLabel.font = NSFont.monospacedSystemFont(ofSize: 12, weight: .medium)
        bonusLabel.alignment = .center
        bonusLabel.textColor = .systemOrange
        bonusLabel.backgroundColor = .controlBackgroundColor
        bonusLabel.isBezeled = true
        bonusLabel.isEditable = false
        mainStack.addArrangedSubview(bonusLabel)

        // Сохраняем ссылку на bonusLabel
        self.bonusLabel = bonusLabel

        // Кнопка тестирования окна
        let testButton = NSButton(title: "🧪 Тест окна взращивания", target: self, action: #selector(testWindowClicked))
        testButton.bezelStyle = .rounded
        testButton.controlSize = .regular
        testButton.translatesAutoresizingMaskIntoConstraints = false
        mainStack.addArrangedSubview(testButton)

        // Constraints
        NSLayoutConstraint.activate([
            mainStack.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            mainStack.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            mainStack.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            mainStack.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            resultLabel.heightAnchor.constraint(equalToConstant: 40),
            bonusLabel.heightAnchor.constraint(equalToConstant: 40)
        ])
    }
    

    
    private func createSliderRow(
        title: String,
        minValue: Double, maxValue: Double, currentValue: Double,
        slider: inout NSSlider!,
        label: inout NSTextField!,
        action: Selector
    ) -> NSStackView {
        let row = NSStackView()
        row.orientation = .horizontal
        row.spacing = 10
        
        let titleLabel = NSTextField(labelWithString: title)
        titleLabel.setContentHuggingPriority(.required, for: .horizontal)
        titleLabel.widthAnchor.constraint(equalToConstant: 150).isActive = true
        row.addArrangedSubview(titleLabel)
        
        slider = NSSlider(value: currentValue, minValue: minValue, maxValue: maxValue, target: self, action: action)
        slider.widthAnchor.constraint(equalToConstant: 200).isActive = true
        row.addArrangedSubview(slider)
        
        label = NSTextField(labelWithString: "\(Int(currentValue)) мин")
        label.setContentHuggingPriority(.required, for: .horizontal)
        label.widthAnchor.constraint(equalToConstant: 60).isActive = true
        row.addArrangedSubview(label)
        
        return row
    }
    


    // MARK: - Event Handlers

    @objc private func startMinutesChanged() {
        currentStartMinutes = Int(startMinutesSlider.doubleValue)
        startMinutesLabel.stringValue = "\(currentStartMinutes) мин"

        // Автоматически корректируем планку если она меньше старта
        if currentTargetMinutes <= currentStartMinutes {
            currentTargetMinutes = currentStartMinutes + 5
            targetMinutesSlider.doubleValue = Double(currentTargetMinutes)
            targetMinutesLabel.stringValue = "\(currentTargetMinutes) мин"
        }

        updateResults()
    }

    @objc private func targetMinutesChanged() {
        currentTargetMinutes = Int(targetMinutesSlider.doubleValue)
        targetMinutesLabel.stringValue = "\(currentTargetMinutes) мин"

        // Автоматически корректируем старт если он больше планки
        if currentStartMinutes >= currentTargetMinutes {
            currentStartMinutes = max(3, currentTargetMinutes - 5)
            startMinutesSlider.doubleValue = Double(currentStartMinutes)
            startMinutesLabel.stringValue = "\(currentStartMinutes) мин"
        }

        updateResults()
    }



    // MARK: - Helper Methods

    private func updateResults() {
        // Определяем целевую планку (как в SessionGrowingEngine)
        let growthTarget = determineGrowthTarget(userBarMinutes: currentTargetMinutes)

        // Рассчитываем взращивание до целевой планки
        let growthSequence = SessionGrowthMatrix.calculateGrowthSequence(
            startMinutes: currentStartMinutes,
            targetTotalMinutes: growthTarget,
            mode: .standard
        )

        // Показываем взращивание до целевой планки
        let sequenceStr = growthSequence.map(String.init).joined(separator: " → ")
        let total = growthSequence.reduce(0, +)

        if growthSequence.count > 1 {
            let targetType = currentTargetMinutes <= 15 ? "неформальной планки" : "планки"
            resultLabel.stringValue = "\(sequenceStr) (итого \(total) мин до \(targetType))"
        } else {
            let remaining = growthTarget - currentStartMinutes
            if remaining <= 1 {
                resultLabel.stringValue = "Взращивание не предлагается (остается ≤1 мин)"
            } else {
                resultLabel.stringValue = "Взращивание не предлагается"
            }
        }

        // Показываем дополнительно до максимума
        let bonusMinutes = 52 - total
        if bonusMinutes > 0 && total > 0 {
            bonusLabel.stringValue = "+\(bonusMinutes) мин (итого 52 мин)"
        } else {
            bonusLabel.stringValue = "Не требуется"
        }
    }

    /// Определяет целевую планку для взращивания (копия логики из SessionGrowingEngine)
    private func determineGrowthTarget(userBarMinutes: Int) -> Int {
        if userBarMinutes <= 15 {
            return 30  // Неформальная планка для новичков
        } else {
            return userBarMinutes  // Реальная планка для опытных
        }
    }

    @objc private func testWindowClicked() {
        // Определяем тип окна на основе текущих параметров
        let growthTarget = determineGrowthTarget(userBarMinutes: currentTargetMinutes)
        let growthSequence = SessionGrowthMatrix.calculateGrowthSequence(
            startMinutes: currentStartMinutes,
            targetTotalMinutes: growthTarget,
            mode: .standard
        )

        guard growthSequence.count > 1 else {
            let alert = NSAlert()
            alert.messageText = "Тест невозможен"
            alert.informativeText = "Взращивание не предлагается для данных параметров"
            alert.runModal()
            return
        }

        // Берем следующий интервал (второй в последовательности)
        let nextInterval = growthSequence[1]
        let afterNextTotal = currentStartMinutes + nextInterval

        // Определяем тип окна
        let windowType: SessionGrowthWindow.GrowthWindowType

        if afterNextTotal >= growthTarget {
            // Достигнем целевой планки
            if growthTarget < 52 {
                let remainingToMax = 52 - afterNextTotal
                windowType = .reachMaximum(remainingMinutes: remainingToMax)
            } else {
                windowType = .reachTarget(remainingMinutes: nextInterval)
            }
        } else {
            // Промежуточное взращивание
            windowType = .continueGrowth(nextMinutes: nextInterval)
        }

        // Показываем тестовое окно
        let testWindow = SessionGrowthWindow(type: windowType)
        testWindow.onAccept = {
            print("🧪 Тест: Пользователь принял предложение")
        }
        testWindow.onDecline = {
            print("🧪 Тест: Пользователь отклонил предложение")
        }
        testWindow.showWithAnimation()
    }
}
