import Cocoa

/// Окно мягкого предложения через 3 часа после отказов
class GentleAfternoonOfferWindow: NSWindow {
    
    // MARK: - UI Elements
    private var containerView: NSView!
    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var motivationLabel: NSTextField!
    private var yesButton: NSButton!
    private var noButton: NSButton!
    private var projectLabel: NSTextField!
    
    // MARK: - Callbacks
    var onAccept: ((UUID?) -> Void)?
    var onDecline: (() -> Void)?
    
    // MARK: - Properties
    private var currentProjectId: UUID?
    
    // MARK: - Initialization
    
    init() {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 400, height: 180),
                   styleMask: [.borderless],
                   backing: .buffered,
                   defer: false)

        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true
        // ИСПРАВЛЕНО: Убираем center() - позиционирование будет через positionRelativeToStatusItem()

        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // Создаем основной контейнер
        containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Настраиваем унифицированный фон (тип "отдых" для мягкого предложения)
        self.setupUnifiedBackground(type: .rest, for: containerView)

        self.contentView = containerView

        // Создаем элементы UI
        createUIElements()
        setupConstraints()

        Logger.shared.log(.info, "GentleAfternoonOffer", "🪟 Окно создано")
    }
    
    private func createUIElements() {
        // Заголовок
        titleLabel = NSTextField.createUnifiedTitle(text: "🌅 Может все-таки поработаем?")

        // Подзаголовок
        subtitleLabel = NSTextField.createUnifiedSubtitle(text: "Настроение могло измениться")

        // Мотивационное сообщение
        motivationLabel = NSTextField.createUnifiedSubtitle(text: getMotivationalMessage())
        motivationLabel.font = NSFont.systemFont(ofSize: 12, weight: .regular)
        motivationLabel.textColor = NSColor(red: 0.8, green: 0.9, blue: 1.0, alpha: 1.0)

        // Кнопки
        yesButton = NSButton.createUnifiedButton(title: "Да, попробуем", type: UIButtonType.green, isSmall: false)
        yesButton.target = self
        yesButton.action = #selector(acceptButtonClicked)

        noButton = NSButton.createUnifiedButton(title: "Нет, спасибо", type: UIButtonType.gray, isSmall: false)
        noButton.target = self
        noButton.action = #selector(declineButtonClicked)
        
        // Информация о проекте
        let projectInfo = getCurrentProject()
        projectLabel = NSTextField.createUnifiedSubtitle(text: "\(projectInfo.emoji) \(projectInfo.name)")
        projectLabel.font = NSFont.systemFont(ofSize: 11, weight: .regular)
        projectLabel.textColor = NSColor(white: 0.7, alpha: 1.0)
        
        // Добавляем элементы в контейнер
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(motivationLabel)
        containerView.addSubview(yesButton)
        containerView.addSubview(noButton)
        containerView.addSubview(projectLabel)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            titleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            // Подзаголовок
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 6),
            subtitleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            subtitleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            subtitleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            // Мотивационное сообщение
            motivationLabel.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 8),
            motivationLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            motivationLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            motivationLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            // Кнопки
            yesButton.topAnchor.constraint(equalTo: motivationLabel.bottomAnchor, constant: 20),
            yesButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            yesButton.heightAnchor.constraint(equalToConstant: 32),
            yesButton.widthAnchor.constraint(equalToConstant: 150),
            
            noButton.topAnchor.constraint(equalTo: motivationLabel.bottomAnchor, constant: 20),
            noButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            noButton.heightAnchor.constraint(equalToConstant: 32),
            noButton.widthAnchor.constraint(equalToConstant: 150),
            
            // Информация о проекте - выравниваем по центру зеленой кнопки
            projectLabel.topAnchor.constraint(equalTo: yesButton.bottomAnchor, constant: 8),
            projectLabel.centerXAnchor.constraint(equalTo: yesButton.centerXAnchor),
            projectLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15)
        ])
    }

    // MARK: - Positioning

    /// Позиционирует окно под иконкой приложения в status bar (как все остальные окна)
    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        Logger.shared.log(.info, "GentleAfternoonOffer", "📍 Позиционирование под status item")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом (как ModernCompletionWindow)
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        self.setFrameOrigin(NSPoint(x: x, y: y))
        Logger.shared.log(.info, "GentleAfternoonOffer", "📍 Позиция установлена: x=\(Int(x)), y=\(Int(y))")
    }

    // MARK: - Public Methods
    
    /// Показывает окно с мягким предложением
    func showGentleOffer(projectId: UUID? = nil) {
        self.currentProjectId = projectId
        updateProjectInfo()
        updateMotivationalMessage()
        makeKeyAndOrderFront(nil)
        
        Logger.shared.log(.info, "GentleAfternoonOffer", "🌅 Показано мягкое предложение")
    }
    
    /// Обновляет информацию о проекте
    private func updateProjectInfo() {
        let projectInfo = getCurrentProject()
        projectLabel.stringValue = "\(projectInfo.emoji) \(projectInfo.name)"
    }
    
    /// Обновляет мотивационное сообщение
    private func updateMotivationalMessage() {
        motivationLabel.stringValue = getMotivationalMessage()
    }
    
    // MARK: - Actions
    
    @objc private func acceptButtonClicked() {
        Logger.shared.log(.info, "GentleAfternoonOffer", "✅ Пользователь согласился на мягкое предложение")
        
        self.hideWithUnifiedAnimation { [weak self] in
            self?.onAccept?(self?.currentProjectId)
        }
    }
    
    @objc private func declineButtonClicked() {
        Logger.shared.log(.info, "GentleAfternoonOffer", "❌ Пользователь отказался от мягкого предложения")

        // ИСПРАВЛЕНО: Уведомляем DailyWorkloadManager о ФИНАЛЬНОМ отказе от мягкого предложения
        // Это означает что больше не беспокоим пользователя сегодня
        DailyWorkloadManager.shared.handleGentleOfferRefusal()

        self.hideWithUnifiedAnimation { [weak self] in
            self?.onDecline?()
        }
    }
    
    // MARK: - Helper Methods
    
    private func getCurrentProject() -> (name: String, emoji: String) {
        // Получаем текущий проект из AppDelegate
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate,
           let projectId = currentProjectId ?? appDelegate.currentProjectId,
           let project = appDelegate.projectManager.getProject(by: projectId) {
            return (name: project.name, emoji: project.effectiveEmoji)
        }

        // Возвращаем проект по умолчанию
        return (name: "Общая работа", emoji: "💼")
    }
    
    private func getMotivationalMessage() -> String {
        let messages = [
            "Даже 3 минуты могут быть полезными",
            "Небольшая сессия поможет завершить день на позитивной ноте",
            "Иногда самые продуктивные моменты приходят неожиданно",
            "Короткая работа лучше, чем никакой работы",
            "Может быть, сейчас как раз подходящий момент?",
            "Попробуйте минималочку - всего несколько минут"
        ]
        
        // Выбираем сообщение на основе времени дня
        let hour = Calendar.current.component(.hour, from: Date())
        let index = hour % messages.count
        return messages[index]
    }
    
    private func getTimeBasedTitle() -> String {
        let hour = Calendar.current.component(.hour, from: Date())
        
        if hour < 15 {
            return "🌅 Может все-таки поработаем?"
        } else if hour < 17 {
            return "🌇 Последний шанс поработать?"
        } else {
            return "🌆 Завершим день продуктивно?"
        }
    }
    
    // MARK: - Keyboard Support
    
    override func keyDown(with event: NSEvent) {
        switch event.keyCode {
        case 36: // Enter
            acceptButtonClicked()
        case 53: // Escape
            declineButtonClicked()
        default:
            super.keyDown(with: event)
        }
    }
    
    // MARK: - Window Lifecycle
    
    override func awakeFromNib() {
        super.awakeFromNib()
        self.acceptsMouseMovedEvents = true
    }
    
    override func mouseDown(with event: NSEvent) {
        // Позволяем перетаскивать окно
        self.performDrag(with: event)
    }
}

// MARK: - Static Factory Method

extension GentleAfternoonOfferWindow {
    
    /// Создает и показывает окно мягкого предложения
    static func show(projectId: UUID? = nil,
                     statusItemFrame: NSRect = NSRect.zero,
                     onAccept: @escaping (UUID?) -> Void,
                     onDecline: @escaping () -> Void) {
        let window = GentleAfternoonOfferWindow()
        window.onAccept = onAccept
        window.onDecline = onDecline

        // ИСПРАВЛЕНО: Позиционируем окно под иконкой перед показом
        if statusItemFrame != NSRect.zero {
            window.positionRelativeToStatusItem(statusItemFrame: statusItemFrame)
        }

        window.showGentleOffer(projectId: projectId)
    }
}
