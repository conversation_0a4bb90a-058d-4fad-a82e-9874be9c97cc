//
//  CriticalZoneWindow.swift
//  uProd
//
//  Критическое полноэкранное окно для уровня 4+ эскалации
//  Блокирует весь экран, нет кнопки "I need a couple of minutes"
//

import Cocoa

protocol CriticalZoneWindowDelegate: AnyObject {
    func criticalZoneWindowDidTakeBreak()
}

class CriticalZoneWindow: NSWindow {

    // MARK: - Properties

    weak var criticalDelegate: CriticalZoneWindowDelegate?
    private let minutes: Int
    private let intervalType: String
    
    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var timeLabel: NSTextField!
    private var messageLabel: NSTextField!
    private var takeBreakButton: NSButton!
    
    // MARK: - Initialization
    
    init(minutes: Int, intervalType: String) {
        self.minutes = minutes
        self.intervalType = intervalType
        
        // Получаем размер главного экрана
        let screenFrame = NSScreen.main?.frame ?? NSRect(x: 0, y: 0, width: 1920, height: 1080)
        
        super.init(
            contentRect: screenFrame,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
        setupUI()
    }
    
    // MARK: - Window Setup
    
    private func setupWindow() {
        print("🚨 CriticalZoneWindow: Настройка полноэкранного окна")
        
        // Критические настройки для блокировки экрана
        self.level = .screenSaver  // Самый высокий уровень
        self.isOpaque = false
        self.backgroundColor = NSColor.black.withAlphaComponent(0.95) // Почти черный фон
        self.ignoresMouseEvents = false
        self.hasShadow = false
        
        // Показываем на всех рабочих столах и пространствах
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
        
        // Делаем окно всегда поверх всего
        self.hidesOnDeactivate = false
        
        print("🚨 CriticalZoneWindow: Окно настроено для блокировки экрана")
    }
    
    private func setupUI() {
        print("🚨 CriticalZoneWindow: Создание UI")

        // Создаем основной контейнер с наблюдением за layout
        let containerView = LayoutObservingView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        // Главный заголовок
        titleLabel = NSTextField(labelWithString: "🚨 CRITICAL ZONE")
        titleLabel.font = NSFont.systemFont(ofSize: 48, weight: .bold)
        titleLabel.textColor = NSColor.red
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // Подзаголовок
        let totalMinutes = (intervalType == "informal" ? 52 : 25) + minutes
        subtitleLabel = NSTextField(labelWithString: "You've been working for \(totalMinutes)+ minutes")
        subtitleLabel.font = NSFont.systemFont(ofSize: 24, weight: .medium)
        subtitleLabel.textColor = NSColor.white
        subtitleLabel.alignment = .center
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // Время переработки
        timeLabel = NSTextField(labelWithString: "Overtime: \(minutes) minutes")
        timeLabel.font = NSFont.systemFont(ofSize: 20, weight: .regular)
        timeLabel.textColor = NSColor.orange
        timeLabel.alignment = .center
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // Сообщение о здоровье
        messageLabel = NSTextField(labelWithString: "⚠️ Extended computer use can harm your health\n💪 Take a break to protect your eyes, posture, and mental well-being\n🌿 Your productivity will improve after rest")
        messageLabel.font = NSFont.systemFont(ofSize: 18, weight: .regular)
        messageLabel.textColor = NSColor.white
        messageLabel.alignment = .center
        messageLabel.maximumNumberOfLines = 0
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // Единственная кнопка - Take a break
        takeBreakButton = NSButton(title: "🌿 Take a break now", target: self, action: #selector(takeBreakPressed))
        takeBreakButton.font = NSFont.systemFont(ofSize: 20, weight: .bold)
        takeBreakButton.translatesAutoresizingMaskIntoConstraints = false
        
        // Стилизуем кнопку
        setupTakeBreakButton()
        
        // Добавляем все элементы
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(messageLabel)
        containerView.addSubview(takeBreakButton)
        
        self.contentView = containerView

        // Настраиваем callback для обновления градиента при изменении layout
        containerView.onLayoutUpdate = { [weak self] in
            guard let self = self else { return }
            if let gradientLayer = self.takeBreakButton.layer?.value(forKey: "gradientLayer") as? CAGradientLayer {
                gradientLayer.frame = self.takeBreakButton.bounds
            }
        }

        // Настраиваем constraints
        setupConstraints()

        print("🚨 CriticalZoneWindow: UI создан")
    }
    
    private func setupTakeBreakButton() {
        // Убираем стандартный стиль кнопки
        takeBreakButton.isBordered = false
        takeBreakButton.wantsLayer = true

        // Создаем градиентный фон для кнопки
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            NSColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0).cgColor,
            NSColor(red: 0.1, green: 0.6, blue: 0.1, alpha: 1.0).cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = 12

        // Тень для кнопки
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.5
        gradientLayer.shadowOffset = CGSize(width: 0, height: 4)
        gradientLayer.shadowRadius = 8

        // Настраиваем слой кнопки
        takeBreakButton.layer?.backgroundColor = NSColor.clear.cgColor
        takeBreakButton.layer?.cornerRadius = 12

        // Добавляем градиент как подслой, а не заменяем основной слой
        takeBreakButton.layer?.insertSublayer(gradientLayer, at: 0)

        // Белый текст
        takeBreakButton.attributedTitle = NSAttributedString(
            string: "🌿 Take a break now",
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 20, weight: .bold)
            ]
        )

        // Сохраняем ссылку на градиентный слой для обновления размера
        takeBreakButton.layer?.setValue(gradientLayer, forKey: "gradientLayer")

        // Обновляем размер градиента при изменении размера кнопки
        DispatchQueue.main.async {
            gradientLayer.frame = self.takeBreakButton.bounds
        }
    }

    // Создаем кастомный контейнер для обработки изменений layout
    class LayoutObservingView: NSView {
        var onLayoutUpdate: (() -> Void)?

        override func layout() {
            super.layout()
            onLayoutUpdate?()
        }
    }

    private func setupConstraints() {
        guard let containerView = self.contentView else { return }
        
        NSLayoutConstraint.activate([
            // Заголовок
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 150),
            
            // Подзаголовок
            subtitleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 30),
            
            // Время
            timeLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            timeLabel.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 20),
            
            // Сообщение
            messageLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            messageLabel.topAnchor.constraint(equalTo: timeLabel.bottomAnchor, constant: 50),
            messageLabel.widthAnchor.constraint(lessThanOrEqualToConstant: 800),
            
            // Кнопка
            takeBreakButton.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            takeBreakButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 80),
            takeBreakButton.widthAnchor.constraint(equalToConstant: 300),
            takeBreakButton.heightAnchor.constraint(equalToConstant: 60)
        ])
    }
    
    // MARK: - Actions
    
    @objc private func takeBreakPressed() {
        print("🌿 CriticalZoneWindow: Пользователь выбрал отдых")
        
        // Анимация исчезновения
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            self.alphaValue = 0
        }) {
            // Закрываем окно и уведомляем делегата
            self.orderOut(nil)
            self.criticalDelegate?.criticalZoneWindowDidTakeBreak()
        }
    }
    
    // MARK: - Public Methods
    
    func showWithAnimation() {
        print("🚨 CriticalZoneWindow: Показываем с анимацией")
        
        // Начинаем с прозрачности
        self.alphaValue = 0
        self.makeKeyAndOrderFront(nil)
        
        // Анимация появления
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.5
            self.alphaValue = 1.0
        })
        
        // Принудительно захватываем фокус
        NSApp.activate(ignoringOtherApps: true)
        self.makeKey()
        
        print("🚨 CriticalZoneWindow: Анимация показа завершена")
    }
    
    // MARK: - Window Events
    
    override func keyDown(with event: NSEvent) {
        // Блокируем все клавиши кроме Escape (для экстренного выхода)
        if event.keyCode == 53 { // Escape
            print("🚨 CriticalZoneWindow: Escape нажат - экстренное закрытие")
            takeBreakPressed()
        }
        // Все остальные клавиши игнорируем
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
}
