import Cocoa
import CoreGraphics

/// Единая система проверки активности пользователя для всех компонентов приложения
/// Используется для формальных интервалов, неформальных интервалов, отдыха и статистики
class UnifiedActivityChecker {
    
    // MARK: - Singleton
    static let shared = UnifiedActivityChecker()
    private init() {}
    
    // MARK: - Configuration
    
    /// Порог активности в секундах (если активность была менее N секунд назад - считаем активным)
    private let activityThreshold: TimeInterval = 30.0
    
    /// Порог для "недавней активности" (для проверки за последнюю минуту)
    private let recentActivityThreshold: TimeInterval = 60.0
    
    // MARK: - Public Methods
    
    /// Основной метод проверки активности пользователя
    /// Возвращает true, если пользователь активен прямо сейчас (активность за последние 15 секунд)
    func isUserCurrentlyActive() -> Bool {
        let isActive = checkSystemActivity()

        // Убираем избыточное логирование - вызывается каждые 3 секунды
        // logDebug("ActivityChecker", "🔍 Проверка текущей активности - \(isActive ? "АКТИВЕН" : "НЕАКТИВЕН")")
        return isActive
    }
    
    /// Проверяет, была ли активность за последнюю минуту
    /// Используется для проверки "недавней активности"
    func wasActiveInLastMinute() -> Bool {
        let isActive = checkSystemActivity(threshold: recentActivityThreshold)
        
        // Убираем избыточное логирование
        return isActive
    }
    
    /// Проверяет, находится ли приложение в состоянии "idle" (неактивности)
    /// Возвращает true, если пользователь НЕ активен
    func isAppIdle() -> Bool {
        let isIdle = !isUserCurrentlyActive()
        
        // Убираем избыточное логирование
        return isIdle
    }
    
    // MARK: - Private Methods
    
    /// Проверяет активность пользователя через macOS APIs
    /// - Parameter threshold: Порог времени в секундах (по умолчанию activityThreshold)
    /// - Returns: true если была активность за указанный период
    private func checkSystemActivity(threshold: TimeInterval? = nil) -> Bool {
        // Для тестирования: если установлен принудительный результат, возвращаем его
        if let forcedResult = forcedActivityResult {
            print("🧪 UnifiedActivityChecker: Возвращаем принудительный результат: \(forcedResult)")
            return forcedResult
        }
        let checkThreshold = threshold ?? activityThreshold
        
        // Получаем время последней активности пользователя через системные API
        let mouseIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .mouseMoved)
        let keyboardIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .keyDown)
        let clickIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .leftMouseDown)
        let scrollIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .scrollWheel)
        
        // Считаем пользователя активным, если последняя активность была менее checkThreshold секунд назад
        let isActive = mouseIdleTime < checkThreshold ||
                      keyboardIdleTime < checkThreshold ||
                      clickIdleTime < checkThreshold ||
                      scrollIdleTime < checkThreshold
        
        // Дополнительная проверка через системные процессы (временно отключена для отладки)
        let isSystemActive = false // isSystemShowingActivity()

        let finalResult = isActive || isSystemActive
        
        // Логируем детали активности для отладки (всегда)
        let logMessage = "мышь: \(String(format: "%.1f", mouseIdleTime))с, клавиатура: \(String(format: "%.1f", keyboardIdleTime))с, клики: \(String(format: "%.1f", clickIdleTime))с, скролл: \(String(format: "%.1f", scrollIdleTime))с, система: \(isSystemActive)"

        // Убираем избыточное логирование деталей - вызывается каждые 3 секунды
        // Оставляем только для отладки при необходимости
        /*
        if finalResult {
            logDebug("ActivityChecker", "🔍 Активность обнаружена - \(logMessage)")
        } else {
            logDebug("ActivityChecker", "🔍 Активность НЕ обнаружена - \(logMessage)")
        }
        */

        return finalResult
    }
    
    /// Дополнительная проверка активности через системные процессы
    /// Проверяет, показывает ли система признаки активности (видео, аудио и т.д.)
    private func isSystemShowingActivity() -> Bool {
        // Проверяем, не воспроизводится ли аудио/видео
        let workspace = NSWorkspace.shared
        let runningApps = workspace.runningApplications
        
        // Список приложений, которые могут указывать на активность пользователя
        let mediaApps = ["Safari", "Chrome", "Firefox", "VLC", "QuickTime Player", "Music", "Spotify", "YouTube"]
        
        for app in runningApps {
            if let appName = app.localizedName,
               mediaApps.contains(appName) && app.isActive {
                print("🔍 UnifiedActivityChecker: Обнаружено активное медиа-приложение: \(appName)")
                logInfo("ActivityChecker", "🔍 Обнаружено активное медиа-приложение: \(appName)")
                return true
            }
        }
        
        return false
    }
    
    // MARK: - Debug Methods
    
    /// Возвращает детальную информацию о состоянии активности для отладки
    func getDebugInfo() -> String {
        let mouseIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .mouseMoved)
        let keyboardIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .keyDown)
        let clickIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .leftMouseDown)
        let scrollIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .scrollWheel)
        
        let isCurrentlyActive = isUserCurrentlyActive()
        let wasActiveRecently = wasActiveInLastMinute()
        let isIdle = isAppIdle()
        
        var info = "UnifiedActivityChecker Debug:\n"
        info += "- Мышь idle: \(String(format: "%.1f", mouseIdleTime))с\n"
        info += "- Клавиатура idle: \(String(format: "%.1f", keyboardIdleTime))с\n"
        info += "- Клики idle: \(String(format: "%.1f", clickIdleTime))с\n"
        info += "- Скролл idle: \(String(format: "%.1f", scrollIdleTime))с\n"
        info += "- Активен сейчас: \(isCurrentlyActive)\n"
        info += "- Активен за минуту: \(wasActiveRecently)\n"
        info += "- Приложение idle: \(isIdle)\n"
        info += "- Порог активности: \(activityThreshold)с\n"
        info += "- Порог недавней активности: \(recentActivityThreshold)с"
        
        return info
    }

    // MARK: - Test Methods

    /// Симулирует неактивность пользователя для тестирования
    /// Временно увеличивает порог активности, чтобы система считала пользователя неактивным
    func simulateInactivity(duration: TimeInterval = 30.0) {
        print("🧪 UnifiedActivityChecker: Симуляция неактивности на \(duration) секунд")

        // Сохраняем оригинальные пороги
        let originalThreshold = activityThreshold
        let originalRecentThreshold = recentActivityThreshold

        // Устанавливаем очень маленькие пороги, чтобы система считала пользователя неактивным
        let testThreshold: TimeInterval = 0.1

        // Временно меняем пороги (через приватные свойства нельзя, поэтому используем другой подход)
        // Вместо этого будем возвращать false в течение указанного времени

        let endTime = Date().addingTimeInterval(duration)

        // Запускаем таймер, который будет логировать состояние
        let timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            let remaining = endTime.timeIntervalSinceNow
            if remaining <= 0 {
                timer.invalidate()
                print("🧪 UnifiedActivityChecker: Симуляция неактивности завершена")
            } else {
                print("🧪 UnifiedActivityChecker: Симуляция неактивности - осталось \(Int(remaining)) сек")
            }
        }
    }

    /// Принудительно возвращает результат проверки активности для тестирования
    private var forcedActivityResult: Bool? = nil

    /// Устанавливает принудительный результат проверки активности для тестирования
    func setForcedActivityResult(_ result: Bool?) {
        forcedActivityResult = result
        if let result = result {
            print("🧪 UnifiedActivityChecker: Принудительный результат активности установлен: \(result)")
        } else {
            print("🧪 UnifiedActivityChecker: Принудительный результат активности сброшен")
        }
    }
}
