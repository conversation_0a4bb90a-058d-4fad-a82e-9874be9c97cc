import Cocoa
import Foundation

/// Трекер общего времени за компьютером
/// Отслеживает активность пользователя каждую минуту и сохраняет данные о времени активности
/// ОБНОВЛЕНО: Интегрирован с новой системой MinuteActivityTracker для более точного обнаружения
class ComputerTimeTracker {

    // MARK: - Properties

    private(set) var isTracking = false
    private var checkTimer: Timer?
    internal var lastActivityTime = Date()
    private var activityThreshold: TimeInterval = 1.0  // Минимум 1 секунда между проверками

    // Мониторы событий (УСТАРЕЛО: теперь используется MinuteActivityTracker)
    private var mouseMonitor: Any?
    private var keyboardMonitor: Any?

    // Статистика
    private let statisticsManager: StatisticsManager

    // Callback для уведомления о записанной активности
    var onActivityRecorded: ((Bool) -> Void)?

    // Для тестирования - возможность изменить интервал проверки
    private var checkInterval: TimeInterval = 60.0  // По умолчанию 60 секунд

    // Флаг для отслеживания состояния сна
    private var systemIsSleeping = false
    internal var sleepStartTime: Date?

    // НОВОЕ: Интеграция с 20-отрезковой системой активности с погрешностью
    private let minuteActivityTracker: MinuteActivityTracker
    private var useNewActivitySystem = true  // Флаг для переключения между старой и новой системой

    // MARK: - Initialization

    init(statisticsManager: StatisticsManager, minuteActivityTracker: MinuteActivityTracker? = nil) {
        self.statisticsManager = statisticsManager

        // Используем переданный трекер или создаем новый
        if let tracker = minuteActivityTracker {
            self.minuteActivityTracker = tracker
        } else {
            self.minuteActivityTracker = MinuteActivityTracker()
        }

        setupSleepWakeNotifications()
        setupMinuteActivityTracker()
    }

    /// Настройка интеграции с MinuteActivityTracker
    private func setupMinuteActivityTracker() {
        // Подписываемся на завершение минут
        minuteActivityTracker.onMinuteCompleted = { [weak self] isActive in
            self?.handleMinuteActivityResult(isActive)
        }
    }
    
    // MARK: - Public Methods
    
    /// Начинает отслеживание времени за компьютером
    func startTracking() {
        guard !isTracking else { return }

        print("💻 ComputerTimeTracker: Начинаем отслеживание времени за компьютером")
        isTracking = true
        lastActivityTime = Date()

        if useNewActivitySystem {
            // НОВОЕ: Используем MinuteActivityTracker для более точного обнаружения
            minuteActivityTracker.startTracking()
            print("💻 ComputerTimeTracker: Запущена новая 4-бандовая система активности")

            // Запускаем таймер проверки каждую минуту для принудительного завершения
            checkTimer = Timer.scheduledTimer(withTimeInterval: checkInterval, repeats: true) { [weak self] _ in
                self?.checkActivityForMinuteNew()
            }
        } else {
            // СТАРОЕ: Используем старую систему мониторинга событий
            startActivityMonitoring()

            // Запускаем таймер проверки каждую минуту (или с настраиваемым интервалом для тестирования)
            checkTimer = Timer.scheduledTimer(withTimeInterval: checkInterval, repeats: true) { [weak self] _ in
                self?.checkActivityForMinute()
            }
        }
    }
    
    /// Останавливает отслеживание времени за компьютером
    func stopTracking() {
        guard isTracking else { return }

        print("💻 ComputerTimeTracker: Останавливаем отслеживание времени за компьютером")
        isTracking = false

        if useNewActivitySystem {
            // НОВОЕ: Останавливаем MinuteActivityTracker
            minuteActivityTracker.stopTracking()
        } else {
            // СТАРОЕ: Останавливаем мониторинг активности
            stopActivityMonitoring()
        }

        // Останавливаем таймер
        checkTimer?.invalidate()
        checkTimer = nil
    }
    
    // MARK: - Private Methods
    
    private func startActivityMonitoring() {
        // Мониторинг движений мыши (только значительные движения)
        mouseMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.mouseMoved, .leftMouseDown, .rightMouseDown, .scrollWheel]) { [weak self] event in
            self?.handleMouseActivity(event)
        }
        
        // Мониторинг клавиатуры
        keyboardMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.keyDown]) { [weak self] event in
            self?.handleKeyboardActivity(event)
        }
    }
    
    private func stopActivityMonitoring() {
        if let mouseMonitor = mouseMonitor {
            NSEvent.removeMonitor(mouseMonitor)
            self.mouseMonitor = nil
        }
        
        if let keyboardMonitor = keyboardMonitor {
            NSEvent.removeMonitor(keyboardMonitor)
            self.keyboardMonitor = nil
        }
    }
    
    private func handleMouseActivity(_ event: NSEvent) {
        // Фильтруем только значительные движения мыши
        if event.type == .mouseMoved {
            // Проверяем, что движение достаточно значительное
            let deltaX = abs(event.deltaX)
            let deltaY = abs(event.deltaY)
            
            // Игнорируем микро-движения (случайные толчки мыши)
            if deltaX < 5 && deltaY < 5 {
                return
            }
        }
        
        markActivity()
    }
    
    private func handleKeyboardActivity(_ event: NSEvent) {
        markActivity()
    }
    
    private func markActivity() {
        let now = Date()
        let timeSinceLastActivity = now.timeIntervalSince(lastActivityTime)
        
        // Обновляем время последней активности только если прошло достаточно времени
        if timeSinceLastActivity >= activityThreshold {
            lastActivityTime = now
            // Не логируем каждую активность, чтобы не засорять консоль
        }
    }
    
    /// НОВОЕ: Проверяет активность через MinuteActivityTracker
    private func checkActivityForMinuteNew() {
        // Пропускаем проверку если система спит
        guard !systemIsSleeping else {
            print("💻 ComputerTimeTracker: Пропускаем проверку - система спит")
            return
        }

        // Принудительно завершаем текущую минуту и получаем результат
        let wasActiveThisPeriod = minuteActivityTracker.forceCompleteCurrentMinute()

        if wasActiveThisPeriod {
            // Записываем активную минуту
            statisticsManager.recordActiveMinute()
            print("💻 ComputerTimeTracker: Активная минута зафиксирована (новая система)")
        } else {
            print("💻 ComputerTimeTracker: Минута без активности (новая система)")
        }

        // Уведомляем детектор неформальных сессий о активности
        onActivityRecorded?(wasActiveThisPeriod)
    }

    /// СТАРОЕ: Проверяет активность за последнюю минуту и записывает результат
    private func checkActivityForMinute() {
        // Пропускаем проверку если система спит
        guard !systemIsSleeping else {
            print("💻 ComputerTimeTracker: Пропускаем проверку - система спит")
            return
        }

        let checkPeriod = checkInterval // Используем настраиваемый интервал
        let periodAgo = Date().addingTimeInterval(-checkPeriod)
        let wasActiveThisPeriod = lastActivityTime > periodAgo

        if wasActiveThisPeriod {
            // Записываем активную минуту
            statisticsManager.recordActiveMinute()
            print("💻 ComputerTimeTracker: Активная минута зафиксирована (старая система)")
        } else {
            print("💻 ComputerTimeTracker: Минута без активности (старая система)")
        }

        // Уведомляем детектор неформальных сессий о активности
        onActivityRecorded?(wasActiveThisPeriod)
    }

    /// НОВОЕ: Обрабатывает результат завершения минуты от MinuteActivityTracker
    private func handleMinuteActivityResult(_ isActive: Bool) {
        // Пропускаем если система спит
        guard !systemIsSleeping else {
            print("💻 ComputerTimeTracker: Пропускаем результат - система спит")
            return
        }

        if isActive {
            // Записываем активную минуту
            statisticsManager.recordActiveMinute()
            print("💻 ComputerTimeTracker: Активная минута зафиксирована (автоматически)")
        } else {
            print("💻 ComputerTimeTracker: Минута без активности (автоматически)")
        }

        // Уведомляем детектор неформальных сессий о активности
        onActivityRecorded?(isActive)
    }
    
    // MARK: - Debug Methods

    /// Устанавливает интервал проверки для тестирования (в секундах)
    func setCheckInterval(_ interval: TimeInterval) {
        checkInterval = interval
        print("💻 ComputerTimeTracker: Интервал проверки изменен на \(interval) сек")

        // Перезапускаем таймер если он активен
        if isTracking {
            checkTimer?.invalidate()
            checkTimer = Timer.scheduledTimer(withTimeInterval: checkInterval, repeats: true) { [weak self] _ in
                self?.checkActivityForMinute()
            }
        }
    }

    /// Проверяет, активен ли пользователь в данный момент
    /// Возвращает true, если была активность за последнюю минуту
    func isUserCurrentlyActive() -> Bool {
        if useNewActivitySystem {
            // НОВОЕ: Используем MinuteActivityTracker для более точной проверки
            let isActive = minuteActivityTracker.wasCurrentMinuteActive()
            print("💻 ComputerTimeTracker: Проверка текущей активности (новая система) - \(isActive ? "активен" : "неактивен")")
            return isActive
        } else {
            // СТАРОЕ: Используем старую логику
            let oneMinuteAgo = Date().addingTimeInterval(-60) // Последняя минута
            let isActive = lastActivityTime > oneMinuteAgo
            print("💻 ComputerTimeTracker: Проверка текущей активности (старая система) - \(isActive ? "активен" : "неактивен")")
            return isActive
        }
    }

    /// НОВОЕ: Переключает между старой и новой системой активности
    func setUseNewActivitySystem(_ useNew: Bool) {
        let wasTracking = isTracking

        if wasTracking {
            stopTracking()
        }

        useNewActivitySystem = useNew
        print("💻 ComputerTimeTracker: Переключено на \(useNew ? "новую" : "старую") систему активности")

        if wasTracking {
            startTracking()
        }
    }

    /// НОВОЕ: Возвращает статистику новой системы активности
    func getMinuteActivityStats() -> MinuteActivityTracker.MinuteStats? {
        guard useNewActivitySystem else { return nil }
        return minuteActivityTracker.getCurrentMinuteStats()
    }

    /// Симулирует событие засыпания системы
    func simulateSystemSleep() {
        print("💻 ComputerTimeTracker: 🧪 Симуляция засыпания системы")
        systemWillSleep()
    }

    /// Симулирует событие пробуждения системы
    func simulateSystemWake() {
        print("💻 ComputerTimeTracker: 🧪 Симуляция пробуждения системы")
        systemDidWake()
    }

    // MARK: - Sleep/Wake Notifications

    private func setupSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemWillSleep),
            name: NSWorkspace.willSleepNotification,
            object: nil
        )

        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemDidWake),
            name: NSWorkspace.didWakeNotification,
            object: nil
        )
    }

    private func removeSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.removeObserver(self)
    }

    @objc private func systemWillSleep() {
        print("💻 ComputerTimeTracker: 💤 СИСТЕМА ЗАСЫПАЕТ - приостанавливаем отслеживание")
        NSLog("💻 ComputerTimeTracker: 💤 СИСТЕМА ЗАСЫПАЕТ - приостанавливаем отслеживание")
        systemIsSleeping = true
        sleepStartTime = Date()

        // Уведомление для тестирования (закомментировано для продакшена)
        // DispatchQueue.main.async {
        //     let alert = NSAlert()
        //     alert.messageText = "💤 Система засыпает"
        //     alert.informativeText = "ComputerTimeTracker: Отслеживание приостановлено"
        //     alert.addButton(withTitle: "OK")
        //     alert.runModal()
        // }
    }

    @objc private func systemDidWake() {
        print("💻 ComputerTimeTracker: 🌅 СИСТЕМА ПРОСНУЛАСЬ - возобновляем отслеживание")
        NSLog("💻 ComputerTimeTracker: 🌅 СИСТЕМА ПРОСНУЛАСЬ - возобновляем отслеживание")
        systemIsSleeping = false

        // Определяем длительность сна
        let sleepDuration: TimeInterval
        if let sleepStart = sleepStartTime {
            sleepDuration = Date().timeIntervalSince(sleepStart)
        } else {
            sleepDuration = 0
        }

        let sleepMinutes = Int(sleepDuration / 60)
        print("💻 ComputerTimeTracker: Длительность сна: \(sleepMinutes) минут")

        // Если сон был длительным (больше 10 минут), сбрасываем время активности
        // Если короткий сон (меньше 10 минут), оставляем как есть
        let longSleepThreshold: TimeInterval = 10 * 60 // 10 минут

        if sleepDuration > longSleepThreshold {
            print("💻 ComputerTimeTracker: Длительный сон (\(sleepMinutes) мин) - сбрасываем активность")
            lastActivityTime = Date().addingTimeInterval(-checkInterval * 2)

            // Уведомляем детектор неформальных сессий о длительном сне
            onActivityRecorded?(false) // Записываем неактивную минуту
        } else {
            print("💻 ComputerTimeTracker: Короткий сон (\(sleepMinutes) мин) - сохраняем активность")
        }

        sleepStartTime = nil

        // Уведомление для тестирования (закомментировано для продакшена)
        // DispatchQueue.main.async {
        //     let alert = NSAlert()
        //     alert.messageText = "🌅 Система проснулась"
        //     alert.informativeText = "Сон: \(sleepMinutes) мин\n\(sleepDuration > longSleepThreshold ? "Активность сброшена" : "Активность сохранена")"
        //     alert.addButton(withTitle: "OK")
        //     alert.runModal()
        // }
    }

    deinit {
        removeSleepWakeNotifications()
        stopTracking()
    }
}
