import Cocoa

/// Режимы работы окна завершения отдыха
enum BreakEndMode {
    case breakCompleted                                    // Обычное завершение отдыха
    case userReturnPartialRest(minutes: Int)              // 2-10 мин: выбор продолжить отдых или работать
    case userReturnChoiceRest(minutes: Int)               // 10-17 мин: хорошее время для выбора
    case userReturnFullRest(minutes: Int)                 // 17+ мин: полноценный отдых завершен
}

/// Окно завершения отдыха с предложением начать новый интервал
/// Теперь поддерживает разные режимы для возвращения пользователя
class BreakEndWindow: NSWindow {

    // Колбэки для действий
    var onStartInterval: (() -> Void)?
    var onStartIntervalWithProject: (() -> Void)?
    var onPostpone: (() -> Void)?

    // Новые колбэки для возвращения пользователя
    var onContinueRest: (() -> Void)?      // Продолжить отдых
    var onStartWork: (() -> Void)?         // Начать работу

    // Колбэк для получения информации о последнем проекте
    var getLastUsedProject: (() -> (name: String, emoji: String)?)?

    // Текущий режим работы
    private var currentMode: BreakEndMode = .breakCompleted
    
    // UI элементы
    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var startButton: NSButton!
    private var secondButton: NSButton!
    private var projectLabel: NSButton!
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 380, height: 120),
                  styleMask: [.borderless],
                  backing: .buffered,
                  defer: false)

        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        print("🌿 BreakEndWindow: Setting up break end window")

        // Настройка окна
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true
    }

    /// Конфигурирует окно для определенного режима
    func configureForMode(_ mode: BreakEndMode) {
        print("🌿 BreakEndWindow: Конфигурация для режима \(mode)")
        currentMode = mode
        updateContentForMode()
    }

    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        print("🌿 BreakEndWindow: Positioning relative to status item")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        self.setFrameOrigin(NSPoint(x: x, y: y))
    }
    
    private func setupUI() {
        print("🌿 BreakEndWindow: Creating UI for break end")

        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Создаем унифицированный фон (тип "отдых")
        self.setupUnifiedBackground(type: .rest, for: containerView)

        self.contentView = containerView

        // Заголовок (по центру, обычный шрифт как в BreakTypeSelectionWindow)
        titleLabel = NSTextField(labelWithString: "✨ Break completed!")
        titleLabel.font = NSFont.systemFont(ofSize: 15, weight: .medium)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Подзаголовок (меньший шрифт, серый цвет)
        subtitleLabel = NSTextField(labelWithString: "Ready to start working?")
        subtitleLabel.font = NSFont.systemFont(ofSize: 12, weight: .regular)
        subtitleLabel.textColor = NSColor.lightGray
        subtitleLabel.alignment = .center
        subtitleLabel.maximumNumberOfLines = 0 // Поддержка многострочного текста
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Создаем унифицированные кнопки
        startButton = NSButton.createUnifiedButton(title: "Start New Session", type: UIButtonType.green, isSmall: false)
        startButton.target = self
        startButton.action = #selector(startIntervalClicked)

        secondButton = NSButton.createUnifiedButton(title: "Later", type: UIButtonType.gray, isSmall: false)
        secondButton.target = self
        secondButton.action = #selector(postponeClicked)

        // Маленькая подпись с проектом под кнопкой Start Session
        let projectInfo = getProjectForButton()
        let projectText = "\(projectInfo.emoji) \(projectInfo.name)"
        projectLabel = createProjectLabel(projectName: projectText)
        projectLabel.target = self
        projectLabel.action = #selector(startWithProjectClicked)

        // Добавляем все элементы
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(startButton)
        containerView.addSubview(secondButton)
        containerView.addSubview(projectLabel)

        // Настройка constraints как в BreakTypeSelectionWindow
        NSLayoutConstraint.activate([
            // Контейнер заполняет все окно
            containerView.topAnchor.constraint(equalTo: self.contentView!.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: self.contentView!.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: self.contentView!.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: self.contentView!.bottomAnchor),

            // Заголовок (по центру сверху)
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),

            // Подзаголовок (под заголовком)
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),

            // Кнопки рядом снизу (равной ширины, как в BreakTypeSelectionWindow)
            // Start New Session (слева)
            startButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 16),
            startButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            startButton.heightAnchor.constraint(equalToConstant: 32),
            startButton.bottomAnchor.constraint(equalTo: projectLabel.topAnchor, constant: -8),

            // Later (справа)
            secondButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 16),
            secondButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            secondButton.heightAnchor.constraint(equalToConstant: 32),

            // Равная ширина кнопок с промежутком
            secondButton.leadingAnchor.constraint(equalTo: startButton.trailingAnchor, constant: 10),
            startButton.widthAnchor.constraint(equalTo: secondButton.widthAnchor),

            // Маленькая подпись с проектом под кнопкой Start Session (справа)
            projectLabel.centerXAnchor.constraint(equalTo: secondButton.centerXAnchor),
            projectLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12),

            // Принудительно задаем минимальные размеры контейнера
            containerView.widthAnchor.constraint(greaterThanOrEqualToConstant: 380),
            containerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 100)
        ])

        print("🌿 BreakEndWindow: UI setup completed")

        // Устанавливаем начальный контент
        updateContentForMode()
    }

    /// Обновляет контент окна в зависимости от режима
    private func updateContentForMode() {
        switch currentMode {
        case .breakCompleted:
            titleLabel.stringValue = "✨ Break completed!"

            // Проверяем завтрашний выходной
            var subtitle = "Ready to start working?"
            if let tomorrowWeekendMessage = getTomorrowWeekendMessage() {
                subtitle += "\n\n🏖️ " + tomorrowWeekendMessage
            }
            subtitleLabel.stringValue = subtitle

            startButton.title = "Start New Session"
            secondButton.title = "Later"
            startButton.target = self
            startButton.action = #selector(startIntervalClicked)
            secondButton.target = self
            secondButton.action = #selector(postponeClicked)
            projectLabel.isHidden = false

        case .userReturnPartialRest(let minutes):
            titleLabel.stringValue = "Вы отдыхали \(minutes) \(minuteWord(minutes))"
            subtitleLabel.stringValue = "Хотите продолжить отдых или вернуться к работе?"
            startButton.title = "Keep resting"
            startButton.target = self
            startButton.action = #selector(continueRestClicked)
            // Вторая кнопка скрыта для 2-10 минут (меньше 10)
            secondButton.isHidden = true
            projectLabel.isHidden = true

        case .userReturnChoiceRest(let minutes):
            titleLabel.stringValue = "Вы отдыхали \(minutes) \(minuteWord(minutes))"
            subtitleLabel.stringValue = "Это отличное время для восстановления! Что выберете?"
            startButton.title = "Keep resting"
            secondButton.title = "Start session"
            startButton.target = self
            startButton.action = #selector(continueRestClicked)
            secondButton.target = self
            secondButton.action = #selector(startWorkClicked)
            secondButton.isHidden = false
            projectLabel.isHidden = false

        case .userReturnFullRest(let minutes):
            titleLabel.stringValue = "Отлично! Вы отдыхали \(minutes) \(minuteWord(minutes))"
            subtitleLabel.stringValue = "Теперь вы готовы к продуктивной работе!"
            startButton.title = "Keep resting"
            secondButton.title = "Start session"
            startButton.target = self
            startButton.action = #selector(continueRestClicked)
            secondButton.target = self
            secondButton.action = #selector(startWorkClicked)
            secondButton.isHidden = false
            projectLabel.isHidden = false
        }
    }

    /// Возвращает правильную форму слова "минута" для русского языка
    private func minuteWord(_ minutes: Int) -> String {
        let lastDigit = minutes % 10
        let lastTwoDigits = minutes % 100

        if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
            return "минут"
        } else if lastDigit == 1 {
            return "минуту"
        } else if lastDigit >= 2 && lastDigit <= 4 {
            return "минуты"
        } else {
            return "минут"
        }
    }

    // MARK: - Старые методы удалены - теперь используем UIStyles.swift

    private func createProjectLabel(projectName: String) -> NSButton {
        let button = NSButton(title: projectName, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Создаем основной слой
        let containerLayer = CALayer()
        containerLayer.backgroundColor = NSColor.clear.cgColor
        button.layer = containerLayer

        // Создаем слой с пунктирным подчеркиванием только под текстом (без эмодзи и пробела)
        let underlineLayer = CAShapeLayer()
        underlineLayer.fillColor = NSColor.clear.cgColor
        underlineLayer.strokeColor = NSColor.white.withAlphaComponent(0.4).cgColor
        underlineLayer.lineWidth = 0.5
        underlineLayer.lineDashPattern = [2, 2] // Маленькие пунктиры

        containerLayer.addSublayer(underlineLayer)

        // Обновляем путь при изменении размера - линия только под текстом
        DispatchQueue.main.async {
            let font = NSFont.systemFont(ofSize: 6, weight: .ultraLight)
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font
            ]

            // Разделяем строку на эмодзи+пробел и чистый текст
            let components = projectName.components(separatedBy: " ")
            if components.count >= 2 {
                let emojiPart = components[0] + " " // Эмодзи + пробел
                let textPart = components.dropFirst().joined(separator: " ") // Остальной текст

                // Вычисляем точные размеры
                let emojiWidth = emojiPart.size(withAttributes: attributes).width
                let textWidth = textPart.size(withAttributes: attributes).width

                let path = NSBezierPath()
                // Выравниваем подчеркивание по правому краю кнопки
                let startX = button.bounds.width - textWidth
                let endX = button.bounds.width
                let lineY = button.bounds.height - 0 // Опускаем линию ниже от текста

                path.move(to: NSPoint(x: startX, y: lineY))
                path.line(to: NSPoint(x: endX, y: lineY))
                underlineLayer.path = path.cgPath
                underlineLayer.frame = button.bounds
            }
        }

        // Нормальный размер шрифта для проекта (как в оригинальном окне)
        button.attributedTitle = NSAttributedString(
            string: projectName,
            attributes: [
                .foregroundColor: NSColor.white.withAlphaComponent(0.7),
                .font: NSFont.systemFont(ofSize: 12, weight: .regular)
            ]
        )

        return button
    }

    private func getProjectForButton() -> (name: String, emoji: String) {
        // Сначала пытаемся получить последний проект
        if let lastProject = getLastUsedProject?() {
            return lastProject
        }

        // Если последнего проекта нет, возвращаем заглушку
        return (name: "Select Project", emoji: "📁")
    }

    func updateProjectButton() {
        // Обновляем текст подписи проекта
        let projectInfo = getProjectForButton()
        let projectText = "\(projectInfo.emoji) \(projectInfo.name)"

        // Найдем projectLabel в subviews и обновим его
        if let containerView = self.contentView {
            for subview in containerView.subviews {
                if let button = subview as? NSButton, button.action == #selector(startWithProjectClicked) {
                    // Обновляем и обычный title и attributedTitle
                    button.title = projectText
                    button.attributedTitle = NSAttributedString(
                        string: projectText,
                        attributes: [
                            .foregroundColor: NSColor.white.withAlphaComponent(0.7),
                            .font: NSFont.systemFont(ofSize: 12, weight: .regular)
                        ]
                    )

                    // Обновляем подчеркивание
                    updateUnderlineForButton(button, projectText: projectText)

                    // Принудительно обновляем отображение
                    button.needsDisplay = true
                    button.needsLayout = true
                    self.contentView?.needsDisplay = true

                    break
                }
            }
        }
    }

    private func updateUnderlineForButton(_ button: NSButton, projectText: String) {
        guard let containerLayer = button.layer else { return }

        // Находим слой подчеркивания
        for sublayer in containerLayer.sublayers ?? [] {
            if let shapeLayer = sublayer as? CAShapeLayer {
                // Обновляем путь подчеркивания
                DispatchQueue.main.async {
                    let font = NSFont.systemFont(ofSize: 12, weight: .regular)
                    let attributes: [NSAttributedString.Key: Any] = [
                        .font: font
                    ]

                    // Разделяем строку на эмодзи+пробел и чистый текст
                    let components = projectText.components(separatedBy: " ")
                    if components.count >= 2 {
                        let emojiPart = components[0] + " " // Эмодзи + пробел
                        let textPart = components.dropFirst().joined(separator: " ") // Остальной текст

                        // Вычисляем точные размеры
                        let _ = emojiPart.size(withAttributes: attributes).width
                        let textWidth = textPart.size(withAttributes: attributes).width

                        let path = NSBezierPath()
                        // Выравниваем подчеркивание по правому краю кнопки
                        let startX = button.bounds.width - textWidth
                        let endX = button.bounds.width
                        let lineY = button.bounds.height - 0 // Y-позиция как настроил пользователь

                        path.move(to: NSPoint(x: startX, y: lineY))
                        path.line(to: NSPoint(x: endX, y: lineY))
                        shapeLayer.path = path.cgPath
                        shapeLayer.frame = button.bounds
                    }
                }
                break
            }
        }
    }

    // MARK: - Проверка завтрашнего выходного

    /// Проверяет является ли завтра выходным днем и возвращает соответствующее сообщение
    private func getTomorrowWeekendMessage() -> String? {
        // Проверяем доступна ли система выходных
        guard WeekendManager.shared.isWeekendSystemAvailable() else { return nil }

        // Получаем завтрашнюю дату
        let calendar = Calendar.current
        guard let tomorrow = calendar.date(byAdding: .day, value: 1, to: Date()) else { return nil }

        // Проверяем является ли завтра выходным
        if WeekendManager.shared.isWeekendDay(tomorrow) {
            return WeekendMessageManager.shared.getTomorrowWeekendMessage()
        }

        return nil
    }

    // MARK: - Обновление статистики

    func updateStatistics(with stats: BreakStatistics) {
        // В новом дизайне мы не показываем статистику отдыха
        // Оставляем метод для совместимости, но ничего не делаем
    }

    // MARK: - Actions

    @objc private func startIntervalClicked() {
        print("🌿 BreakEndWindow: Start interval")
        hideWithAnimation {
            self.onStartInterval?()
        }
    }

    @objc private func startWithProjectClicked() {
        print("🌿 BreakEndWindow: Select project")
        // Не закрываем окно, просто показываем выбор проекта
        self.onStartIntervalWithProject?()
    }

    @objc private func postponeClicked() {
        print("🌿 BreakEndWindow: Postpone")

        // НОВОЕ: Уведомляем DailyWorkloadManager об отказе
        DailyWorkloadManager.shared.handleBreakEndRefusal()

        hideWithAnimation {
            self.onPostpone?()
        }
    }

    // MARK: - New Actions for User Return

    @objc private func continueRestClicked() {
        print("🌿 BreakEndWindow: Continue rest")
        hideWithAnimation {
            self.onContinueRest?()
        }
    }

    @objc private func startWorkClicked() {
        print("🌿 BreakEndWindow: Start work")
        hideWithAnimation {
            self.onStartWork?()
        }
    }

    // MARK: - Анимации

    func showWithAnimation() {
        self.alphaValue = 0.0
        self.makeKeyAndOrderFront(nil)

        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1.0
        })
    }

    func hideWithAnimation(completion: @escaping () -> Void) {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0.0
        }, completionHandler: {
            self.orderOut(nil)
            completion()
        })
    }
}
