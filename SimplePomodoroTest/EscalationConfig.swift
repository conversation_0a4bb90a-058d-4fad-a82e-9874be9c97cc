//
//  EscalationConfig.swift
//  uProd
//
//  Конфигурация системы эскалации напоминаний
//

import Foundation

/// Конфигурация уровней эскалации для единой системы напоминаний
struct EscalationConfig {
    
    // MARK: - Level Configuration
    
    /// Получает уровень эскалации для заданного количества минут
    /// - Parameter minutes: Количество минут с начала эскалации
    /// - Returns: Уровень эскалации (0 = зеленый, 1 = желтый, 2 = оранжевый, 3+ = красный)
    static func getEscalationLevel(for minutes: Int) -> Int {
        switch minutes {
        case 0:
            return 0  // Зеленый - первое напоминание
        case 1:
            return 1  // Желтый - через 1 минуту
        case 3:
            return 2  // Оранжевый - через 3 минуты
        case 6:
            return 3  // Красный - через 6 минут
        default:
            if minutes >= 6 {
                // Красный с увеличивающейся интенсивностью каждые 5 минут
                return 3 + ((minutes - 6) / 5)
            }
            return 0
        }
    }
    
    /// Получает цвет для уровня эскалации
    static func getColorForLevel(_ level: Int) -> EscalationColor {
        switch level {
        case 0:
            return .green
        case 1:
            return .yellow
        case 2:
            return .orange
        default:
            return .red
        }
    }
    
    /// Получает сообщение для уровня эскалации
    static func getMessageForLevel(_ level: Int, minutes: Int, isInformal: Bool) -> String {
        let baseMinutes = isInformal ? 52 : 25 // Базовое время (неформальные начинаются с 52+ минут)
        let totalMinutes = baseMinutes + minutes
        
        switch level {
        case 0:
            if isInformal {
                return "🌿 Time for rest\nUsing laptop \(totalMinutes)+ minutes"
            } else {
                return "🎉 Session completed!\nWhat's next?"
            }
        case 1:
            return "⚠️ You've been working for \(totalMinutes)+ minutes"
        case 2:
            return "🔥 You've been working for \(totalMinutes)+ minutes"
        default:
            return "🚨 You've been working for \(totalMinutes)+ minutes"
        }
    }
    
    /// Определяет нужна ли тряска для данного уровня при обновлении существующего окна
    static func shouldShakeForLevel(_ level: Int) -> Bool {
        return level > 0 // Тряска только для уровней 1+ (желтый, оранжевый, красный)
    }
    
    /// Получает интенсивность тряски для уровня
    static func getShakeIntensityForLevel(_ level: Int) -> Double {
        switch level {
        case 1:
            return 1.0  // Легкая тряска
        case 2:
            return 1.5  // Средняя тряска
        default:
            return 2.0  // Сильная тряска
        }
    }
    
    // MARK: - Timing Configuration
    
    /// Интервал проверки эскалации (в секундах)
    static let checkInterval: TimeInterval = 1.0
    
    /// Пороги времени для каждого уровня (в минутах)
    static let levelThresholds: [Int] = [0, 1, 3, 6]
    
    /// Интервал повторения для красного уровня (в минутах)
    static let redLevelRepeatInterval: Int = 5
}

// MARK: - Supporting Types

/// Цвета эскалации
enum EscalationColor: String, CaseIterable {
    case green = "green"
    case yellow = "yellow"
    case orange = "orange"
    case red = "red"
    
    var displayName: String {
        switch self {
        case .green: return "Зеленый"
        case .yellow: return "Желтый"
        case .orange: return "Оранжевый"
        case .red: return "Красный"
        }
    }
}

/// Тип напоминания для единой системы
enum UnifiedReminderType {
    case formalCompleted           // Формальный интервал завершен
    case informalDetected          // Неформальная работа обнаружена
    case escalation(minutes: Int, level: Int)  // Эскалация напоминаний
    
    var isInformal: Bool {
        switch self {
        case .formalCompleted:
            return false
        case .informalDetected, .escalation:
            return true
        }
    }
    
    var level: Int {
        switch self {
        case .formalCompleted, .informalDetected:
            return 0
        case .escalation(_, let level):
            return level
        }
    }
}
