import Cocoa
import Foundation

// Кастомный контейнер для drag & drop
class DropTargetView: NSView {
    weak var dropDelegate: ProjectsViewController?

    override func awakeFromNib() {
        super.awakeFromNib()
        registerForDraggedTypes([.string])
    }

    override func draggingEntered(_ sender: NSDraggingInfo) -> NSDragOperation {
        return dropDelegate?.draggingEntered(sender) ?? []
    }

    override func draggingUpdated(_ sender: NSDraggingInfo) -> NSDragOperation {
        return dropDelegate?.draggingUpdated(sender) ?? []
    }

    override func draggingExited(_ sender: NSDraggingInfo?) {
        dropDelegate?.draggingExited(sender)
    }

    override func performDragOperation(_ sender: NSDraggingInfo) -> Bool {
        return dropDelegate?.performDragOperation(sender) ?? false
    }
}

class ProjectsViewController: NSViewController, ProjectFormDelegate {
    
    // MARK: - Properties
    private var projectManager: ProjectManager
    private var scrollView: NSScrollView!
    private var contentView: NSView!
    private var projectCardsContainer: DropTargetView!
    private var headerView: NSView!
    private var createProjectButton: NSButton!
    private var searchField: NSTextField!
    
    private var projectCards: [ProjectCard] = []
    private var dropIndicatorView: NSView?
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager) {
        self.projectManager = projectManager
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func loadView() {
        view = NSView()
        view.wantsLayer = true
        setupUI()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Устанавливаем минимальную высоту окна
        if let window = view.window {
            window.minSize = NSSize(width: 600, height: 450)
        }

        // Настраиваем drag & drop
        setupDropTarget()

        loadProjects()

        // Убеждаемся, что скролл в начале
        DispatchQueue.main.async { [weak self] in
            self?.scrollToTop()
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.scrollToTop()
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.scrollToTop()
        }
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        setupBackground()
        setupHeader()
        setupScrollView()
        setupConstraints()
    }
    
    private func setupBackground() {
        // Создаем градиентный фон как в StatisticsWindow
        let gradientView = GradientView()
        view.addSubview(gradientView)
        gradientView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            gradientView.topAnchor.constraint(equalTo: view.topAnchor),
            gradientView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            gradientView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            gradientView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupHeader() {
        headerView = NSView()
        headerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(headerView)
        
        // Заголовок
        let titleLabel = NSTextField(labelWithString: "Проекты")
        titleLabel.font = NSFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(titleLabel)
        
        // Убираем поиск - не нужен
        
        // Кнопка создания проекта
        createProjectButton = createStableButton(title: "Создать проект", isPrimary: true)
        createProjectButton.target = self
        createProjectButton.action = #selector(createProjectClicked)
        headerView.addSubview(createProjectButton)
        
        // Constraints для header - размещаем заголовок и кнопку на одном уровне
        NSLayoutConstraint.activate([
            // Заголовок слева
            titleLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 30),

            // Кнопка справа на том же уровне
            createProjectButton.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            createProjectButton.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -30),
            createProjectButton.widthAnchor.constraint(equalToConstant: 160),
            createProjectButton.heightAnchor.constraint(equalToConstant: 36),

            // Высота header с увеличенными отступами
            headerView.heightAnchor.constraint(equalToConstant: 120)
        ])
    }
    
    private func setupSearchFieldStyle() {
        searchField.wantsLayer = true
        searchField.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.2).cgColor
        searchField.layer?.cornerRadius = 8
        searchField.layer?.borderColor = NSColor.white.withAlphaComponent(0.1).cgColor
        searchField.layer?.borderWidth = 1
        searchField.textColor = NSColor.white
        searchField.font = NSFont.systemFont(ofSize: 14)
        
        // Стиль placeholder
        if let cell = searchField.cell as? NSTextFieldCell {
            cell.placeholderAttributedString = NSAttributedString(
                string: searchField.placeholderString ?? "",
                attributes: [
                    .foregroundColor: NSColor.white.withAlphaComponent(0.5),
                    .font: NSFont.systemFont(ofSize: 14)
                ]
            )
        }
    }
    
    private func setupScrollView() {
        scrollView = NSScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.backgroundColor = NSColor.clear

        // Принудительно устанавливаем начальную позицию скролла
        scrollView.automaticallyAdjustsContentInsets = false
        scrollView.contentInsets = NSEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)

        view.addSubview(scrollView)
        
        // Content view для карточек проектов
        contentView = NSView()
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        projectCardsContainer = DropTargetView()
        projectCardsContainer.dropDelegate = self
        projectCardsContainer.translatesAutoresizingMaskIntoConstraints = false
        projectCardsContainer.registerForDraggedTypes([.string])
        contentView.addSubview(projectCardsContainer)
        
        scrollView.documentView = contentView

        print("📦 DocumentView установлен. Bounds clipView: \(scrollView.contentView.bounds)")

        // Сразу устанавливаем скролл в начало
        DispatchQueue.main.async { [weak self] in
            print("📦 Вызываем scrollToTop после установки documentView")
            self?.scrollToTop()
        }
        
        // Constraints для projectCardsContainer
        let containerBottomConstraint = projectCardsContainer.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -30)
        containerBottomConstraint.priority = NSLayoutConstraint.Priority(999) // Высокий приоритет, но не обязательный

        NSLayoutConstraint.activate([
            projectCardsContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 30),
            projectCardsContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            projectCardsContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30),
            containerBottomConstraint
        ])
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            headerView.topAnchor.constraint(equalTo: view.topAnchor),
            headerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            headerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            scrollView.topAnchor.constraint(equalTo: headerView.bottomAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
            // Убираем constraint по высоте - пусть contentView определяет высоту по содержимому
        ])
    }

    // MARK: - Scroll Management

    private func scrollToTop() {
        // Принудительно обновляем layout перед скроллом
        view.layoutSubtreeIfNeeded()

        // ИСПРАВЛЕНИЕ: Правильный скролл в начало для macOS
        let clipView = scrollView.contentView
        guard let documentView = scrollView.documentView else { return }

        let documentHeight = documentView.bounds.height
        let clipHeight = clipView.bounds.height
        let topY = max(0, documentHeight - clipHeight)
        let topPoint = NSPoint(x: 0, y: topY)

        clipView.scroll(to: topPoint)
        scrollView.reflectScrolledClipView(clipView)

        print("🔄 Скролл установлен в начало (topY: \(topY))")
        NSLog("🔄 Скролл установлен в начало (topY: \(topY))")
    }

    private func forceScrollToTop() {
        // Принудительно обновляем layout
        view.layoutSubtreeIfNeeded()
        scrollView.layoutSubtreeIfNeeded()
        contentView.layoutSubtreeIfNeeded()
        projectCardsContainer.layoutSubtreeIfNeeded()

        let clipView = scrollView.contentView
        guard let documentView = scrollView.documentView else { return }

        // ИСПРАВЛЕНИЕ: В macOS Y=0 это низ документа!
        // Чтобы показать верх, нужно установить Y = documentHeight - clipHeight
        let documentHeight = documentView.bounds.height
        let clipHeight = clipView.bounds.height
        let topY = max(0.0, documentHeight - clipHeight)

        // Устанавливаем правильную позицию для показа верха
        let topPoint = NSPoint(x: 0, y: topY)

        // Множественные методы для надежности
        var bounds = clipView.bounds
        bounds.origin = topPoint
        clipView.bounds = bounds

        clipView.scroll(to: topPoint)
        scrollView.reflectScrolledClipView(clipView)

        print("🔄 Скролл установлен в начало (topY: \(topY))")
    }

    // MARK: - Data Loading
    
    private func loadProjects() {
        let allProjects = projectManager.getAllProjects()
        print("🎯 loadProjects: найдено \(allProjects.count) проектов")
        NSLog("🎯 loadProjects: найдено \(allProjects.count) проектов")

        // Сортируем проекты с учетом приоритета (Ichiban), избранных и пользовательского порядка
        let sortedProjects = sortProjects(allProjects)

        for project in sortedProjects {
            print("🎯 Проект: \(project.name) (избранный: \(projectManager.isFavorite(project)), завершен: \(project.isArchived))")
        }
        updateProjectCards(with: sortedProjects)

        // Принудительно устанавливаем скролл в начало с задержкой
        DispatchQueue.main.async { [weak self] in
            self?.scrollToTop()
        }

        // Дополнительный вызов с большей задержкой для надежности
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.scrollToTop()
        }
    }

    private func sortProjects(_ projects: [Project]) -> [Project] {
        return projects.sorted { project1, project2 in
            let isPriority1 = projectManager.isPriorityProject(project1)
            let isPriority2 = projectManager.isPriorityProject(project2)
            let isFavorite1 = projectManager.isFavorite(project1)
            let isFavorite2 = projectManager.isFavorite(project2)

            // Сначала приоритетный проект (Ichiban) - всегда наверху
            if isPriority1 && !isPriority2 {
                return true
            }
            if !isPriority1 && isPriority2 {
                return false
            }

            // Затем избранные
            if isFavorite1 && !isFavorite2 {
                return true
            }
            if !isFavorite1 && isFavorite2 {
                return false
            }

            // Среди одинаковых по избранности: активные перед завершенными
            if project1.isArchived != project2.isArchived {
                return !project1.isArchived && project2.isArchived
            }

            // Среди одинаковых: по дате последнего использования (новые сверху)
            let lastUsed1 = project1.lastUsedAt ?? project1.createdAt
            let lastUsed2 = project2.lastUsedAt ?? project2.createdAt
            return lastUsed1 > lastUsed2
        }
    }

    private func sortProjectsWithUserOrder(_ projects: [Project]) -> [Project] {
        // Получаем проекты в пользовательском порядке
        let orderedProjects = projectManager.getProjectsInOrder()

        // Фильтруем только те проекты, которые есть в переданном списке
        let projectIds = Set(projects.map { $0.id })
        return orderedProjects.filter { projectIds.contains($0.id) }
    }
    
    private func updateProjectCards(with projects: [Project]) {
        // Удаляем старые карточки
        projectCards.forEach { $0.removeFromSuperview() }
        projectCards.removeAll()
        
        // Создаем новые карточки
        for (index, project) in projects.enumerated() {
            let card = ProjectCard(project: project, projectManager: projectManager)
            card.delegate = self
            card.translatesAutoresizingMaskIntoConstraints = false
            projectCardsContainer.addSubview(card)
            projectCards.append(card)
            
            // Constraints для карточки
            NSLayoutConstraint.activate([
                card.leadingAnchor.constraint(equalTo: projectCardsContainer.leadingAnchor),
                card.trailingAnchor.constraint(equalTo: projectCardsContainer.trailingAnchor),
                card.heightAnchor.constraint(equalToConstant: 95)
            ])
            
            if index == 0 {
                card.topAnchor.constraint(equalTo: projectCardsContainer.topAnchor).isActive = true
            } else {
                card.topAnchor.constraint(equalTo: projectCards[index - 1].bottomAnchor, constant: 16).isActive = true
            }
            
            if index == projects.count - 1 {
                card.bottomAnchor.constraint(equalTo: projectCardsContainer.bottomAnchor).isActive = true

                print("📦 Добавлена последняя карточка (\(index + 1) из \(projects.count))")
                print("📦 Bounds clipView после добавления: \(scrollView.contentView.bounds)")

                // После добавления последней карточки принудительно скроллим в начало
                DispatchQueue.main.async { [weak self] in
                    print("📦 Вызываем forceScrollToTop после добавления последней карточки")
                    self?.forceScrollToTop()
                }
            }
        }
        
        // Принудительно устанавливаем скролл в начало ПЕРЕД анимацией
        DispatchQueue.main.async { [weak self] in
            self?.forceScrollToTop()
        }

        // Анимация появления карточек
        animateCardsAppearance()
    }
    
    private func animateCardsAppearance() {
        for (index, card) in projectCards.enumerated() {
            card.layer?.opacity = 0
            card.layer?.transform = CATransform3DMakeTranslation(0, 20, 0)

            DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 0.1) {
                NSAnimationContext.runAnimationGroup { context in
                    context.duration = 0.5
                    context.timingFunction = CAMediaTimingFunction(name: .easeOut)
                    card.animator().layer?.opacity = 1
                    card.animator().layer?.transform = CATransform3DIdentity
                }
            }
        }

        // Скроллим в начало после завершения всех анимаций
        let totalAnimationTime = Double(projectCards.count) * 0.1 + 0.5 // время всех анимаций + длительность анимации
        DispatchQueue.main.asyncAfter(deadline: .now() + totalAnimationTime + 0.1) { [weak self] in
            self?.forceScrollToTop()

            // Обновляем состояние кнопок у всех карточек
            self?.projectCards.forEach { card in
                card.updateButtonStates()
            }
        }
    }
    
    // MARK: - Actions
    
    @objc private func createProjectClicked() {
        print("🎯 Создание проекта - начало")
        NSLog("🎯 Создание проекта - начало")

        showProjectForm(for: nil) // nil означает создание нового проекта
    }
    
    @objc private func searchTextChanged() {
        let searchText = searchField.stringValue.lowercased()
        let allProjects = projectManager.getAllProjects()

        if searchText.isEmpty {
            let sortedProjects = sortProjects(allProjects)
            updateProjectCards(with: sortedProjects)
        } else {
            let filteredProjects = allProjects.filter { project in
                project.name.lowercased().contains(searchText) ||
                project.type.displayName.lowercased().contains(searchText)
            }
            let sortedFilteredProjects = sortProjects(filteredProjects)
            updateProjectCards(with: sortedFilteredProjects)
        }
    }

    // MARK: - Universal Project Form

    private func showProjectForm(for project: Project?) {
        let projectForm = ProjectForm(project: project)
        projectForm.delegate = self
        projectForm.translatesAutoresizingMaskIntoConstraints = false

        view.addSubview(projectForm)

        NSLayoutConstraint.activate([
            projectForm.topAnchor.constraint(equalTo: view.topAnchor),
            projectForm.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            projectForm.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            projectForm.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    private func showCreateProjectForm() {
        let createForm = ProjectCreateForm(projectManager: projectManager)
        createForm.delegate = self
        
        // Показываем форму как slide-in панель
        showSlideInPanel(createForm)
    }
    
    private func showSlideInPanel(_ formView: NSView) {
        // Создаем overlay
        let overlay = NSView()
        overlay.wantsLayer = true
        overlay.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.5).cgColor
        overlay.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(overlay)
        
        // Добавляем форму
        formView.translatesAutoresizingMaskIntoConstraints = false
        overlay.addSubview(formView)
        
        NSLayoutConstraint.activate([
            overlay.topAnchor.constraint(equalTo: view.topAnchor),
            overlay.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            overlay.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            overlay.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            formView.centerXAnchor.constraint(equalTo: overlay.centerXAnchor),
            formView.centerYAnchor.constraint(equalTo: overlay.centerYAnchor),
            formView.widthAnchor.constraint(equalToConstant: 500),
            formView.heightAnchor.constraint(equalToConstant: 600)
        ])
        
        // Анимация появления
        overlay.layer?.opacity = 0
        formView.layer?.transform = CATransform3DMakeScale(0.8, 0.8, 1)
        
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            overlay.animator().layer?.opacity = 1
            formView.animator().layer?.transform = CATransform3DIdentity
        }
    }
}

// MARK: - ProjectCard Delegate

extension ProjectsViewController: ProjectCardDelegate {
    func projectCardDidRequestEdit(_ card: ProjectCard) {
        print("📝 Запрос на редактирование проекта: \(card.currentProject.name)")
        NSLog("📝 Запрос на редактирование проекта: \(card.currentProject.name)")

        showProjectForm(for: card.currentProject) // передаем проект для редактирования
    }

    func projectCardDidRequestDelete(_ card: ProjectCard) {
        // Найдем проект по карточке
        guard let projectIndex = projectCards.firstIndex(of: card) else { return }
        let projects = projectManager.getAllProjects()
        guard projectIndex < projects.count else { return }
        let projectToDelete = projects[projectIndex]

        print("🗑️ Запрос на удаление проекта: \(projectToDelete.name)")
        NSLog("🗑️ Запрос на удаление проекта: \(projectToDelete.name)")

        let alert = NSAlert()
        alert.messageText = "Удалить проект \"\(projectToDelete.name)\"?"
        alert.informativeText = "Это действие нельзя отменить"
        alert.addButton(withTitle: "Удалить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .warning

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            print("🗑️ Удаляем проект: \(projectToDelete.name)")
            NSLog("🗑️ Удаляем проект: \(projectToDelete.name)")

            // Удаляем проект
            projectManager.deleteProject(projectToDelete.id)

            // Перезагружаем список
            loadProjects()
        }
    }


    func projectCardDidRequestToggleFavorite(_ card: ProjectCard) {
        let project = card.currentProject
        print("🌟 Переключаем избранное для проекта: \(project.name)")
        NSLog("🌟 Переключаем избранное для проекта: \(project.name)")

        // Переключаем избранное
        if projectManager.isFavorite(project) {
            projectManager.removeFromFavorites(project)
            print("🌟 Убрали из избранного: \(project.name)")
        } else {
            projectManager.addToFavorites(project)
            print("🌟 Добавили в избранное: \(project.name)")
        }

        // Перезагружаем список для обновления UI
        loadProjects()

        // Уведомляем AppDelegate об изменении избранных для обновления меню
        NotificationCenter.default.post(name: NSNotification.Name("FavoritesChanged"), object: nil)
    }

    func projectCardDidRequestTogglePriority(_ card: ProjectCard) {
        let project = card.currentProject
        print("🎯 Переключаем приоритет для проекта: \(project.name)")
        NSLog("🎯 Переключаем приоритет для проекта: \(project.name)")

        // Переключаем приоритет
        if projectManager.isPriorityProject(project) {
            projectManager.setPriorityProject(nil)
            print("🎯 Убрали приоритет: \(project.name)")
        } else {
            // Проверяем, есть ли уже приоритетный проект
            if let currentPriorityProject = projectManager.getPriorityProject(),
               currentPriorityProject.id != project.id {

                // Показываем диалог подтверждения
                let alert = NSAlert()
                alert.messageText = "Смена приоритетного проекта"
                alert.informativeText = "У вас уже есть приоритетный проект \"\(currentPriorityProject.name)\". Заменить его на \"\(project.name)\"?"
                alert.addButton(withTitle: "Заменить")
                alert.addButton(withTitle: "Отмена")
                alert.alertStyle = .warning

                let response = alert.runModal()
                if response == .alertFirstButtonReturn {
                    // Пользователь подтвердил замену
                    projectManager.setPriorityProject(project)
                    print("🎯 Заменен приоритетный проект: \(project.name)")
                } else {
                    // Пользователь отменил, ничего не делаем
                    return
                }
            } else {
                // Нет текущего приоритетного проекта
                projectManager.setPriorityProject(project)
                print("🎯 Установили приоритет: \(project.name)")
            }
        }

        // Обновляем состояние кнопок у всех карточек
        for card in projectCards {
            card.updateButtonStates()
        }

        // Обновляем сортировку проектов (приоритетный должен быть наверху)
        loadProjects()

        // Уведомляем AppDelegate об изменении приоритета для обновления меню
        NotificationCenter.default.post(name: NSNotification.Name("PriorityProjectChanged"), object: nil)
    }

    func projectCardDidStartDrag(_ card: ProjectCard) {
        print("🔄 Начало перетаскивания проекта: \(card.currentProject.name)")
        NSLog("🔄 Начало перетаскивания проекта: \(card.currentProject.name)")

        // Настраиваем контейнер для приема drop событий
        setupDropTarget()
    }

    func projectCardDidEndDrag(_ card: ProjectCard, to targetCard: ProjectCard?) {
        print("🔄 Завершение перетаскивания проекта: \(card.currentProject.name)")
        NSLog("🔄 Завершение перетаскивания проекта: \(card.currentProject.name)")

        guard let targetCard = targetCard else {
            print("🔄 Перетаскивание отменено - нет целевой карточки")
            return
        }

        // Находим индексы карточек
        guard let sourceIndex = projectCards.firstIndex(of: card),
              let targetIndex = projectCards.firstIndex(of: targetCard) else {
            print("❌ Не удалось найти индексы карточек для перестановки")
            return
        }

        print("🔄 Перемещаем проект с позиции \(sourceIndex) на позицию \(targetIndex)")

        // Создаем новый порядок проектов
        var newOrder = projectCards.map { $0.currentProject.id }
        let movedProjectId = newOrder.remove(at: sourceIndex)
        newOrder.insert(movedProjectId, at: targetIndex)

        // Сохраняем новый порядок
        projectManager.setProjectOrder(newOrder)

        // Перезагружаем список для отображения нового порядка
        loadProjects()

        // Уведомляем AppDelegate об изменении порядка для обновления меню
        NotificationCenter.default.post(name: NSNotification.Name("ProjectOrderChanged"), object: nil)
    }

    // MARK: - Drop Target Setup

    private func setupDropTarget() {
        print("🔧 setupDropTarget вызван")
        NSLog("🔧 setupDropTarget вызван")

        // Проверяем, что контейнер существует и настроен
        print("📦 projectCardsContainer: \(projectCardsContainer)")
        NSLog("📦 projectCardsContainer: \(projectCardsContainer)")
        print("📦 projectCardsContainer.dropDelegate: \(String(describing: projectCardsContainer.dropDelegate))")
        NSLog("📦 projectCardsContainer.dropDelegate: \(String(describing: projectCardsContainer.dropDelegate))")
    }

    private func showDropIndicator(at index: Int) {
        hideDropIndicator()

        // Создаем индикатор drop
        dropIndicatorView = NSView()
        dropIndicatorView?.wantsLayer = true
        // Используем яркий синий цвет без полупрозрачности
        dropIndicatorView?.layer?.backgroundColor = NSColor(red: 0.0, green: 0.5, blue: 1.0, alpha: 1.0).cgColor
        dropIndicatorView?.layer?.cornerRadius = 1.5

        guard let indicator = dropIndicatorView else { return }

        projectCardsContainer.addSubview(indicator)

        // Позиционируем индикатор между карточками
        let yPosition: CGFloat
        let spacing: CGFloat = 10 // Расстояние между карточками

        if index == 0 {
            // Перед первой карточкой
            if let firstCard = projectCards.first {
                yPosition = firstCard.frame.maxY + spacing/2
            } else {
                yPosition = projectCardsContainer.frame.height - 20
            }
        } else if index >= projectCards.count {
            // После последней карточки
            if let lastCard = projectCards.last {
                yPosition = lastCard.frame.minY - spacing/2
            } else {
                yPosition = 20
            }
        } else {
            // Между карточками - точно по центру
            let prevCard = projectCards[index - 1]
            let nextCard = projectCards[index]
            // Рассчитываем точный центр между нижним краем предыдущей карточки и верхним краем следующей
            yPosition = prevCard.frame.minY + (nextCard.frame.maxY - prevCard.frame.minY) / 2
        }

        indicator.frame = NSRect(x: 20, y: yPosition - 2, width: projectCardsContainer.frame.width - 40, height: 4)

        // Анимация появления
        indicator.alphaValue = 0
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.15
            indicator.animator().alphaValue = 1
        }

        print("🔵 Показываем drop индикатор на позиции \(index), Y: \(yPosition)")
    }

    private func hideDropIndicator() {
        dropIndicatorView?.removeFromSuperview()
        dropIndicatorView = nil
    }

    private func getDropIndex(for location: NSPoint) -> Int {
        // Определяем индекс для вставки на основе позиции мыши
        for (index, card) in projectCards.enumerated() {
            let cardMidY = card.frame.midY
            if location.y > cardMidY {
                return index
            }
        }
        return projectCards.count
    }

    // MARK: - NSDraggingDestination

    func draggingEntered(_ sender: NSDraggingInfo) -> NSDragOperation {
        print("🎯 draggingEntered вызван в ProjectsViewController")
        NSLog("🎯 draggingEntered вызван в ProjectsViewController")

        // Проверяем, что это наш тип данных
        guard sender.draggingPasteboard.canReadObject(forClasses: [NSString.self], options: nil) else {
            print("❌ Неподходящий тип данных в pasteboard")
            NSLog("❌ Неподходящий тип данных в pasteboard")
            return []
        }

        print("✅ Подходящий тип данных найден")
        NSLog("✅ Подходящий тип данных найден")

        // Показываем индикатор drop
        let location = projectCardsContainer.convert(sender.draggingLocation, from: nil)
        let dropIndex = getDropIndex(for: location)
        print("🎯 Показываем drop индикатор на позиции \(dropIndex), location: \(location)")
        NSLog("🎯 Показываем drop индикатор на позиции \(dropIndex), location: \(location)")
        showDropIndicator(at: dropIndex)

        return .move
    }

    func draggingUpdated(_ sender: NSDraggingInfo) -> NSDragOperation {
        print("🔄 draggingUpdated вызван")

        // Обновляем позицию индикатора при движении мыши
        let location = projectCardsContainer.convert(sender.draggingLocation, from: nil)
        let dropIndex = getDropIndex(for: location)
        print("🔄 Обновляем drop индикатор на позиции \(dropIndex)")
        showDropIndicator(at: dropIndex)

        return .move
    }

    func draggingExited(_ sender: NSDraggingInfo?) {
        hideDropIndicator()
    }

    func performDragOperation(_ sender: NSDraggingInfo) -> Bool {
        hideDropIndicator()

        guard let draggedProjectId = sender.draggingPasteboard.string(forType: .string),
              let draggedUUID = UUID(uuidString: draggedProjectId) else {
            return false
        }

        // Находим карточку проекта
        guard let sourceCard = projectCards.first(where: { $0.currentProject.id == draggedUUID }) else {
            return false
        }

        // Определяем новую позицию
        let location = projectCardsContainer.convert(sender.draggingLocation, from: nil)
        let targetIndex = getDropIndex(for: location)

        // Выполняем перестановку
        performProjectReorder(sourceCard: sourceCard, targetIndex: targetIndex)

        return true
    }

    private func performProjectReorder(sourceCard: ProjectCard, targetIndex: Int) {
        // Находим индекс источника
        guard let sourceIndex = projectCards.firstIndex(of: sourceCard) else {
            print("❌ Не удалось найти индекс источника")
            return
        }

        print("🔄 Перемещаем проект с позиции \(sourceIndex) на позицию \(targetIndex)")

        // Создаем новый порядок проектов
        var newOrder = projectCards.map { $0.currentProject.id }
        let movedProjectId = newOrder.remove(at: sourceIndex)

        // Корректируем целевой индекс если нужно
        let adjustedTargetIndex = targetIndex > sourceIndex ? targetIndex - 1 : targetIndex
        newOrder.insert(movedProjectId, at: adjustedTargetIndex)

        // Сохраняем новый порядок
        projectManager.setProjectOrder(newOrder)

        // Перезагружаем список для отображения нового порядка
        loadProjects()

        // Уведомляем AppDelegate об изменении порядка для обновления меню
        NotificationCenter.default.post(name: NSNotification.Name("ProjectOrderChanged"), object: nil)
    }
}

// MARK: - ProjectCreateForm Delegate

extension ProjectsViewController: ProjectCreateFormDelegate {
    func projectCreateFormDidCreateProject(_ project: Project) {
        print("🎯 Делегат: проект создан - \(project.name)")
        NSLog("🎯 Делегат: проект создан - \(project.name)")
        loadProjects() // Перезагружаем список проектов
        print("🎯 Делегат: loadProjects() вызван")
        NSLog("🎯 Делегат: loadProjects() вызван")
    }
    
    func projectCreateFormDidCancel() {
        // Закрываем форму
        if let overlay = view.subviews.last {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.2
                overlay.animator().layer?.opacity = 0
            } completionHandler: {
                overlay.removeFromSuperview()
            }
        }
    }

    // MARK: - Stable Button Creation

    private func createStableButton(title: String, isPrimary: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true
        button.isEnabled = true
        button.bezelStyle = .rounded

        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        gradientLayer.cornerRadius = 8

        if isPrimary {
            // Зеленый градиент для основной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.3, alpha: 1.0).cgColor
            ]
        } else {
            // Серый градиент для вторичной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0).cgColor,
                NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor
            ]
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)

        // Добавляем градиент как подслой, а не заменяем основной слой
        button.layer?.addSublayer(gradientLayer)

        // Белый текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 14, weight: .medium)
            ]
        )

        // Обновляем размер градиента при изменении размера кнопки
        DispatchQueue.main.async {
            gradientLayer.frame = button.bounds
        }

        return button
    }

    // MARK: - Edit Project Form

    private func showEditProjectForm(for project: Project) {
        // Создаем overlay
        let overlay = NSView()
        overlay.wantsLayer = true
        overlay.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.5).cgColor
        overlay.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(overlay)

        // Создаем форму редактирования
        let formView = NSView()
        formView.wantsLayer = true
        formView.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        formView.layer?.cornerRadius = 12
        formView.translatesAutoresizingMaskIntoConstraints = false
        overlay.addSubview(formView)

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "Редактировать проект")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(titleLabel)

        // Поле названия
        let nameLabel = NSTextField(labelWithString: "Название:")
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(nameLabel)

        let nameField = NSTextField()
        nameField.stringValue = project.name
        nameField.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(nameField)

        // Поле эмодзи
        let emojiLabel = NSTextField(labelWithString: "Эмодзи:")
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(emojiLabel)

        let emojiField = NSTextField()
        emojiField.stringValue = project.customEmoji ?? project.type.emoji
        emojiField.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(emojiField)

        // Кнопки
        let cancelButton = createStableButton(title: "Отмена", isPrimary: false)
        cancelButton.target = self
        cancelButton.action = #selector(closeEditForm)
        formView.addSubview(cancelButton)

        let saveButton = createStableButton(title: "Сохранить", isPrimary: true)
        saveButton.target = self
        saveButton.action = #selector(saveEditedProject)
        formView.addSubview(saveButton)

        // Сохраняем ссылки для доступа в action методах
        overlay.setValue(project, forKey: "editingProject")
        overlay.setValue(nameField, forKey: "nameField")
        overlay.setValue(emojiField, forKey: "emojiField")

        // Констрейнты
        NSLayoutConstraint.activate([
            // Overlay
            overlay.topAnchor.constraint(equalTo: view.topAnchor),
            overlay.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            overlay.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            overlay.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // Form
            formView.centerXAnchor.constraint(equalTo: overlay.centerXAnchor),
            formView.centerYAnchor.constraint(equalTo: overlay.centerYAnchor),
            formView.widthAnchor.constraint(equalToConstant: 400),
            formView.heightAnchor.constraint(equalToConstant: 300),

            // Title
            titleLabel.topAnchor.constraint(equalTo: formView.topAnchor, constant: 20),
            titleLabel.centerXAnchor.constraint(equalTo: formView.centerXAnchor),

            // Name
            nameLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 30),
            nameLabel.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),

            nameField.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 8),
            nameField.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            nameField.trailingAnchor.constraint(equalTo: formView.trailingAnchor, constant: -20),

            // Emoji
            emojiLabel.topAnchor.constraint(equalTo: nameField.bottomAnchor, constant: 20),
            emojiLabel.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),

            emojiField.topAnchor.constraint(equalTo: emojiLabel.bottomAnchor, constant: 8),
            emojiField.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            emojiField.trailingAnchor.constraint(equalTo: formView.trailingAnchor, constant: -20),

            // Buttons
            cancelButton.bottomAnchor.constraint(equalTo: formView.bottomAnchor, constant: -20),
            cancelButton.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            cancelButton.widthAnchor.constraint(equalToConstant: 100),
            cancelButton.heightAnchor.constraint(equalToConstant: 32),

            saveButton.bottomAnchor.constraint(equalTo: formView.bottomAnchor, constant: -20),
            saveButton.trailingAnchor.constraint(equalTo: formView.trailingAnchor, constant: -20),
            saveButton.widthAnchor.constraint(equalToConstant: 100),
            saveButton.heightAnchor.constraint(equalToConstant: 32)
        ])

        // Анимация появления
        overlay.layer?.opacity = 0
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.2
            overlay.animator().layer?.opacity = 1
        }
    }

    @objc private func closeEditForm() {
        // Закрываем форму
        if let overlay = view.subviews.last {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.2
                overlay.animator().layer?.opacity = 0
            } completionHandler: {
                overlay.removeFromSuperview()
            }
        }
    }

    @objc private func saveEditedProject() {
        guard let overlay = view.subviews.last,
              let project = overlay.value(forKey: "editingProject") as? Project,
              let nameField = overlay.value(forKey: "nameField") as? NSTextField,
              let emojiField = overlay.value(forKey: "emojiField") as? NSTextField else {
            return
        }

        let newName = nameField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let newEmoji = emojiField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !newName.isEmpty else {
            let alert = NSAlert()
            alert.messageText = "Ошибка"
            alert.informativeText = "Введите название проекта"
            alert.addButton(withTitle: "OK")
            alert.runModal()
            return
        }

        // Обновляем проект
        var updatedProject = project
        updatedProject.name = newName
        updatedProject.customEmoji = newEmoji.isEmpty ? nil : newEmoji

        projectManager.updateProject(updatedProject)

        print("📝 Проект обновлен: \(updatedProject.name)")
        NSLog("📝 Проект обновлен: \(updatedProject.name)")

        // Перезагружаем список
        loadProjects()

        // Закрываем форму
        closeEditForm()
    }

    // MARK: - ProjectFormDelegate

    func projectFormDidSave(_ form: ProjectForm, project: Project) {
        if let existingProject = projectManager.getAllProjects().first(where: { $0.id == project.id }) {
            // Редактирование существующего проекта
            projectManager.updateProject(project)
            print("📝 Проект обновлен через универсальную форму: \(project.name)")
            NSLog("📝 Проект обновлен через универсальную форму: \(project.name)")

            // Уведомляем об изменении проекта для обновления контекстного меню
            NotificationCenter.default.post(name: NSNotification.Name("FavoritesChanged"), object: nil)
        } else {
            // Создание нового проекта
            projectManager.createProject(
                name: project.name,
                type: project.type,
                parentId: project.parentId,
                color: project.color,
                customEmoji: project.customEmoji
            )
            print("🎯 Проект создан через универсальную форму: \(project.name)")
            NSLog("🎯 Проект создан через универсальную форму: \(project.name)")

            // Уведомляем об изменении проектов для обновления контекстного меню
            NotificationCenter.default.post(name: NSNotification.Name("FavoritesChanged"), object: nil)
        }

        // Перезагружаем список проектов
        loadProjects()

        // Удаляем форму
        form.removeFromSuperview()
    }

    func projectFormDidCancel(_ form: ProjectForm) {
        print("🚫 Отмена формы проекта")
        NSLog("🚫 Отмена формы проекта")

        // Удаляем форму
        form.removeFromSuperview()
    }
}
