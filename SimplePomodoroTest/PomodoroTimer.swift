import Foundation
import Cocoa

enum PomodoroState: String {
    case idle = "idle"
    case working = "working"
    case overtime = "overtime"
    case onBreak = "onBreak"
}

/// Структура для управления уровнями переработки (DRY принцип)
struct OvertimeLevel {
    let level: Int
    let minMinutes: Int
    let maxMinutes: Int?
    let color: NSColor
    let title: String
    let subtitle: String

    /// Проверяет, попадает ли количество минут в этот уровень
    func contains(minutes: Int) -> Bool {
        if let max = maxMinutes {
            return minutes >= minMinutes && minutes < max
        } else {
            return minutes >= minMinutes
        }
    }
}

/// ⚙️ НАСТРОЙКА УВЕДОМЛЕНИЙ О ПЕРЕРАБОТКЕ ⚙️
///
/// 🎯 ЗДЕСЬ МОЖНО ЛЕГКО ИЗМЕНИТЬ:
/// • ⏰ Временные границы (когда появляются уведомления)
/// • 🎨 Цвета статус-бара для каждого уровня
/// • 📝 Тексты заголовков и подзаголовков
/// • 😀 Эмодзи и иконки
///
/// 🔄 Изменения автоматически применятся к:
/// • Цветам в статус-баре
/// • Анимациям окна (пульсация/тряска)
/// • Текстам в уведомлениях
///
/// 📋 Формат: level (порядок), minMinutes-maxMinutes (границы), color, title, subtitle
struct OvertimeConfig {
    static let levels: [OvertimeLevel] = [
        // 🎉 УРОВЕНЬ 0: Сессия завершена (0-1 мин) - обычный цвет, без анимаций
        OvertimeLevel(
            level: 0,
            minMinutes: 0,
            maxMinutes: 1,
            color: NSColor.controlTextColor,
            title: "🎉 Session completed!",
            subtitle: "What's next?"
        ),

        // ⚠️ УРОВЕНЬ 1: Желтая зона (1-3 мин) - желтый цвет, легкая пульсация
        OvertimeLevel(
            level: 1,
            minMinutes: 1,
            maxMinutes: 3,
            color: NSColor.systemYellow,
            title: "⚠️ Yellow zone",
            subtitle: "Consider taking a break"
        ),

        // 🟠 УРОВЕНЬ 2: Оранжевая зона (3-5 мин) - оранжевый цвет, сильная пульсация
        OvertimeLevel(
            level: 2,
            minMinutes: 3,
            maxMinutes: 5,
            color: NSColor.systemOrange,
            title: "🟠 Orange zone",
            subtitle: "Time for a break!"
        ),

        // 🔴 УРОВЕНЬ 3: Красная зона (5-10 мин) - красный цвет, тряска
        OvertimeLevel(
            level: 3,
            minMinutes: 5,
            maxMinutes: 10,
            color: NSColor.systemRed,
            title: "🔴 Red zone",
            subtitle: "Please take a rest NOW!"
        ),

        // 🚨 УРОВЕНЬ 4: Критическая зона (10-15 мин) - темно-красный, сильная тряска
        OvertimeLevel(
            level: 4,
            minMinutes: 10,
            maxMinutes: 15,
            color: NSColor(red: 0.7, green: 0.1, blue: 0.1, alpha: 1.0),
            title: "🚨 Critical zone",
            subtitle: "Health break needed!"
        ),

        // 🆘 УРОВЕНЬ 5: Экстренная зона (15+ мин) - темно-красный, критическая тряска
        OvertimeLevel(
            level: 5,
            minMinutes: 15,
            maxMinutes: nil, // nil = без ограничения сверху
            color: NSColor(red: 0.7, green: 0.1, blue: 0.1, alpha: 1.0),
            title: "🆘 URGENT",
            subtitle: "Take a break immediately!"
        )
    ]

    /// Получает уровень переработки по количеству минут
    static func getLevel(for minutes: Int) -> OvertimeLevel {
        return levels.first { $0.contains(minutes: minutes) } ?? levels.last!
    }

    /// Получает цвет для статус-бара по количеству минут
    static func getColor(for minutes: Int) -> NSColor {
        return getLevel(for: minutes).color
    }

    /// Получает номер уровня по количеству минут
    static func getLevelNumber(for minutes: Int) -> Int {
        return getLevel(for: minutes).level
    }
}

class PomodoroTimer: ObservableObject {
    // Константы
    static var workDuration: TimeInterval = 52 * 60 // 52 минуты (изменяемая)
    static let reminderInterval: TimeInterval = 5 * 60 // 5 минут
    
    // Состояние таймера
    @Published var state: PomodoroState = .idle
    @Published var timeRemaining: TimeInterval = 0
    @Published var overtimeElapsed: TimeInterval = 0
    @Published var reminderCount: Int = 0
    @Published var breakTimeRemaining: TimeInterval = 0

    // Отслеживание полноценных интервалов
    private var isFullInterval: Bool = false
    private var intervalStartTime: Date?
    var naturallyCompleted: Bool = false

    // Отслеживание растущих сессий
    private var isGrowthSegment: Bool = false  // Это сегмент взращивания, не отдельный интервал

    // Счетчик интервалов для определения типа отдыха
    @Published var completedIntervals: Int = 0

    // Логика Long отдыхов с прогрессивной усталостью
    private var intervalsSinceLastLong: Int = 0  // Интервалы с последнего Long отдыха
    private var isFirstCycle: Bool = true        // Первый цикл (3 интервала) или последующие (2 интервала)

    // Таймеры
    private var workTimer: Timer?
    private var overtimeTimer: Timer?
    private var reminderTimer: Timer?

    // Таймер отдыха
    var breakTimer: BreakTimer  // Делаем публичным для доступа к качеству отдыха

    // Колбэки
    var onStateChanged: ((PomodoroState) -> Void)?
    var onTimeUpdate: ((TimeInterval, TimeInterval) -> Void)?
    var onIntervalCompleted: (() -> Void)?
    var onReminderTriggered: ((Int) -> Void)?
    var onOvertimeColorChanged: ((Int) -> Void)? // Новый колбэк для изменения цвета переработки
    var onCheckUserActivity: (() -> Bool)?  // Колбэк для проверки активности пользователя
    var onFullIntervalCompleted: ((TimeInterval) -> Void)?
    var onBreakStarted: (() -> Void)?
    var onBreakCompleted: ((BreakStatistics?) -> Void)?
    var onBreakActivityDetected: (() -> Void)?
    var onBreakStatisticsReady: ((BreakStatistics) -> Void)?
    var onFullBreakCompleted: ((BreakStatistics) -> Void)? // Только для полноценных отдыхов
    var onBreakTypeSelection: ((Int, @escaping (Bool) -> Void) -> Void)? // (интервалы, колбэк с выбором)
    var onSessionGrowthOffered: ((Int, @escaping (Bool) -> Void) -> Void)? // Предложение взращивания сессии

    // Звуковой менеджер
    var soundManager: SoundManager?

    // Отслеживание изменений цвета переработки
    private var lastOvertimeColorLevel: Int = -1

    init(minuteActivityTracker: MinuteActivityTracker? = nil) {
        timeRemaining = Self.workDuration
        breakTimer = BreakTimer(minuteActivityTracker: minuteActivityTracker)
        setupBreakTimer()
        setupSessionGrowingEngine()
        logInfo("PomodoroTimer", "🌿 PomodoroTimer инициализирован с \(minuteActivityTracker != nil ? "переданным" : "новым") MinuteActivityTracker")
    }

    // MARK: - Настройки

    func updateWorkDuration(_ newDuration: TimeInterval) {
        Self.workDuration = newDuration
        if state == .idle {
            timeRemaining = newDuration
            onTimeUpdate?(timeRemaining, overtimeElapsed)
        }
    }

    /// Устанавливает MinuteActivityTracker для BreakTimer (для унификации с ActivityStateManager)
    func setMinuteActivityTracker(_ tracker: MinuteActivityTracker) {
        // Создаем новый BreakTimer с переданным трекером
        let oldBreakTimer = breakTimer
        breakTimer = BreakTimer(minuteActivityTracker: tracker)

        // Копируем настройки звука
        breakTimer.soundManager = oldBreakTimer.soundManager

        // Перенастраиваем колбэки
        setupBreakTimer()

        logInfo("PomodoroTimer", "🌿 MinuteActivityTracker установлен для BreakTimer")
    }
    
    // MARK: - Основные методы управления
    
    func startInterval(testDuration: Int? = nil) {
        guard state == .idle else {
            logWarning("PomodoroTimer", "Попытка запуска интервала в состоянии: \(state)")
            return
        }

        let previousState = state
        state = .working
        timeRemaining = testDuration != nil ? TimeInterval(testDuration!) : Self.workDuration
        overtimeElapsed = 0
        reminderCount = 0
        lastOvertimeColorLevel = -1 // Сбрасываем отслеживание цвета

        // Отслеживание полноценных интервалов
        isFullInterval = testDuration == nil // Только полные интервалы, не тестовые
        intervalStartTime = Date()
        naturallyCompleted = false

        startWorkTimer()
        onStateChanged?(state)

        let duration = testDuration != nil ? "\(testDuration!) сек (тест)" : "\(Int(Self.workDuration)) сек"

        logInfo("Timer", "▶️ Интервал запущен (\(Int(timeRemaining))с, интервалов: \(completedIntervals))")

        print("🍅 PomodoroTimer: Интервал начат. Продолжительность: \(duration). Полноценный: \(isFullInterval)")
    }

    /// Запускает интервал с произвольной длительностью (для системы взращивания)
    func startCustomInterval(duration: TimeInterval) {
        guard state == .idle else {
            logWarning("PomodoroTimer", "Попытка запуска кастомного интервала в состоянии: \(state)")
            return
        }

        state = .working
        timeRemaining = duration
        overtimeElapsed = 0
        reminderCount = 0
        lastOvertimeColorLevel = -1

        // Кастомные интервалы - это сегменты взращивания, не отдельные интервалы
        isFullInterval = true
        isGrowthSegment = true  // Помечаем как сегмент взращивания
        intervalStartTime = Date()
        naturallyCompleted = false

        startWorkTimer()
        onStateChanged?(state)

        logInfo("Timer", "▶️ Сегмент взращивания запущен (\(Int(duration))с)")
        print("🌱 PomodoroTimer: Сегмент взращивания начат. Продолжительность: \(Int(duration/60)) мин")
    }

    /// Запускает отдых с произвольной длительностью (для адаптивного отдыха)
    func startCustomBreak(duration: TimeInterval) {
        logInfo("PomodoroTimer", "🌿 Запуск кастомного отдыха: \(Int(duration/60)) мин")

        // Останавливаем таймер переработки
        stopOvertimeTimer()

        // Меняем состояние на отдых
        state = .onBreak

        // Запускаем отдых с кастомной длительностью
        breakTimer.startBreak(duration: duration)
        breakTimeRemaining = duration

        onStateChanged?(state)

        print("🌿 PomodoroTimer: Кастомный отдых начат. Длительность: \(Int(duration/60)) мин")
    }

    /// Завершает растущую сессию и записывает общую статистику
    func finishGrowingSession() {
        // Получаем информацию о сессии из SessionGrowingEngine
        let sessionInfo = SessionGrowingEngine.shared.getCurrentSessionInfo()
        logInfo("Timer", "🌱 Завершение растущей сессии: \(sessionInfo)")

        // Получаем общую длительность сессии
        if let totalDuration = SessionGrowingEngine.shared.getCurrentSessionTotalDuration() {
            // Записываем в статистику как ОДИН интервал с общей длительностью
            onFullIntervalCompleted?(totalDuration)

            // Увеличиваем счетчик завершенных интервалов (только один раз за всю сессию)
            completedIntervals += 1
            intervalsSinceLastLong += 1

            logInfo("Timer", "🌱 Растущая сессия завершена: \(Int(totalDuration/60)) мин, интервалов: \(completedIntervals)")
            print("🌱 PomodoroTimer: Растущая сессия завершена. Общая длительность: \(Int(totalDuration/60)) мин")
        }

        // Завершаем сессию в SessionGrowingEngine
        let oldBar = EarlyEngagementSystem.shared.getCurrentUserBar()
        let newBar = SessionGrowingEngine.shared.finishSession(oldBar: oldBar)

        // Обновляем планку в EarlyEngagementSystem
        EarlyEngagementSystem.shared.updateUserBar(newBar)

        logInfo("Timer", "🌱 Сессия завершена: \(Int(oldBar/60)) → \(Int(newBar/60)) мин")
    }

    func stopInterval() {
        guard state != .idle else {
            logWarning("PomodoroTimer", "Попытка остановки интервала в состоянии idle")
            return
        }

        let previousState = state
        let elapsedTime = Self.workDuration - timeRemaining

        logInfo("Timer", "⏹️ Интервал остановлен (прошло: \(Int(elapsedTime))с, переработка: \(Int(overtimeElapsed))с)")

        stopAllTimers()
        resetInterval()

        print("🍅 PomodoroTimer: Интервал остановлен вручную")
    }

    private func resetInterval() {
        state = .idle
        timeRemaining = Self.workDuration
        overtimeElapsed = 0
        reminderCount = 0
        breakTimeRemaining = 0
        isFullInterval = false
        intervalStartTime = nil
        naturallyCompleted = false

        onStateChanged?(state)
        onTimeUpdate?(timeRemaining, overtimeElapsed)
    }
    
    func completeInterval() {
        // НЕ воспроизводим звук здесь - он воспроизводится в AppDelegate.showCompletionWindow()
        NSLog("🍅 PomodoroTimer: Интервал завершен. naturallyCompleted: \(naturallyCompleted). Звук воспроизводится в showCompletionWindow()")

        // Проверяем, был ли это полноценный интервал, который естественно завершился
        if isFullInterval && naturallyCompleted, let startTime = intervalStartTime {
            let actualDuration = Date().timeIntervalSince(startTime)

            onFullIntervalCompleted?(actualDuration)
            print("🍅 PomodoroTimer: Полноценный интервал завершен! Продолжительность: \(Int(actualDuration/60)) мин")

            // Предлагаем взращивание сессии для интервалов < 52 минут
            if SessionGrowingEngine.shared.canOfferGrowth(for: actualDuration) {
                // Получаем текущую планку пользователя
                let currentUserBar = EarlyEngagementSystem.shared.getCurrentUserBar()
                SessionGrowingEngine.shared.offerGrowth(for: actualDuration, userBar: currentUserBar)
            }
        }

        // Увеличиваем счетчики только для полных интервалов, не для сегментов взращивания
        if !isGrowthSegment {
            completedIntervals += 1
            intervalsSinceLastLong += 1
            print("🍅 PomodoroTimer: Полный интервал завершен. Всего завершено: \(completedIntervals), с последнего Long: \(intervalsSinceLastLong)")
        } else {
            print("🌱 PomodoroTimer: Сегмент взращивания завершен. Счетчики не изменены.")
        }

        stopAllTimers()

        // Сбрасываем флаг сегмента взращивания
        isGrowthSegment = false
    }

    /// Начинает сегмент взращивания сессии
    /// - Parameter minutes: Количество минут для взращивания
    func startSessionGrowth(minutes: Int) {
        guard state == .idle else {
            logWarning("PomodoroTimer", "Попытка начать взращивание в состоянии \(state)")
            return
        }

        let duration = TimeInterval(minutes * 60)

        // Устанавливаем новую длительность
        timeRemaining = duration
        Self.workDuration = duration // Временно изменяем рабочую длительность

        // Запускаем интервал
        startInterval()

        logInfo("PomodoroTimer", "🌱 Начато взращивание сессии: \(minutes) мин")
    }
    
    func extendInterval(minutes: Int = 5) {
        guard state == .overtime else { return }

        print("🔧 PomodoroTimer: Продление интервала на \(minutes) минут")
        print("🔧 PomodoroTimer: Текущая переработка: \(overtimeElapsed) сек")

        // Останавливаем только таймер напоминаний
        stopReminderTimer()

        // ОСТАЕМСЯ в состоянии overtime - НЕ переходим в working!
        // overtimeTimer продолжает работать и увеличивать overtimeElapsed
        // Просто сбрасываем счетчик напоминаний и запускаем новый таймер напоминаний
        reminderCount = 0

        // Запускаем таймер напоминаний с новым интервалом
        // startReminderTimer(delayMinutes: minutes) // ОТКЛЮЧЕНО: теперь используем onOvertimeColorChanged

        print("🔧 PomodoroTimer: Интервал продлен на \(minutes) мин. Остаемся в режиме переработки. Переработка: \(overtimeElapsed) сек")
    }
    
    // MARK: - Приватные методы таймеров
    
    private func startWorkTimer() {
        workTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateWorkTimer()
        }
    }
    
    private func updateWorkTimer() {
        timeRemaining -= 1
        onTimeUpdate?(timeRemaining, overtimeElapsed)

        // Логируем каждые 10 минут или при переходе в переработку
        if timeRemaining.truncatingRemainder(dividingBy: 600) == 0 || timeRemaining <= 0 {
            logDebug("Timer", "⏱️ Обновление (осталось: \(Int(timeRemaining))с)")
        }

        if timeRemaining <= 0 {
            logInfo("Timer", "🔥 Переход в переработку (работали: \(Int(Self.workDuration))с)")

            workTimer?.invalidate()
            workTimer = nil

            // Отмечаем, что интервал естественно завершился
            naturallyCompleted = true

            // Переходим в режим переработки
            state = .overtime
            onStateChanged?(state)

        // Показываем окно завершения только если это первый переход в переработку
        // (если overtimeElapsed == 0, значит это не продление)
        if overtimeElapsed == 0 {
            logInfo("PomodoroTimer", "Вызов onIntervalCompleted")
            onIntervalCompleted?()
        }

            // Запускаем таймер переработки (напоминания теперь через onOvertimeColorChanged)
            startOvertimeTimer()
            // startReminderTimer() // ОТКЛЮЧЕНО: теперь используем onOvertimeColorChanged
        }
    }
    
    private func startOvertimeTimer() {
        overtimeTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateOvertimeTimer()
        }
    }

    private func stopOvertimeTimer() {
        overtimeTimer?.invalidate()
        overtimeTimer = nil
        print("⏹️ PomodoroTimer: Таймер переработки остановлен")
    }

    // MARK: - Логика Long отдыхов

    /// Проверяет, нужно ли показать выбор Long/Short отдыха
    func shouldShowBreakTypeSelection() -> Bool {
        let requiredIntervals = isFirstCycle ? 3 : 2
        let shouldShow = intervalsSinceLastLong >= requiredIntervals

        print("🎯 PomodoroTimer: Проверка Long отдыха - интервалов с последнего Long: \(intervalsSinceLastLong), требуется: \(requiredIntervals), первый цикл: \(isFirstCycle), показать выбор: \(shouldShow)")

        return shouldShow
    }

    /// Вызывается когда пользователь выбрал Long отдых
    func userSelectedLongBreak() {
        print("🎯 PomodoroTimer: Пользователь выбрал Long отдых - сбрасываем счетчик")
        intervalsSinceLastLong = 0
        isFirstCycle = false  // После первого Long отдыха все циклы по 2 интервала
    }
    
    private func updateOvertimeTimer() {
        overtimeElapsed += 1
        // НЕ вызываем onTimeUpdate для переработки - используем SimpleUnifiedSystem
        // onTimeUpdate?(timeRemaining, overtimeElapsed)

        // Проверяем изменение цветового уровня переработки
        let currentColorLevel = getOvertimeColorLevel()
        // Убираем избыточное логирование каждую секунду - засоряет логи

        if currentColorLevel != lastOvertimeColorLevel {
            logInfo("Timer", "🔍 Уровень изменился! \(lastOvertimeColorLevel) → \(currentColorLevel)")
            lastOvertimeColorLevel = currentColorLevel

            // ВАЖНО: Проверяем активность пользователя перед показом эскалации
            // Если пользователь неактивен - НЕ показываем следующий уровень эскалации
            if let activityChecker = onCheckUserActivity {
                let isActive = activityChecker()
                logInfo("Timer", "🔍 Проверка активности: \(isActive)")
                if !isActive {
                    logInfo("Timer", "🎨 Пользователь неактивен - пропускаем эскалацию уровня \(currentColorLevel)")
                    return
                }
            } else {
                logWarning("Timer", "🔍 onCheckUserActivity НЕ УСТАНОВЛЕН!")
            }

            logInfo("Timer", "🔍 Вызываем onOvertimeColorChanged(\(currentColorLevel))")
            onOvertimeColorChanged?(currentColorLevel)
            logInfo("Timer", "🎨 Изменение цвета переработки на уровень \(currentColorLevel)")
        }
        // Убираем логирование "Уровень НЕ изменился" - вызывается каждую секунду
    }

    private func getOvertimeColorLevel() -> Int {
        let minutes = Int(overtimeElapsed) / 60
        return OvertimeConfig.getLevelNumber(for: minutes)
    }
    
    private func startReminderTimer(delayMinutes: Int? = nil) {
        let interval = delayMinutes != nil ? TimeInterval(delayMinutes! * 60) : Self.reminderInterval
        reminderTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.triggerReminder()
        }
    }
    
    private func triggerReminder() {
        reminderCount += 1
        onReminderTriggered?(reminderCount)
    }
    
    private func stopAllTimers() {
        logDebug("Timer", "⏹️ Остановка всех таймеров (состояние: \(state.rawValue))")

        workTimer?.invalidate()
        workTimer = nil

        overtimeTimer?.invalidate()
        overtimeTimer = nil

        stopReminderTimer()
    }
    
    private func stopReminderTimer() {
        reminderTimer?.invalidate()
        reminderTimer = nil
    }

    // MARK: - Pause/Resume для выбора типа отдыха

    func pauseOvertimeTimer() {
        guard state == .overtime else { return }
        overtimeTimer?.invalidate()
        overtimeTimer = nil
        print("⏸️ PomodoroTimer: Таймер переработки приостановлен для выбора отдыха")
    }

    func resumeOvertimeTimer() {
        guard state == .overtime else { return }
        startOvertimeTimer()
        print("▶️ PomodoroTimer: Таймер переработки возобновлен")
    }

    // MARK: - Pause/Resume для системы обнаружения активности

    /// Приостанавливает основной рабочий таймер при неактивности
    func pauseWorkTimer() {
        guard state == .working else { return }
        workTimer?.invalidate()
        workTimer = nil
        print("⏸️ PomodoroTimer: Основной таймер приостановлен из-за неактивности")
    }

    /// Возобновляет основной рабочий таймер при возвращении активности
    func resumeWorkTimer() {
        guard state == .working else { return }
        startWorkTimer()
        print("▶️ PomodoroTimer: Основной таймер возобновлен")
    }

    /// Приостанавливает все активные таймеры в зависимости от текущего состояния
    func pauseAllTimers() {
        switch state {
        case .working:
            pauseWorkTimer()
        case .overtime:
            pauseOvertimeTimer()
        case .idle, .onBreak:
            // В этих состояниях нет активных таймеров для приостановки
            break
        }
        print("⏸️ PomodoroTimer: Все таймеры приостановлены (состояние: \(state))")
    }

    /// Возобновляет все таймеры в зависимости от текущего состояния
    func resumeAllTimers() {
        switch state {
        case .working:
            resumeWorkTimer()
        case .overtime:
            resumeOvertimeTimer()
        case .idle, .onBreak:
            // В этих состояниях нет таймеров для возобновления
            break
        }
        print("▶️ PomodoroTimer: Все таймеры возобновлены (состояние: \(state))")
    }
    
    // MARK: - Утилиты
    
    func formatTime(_ seconds: TimeInterval) -> String {
        let minutes = Int(seconds) / 60
        let remainingSeconds = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    func getStatusText(projectName: String? = nil) -> String {
        switch state {
        case .idle:
            return "🍅"
        case .working:
            if let projectName = projectName {
                return "\(projectName) \(formatTime(timeRemaining))"
            }
            return "🍅 \(formatTime(timeRemaining))"
        case .overtime:
            if let projectName = projectName {
                return "\(projectName) +\(formatTime(overtimeElapsed))"
            }
            return "\(getOvertimeIcon()) +\(formatTime(overtimeElapsed))"
        case .onBreak:
            return "🍃 \(formatTime(breakTimeRemaining))"
        }
    }

    /// Возвращает реальную продолжительность текущего интервала
    func getActualIntervalDuration() -> TimeInterval {
        guard let startTime = intervalStartTime else {
            // Если нет времени начала, возвращаем время, которое прошло от начала интервала
            return Self.workDuration - timeRemaining
        }

        // Возвращаем реальное время с момента начала интервала
        return Date().timeIntervalSince(startTime)
    }

    private func getOvertimeIcon() -> String {
        let minutes = Int(overtimeElapsed) / 60

        switch minutes {
        case 0:
            return "⚠️"  // Первая минута (0-59 сек) - обычное предупреждение
        case 1..<3:
            return "⚠️"  // 1-2 минуты - обычное предупреждение (цвет желтый)
        case 3..<5:
            return "⚠️"  // 3-4 минуты - обычное предупреждение (цвет оранжевый)
        case 5..<10:
            return "⚠️"  // 5-9 минут - обычное предупреждение (цвет красный)
        case 10..<15:
            return "⚠️"  // 10-14 минут - обычное предупреждение (цвет темно-бордовый)
        default:
            return "🚨"  // 15+ минут - мигалка (цвет красный фон)
        }
    }
    
    func isActive() -> Bool {
        return state != .idle
    }

    // MARK: - Break Management

    private func setupSessionGrowingEngine() {
        SessionGrowingEngine.shared.onGrowthOffered = { [weak self] minutes, callback in
            // Передаем предложение взращивания через колбэк
            self?.onSessionGrowthOffered?(minutes, callback)
        }
    }

    private func setupBreakTimer() {
        breakTimer.onBreakCompleted = { [weak self] in
            DispatchQueue.main.async {
                self?.handleBreakCompleted()
            }
        }

        breakTimer.onTimeUpdate = { [weak self] timeRemaining in
            DispatchQueue.main.async {
                self?.breakTimeRemaining = timeRemaining
                // Уведомляем о обновлении времени для обновления UI
                self?.onTimeUpdate?(self?.timeRemaining ?? 0, self?.overtimeElapsed ?? 0)
            }
        }

        breakTimer.onActivityDetected = { [weak self] in
            DispatchQueue.main.async {
                self?.onBreakActivityDetected?()
            }
        }
    }

    func startBreak() {
        guard state != .onBreak else { return }

        // Автоматически короткий отдых (старая логика для совместимости)
        startBreakWithType(isLong: false)
    }

    func startBreakWithType(isLong: Bool, testDuration: TimeInterval? = nil) {
        logInfo("PomodoroTimer", "🌿 startBreakWithType вызван: isLong=\(isLong), testDuration=\(testDuration?.description ?? "nil")")

        // Сначала останавливаем таймер переработки (пока состояние еще .overtime)
        stopOvertimeTimer()

        // Теперь меняем состояние на отдых
        state = .onBreak
        logInfo("PomodoroTimer", "🌿 Состояние изменено на .onBreak")

        let duration = testDuration ?? (isLong ? BreakTimer.longBreakDuration : BreakTimer.shortBreakDuration)
        logInfo("PomodoroTimer", "🌿 Вызываем breakTimer.startBreak(duration: \(duration))")
        breakTimer.startBreak(duration: duration)
        breakTimeRemaining = duration
        logInfo("PomodoroTimer", "🌿 BreakTimer запущен, breakTimeRemaining=\(breakTimeRemaining)")

        // Если это длинный отдых, сбрасываем счетчик
        if isLong {
            completedIntervals = 0
        }

        onStateChanged?(state)
        onBreakStarted?()

        let breakType = testDuration != nil ? "тестовый (\(Int(testDuration!)) сек)" : (isLong ? "длинный (90 мин)" : "короткий (17 мин)")
        print("🌿 PomodoroTimer: \(breakType) отдых начат. Завершено интервалов: \(completedIntervals)")
    }

    func stopBreak() {
        guard state == .onBreak else { return }

        breakTimer.stopBreak()
        resetInterval()

        print("🌿 PomodoroTimer: Отдых остановлен")
    }

    private func handleBreakCompleted() {
        guard state == .onBreak else { return }

        // Получаем статистику отдыха перед сбросом
        let breakStats = breakTimer.getBreakStatistics()
        if let stats = breakStats {
            // Проверяем, был ли это полноценный отдых
            if breakTimer.isFullBreakCompleted() {
                onFullBreakCompleted?(stats)
                print("🌿 PomodoroTimer: Полноценный отдых завершен! Продолжительность: \(Int(stats.duration/60)) мин")
            } else {
                // Для тестовых отдыхов используем старый колбэк
                onBreakStatisticsReady?(stats)
                print("🌿 PomodoroTimer: Тестовый отдых завершен (не записывается в статистику)")
            }
        }

        resetInterval()

        // Передаем статистику в колбэк для бесшовного перехода
        onBreakCompleted?(breakStats)

        print("🌿 PomodoroTimer: Отдых завершен")
    }

    func getBreakStatistics() -> BreakStatistics? {
        return breakTimer.getBreakStatistics()
    }

    // MARK: - Testing helpers

    #if DEBUG
    func setNaturallyCompleted(_ value: Bool) {
        naturallyCompleted = value
    }

    func setIsFullInterval(_ value: Bool) {
        isFullInterval = value
    }

    func setIntervalStartTime(_ date: Date) {
        intervalStartTime = date
    }
    #endif

    // Метод для тестирования (доступен в Release для тестовых функций)
    func setIntervalsSinceLastLong(_ value: Int) {
        intervalsSinceLastLong = value
        logInfo("Timer", "🧪 Тест: установлено intervalsSinceLastLong = \(value)")
    }
}
