//
//  ReturnMessageGenerator.swift
//  uProd
//
//  Генератор умных сообщений при возвращении пользователя после периодов неактивности
//

import Foundation

/// Генератор сообщений для возвращения пользователя после отдыха
class ReturnMessageGenerator {
    
    // MARK: - Message Data Structures
    
    /// Данные сообщения для отображения пользователю
    struct ReturnMessageData {
        let title: String           // Заголовок окна
        let message: String         // Основное сообщение
        let primaryButton: String   // Текст основной кнопки
        let secondaryButton: String? // Текст дополнительной кнопки (если есть)
        let messageType: MessageType // Тип сообщения для стилизации
    }
    
    /// Тип сообщения для определения стиля отображения
    enum MessageType {
        case silent         // Молчаливое продолжение (не показывается)
        case gentle         // Мягкое сообщение (зеленый/синий)
        case choice         // Сообщение с выбором (желтый/оранжевый)
        case positive       // Позитивное сообщение (зеленый)
    }
    
    // MARK: - Public Methods
    
    /// Генерирует данные сообщения на основе типа возвращения и времени отсутствия
    /// - Parameters:
    ///   - returnMessage: Тип сообщения при возвращении
    ///   - awayTimeMinutes: Время отсутствия в минутах
    /// - Returns: Данные сообщения для отображения или nil для молчаливого продолжения
    static func generateMessage(for returnMessage: ActivityStateManager.ReturnMessage, awayTimeMinutes: Int) -> ReturnMessageData? {
        
        switch returnMessage {
        case .resumeSilently:
            // 0-2 минуты: продолжить молча, никаких сообщений
            return nil
            
        case .partialRest:
            // 2-10 минут: частичный отдых
            return ReturnMessageData(
                title: "Частичный отдых",
                message: "Вы отдыхали \(awayTimeMinutes) \(minuteWord(awayTimeMinutes)). Хотите продолжить отдых или вернуться к работе?",
                primaryButton: "Вернуться к работе",
                secondaryButton: "Продолжить отдых",
                messageType: .choice
            )
            
        case .chooseRestOrWork:
            // 10-17 минут: выбор между отдыхом и работой
            return ReturnMessageData(
                title: "Хорошее время для отдыха",
                message: "Вы отдыхали \(awayTimeMinutes) \(minuteWord(awayTimeMinutes)). Это отличное время для восстановления! Что выберете?",
                primaryButton: "Начать работу",
                secondaryButton: "Продолжить отдых",
                messageType: .choice
            )
            
        case .fullRest:
            // 17+ минут: полноценный отдых
            return ReturnMessageData(
                title: "Полноценный отдых",
                message: "Отлично! Вы отдыхали \(awayTimeMinutes) \(minuteWord(awayTimeMinutes)). Теперь вы готовы к продуктивной работе!",
                primaryButton: "Начать работу",
                secondaryButton: nil,
                messageType: .positive
            )
        }
    }
    
    /// Генерирует краткое сообщение для статус-бара
    /// - Parameters:
    ///   - returnMessage: Тип сообщения при возвращении
    ///   - awayTimeMinutes: Время отсутствия в минутах
    /// - Returns: Краткий текст для статус-бара
    static func generateStatusBarMessage(for returnMessage: ActivityStateManager.ReturnMessage, awayTimeMinutes: Int) -> String {
        
        switch returnMessage {
        case .resumeSilently:
            return "🟢 Работа продолжена"
            
        case .partialRest:
            return "🟡 Частичный отдых (\(awayTimeMinutes) мин)"
            
        case .chooseRestOrWork:
            return "🟠 Время выбора (\(awayTimeMinutes) мин)"
            
        case .fullRest:
            return "🟢 Полный отдых (\(awayTimeMinutes) мин)"
        }
    }
    
    /// Генерирует сообщение для логов
    /// - Parameters:
    ///   - returnMessage: Тип сообщения при возвращении
    ///   - awayTimeMinutes: Время отсутствия в минутах
    /// - Returns: Подробное сообщение для логирования
    static func generateLogMessage(for returnMessage: ActivityStateManager.ReturnMessage, awayTimeMinutes: Int) -> String {
        
        switch returnMessage {
        case .resumeSilently:
            return "Пользователь вернулся после \(awayTimeMinutes) мин. Продолжение работы без уведомлений."
            
        case .partialRest:
            return "Пользователь вернулся после \(awayTimeMinutes) мин. Показано сообщение о частичном отдыхе с выбором."
            
        case .chooseRestOrWork:
            return "Пользователь вернулся после \(awayTimeMinutes) мин. Показано сообщение с выбором между отдыхом и работой."
            
        case .fullRest:
            return "Пользователь вернулся после \(awayTimeMinutes) мин. Показано позитивное сообщение о полноценном отдыхе."
        }
    }
    
    // MARK: - Helper Methods
    
    /// Возвращает правильную форму слова "минута" в зависимости от числа
    /// - Parameter minutes: Количество минут
    /// - Returns: "минуту", "минуты" или "минут"
    private static func minuteWord(_ minutes: Int) -> String {
        let lastDigit = minutes % 10
        let lastTwoDigits = minutes % 100
        
        // Исключения для 11-14
        if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
            return "минут"
        }
        
        switch lastDigit {
        case 1:
            return "минуту"
        case 2, 3, 4:
            return "минуты"
        default:
            return "минут"
        }
    }
}

// MARK: - Extensions

extension ReturnMessageGenerator.MessageType: CustomStringConvertible {
    var description: String {
        switch self {
        case .silent: return "Молчаливое"
        case .gentle: return "Мягкое"
        case .choice: return "С выбором"
        case .positive: return "Позитивное"
        }
    }
}

extension ReturnMessageGenerator.ReturnMessageData: CustomStringConvertible {
    var description: String {
        let secondaryText = secondaryButton != nil ? ", вторая кнопка: '\(secondaryButton!)'" : ""
        return "Сообщение '\(title)': '\(message)', кнопка: '\(primaryButton)'\(secondaryText), тип: \(messageType)"
    }
}
