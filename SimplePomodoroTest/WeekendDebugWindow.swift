import Cocoa
import Foundation

/// ОКНО ОТЛАДКИ СИСТЕМЫ ВЫХОДНЫХ
/// Позволяет эмулировать различные состояния системы выходных и тестировать все сценарии
class WeekendDebugWindow: NSWindow {
    
    // UI элементы для эмуляции уровня пользователя
    private var levelPopupButton: NSPopUpButton!
    private var pointsTextField: NSTextField!
    private var setLevelButton: NSButton!
    
    // UI элементы для эмуляции стратегии выходных
    private var strategyPopupButton: NSPopUpButton!
    private var setStrategyButton: NSButton!
    
    // UI элементы для эмуляции истории работы
    private var daysWithoutRestTextField: NSTextField!
    private var workingDaysTextField: NSTextField!
    private var workedTodayCheckbox: NSButton!
    private var lastWeekendDatePicker: NSDatePicker!
    private var setHistoryButton: NSButton!
    
    // UI элементы для эмуляции рабочих дней
    private var workDaysCheckboxes: [NSButton] = []
    private var setWorkDaysButton: NSButton!
    
    // UI элементы для тестирования
    private var resetSystemButton: NSButton!
    private var returnToUserDataButton: NSButton!
    private var setLevelOnlyButton: NSButton!
    
    // Информационные лейблы
    private var currentStatusLabel: NSTextField!
    private var nextWeekendLabel: NSTextField!
    private var menuPreviewLabel: NSTextField!
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 100, y: 100, width: 700, height: 800),
                  styleMask: [.titled, .closable, .resizable],
                  backing: .buffered,
                  defer: false)
        
        setupWindow()
        setupUI()
        updateStatus()
    }
    
    private func setupWindow() {
        title = "🏖️ Отладка системы выходных"
        center()
        isReleasedWhenClosed = false
        
        print("🔧 Открыто окно отладки системы выходных")
    }
    
    private func setupUI() {
        let contentView = NSView(frame: self.contentView!.bounds)
        contentView.autoresizingMask = [.width, .height]
        self.contentView = contentView
        
        var yPosition: CGFloat = 750
        let leftMargin: CGFloat = 20
        let _ = 20 // rightMargin не используется
        let spacing: CGFloat = 30
        
        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🏖️ Отладка системы выходных")
        titleLabel.font = NSFont.boldSystemFont(ofSize: 16)
        titleLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 400, height: 25)
        contentView.addSubview(titleLabel)
        yPosition -= spacing
        
        // Секция 1: Эмуляция уровня пользователя
        yPosition = addSectionHeader("1. Эмуляция уровня пользователя", at: yPosition, in: contentView)
        
        // Уровень пользователя
        let levelLabel = NSTextField(labelWithString: "Уровень:")
        levelLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 100, height: 20)
        contentView.addSubview(levelLabel)
        
        levelPopupButton = NSPopUpButton(frame: NSRect(x: leftMargin + 110, y: yPosition, width: 150, height: 25))
        levelPopupButton.addItems(withTitles: ["Новичок", "Начинающий", "Обычный", "Продвинутый"])
        contentView.addSubview(levelPopupButton)
        
        // Очки
        let pointsLabel = NSTextField(labelWithString: "Очки:")
        pointsLabel.frame = NSRect(x: leftMargin + 280, y: yPosition, width: 50, height: 20)
        contentView.addSubview(pointsLabel)
        
        pointsTextField = NSTextField(frame: NSRect(x: leftMargin + 340, y: yPosition, width: 80, height: 25))
        pointsTextField.stringValue = "0"
        contentView.addSubview(pointsTextField)
        
        setLevelButton = NSButton(title: "Установить уровень", target: self, action: #selector(setUserLevel))
        setLevelButton.frame = NSRect(x: leftMargin + 440, y: yPosition, width: 150, height: 25)
        contentView.addSubview(setLevelButton)
        yPosition -= spacing
        
        // Секция 2: Эмуляция стратегии выходных
        yPosition = addSectionHeader("2. Эмуляция стратегии выходных", at: yPosition, in: contentView)

        let strategyLabel = NSTextField(labelWithString: "Стратегия:")
        strategyLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 100, height: 20)
        contentView.addSubview(strategyLabel)

        strategyPopupButton = NSPopUpButton(frame: NSRect(x: leftMargin + 110, y: yPosition, width: 150, height: 25))
        strategyPopupButton.addItems(withTitles: ["3/1 (рекомендуемая)", "5/2 (классическая)", "Своя"])
        contentView.addSubview(strategyPopupButton)

        setStrategyButton = NSButton(title: "Установить стратегию", target: self, action: #selector(setWeekendStrategy))
        setStrategyButton.frame = NSRect(x: leftMargin + 280, y: yPosition, width: 150, height: 25)
        contentView.addSubview(setStrategyButton)
        yPosition -= 35

        // Настройка рабочих дней (для стратегии "Своя")
        let daysOfWeek = ["Пн", "Вт", "Ср", "Чт", "Пт", "Сб", "Вс"]
        for (index, day) in daysOfWeek.enumerated() {
            let checkbox = NSButton(checkboxWithTitle: day, target: self, action: nil)
            checkbox.frame = NSRect(x: leftMargin + CGFloat(index * 80), y: yPosition, width: 70, height: 20)
            checkbox.state = index < 5 ? .on : .off // По умолчанию Пн-Пт
            workDaysCheckboxes.append(checkbox)
            contentView.addSubview(checkbox)
        }
        yPosition -= 25

        setWorkDaysButton = NSButton(title: "Установить рабочие дни", target: self, action: #selector(setCustomWorkDays))
        setWorkDaysButton.frame = NSRect(x: leftMargin, y: yPosition, width: 150, height: 25)
        contentView.addSubview(setWorkDaysButton)
        yPosition -= spacing
        
        // Секция 3: Эмуляция истории работы
        yPosition = addSectionHeader("3. Эмуляция истории работы", at: yPosition, in: contentView)
        
        let daysLabel = NSTextField(labelWithString: "Дней без отдыха:")
        daysLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 120, height: 20)
        contentView.addSubview(daysLabel)
        
        daysWithoutRestTextField = NSTextField(frame: NSRect(x: leftMargin + 130, y: yPosition, width: 60, height: 25))
        daysWithoutRestTextField.stringValue = "0"
        contentView.addSubview(daysWithoutRestTextField)
        yPosition -= 35

        let workingDaysLabel = NSTextField(labelWithString: "Дней работы после выходного:")
        workingDaysLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 180, height: 20)
        contentView.addSubview(workingDaysLabel)

        workingDaysTextField = NSTextField(frame: NSRect(x: leftMargin + 190, y: yPosition, width: 60, height: 25))
        workingDaysTextField.stringValue = "0"
        contentView.addSubview(workingDaysTextField)
        yPosition -= 35

        workedTodayCheckbox = NSButton(frame: NSRect(x: leftMargin, y: yPosition, width: 200, height: 20))
        workedTodayCheckbox.setButtonType(.switch)
        workedTodayCheckbox.title = "Работал сегодня"
        workedTodayCheckbox.state = .off
        contentView.addSubview(workedTodayCheckbox)
        yPosition -= 35

        let lastWeekendLabel = NSTextField(labelWithString: "Последний выходной:")
        lastWeekendLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 130, height: 20)
        contentView.addSubview(lastWeekendLabel)
        
        lastWeekendDatePicker = NSDatePicker(frame: NSRect(x: leftMargin + 140, y: yPosition, width: 120, height: 25))
        lastWeekendDatePicker.datePickerStyle = .textField
        lastWeekendDatePicker.dateValue = Date()
        contentView.addSubview(lastWeekendDatePicker)
        
        setHistoryButton = NSButton(title: "Установить историю", target: self, action: #selector(setWorkHistory))
        setHistoryButton.frame = NSRect(x: leftMargin + 270, y: yPosition, width: 150, height: 25)
        contentView.addSubview(setHistoryButton)
        yPosition -= spacing

        // Секция 4: Тестирование
        yPosition = addSectionHeader("4. Тестирование", at: yPosition, in: contentView)

        // Первая строка кнопок
        let testMorningButton = NSButton(title: "Утреннее окно", target: self, action: #selector(testMorningWindow))
        testMorningButton.frame = NSRect(x: leftMargin, y: yPosition, width: 120, height: 25)
        contentView.addSubview(testMorningButton)

        let testEveningButton = NSButton(title: "Вечернее окно", target: self, action: #selector(testEveningWindow))
        testEveningButton.frame = NSRect(x: leftMargin + 140, y: yPosition, width: 120, height: 25)
        contentView.addSubview(testEveningButton)

        resetSystemButton = NSButton(title: "Сбросить систему", target: self, action: #selector(resetSystem))
        resetSystemButton.frame = NSRect(x: leftMargin + 280, y: yPosition, width: 130, height: 25)
        contentView.addSubview(resetSystemButton)

        returnToUserDataButton = NSButton(title: "К пользовательским данным", target: self, action: #selector(returnToUserData))
        returnToUserDataButton.frame = NSRect(x: leftMargin + 430, y: yPosition, width: 180, height: 25)
        contentView.addSubview(returnToUserDataButton)
        yPosition -= spacing

        // Секция 5: Текущий статус
        yPosition = addSectionHeader("5. Текущий статус", at: yPosition, in: contentView)

        currentStatusLabel = NSTextField(labelWithString: "Статус: Загрузка...")
        currentStatusLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 650, height: 20)
        currentStatusLabel.isEditable = false
        currentStatusLabel.isBordered = false
        currentStatusLabel.backgroundColor = NSColor.clear
        contentView.addSubview(currentStatusLabel)
        yPosition -= 30  // Увеличили отступ

        nextWeekendLabel = NSTextField(labelWithString: "Следующий выходной: Загрузка...")
        nextWeekendLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 650, height: 20)
        nextWeekendLabel.isEditable = false
        nextWeekendLabel.isBordered = false
        nextWeekendLabel.backgroundColor = NSColor.clear
        contentView.addSubview(nextWeekendLabel)
        yPosition -= 30  // Увеличили отступ

        menuPreviewLabel = NSTextField(labelWithString: "Предпросмотр меню: Загрузка...")
        menuPreviewLabel.frame = NSRect(x: leftMargin, y: yPosition, width: 650, height: 40)
        menuPreviewLabel.isEditable = false
        menuPreviewLabel.isBordered = false
        menuPreviewLabel.backgroundColor = NSColor.clear
        menuPreviewLabel.maximumNumberOfLines = 2
        contentView.addSubview(menuPreviewLabel)
    }
    
    private func addSectionHeader(_ title: String, at yPosition: CGFloat, in contentView: NSView) -> CGFloat {
        let headerLabel = NSTextField(labelWithString: title)
        headerLabel.font = NSFont.boldSystemFont(ofSize: 14)
        headerLabel.frame = NSRect(x: 20, y: yPosition, width: 650, height: 20)
        contentView.addSubview(headerLabel)
        return yPosition - 30
    }

    // MARK: - Actions

    @objc private func setUserLevel() {
        let selectedLevel = levelPopupButton.indexOfSelectedItem
        let points = Int(pointsTextField.stringValue) ?? 0

        // Устанавливаем уровень через DailyWorkloadManager (правильный способ)
        let userLevels: [String] = ["beginner", "starter", "regular", "advanced"]
        let levelNames = ["Новичок", "Начинающий", "Обычный", "Продвинутый"]

        // Простой способ - через UserDefaults, который DailyWorkloadManager может читать
        UserDefaults.standard.set(levelNames[selectedLevel], forKey: "debugUserLevel")
        UserDefaults.standard.set(userLevels[selectedLevel], forKey: "debugUserLevelRaw")
        UserDefaults.standard.set(points, forKey: "userPoints")
        UserDefaults.standard.set(true, forKey: "debugModeEnabled")

        print("🔧 Установлен уровень: \(levelNames[selectedLevel]) (\(points) очков)")

        // Принудительно обновляем меню статус-бара
        DispatchQueue.main.async {
            if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                appDelegate.updateStatusBarMenu()
                print("🔄 Меню статус-бара обновлено после изменения уровня пользователя")
            }
        }

        updateStatus()
    }

    @objc private func setWeekendStrategy() {
        let selectedStrategy = strategyPopupButton.indexOfSelectedItem
        let strategyNames = ["3/1 (рекомендуемая)", "5/2 (классическая)", "Своя"]

        UserDefaults.standard.set(strategyNames[selectedStrategy], forKey: "weekendStrategy")

        print("🔧 Установлена стратегия: \(strategyNames[selectedStrategy])")

        // Принудительно обновляем меню статус-бара
        DispatchQueue.main.async {
            if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                appDelegate.updateStatusBarMenu()
            }
        }

        updateStatus()
    }

    @objc private func setWorkHistory() {
        let daysWithoutRest = Int(daysWithoutRestTextField.stringValue) ?? 0
        let workingDays = Int(workingDaysTextField.stringValue) ?? 0
        let workedToday = workedTodayCheckbox.state == .on
        let lastWeekendDate = lastWeekendDatePicker.dateValue

        // Устанавливаем историю через UserDefaults
        UserDefaults.standard.set(daysWithoutRest, forKey: "daysWithoutRest")
        UserDefaults.standard.set(lastWeekendDate, forKey: "lastWeekendDate")

        // Эмулируем дни работы через создание фиктивной истории
        if workingDays > 0 {
            let calendar = Calendar.current
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"

            for i in 1...workingDays {
                if let workDate = calendar.date(byAdding: .day, value: -i, to: Date()) {
                    // Создаем фиктивную запись о работе
                    let dateStr = dateFormatter.string(from: workDate)
                    UserDefaults.standard.set(true, forKey: "worked_\(dateStr)")
                    print("🧪 Установлен эмулированный рабочий день: \(dateStr)")
                }
            }
        }

        // Эмулируем работу сегодня если галочка включена
        if workedToday {
            let calendar = Calendar.current
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let todayStr = dateFormatter.string(from: Date())
            UserDefaults.standard.set(true, forKey: "worked_today_\(todayStr)")
            print("🧪 Установлена работа сегодня: \(todayStr)")
        } else {
            // Очищаем работу сегодня
            let calendar = Calendar.current
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let todayStr = dateFormatter.string(from: Date())
            UserDefaults.standard.removeObject(forKey: "worked_today_\(todayStr)")
            print("🧪 Очищена работа сегодня: \(todayStr)")
        }

        print("🔧 Установлена история: \(daysWithoutRest) дней без отдыха, \(workingDays) дней работы, работал сегодня: \(workedToday), последний выходной: \(lastWeekendDate)")

        // Принудительно обновляем меню статус-бара
        DispatchQueue.main.async {
            if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                appDelegate.updateStatusBarMenu()
            }
        }

        updateStatus()
    }

    @objc private func testMorningWindow() {
        print("🧪 Тестируем утреннее окно...")

        // Проверяем доступна ли система выходных
        let weekendManager = WeekendManager.shared
        guard weekendManager.isWeekendSystemAvailable() else {
            print("❌ Система выходных недоступна для текущего уровня пользователя")
            return
        }

        // Проверяем сегодня выходной или нет (с учетом всей логики)
        let nextWeekendDate = weekendManager.calculateNextWeekendDay()
        let today = Date()
        let calendar = Calendar.current
        let isWeekendToday = nextWeekendDate != nil && calendar.isDate(nextWeekendDate!, inSameDayAs: today)

        print("🔍 Следующий выходной: \(nextWeekendDate?.description ?? "nil")")
        print("🔍 Сегодня: \(today)")
        print("🔍 Сегодня выходной: \(isWeekendToday)")

        if isWeekendToday {
            // Показываем утреннее окно отдыха
            print("🏖️ Показываем утреннее окно отдыха")
            showWeekendChoiceWindow()
        } else {
            // Показываем систему раннего вовлечения
            print("💼 Показываем систему раннего вовлечения")
            showEarlyEngagementWindow()
        }
    }

    private func showWeekendChoiceWindow() {
        // Создаем и показываем окно выбора выходного дня
        let weekendWindow = WeekendChoiceWindow()

        // ИСПРАВЛЕНИЕ: Получаем позицию status item для правильного позиционирования
        var statusFrame: NSRect? = nil
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate,
           let statusButton = appDelegate.statusItem.button {
            statusFrame = statusButton.window?.convertToScreen(statusButton.frame) ?? NSRect.zero
            print("🎯 ОТЛАДКА WeekendDebugWindow: statusFrame получен = \(statusFrame!)")
        } else {
            print("❌ WeekendDebugWindow: НЕ УДАЛОСЬ ПОЛУЧИТЬ STATUS BUTTON!")
        }

        weekendWindow.showWeekendChoice(
            onRest: {
                print("🏖️ Пользователь выбрал отдых")
            },
            onWork: {
                print("💼 Пользователь выбрал работу")
            },
            statusItemFrame: statusFrame
        )
    }

    private func showEarlyEngagementWindow() {
        // Создаем и показываем окно раннего вовлечения
        let earlyEngagementWindow = EarlyEngagementWindow()

        // Создаем тестовое сообщение
        let testMessage = EngagementMessage(
            title: "Тест утреннего окна",
            subtitle: "Это тестовое сообщение раннего вовлечения",
            proposedDuration: 25 * 60, // 25 минут
            buttonText: "Начать работу",
            level: 1, // Мягкий уровень
            timeOfDay: 0 // Утро
        )

        earlyEngagementWindow.showEngagementMessage(
            testMessage,
            onAccept: { projectId in
                print("💼 Пользователь принял предложение работы")
            },
            onDecline: {
                print("❌ Пользователь отклонил предложение")
            },
            onSnooze: {
                print("⏰ Пользователь отложил решение")
            },
            onFullSession: { projectId in
                print("🎯 Пользователь выбрал полную сессию")
            }
        )
    }

    @objc private func setCustomWorkDays() {
        var customWorkDays: [Int] = []

        for (index, checkbox) in workDaysCheckboxes.enumerated() {
            if checkbox.state == .on {
                // Преобразуем индекс в weekday (1 = воскресенье, 2 = понедельник, ...)
                let weekday = index == 6 ? 1 : index + 2
                customWorkDays.append(weekday)
            }
        }

        // Сохраняем в UserDefaults для стратегии "Своя"
        UserDefaults.standard.set(customWorkDays, forKey: "customWorkDays")

        print("🔧 Установлены рабочие дни: \(customWorkDays)")
        updateStatus()
    }

    @objc private func returnToUserData() {
        // Отключаем отладочный режим и возвращаемся к реальным пользовательским данным
        UserDefaults.standard.removeObject(forKey: "debugModeEnabled")
        UserDefaults.standard.removeObject(forKey: "debugUserLevel")
        UserDefaults.standard.removeObject(forKey: "debugUserLevelRaw")

        // Удаляем отладочные данные о выходных (но оставляем реальные пользовательские)
        UserDefaults.standard.removeObject(forKey: "weekendStrategy")
        UserDefaults.standard.removeObject(forKey: "daysWithoutRest")
        UserDefaults.standard.removeObject(forKey: "lastWeekendDate")
        UserDefaults.standard.removeObject(forKey: "customWorkDays")

        // Очищаем эмулированные дни работы
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        for i in 1...14 {
            if let workDate = calendar.date(byAdding: .day, value: -i, to: Date()) {
                let dateStr = dateFormatter.string(from: workDate)
                UserDefaults.standard.removeObject(forKey: "worked_\(dateStr)")
            }
        }

        // Сбрасываем UI к начальным значениям
        levelPopupButton.selectItem(at: 0)
        pointsTextField.stringValue = "0"
        strategyPopupButton.selectItem(at: 0)
        daysWithoutRestTextField.stringValue = "0"
        workingDaysTextField.stringValue = "0"
        workedTodayCheckbox.state = .off
        lastWeekendDatePicker.dateValue = Date()

        // Сбрасываем чекбоксы рабочих дней
        for checkbox in workDaysCheckboxes {
            checkbox.state = .off
        }

        // КРИТИЧЕСКИ ВАЖНО: Принудительно обновляем уровень пользователя в DailyWorkloadManager
        // Иначе система продолжает использовать кэшированный отладочный уровень
        DailyWorkloadManager.shared.forceUpdateUserLevel()

        // Обновляем меню статус-бара
        DispatchQueue.main.async {
            if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                appDelegate.updateStatusBarMenu()
            }
        }

        print("🔄 Возвращены пользовательские данные, отладочный режим отключен")
        updateStatus()
    }

    @objc private func testEveningWindow() {
        print("🧪 Тестируем вечернее окно завершения дня...")

        // Устанавливаем достаточно очков для показа DailyGoalCompletedWindow
        let currentLevel = DailyWorkloadManager.shared.getCurrentUserLevel()
        let targetPoints: Int

        switch currentLevel {
        case .beginner:
            targetPoints = 30
        case .starter:
            targetPoints = 60
        case .regular:
            targetPoints = 120
        case .advanced:
            targetPoints = 200
        }

        // Устанавливаем очки чуть больше цели
        let testPoints = targetPoints + 5

        // Временно устанавливаем очки для теста
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let today = dateFormatter.string(from: Date())
        UserDefaults.standard.set(testPoints, forKey: "todayPoints_\(today)")

        print("🎯 Установлены тестовые очки: \(testPoints)/\(targetPoints) для уровня \(currentLevel.displayName)")

        // Показываем DailyGoalCompletedWindow
        DailyGoalCompletedWindow.show(
            currentPoints: testPoints,
            targetPoints: targetPoints,
            onContinue: {
                print("🔄 Тест: Пользователь выбрал продолжить работу")
            },
            onDone: { feedback in
                print("✅ Тест: Пользователь завершил день")
            }
        )

        print("🌅 Показано тестовое вечернее окно завершения дня (DailyGoalCompletedWindow)")
    }

    @objc private func resetSystem() {
        // Сбрасываем все настройки системы выходных
        UserDefaults.standard.removeObject(forKey: "userLevel")
        UserDefaults.standard.removeObject(forKey: "userPoints")
        UserDefaults.standard.removeObject(forKey: "customWorkDays")
        UserDefaults.standard.removeObject(forKey: "weekendStrategy")
        UserDefaults.standard.removeObject(forKey: "daysWithoutRest")
        UserDefaults.standard.removeObject(forKey: "lastWeekendDate")

        // Очищаем эмулированные дни работы
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        for i in 1...14 {
            if let workDate = calendar.date(byAdding: .day, value: -i, to: Date()) {
                let dateStr = dateFormatter.string(from: workDate)
                UserDefaults.standard.removeObject(forKey: "worked_\(dateStr)")
            }
        }

        // Сбрасываем отладочный режим
        UserDefaults.standard.removeObject(forKey: "debugModeEnabled")
        UserDefaults.standard.removeObject(forKey: "debugUserLevel")
        UserDefaults.standard.removeObject(forKey: "debugUserLevelRaw")

        // Сбрасываем UI
        levelPopupButton.selectItem(at: 0)
        pointsTextField.stringValue = "0"
        strategyPopupButton.selectItem(at: 0)
        daysWithoutRestTextField.stringValue = "0"
        workingDaysTextField.stringValue = "0"
        workedTodayCheckbox.state = .off
        lastWeekendDatePicker.dateValue = Date()

        for (index, checkbox) in workDaysCheckboxes.enumerated() {
            checkbox.state = index < 5 ? .on : .off
        }

        print("🔧 Система выходных сброшена")
        updateStatus()
    }

    private func updateStatus() {
        let isDebugMode = UserDefaults.standard.bool(forKey: "debugModeEnabled")
        let currentLevel = UserDefaults.standard.string(forKey: isDebugMode ? "debugUserLevel" : "userLevel") ?? "Начинающий"
        let currentPoints = UserDefaults.standard.integer(forKey: "userPoints")
        let currentStrategy = UserDefaults.standard.string(forKey: "weekendStrategy") ?? "3/1 (рекомендуемая)"
        let daysWithoutRest = UserDefaults.standard.integer(forKey: "daysWithoutRest")

        // Получаем информацию о стриках и системе выходных
        let currentStreak = EarlyEngagementSystem.shared.debugGetCurrentStreak()
        let workedToday = DailyWorkloadManager.shared.didWorkTodayOnPriorityProject()
        let workingDaysAfterWeekend = WeekendManager.shared.calculateWorkingDaysAfterLastWeekend()

        // Простая проверка доступности системы (для новичков и начинающих недоступна)
        let isSystemAvailable = currentLevel != "Начинающий" && currentLevel != "Новичок"

        // Обновляем статус
        let debugText = isDebugMode ? " [ОТЛАДКА]" : ""
        let streakText = workedToday ? "\(currentStreak) дней" : "\(currentStreak) дней (сегодня не работал)"
        let workingDaysText = "Дней работы после выходного: \(workingDaysAfterWeekend)"
        currentStatusLabel.stringValue = "Статус: Система \(isSystemAvailable ? "ДОСТУПНА" : "НЕДОСТУПНА")\(debugText) | Уровень: \(currentLevel)\nСтрик: \(streakText) | Дни без отдыха: \(daysWithoutRest) | \(workingDaysText)"

        // Обновляем информацию о следующем выходном
        if isSystemAvailable {
            // Используем реальный WeekendManager для расчета
            if let nextWeekendDate = WeekendManager.shared.calculateNextWeekendDay() {
                let formatter = DateFormatter()
                formatter.dateFormat = "EEEE, d MMMM"
                formatter.locale = Locale(identifier: "ru_RU")
                nextWeekendLabel.stringValue = "Следующий выходной: \(formatter.string(from: nextWeekendDate))"
            } else {
                nextWeekendLabel.stringValue = "Следующий выходной: Не определен"
            }
        } else {
            nextWeekendLabel.stringValue = "Следующий выходной: Система недоступна"
        }

        // Обновляем предпросмотр меню
        if isSystemAvailable {
            let realDaysWithoutRest = WeekendManager.shared.getDaysWithoutRest()
            if realDaysWithoutRest == 0 {
                if let nextDate = WeekendManager.shared.calculateNextWeekendDay() {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "EEE"
                    formatter.locale = Locale(identifier: "ru_RU")
                    let dayName = formatter.string(from: nextDate)
                    menuPreviewLabel.stringValue = "Предпросмотр меню: 🏖️ Отдых: соблюдается (next: \(dayName))"
                } else {
                    menuPreviewLabel.stringValue = "Предпросмотр меню: 🏖️ Отдых: соблюдается"
                }
            } else {
                if let nextDate = WeekendManager.shared.calculateNextWeekendDay() {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "EEE"
                    formatter.locale = Locale(identifier: "ru_RU")
                    let dayName = formatter.string(from: nextDate)
                    menuPreviewLabel.stringValue = "Предпросмотр меню: ⚠️ Дней без отдыха: \(realDaysWithoutRest) (next: \(dayName))"
                } else {
                    menuPreviewLabel.stringValue = "Предпросмотр меню: ⚠️ Дней без отдыха: \(realDaysWithoutRest)"
                }
            }
        } else {
            menuPreviewLabel.stringValue = "Предпросмотр меню: Пункт о выходных НЕ отображается (уровень недостаточен)"
        }
    }
}
