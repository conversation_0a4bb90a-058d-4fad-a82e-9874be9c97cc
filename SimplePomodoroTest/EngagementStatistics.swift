import Foundation

/// Система сбора и анализа статистики эффективности раннего вовлечения
/// Отслеживает показы сообщений, реакции пользователя, успешность запуска интервалов
class EngagementStatistics {
    
    // MARK: - Singleton
    static let shared = EngagementStatistics()
    private init() {
        Logger.shared.log(.info, "EngagementStats", "📊 EngagementStatistics инициализирован")
    }
    
    // MARK: - Properties
    private let userDefaults = UserDefaults.standard
    private let engagementEventsKey = "engagementStatistics_events"
    private let engagementSessionsKey = "engagementStatistics_sessions"
    
    // MARK: - Public Methods
    
    /// Записывает событие показа сообщения раннего вовлечения
    func recordMessageShown(vertical: Int, horizontal: Int, userBar: TimeInterval, daysWithoutWork: Int) {
        let event = EngagementEvent(
            type: .messageShown,
            timestamp: Date(),
            vertical: vertical,
            horizontal: horizontal,
            userBar: userBar,
            daysWithoutWork: daysWithoutWork
        )
        
        saveEvent(event)
        Logger.shared.log(.info, "EngagementStats", "📊 Записан показ сообщения: v\(vertical)h\(horizontal), планка \(Int(userBar/60))мин")
    }
    
    /// Записывает принятие пользователем предложения
    func recordUserAcceptance(vertical: Int, horizontal: Int, userBar: TimeInterval, projectId: UUID?) {
        let event = EngagementEvent(
            type: .userAccepted,
            timestamp: Date(),
            vertical: vertical,
            horizontal: horizontal,
            userBar: userBar,
            projectId: projectId
        )
        
        saveEvent(event)
        Logger.shared.log(.info, "EngagementStats", "✅ Записано принятие: v\(vertical)h\(horizontal), планка \(Int(userBar/60))мин")
    }
    
    /// Записывает отказ пользователя от предложения
    func recordUserRefusal(vertical: Int, horizontal: Int, userBar: TimeInterval, reason: String? = nil) {
        let event = EngagementEvent(
            type: .userRefused,
            timestamp: Date(),
            vertical: vertical,
            horizontal: horizontal,
            userBar: userBar,
            refusalReason: reason
        )
        
        saveEvent(event)
        Logger.shared.log(.info, "EngagementStats", "❌ Записан отказ: v\(vertical)h\(horizontal), планка \(Int(userBar/60))мин")
    }
    
    /// Записывает отложение пользователем предложения
    func recordUserSnooze(vertical: Int, horizontal: Int, userBar: TimeInterval) {
        let event = EngagementEvent(
            type: .userSnoozed,
            timestamp: Date(),
            vertical: vertical,
            horizontal: horizontal,
            userBar: userBar
        )
        
        saveEvent(event)
        Logger.shared.log(.info, "EngagementStats", "⏰ Записано отложение: v\(vertical)h\(horizontal), планка \(Int(userBar/60))мин")
    }
    
    /// Записывает успешное завершение интервала после принятия предложения
    func recordIntervalCompleted(duration: TimeInterval, projectId: UUID?, wasFromEngagement: Bool = true) {
        let event = EngagementEvent(
            type: .intervalCompleted,
            timestamp: Date(),
            projectId: projectId,
            intervalDuration: duration,
            wasFromEngagement: wasFromEngagement
        )
        
        saveEvent(event)
        Logger.shared.log(.info, "EngagementStats", "🎯 Записано завершение интервала: \(Int(duration/60))мин, от вовлечения: \(wasFromEngagement)")
    }
    
    /// Записывает неудачное завершение интервала (прерван пользователем)
    func recordIntervalAborted(duration: TimeInterval, projectId: UUID?, wasFromEngagement: Bool = true) {
        let event = EngagementEvent(
            type: .intervalAborted,
            timestamp: Date(),
            projectId: projectId,
            intervalDuration: duration,
            wasFromEngagement: wasFromEngagement
        )
        
        saveEvent(event)
        Logger.shared.log(.info, "EngagementStats", "💔 Записано прерывание интервала: \(Int(duration/60))мин, от вовлечения: \(wasFromEngagement)")
    }
    
    /// Начинает новую сессию вовлечения (день)
    func startEngagementSession(date: Date = Date()) {
        let session = EngagementSession(
            date: Calendar.current.startOfDay(for: date),
            messagesShown: 0,
            acceptanceRate: 0.0,
            averageResponseTime: 0.0,
            successfulIntervals: 0,
            totalIntervalTime: 0.0
        )
        
        saveSession(session)
        Logger.shared.log(.info, "EngagementStats", "🌅 Начата новая сессия вовлечения: \(date)")
    }
    
    // MARK: - Analytics Methods
    
    /// Возвращает статистику эффективности за указанный период
    func getEffectivenessStats(from startDate: Date, to endDate: Date) -> EngagementEffectiveness {
        let events = getEventsForPeriod(from: startDate, to: endDate)
        
        let messageShownEvents = events.filter { $0.type == .messageShown }
        let acceptedEvents = events.filter { $0.type == .userAccepted }
        let refusedEvents = events.filter { $0.type == .userRefused }
        let snoozedEvents = events.filter { $0.type == .userSnoozed }
        let completedEvents = events.filter { $0.type == .intervalCompleted && $0.wasFromEngagement == true }
        
        let totalMessages = messageShownEvents.count
        let totalAccepted = acceptedEvents.count
        let totalRefused = refusedEvents.count
        let totalSnoozed = snoozedEvents.count
        let totalCompleted = completedEvents.count
        
        let acceptanceRate = totalMessages > 0 ? Double(totalAccepted) / Double(totalMessages) : 0.0
        let completionRate = totalAccepted > 0 ? Double(totalCompleted) / Double(totalAccepted) : 0.0
        let totalIntervalTime = completedEvents.reduce(0.0) { $0 + ($1.intervalDuration ?? 0) }
        
        return EngagementEffectiveness(
            totalMessagesShown: totalMessages,
            totalAccepted: totalAccepted,
            totalRefused: totalRefused,
            totalSnoozed: totalSnoozed,
            acceptanceRate: acceptanceRate,
            completionRate: completionRate,
            totalSuccessfulIntervals: totalCompleted,
            totalIntervalTime: totalIntervalTime,
            averageUserBar: calculateAverageUserBar(from: events),
            mostEffectiveTime: findMostEffectiveTime(from: events),
            mostEffectiveVertical: findMostEffectiveVertical(from: events)
        )
    }
    
    /// Возвращает статистику по матрице сообщений (какие сообщения наиболее эффективны)
    func getMatrixEffectiveness(from startDate: Date, to endDate: Date) -> [String: EngagementMatrixStats] {
        let events = getEventsForPeriod(from: startDate, to: endDate)
        var matrixStats: [String: EngagementMatrixStats] = [:]
        
        // Группируем события по координатам матрицы
        let groupedEvents = Dictionary(grouping: events) { event in
            "v\(event.vertical ?? 0)h\(event.horizontal ?? 0)"
        }
        
        for (key, eventGroup) in groupedEvents {
            let shown = eventGroup.filter { $0.type == .messageShown }.count
            let accepted = eventGroup.filter { $0.type == .userAccepted }.count
            let refused = eventGroup.filter { $0.type == .userRefused }.count
            let snoozed = eventGroup.filter { $0.type == .userSnoozed }.count
            
            let acceptanceRate = shown > 0 ? Double(accepted) / Double(shown) : 0.0
            
            matrixStats[key] = EngagementMatrixStats(
                timesShown: shown,
                timesAccepted: accepted,
                timesRefused: refused,
                timesSnoozed: snoozed,
                acceptanceRate: acceptanceRate
            )
        }
        
        return matrixStats
    }
    
    /// Возвращает рекомендации по оптимизации системы
    func getOptimizationRecommendations(from startDate: Date, to endDate: Date) -> [String] {
        let effectiveness = getEffectivenessStats(from: startDate, to: endDate)
        _ = getMatrixEffectiveness(from: startDate, to: endDate)
        
        var recommendations: [String] = []
        
        // Анализ общей эффективности
        if effectiveness.acceptanceRate < 0.3 {
            recommendations.append("Низкий уровень принятия (\(Int(effectiveness.acceptanceRate * 100))%). Рассмотрите уменьшение планки или изменение времени показа.")
        }
        
        if effectiveness.completionRate < 0.7 {
            recommendations.append("Низкий уровень завершения интервалов (\(Int(effectiveness.completionRate * 100))%). Планка может быть слишком высокой.")
        }
        
        // Анализ времени показа
        if let bestTime = effectiveness.mostEffectiveTime {
            recommendations.append("Наиболее эффективное время: \(bestTime):00. Сосредоточьтесь на этом времени.")
        }
        
        // Анализ вертикального уровня
        if let bestVertical = effectiveness.mostEffectiveVertical {
            recommendations.append("Наиболее эффективный уровень эскалации: \(bestVertical) дней без работы.")
        }
        
        return recommendations
    }
    
    // MARK: - Private Methods
    
    private func saveEvent(_ event: EngagementEvent) {
        var events = getAllEvents()
        events.append(event)
        
        // Ограничиваем количество событий (последние 1000)
        if events.count > 1000 {
            events = Array(events.suffix(1000))
        }
        
        do {
            let data = try JSONEncoder().encode(events)
            userDefaults.set(data, forKey: engagementEventsKey)
        } catch {
            Logger.shared.log(.error, "EngagementStats", "❌ Ошибка сохранения события: \(error)")
        }
    }
    
    private func saveSession(_ session: EngagementSession) {
        var sessions = getAllSessions()
        
        // Обновляем существующую сессию или добавляем новую
        if let index = sessions.firstIndex(where: { Calendar.current.isDate($0.date, inSameDayAs: session.date) }) {
            sessions[index] = session
        } else {
            sessions.append(session)
        }
        
        // Ограничиваем количество сессий (последние 90 дней)
        if sessions.count > 90 {
            sessions = Array(sessions.suffix(90))
        }
        
        do {
            let data = try JSONEncoder().encode(sessions)
            userDefaults.set(data, forKey: engagementSessionsKey)
        } catch {
            Logger.shared.log(.error, "EngagementStats", "❌ Ошибка сохранения сессии: \(error)")
        }
    }
    
    private func getAllEvents() -> [EngagementEvent] {
        guard let data = userDefaults.data(forKey: engagementEventsKey) else { return [] }
        
        do {
            return try JSONDecoder().decode([EngagementEvent].self, from: data)
        } catch {
            Logger.shared.log(.error, "EngagementStats", "❌ Ошибка загрузки событий: \(error)")
            return []
        }
    }
    
    private func getAllSessions() -> [EngagementSession] {
        guard let data = userDefaults.data(forKey: engagementSessionsKey) else { return [] }
        
        do {
            return try JSONDecoder().decode([EngagementSession].self, from: data)
        } catch {
            Logger.shared.log(.error, "EngagementStats", "❌ Ошибка загрузки сессий: \(error)")
            return []
        }
    }
    
    func getEventsForPeriod(from startDate: Date, to endDate: Date) -> [EngagementEvent] {
        return getAllEvents().filter { event in
            event.timestamp >= startDate && event.timestamp < endDate
        }
    }
    
    private func calculateAverageUserBar(from events: [EngagementEvent]) -> TimeInterval {
        let relevantEvents = events.filter { $0.userBar != nil }
        guard !relevantEvents.isEmpty else { return 0 }
        
        let totalUserBar = relevantEvents.reduce(0.0) { $0 + ($1.userBar ?? 0) }
        return totalUserBar / Double(relevantEvents.count)
    }
    
    private func findMostEffectiveTime(from events: [EngagementEvent]) -> Int? {
        let acceptedEvents = events.filter { $0.type == .userAccepted && $0.horizontal != nil }
        guard !acceptedEvents.isEmpty else { return nil }
        
        let timeGroups = Dictionary(grouping: acceptedEvents) { $0.horizontal! }
        let mostEffective = timeGroups.max { $0.value.count < $1.value.count }
        
        return mostEffective?.key
    }
    
    private func findMostEffectiveVertical(from events: [EngagementEvent]) -> Int? {
        let acceptedEvents = events.filter { $0.type == .userAccepted && $0.vertical != nil }
        guard !acceptedEvents.isEmpty else { return nil }
        
        let verticalGroups = Dictionary(grouping: acceptedEvents) { $0.vertical! }
        let mostEffective = verticalGroups.max { $0.value.count < $1.value.count }
        
        return mostEffective?.key
    }
}

// MARK: - Data Models

/// Тип события в системе раннего вовлечения
enum EngagementEventType: String, Codable {
    case messageShown = "message_shown"
    case userAccepted = "user_accepted"
    case userRefused = "user_refused"
    case userSnoozed = "user_snoozed"
    case intervalCompleted = "interval_completed"
    case intervalAborted = "interval_aborted"
}

/// Событие в системе раннего вовлечения
struct EngagementEvent: Codable {
    let type: EngagementEventType
    let timestamp: Date
    let vertical: Int?              // Вертикальная координата матрицы (дни без работы)
    let horizontal: Int?            // Горизонтальная координата матрицы (время дня)
    let userBar: TimeInterval?      // Текущая планка пользователя
    let daysWithoutWork: Int?       // Количество дней без работы
    let projectId: UUID?            // ID проекта (если применимо)
    let intervalDuration: TimeInterval? // Длительность интервала (если применимо)
    let wasFromEngagement: Bool?    // Был ли интервал запущен через систему вовлечения
    let refusalReason: String?      // Причина отказа (если применимо)
    
    init(type: EngagementEventType, timestamp: Date, vertical: Int? = nil, horizontal: Int? = nil, userBar: TimeInterval? = nil, daysWithoutWork: Int? = nil, projectId: UUID? = nil, intervalDuration: TimeInterval? = nil, wasFromEngagement: Bool? = nil, refusalReason: String? = nil) {
        self.type = type
        self.timestamp = timestamp
        self.vertical = vertical
        self.horizontal = horizontal
        self.userBar = userBar
        self.daysWithoutWork = daysWithoutWork
        self.projectId = projectId
        self.intervalDuration = intervalDuration
        self.wasFromEngagement = wasFromEngagement
        self.refusalReason = refusalReason
    }
}

/// Сессия вовлечения (дневная статистика)
struct EngagementSession: Codable {
    let date: Date                  // Дата сессии (начало дня)
    var messagesShown: Int          // Количество показанных сообщений
    var acceptanceRate: Double      // Процент принятых предложений
    var averageResponseTime: TimeInterval // Среднее время реакции пользователя
    var successfulIntervals: Int    // Количество успешно завершенных интервалов
    var totalIntervalTime: TimeInterval // Общее время работы в интервалах
}

/// Статистика эффективности системы раннего вовлечения
struct EngagementEffectiveness {
    let totalMessagesShown: Int
    let totalAccepted: Int
    let totalRefused: Int
    let totalSnoozed: Int
    let acceptanceRate: Double      // Процент принятых предложений
    let completionRate: Double      // Процент завершенных интервалов от принятых
    let totalSuccessfulIntervals: Int
    let totalIntervalTime: TimeInterval
    let averageUserBar: TimeInterval
    let mostEffectiveTime: Int?     // Наиболее эффективное время показа (час)
    let mostEffectiveVertical: Int? // Наиболее эффективный уровень эскалации
}

/// Статистика эффективности конкретной ячейки матрицы
struct EngagementMatrixStats {
    let timesShown: Int
    let timesAccepted: Int
    let timesRefused: Int
    let timesSnoozed: Int
    let acceptanceRate: Double
}
