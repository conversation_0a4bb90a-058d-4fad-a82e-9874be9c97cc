import Foundation
import CoreGraphics

/// Трекер активности с 20-отрезковой проверкой в минуту
/// Разделяет каждую минуту на 20 отрезков по 3 секунды [0-3] [3-6] ... [57-60]
/// Логика погрешности: 1 отрезок = случайность, 2+ отрезков = реальная активность
class MinuteActivityTracker {

    // MARK: - Configuration

    /// Количество отрезков в минуте
    private let segmentsPerMinute = 20

    /// Длительность одного отрезка в секундах
    private let segmentDurationSeconds: TimeInterval = 3.0

    /// Порог активности в секундах для определения активности в отрезке
    private let activityThreshold: TimeInterval = 15.0

    /// Минимальное количество активных отрезков для считания минуты активной (защита от погрешности)
    private let minActiveSegmentsForActiveMinute = 2

    // MARK: - State

    /// Текущий отрезок (0-19)
    private var currentSegment = 0

    /// Время начала текущего отрезка
    private var currentSegmentStartTime = Date()

    /// Активность в текущих отрезках [отрезок0, отрезок1, ..., отрезок19]
    private var currentMinuteSegments: [Bool] = Array(repeating: false, count: 20)

    /// Таймер для переключения отрезков
    private var segmentTimer: Timer?

    /// Активен ли трекер
    private var isActive = false

    /// История активности по минутам (true = активная минута, false = неактивная)
    private var minuteActivityLog: [Bool] = []

    /// Максимальный размер лога (храним больше чем анализируем для запаса)
    private let maxLogSize = 60

    // MARK: - Callbacks

    /// Вызывается когда минута завершена с результатом активности
    var onMinuteCompleted: ((Bool) -> Void)?

    // MARK: - Dependencies

    /// Используем существующий UnifiedActivityChecker для проверки активности
    private let activityChecker = UnifiedActivityChecker.shared

    // MARK: - Public Methods

    /// Запускает отслеживание активности по отрезкам
    func startTracking() {
        guard !isActive else { return }

        isActive = true
        resetCurrentMinute()
        startSegmentTimer()

        print("🎯 MinuteActivityTracker: Запущено отслеживание 20-отрезковой активности")
    }
    
    /// Останавливает отслеживание
    func stopTracking() {
        guard isActive else { return }

        isActive = false
        segmentTimer?.invalidate()
        segmentTimer = nil

        print("🎯 MinuteActivityTracker: Остановлено отслеживание активности")
    }

    /// Проверяет, была ли активна текущая минута (на основе завершенных отрезков)
    func wasCurrentMinuteActive() -> Bool {
        // Проверяем завершенные отрезки + текущий отрезок
        let segmentsToCheck = Array(currentMinuteSegments.prefix(max(currentSegment, 1)))
        let activeSegments = segmentsToCheck.filter { $0 }.count
        let hasActivity = activeSegments >= minActiveSegmentsForActiveMinute

        print("🎯 MinuteActivityTracker: Проверка текущей минуты - завершено отрезков: \(currentSegment), активных: \(activeSegments), активность: \(hasActivity)")
        return hasActivity
    }

    /// Принудительно завершает текущую минуту и возвращает результат
    func forceCompleteCurrentMinute() -> Bool {
        // Проверяем активность в текущем отрезке перед завершением
        checkCurrentSegmentActivity()

        // Определяем активность минуты с учетом погрешности
        let activeSegments = currentMinuteSegments.filter { $0 }.count
        let isMinuteActive = activeSegments >= minActiveSegmentsForActiveMinute

        print("🎯 MinuteActivityTracker: Принудительное завершение минуты - отрезки: \(currentMinuteSegments), активных: \(activeSegments), результат: \(isMinuteActive)")

        // Сбрасываем для новой минуты
        resetCurrentMinute()

        return isMinuteActive
    }
    
    /// Возвращает текущее состояние отрезков для отладки
    func getCurrentSegmentsState() -> (currentSegment: Int, segments: [Bool], isActive: Bool) {
        return (currentSegment, currentMinuteSegments, isActive)
    }

    /// Возвращает время до следующей проверки отрезка
    func getTimeToNextSegmentCheck() -> TimeInterval {
        let elapsed = Date().timeIntervalSince(currentSegmentStartTime)
        let timeInCurrentSegment = elapsed.truncatingRemainder(dividingBy: segmentDurationSeconds)
        return segmentDurationSeconds - timeInCurrentSegment
    }
    
    // MARK: - Private Methods

    /// Запускает таймер переключения отрезков
    private func startSegmentTimer() {
        // Останавливаем предыдущий таймер если есть
        segmentTimer?.invalidate()

        // Запускаем таймер каждые 3 секунды
        segmentTimer = Timer.scheduledTimer(withTimeInterval: segmentDurationSeconds, repeats: true) { [weak self] _ in
            self?.onSegmentTimerFired()
        }

        print("🎯 MinuteActivityTracker: Запущен таймер отрезков (каждые \(segmentDurationSeconds) сек)")
    }

    /// Обработчик срабатывания таймера отрезка
    private func onSegmentTimerFired() {
        // Проверяем активность в завершающемся отрезке
        checkCurrentSegmentActivity()

        // Переходим к следующему отрезку
        currentSegment += 1
        currentSegmentStartTime = Date()

        print("🎯 MinuteActivityTracker: Переход к отрезку \(currentSegment), активных: \(currentMinuteSegments.filter { $0 }.count)")

        // Если завершили все 20 отрезков - завершаем минуту
        if currentSegment >= segmentsPerMinute {
            completeCurrentMinute()
        }
    }
    
    /// Проверяет активность в текущем отрезке
    private func checkCurrentSegmentActivity() {
        guard currentSegment < segmentsPerMinute else { return }

        // Используем существующий UnifiedActivityChecker
        let isActive = activityChecker.isUserCurrentlyActive()

        // Записываем результат в текущий отрезок
        currentMinuteSegments[currentSegment] = isActive

        print("🎯 MinuteActivityTracker: Отрезок \(currentSegment) - активность: \(isActive ? "ДА" : "НЕТ")")
    }

    /// Завершает текущую минуту и начинает новую
    private func completeCurrentMinute() {
        // Определяем активность минуты с учетом погрешности
        let activeSegments = currentMinuteSegments.filter { $0 }.count
        let isMinuteActive = activeSegments >= minActiveSegmentsForActiveMinute

        print("🎯 MinuteActivityTracker: ✅ Минута завершена - активных отрезков: \(activeSegments)/\(segmentsPerMinute), результат: \(isMinuteActive ? "АКТИВНА" : "НЕАКТИВНА")")

        // Сохраняем в историю
        minuteActivityLog.append(isMinuteActive)

        // Ограничиваем размер лога
        if minuteActivityLog.count > maxLogSize {
            minuteActivityLog.removeFirst()
        }

        // Уведомляем о завершении минуты
        onMinuteCompleted?(isMinuteActive)

        // Сбрасываем для новой минуты
        resetCurrentMinute()
    }
    
    /// Сбрасывает состояние для новой минуты
    private func resetCurrentMinute() {
        currentSegment = 0
        currentSegmentStartTime = Date()
        currentMinuteSegments = Array(repeating: false, count: segmentsPerMinute)
    }
    
    // MARK: - Debug Methods
    
    /// Возвращает детальную информацию для отладки
    func getDebugInfo() -> String {
        let timeInCurrentSegment = Date().timeIntervalSince(currentSegmentStartTime)
        let isCurrentlyActive = activityChecker.isUserCurrentlyActive()
        let activeSegments = currentMinuteSegments.filter { $0 }.count

        var info = "MinuteActivityTracker Debug:\n"
        info += "- Активен: \(isActive)\n"
        info += "- Текущий отрезок: \(currentSegment)/\(segmentsPerMinute)\n"
        info += "- Время в отрезке: \(String(format: "%.1f", timeInCurrentSegment))с\n"
        info += "- Активных отрезков: \(activeSegments)/\(segmentsPerMinute)\n"
        info += "- Активность сейчас: \(isCurrentlyActive)\n"
        info += "- Минута активна: \(wasCurrentMinuteActive())"

        return info
    }
    
    /// Симулирует активность в текущем отрезке (для тестирования)
    func simulateActivityInCurrentSegment() {
        guard currentSegment < segmentsPerMinute else { return }

        currentMinuteSegments[currentSegment] = true
        print("🧪 MinuteActivityTracker: Симулирована активность в отрезке \(currentSegment)")
    }

    /// Принудительно устанавливает состояние отрезка (для тестирования)
    func setForcedSegmentState(segment: Int, isActive: Bool) {
        guard segment >= 0 && segment < segmentsPerMinute else { return }

        currentMinuteSegments[segment] = isActive
        print("🧪 MinuteActivityTracker: Принудительно установлен отрезок \(segment) = \(isActive)")
    }
    
    deinit {
        stopTracking()
    }
}

// MARK: - Extensions

extension MinuteActivityTracker {
    
    /// Статистика текущей минуты
    struct MinuteStats {
        let currentSegment: Int
        let segmentsCompleted: Int
        let activeSegments: Int
        let isMinuteActive: Bool
        let timeInCurrentSegment: TimeInterval
    }

    /// Возвращает статистику текущей минуты
    func getCurrentMinuteStats() -> MinuteStats {
        let timeInCurrentSegment = Date().timeIntervalSince(currentSegmentStartTime)
        let activeSegments = currentMinuteSegments.prefix(currentSegment).filter { $0 }.count
        let totalActiveSegments = currentMinuteSegments.filter { $0 }.count
        let isMinuteActive = totalActiveSegments >= minActiveSegmentsForActiveMinute

        return MinuteStats(
            currentSegment: currentSegment,
            segmentsCompleted: currentSegment,
            activeSegments: activeSegments,
            isMinuteActive: isMinuteActive,
            timeInCurrentSegment: timeInCurrentSegment
        )
    }

    /// Возвращает данные активности за последние N минут
    func getRecentActivity(minutes: Int) -> [Bool] {
        return Array(minuteActivityLog.suffix(minutes))
    }

    /// Записывает неактивные минуты за время сна/неактивности
    /// - Parameter sleepDurationMinutes: длительность сна в минутах
    func recordSleepInactivity(sleepDurationMinutes: Int) {
        print("🎯 MinuteActivityTracker: 🌙 Записываем \(sleepDurationMinutes) неактивных минут за время сна")

        // Записываем неактивные минуты за время сна
        for _ in 0..<sleepDurationMinutes {
            minuteActivityLog.append(false)
        }

        // Ограничиваем размер лога
        while minuteActivityLog.count > maxLogSize {
            minuteActivityLog.removeFirst()
        }

        let activeCount = minuteActivityLog.filter { $0 }.count
        print("🎯 MinuteActivityTracker: 🌙 После записи сна: лог \(minuteActivityLog.count) мин, активных: \(activeCount)")
    }

    /// Сбрасывает лог активности (для очень длительного сна)
    func resetActivityLog() {
        minuteActivityLog.removeAll()
        print("🎯 MinuteActivityTracker: 🔄 Лог активности полностью сброшен")
    }
}
