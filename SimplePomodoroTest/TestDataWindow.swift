import Cocoa

class TestDataWindow: NSWindow {
    
    private var infoLabel: NSTextField!
    private var scenarioButtons: [NSButton] = []
    private var customDayField: NSTextField!
    private var customDurationField: NSTextField!
    
    init() {
        super.init(
            contentRect: NSRect(x: 0, y: 0, width: 500, height: 600),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
        setupUI()
        updateInfo()
    }
    
    private func setupWindow() {
        title = "🧪 Управление тестовыми данными"
        isReleasedWhenClosed = false

        // Применяем стили
        setupStandardWindow()
    }
    
    private func setupUI() {
        let contentView = NSView()
        self.contentView = contentView
        
        // Заголовок
        let titleLabel = NSTextField(labelWithString: "Тестовые данные для системы выходных")
        titleLabel.font = NSFont.boldSystemFont(ofSize: 16)
        titleLabel.alignment = .center
        
        // Информационная панель
        infoLabel = NSTextField(labelWithString: "Загрузка...")
        infoLabel.font = NSFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        infoLabel.backgroundColor = NSColor.controlBackgroundColor
        infoLabel.isBordered = true
        infoLabel.isEditable = false
        infoLabel.isSelectable = true
        
        // Кнопки сценариев
        let scenarioLabel = NSTextField(labelWithString: "Готовые сценарии:")
        scenarioLabel.font = NSFont.boldSystemFont(ofSize: 14)
        
        let scenarios = [
            "1. Работал только сегодня",
            "2. Работал вчера + сегодня",
            "3. Работал 3 дня подряд, сегодня НЕ работал",
            "4. Работал 4 дня подряд, включая сегодня"
        ]
        
        for (index, title) in scenarios.enumerated() {
            let button = NSButton.createUnifiedButton(title: title, type: .green)
            button.target = self
            button.action = #selector(scenarioButtonClicked(_:))
            button.tag = index + 1
            scenarioButtons.append(button)
        }
        
        // Ручное добавление
        let manualLabel = NSTextField(labelWithString: "Ручное добавление:")
        manualLabel.font = NSFont.boldSystemFont(ofSize: 14)
        
        let dayLabel = NSTextField(labelWithString: "Дней назад:")
        customDayField = NSTextField()
        customDayField.stringValue = "1"
        customDayField.placeholderString = "0 = сегодня, 1 = вчера"
        
        let durationLabel = NSTextField(labelWithString: "Минут:")
        customDurationField = NSTextField()
        customDurationField.stringValue = "25"
        customDurationField.placeholderString = "Продолжительность"
        
        let addButton = NSButton.createUnifiedButton(title: "Добавить сессию", type: .green)
        addButton.target = self
        addButton.action = #selector(addCustomSession)

        // Управление
        let controlLabel = NSTextField(labelWithString: "Управление:")
        controlLabel.font = NSFont.boldSystemFont(ofSize: 14)

        let clearButton = NSButton.createUnifiedButton(title: "🧹 Очистить все тестовые данные", type: .gray)
        clearButton.target = self
        clearButton.action = #selector(clearTestData)

        let refreshButton = NSButton.createUnifiedButton(title: "🔄 Обновить информацию", type: .gray)
        refreshButton.target = self
        refreshButton.action = #selector(refreshInfo)

        let debugButton = NSButton.createDashedButton(title: "🐛 Открыть отладку выходных", type: .purple)
        debugButton.target = self
        debugButton.action = #selector(openWeekendDebug)
        
        // Размещение элементов
        let stackView = NSStackView()
        stackView.orientation = .vertical
        stackView.spacing = 15
        stackView.alignment = .leading
        
        // Добавляем элементы
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(infoLabel)
        
        stackView.addArrangedSubview(scenarioLabel)
        for button in scenarioButtons {
            stackView.addArrangedSubview(button)
        }
        
        stackView.addArrangedSubview(manualLabel)
        
        let dayRow = NSStackView()
        dayRow.orientation = .horizontal
        dayRow.spacing = 10
        dayRow.addArrangedSubview(dayLabel)
        dayRow.addArrangedSubview(customDayField)
        stackView.addArrangedSubview(dayRow)
        
        let durationRow = NSStackView()
        durationRow.orientation = .horizontal
        durationRow.spacing = 10
        durationRow.addArrangedSubview(durationLabel)
        durationRow.addArrangedSubview(customDurationField)
        stackView.addArrangedSubview(durationRow)
        
        stackView.addArrangedSubview(addButton)
        
        stackView.addArrangedSubview(controlLabel)
        stackView.addArrangedSubview(clearButton)
        stackView.addArrangedSubview(refreshButton)
        stackView.addArrangedSubview(debugButton)
        
        contentView.addSubview(stackView)
        
        // Настройка размеров
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        infoLabel.translatesAutoresizingMaskIntoConstraints = false
        stackView.translatesAutoresizingMaskIntoConstraints = false
        customDayField.translatesAutoresizingMaskIntoConstraints = false
        customDurationField.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            titleLabel.centerXAnchor.constraint(equalTo: stackView.centerXAnchor),
            
            infoLabel.leadingAnchor.constraint(equalTo: stackView.leadingAnchor),
            infoLabel.trailingAnchor.constraint(equalTo: stackView.trailingAnchor),
            
            customDayField.widthAnchor.constraint(equalToConstant: 100),
            customDurationField.widthAnchor.constraint(equalToConstant: 100)
        ])
        
        for button in scenarioButtons {
            button.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                button.leadingAnchor.constraint(equalTo: stackView.leadingAnchor),
                button.trailingAnchor.constraint(equalTo: stackView.trailingAnchor)
            ])
        }
        
        [addButton, clearButton, refreshButton, debugButton].forEach { button in
            button.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                button.leadingAnchor.constraint(equalTo: stackView.leadingAnchor),
                button.trailingAnchor.constraint(equalTo: stackView.trailingAnchor)
            ])
        }
    }
    
    // MARK: - Actions
    
    @objc private func scenarioButtonClicked(_ sender: NSButton) {
        let scenario = sender.tag
        TestDataManager.shared.createSpecificScenario(scenario)
        updateInfo()
        
        // Показываем уведомление
        showNotification("Сценарий \(scenario) создан")
    }
    
    @objc private func addCustomSession() {
        guard let daysAgo = Int(customDayField.stringValue),
              let duration = Int(customDurationField.stringValue),
              daysAgo >= 0, duration > 0 else {
            showAlert("Ошибка", "Введите корректные числовые значения")
            return
        }
        
        TestDataManager.shared.addTestMicroSession(daysAgo: daysAgo, duration: duration)
        updateInfo()
        
        showNotification("Добавлена сессия: \(duration) мин за \(daysAgo == 0 ? "сегодня" : "\(daysAgo) дн. назад")")
    }
    
    @objc private func clearTestData() {
        let alert = NSAlert()
        alert.messageText = "Очистить тестовые данные?"
        alert.informativeText = "Это удалит все тестовые интервалы, но оставит реальные данные."
        alert.addButton(withTitle: "Очистить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .warning
        
        if alert.runModal() == .alertFirstButtonReturn {
            TestDataManager.shared.clearAllTestData()
            updateInfo()
            showNotification("Тестовые данные очищены")
        }
    }
    
    @objc private func refreshInfo() {
        updateInfo()
        showNotification("Информация обновлена")
    }
    
    @objc private func openWeekendDebug() {
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
            appDelegate.showWeekendDebugWindow()
        }
    }
    
    // MARK: - Helper Methods
    
    private func updateInfo() {
        let info = TestDataManager.shared.getTestDataInfo()
        infoLabel.stringValue = info
    }
    
    private func showAlert(_ title: String, _ message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.runModal()
    }
    
    private func showNotification(_ message: String) {
        // Можно добавить системное уведомление или временную метку
        Logger.shared.log(.info, "TestDataWindow", "💬 \(message)")
    }
    
    func showWindow(statusItemFrame: NSRect) {
        // Позиционируем окно под иконкой статус-бара
        let windowFrame = frame
        let screenFrame = NSScreen.main?.visibleFrame ?? NSRect.zero
        
        let x = statusItemFrame.midX - windowFrame.width / 2
        let y = statusItemFrame.minY - windowFrame.height - 10
        
        let finalX = max(10, min(x, screenFrame.maxX - windowFrame.width - 10))
        let finalY = max(10, y)
        
        setFrameOrigin(NSPoint(x: finalX, y: finalY))
        makeKeyAndOrderFront(nil)
        
        Logger.shared.log(.info, "TestDataWindow", "🪟 Окно тестовых данных показано")
    }
}
