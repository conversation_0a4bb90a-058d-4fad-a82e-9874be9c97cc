//
//  ReturnMessageDemoWindow.swift
//  uProd
//
//  Демонстрационное окно для показа сообщений при возвращении пользователя
//

import Cocoa

class ReturnMessageDemoWindow: NSWindow {
    
    private var titleLabel: NSTextField!
    private var messageLabel: NSTextField!
    private var primaryButton: NSButton!
    private var secondaryButton: NSButton?
    private var demoType: DemoType
    
    enum DemoType: CaseIterable {
        case partialRest    // 2-10 мин
        case chooseRest     // 10-17 мин  
        case fullRest       // 17+ мин
        
        var title: String {
            switch self {
            case .partialRest: return "Частичный отдых"
            case .chooseRest: return "Хорошее время для отдыха"
            case .fullRest: return "Полноценный отдых"
            }
        }
        
        var message: String {
            switch self {
            case .partialRest: return "Вы отдыхали 5 минут. Хотите продолжить отдых или вернуться к работе?"
            case .chooseRest: return "Вы отдыхали 12 минут. Это отличное время для восстановления! Что выберете?"
            case .fullRest: return "Отлично! Вы отдыхали 25 минут. Теперь вы готовы к продуктивной работе!"
            }
        }
        
        var primaryButtonText: String {
            switch self {
            case .partialRest: return "Вернуться к работе"
            case .chooseRest: return "Начать работу"
            case .fullRest: return "Начать работу"
            }
        }
        
        var secondaryButtonText: String? {
            switch self {
            case .partialRest: return "Продолжить отдых"
            case .chooseRest: return "Продолжить отдых"
            case .fullRest: return nil
            }
        }
    }
    
    init(demoType: DemoType) {
        self.demoType = demoType
        
        // Создаем компактное окно в стиле uProd
        let windowRect = NSRect(x: 0, y: 0, width: 380, height: 160)
        
        super.init(
            contentRect: windowRect,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        print("🎨 ReturnMessageDemoWindow: Создание демо окна для \(demoType)")
        setupWindow()
        setupUI()
        positionWindow()
    }
    
    private func setupWindow() {
        // Настройки окна в стиле uProd
        self.level = .floating
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.ignoresMouseEvents = false
        self.hasShadow = true
        
        // Показываем на всех рабочих столах
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
    }
    
    private func positionWindow() {
        // Центрируем окно на экране
        if let screen = NSScreen.main {
            let screenFrame = screen.frame
            let windowFrame = self.frame
            let x = (screenFrame.width - windowFrame.width) / 2
            let y = (screenFrame.height - windowFrame.height) / 2 + 100 // Чуть выше центра
            self.setFrameOrigin(NSPoint(x: x, y: y))
        }
    }
    
    private func setupUI() {
        print("🎨 ReturnMessageDemoWindow: Настройка UI в стиле uProd")
        
        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        self.contentView = contentView

        // Visual effect view для glassmorphism размытия (как в uProd)
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = .hudWindow
        visualEffectView.blendingMode = .behindWindow
        visualEffectView.state = .active
        visualEffectView.wantsLayer = true
        visualEffectView.layer?.cornerRadius = 12
        visualEffectView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(visualEffectView)

        // Основной контейнер
        let containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        visualEffectView.addSubview(containerView)
        
        // Заголовок
        titleLabel = NSTextField(labelWithString: demoType.title)
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.backgroundColor = NSColor.clear
        titleLabel.isBordered = false
        containerView.addSubview(titleLabel)
        
        // Сообщение
        messageLabel = NSTextField(labelWithString: demoType.message)
        messageLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)
        messageLabel.textColor = NSColor.white
        messageLabel.alignment = .center
        messageLabel.maximumNumberOfLines = 3
        messageLabel.preferredMaxLayoutWidth = 320
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.backgroundColor = NSColor.clear
        messageLabel.isBordered = false
        containerView.addSubview(messageLabel)
        
        // Кнопки в стиле uProd
        primaryButton = createStyledButton(title: demoType.primaryButtonText, isGreen: true, isSmall: false)
        primaryButton.target = self
        primaryButton.action = #selector(primaryButtonClicked)
        containerView.addSubview(primaryButton)
        
        if let secondaryText = demoType.secondaryButtonText {
            secondaryButton = createStyledButton(title: secondaryText, isGreen: false, isSmall: false)
            secondaryButton?.target = self
            secondaryButton?.action = #selector(secondaryButtonClicked)
            containerView.addSubview(secondaryButton!)
        }
        
        // Constraints
        setupConstraints(visualEffectView: visualEffectView, containerView: containerView)
        
        print("🎨 ReturnMessageDemoWindow: UI настроен")
    }
    
    private func createStyledButton(title: String, isGreen: Bool, isSmall: Bool) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.wantsLayer = true

        // Настройка размера и шрифта (как в uProd)
        if isSmall {
            button.controlSize = .small
            button.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        } else {
            button.controlSize = .regular
            button.font = NSFont.systemFont(ofSize: 13, weight: .semibold)
        }

        // Настройка цветов (как в uProd)
        if isGreen {
            // Зеленая кнопка "Начать работу"
            button.bezelStyle = .rounded
            button.contentTintColor = NSColor.systemGreen

            // Дополнительная стилизация для зеленой кнопки
            button.layer?.backgroundColor = NSColor.systemGreen.withAlphaComponent(0.1).cgColor
            button.layer?.borderColor = NSColor.systemGreen.cgColor
            button.layer?.borderWidth = 1.0
            button.layer?.cornerRadius = 6.0
        } else {
            // Серая кнопка "Продолжить отдых"
            button.bezelStyle = .rounded
            button.contentTintColor = NSColor.controlAccentColor

            // Дополнительная стилизация для серых кнопок
            button.layer?.backgroundColor = NSColor.controlAccentColor.withAlphaComponent(0.1).cgColor
            button.layer?.borderColor = NSColor.controlAccentColor.cgColor
            button.layer?.borderWidth = 1.0
            button.layer?.cornerRadius = 6.0
        }

        return button
    }
    
    private func setupConstraints(visualEffectView: NSVisualEffectView, containerView: NSView) {
        NSLayoutConstraint.activate([
            // Visual effect view заполняет все окно
            visualEffectView.leadingAnchor.constraint(equalTo: contentView!.leadingAnchor),
            visualEffectView.trailingAnchor.constraint(equalTo: contentView!.trailingAnchor),
            visualEffectView.topAnchor.constraint(equalTo: contentView!.topAnchor),
            visualEffectView.bottomAnchor.constraint(equalTo: contentView!.bottomAnchor),
            
            // Container view с отступами
            containerView.leadingAnchor.constraint(equalTo: visualEffectView.leadingAnchor, constant: 20),
            containerView.trailingAnchor.constraint(equalTo: visualEffectView.trailingAnchor, constant: -20),
            containerView.topAnchor.constraint(equalTo: visualEffectView.topAnchor, constant: 15),
            containerView.bottomAnchor.constraint(equalTo: visualEffectView.bottomAnchor, constant: -15),
            
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 10),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            
            // Сообщение
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            messageLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            messageLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor),
            messageLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor),
        ])
        
        // Кнопки
        if let secondaryButton = secondaryButton {
            // Две кнопки рядом
            NSLayoutConstraint.activate([
                secondaryButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 15),
                secondaryButton.trailingAnchor.constraint(equalTo: containerView.centerXAnchor, constant: -5),
                secondaryButton.widthAnchor.constraint(equalToConstant: 140),
                secondaryButton.heightAnchor.constraint(equalToConstant: 32),
                
                primaryButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 15),
                primaryButton.leadingAnchor.constraint(equalTo: containerView.centerXAnchor, constant: 5),
                primaryButton.widthAnchor.constraint(equalToConstant: 140),
                primaryButton.heightAnchor.constraint(equalToConstant: 32),
                
                primaryButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -10)
            ])
        } else {
            // Одна кнопка по центру
            NSLayoutConstraint.activate([
                primaryButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 15),
                primaryButton.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
                primaryButton.widthAnchor.constraint(equalToConstant: 160),
                primaryButton.heightAnchor.constraint(equalToConstant: 32),
                primaryButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -10)
            ])
        }
    }
    
    @objc private func primaryButtonClicked() {
        print("🎨 Нажата основная кнопка: \(demoType.primaryButtonText)")
        hideWithAnimation()
    }
    
    @objc private func secondaryButtonClicked() {
        print("🎨 Нажата дополнительная кнопка: \(demoType.secondaryButtonText ?? "")")
        hideWithAnimation()
    }
    
    func showWithAnimation() {
        print("🎨 ReturnMessageDemoWindow: Показ окна с анимацией")
        
        // Начинаем с прозрачности (как в uProd)
        self.alphaValue = 0
        self.makeKeyAndOrderFront(nil)
        
        // Анимация появления (как в uProd)
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.4
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1
        })
        
        NSApp.activate(ignoringOtherApps: true)
    }
    
    private func hideWithAnimation() {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            self.animator().alphaValue = 0
        }) {
            self.orderOut(nil)
        }
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
}

// MARK: - Демо функция для тестирования
class ReturnMessageDemo {
    static func showAllDemos() {
        print("🎨 Показ всех демо окон сообщений при возвращении")

        let demos = ReturnMessageDemoWindow.DemoType.allCases

        for (index, demoType) in demos.enumerated() {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 2.0) {
                let window = ReturnMessageDemoWindow(demoType: demoType)
                window.showWithAnimation()

                // Автоматически закрываем через 3 секунды
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    window.orderOut(nil)
                }
            }
        }
    }

    static func showDemo(type: ReturnMessageDemoWindow.DemoType) {
        print("🎨 Показ демо окна: \(type)")
        let window = ReturnMessageDemoWindow(demoType: type)
        window.showWithAnimation()
    }
}
