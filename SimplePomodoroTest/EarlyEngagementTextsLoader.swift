import Foundation

// MARK: - JSON Data Structures

/// Основная структура JSON файла с текстами
struct EarlyEngagementTextsData: Codable {
    let version: String
    let description: String
    let lastUpdated: String
    let streakMode: StreakMode
    let returnMode: ReturnMode
    let subheaders: Subheaders
    let variables: Variables
    let conflictPairs: ConflictPairs
    
    private enum CodingKeys: String, CodingKey {
        case version, description
        case lastUpdated = "last_updated"
        case streakMode = "streak_mode"
        case returnMode = "return_mode"
        case subheaders, variables
        case conflictPairs = "conflict_pairs"
    }
}

/// Режим streak (непрерывная работа)
struct StreakMode: Codable {
    let universalMessages: UniversalMessages
    let specialDays: SpecialDays
    
    private enum CodingKeys: String, CodingKey {
        case universalMessages = "universal_messages"
        case specialDays = "special_days"
    }
}

/// Универсальные сообщения по уровням зрелости
struct UniversalMessages: Codable {
    let level1: MaturityLevel
    let level2: MaturityLevel
    let level3: MaturityLevel
    
    private enum CodingKeys: String, CodingKey {
        case level1 = "level_1"
        case level2 = "level_2"
        case level3 = "level_3"
    }
}

/// Уровень зрелости привычки
struct MaturityLevel: Codable {
    let name: String
    let message1: [String]
    let message2: [String]
    let message3: [String]
    let message4: [String]
    
    private enum CodingKeys: String, CodingKey {
        case name
        case message1 = "message_1"
        case message2 = "message_2"
        case message3 = "message_3"
        case message4 = "message_4"
    }
}

/// Специальные дни
struct SpecialDays: Codable {
    let day1: DayMessages
    let day2: DayMessages
    let day3: DayMessages
    let day5: DayMessages
    let milestoneDays: MilestoneDays
    
    private enum CodingKeys: String, CodingKey {
        case day1 = "day_1"
        case day2 = "day_2"
        case day3 = "day_3"
        case day5 = "day_5"
        case milestoneDays = "milestone_days"
    }
}

/// Сообщения для конкретного дня
struct DayMessages: Codable {
    let message1: [String]
    let message2: [String]
    let message3: [String]
    let message4: [String]
    
    private enum CodingKeys: String, CodingKey {
        case message1 = "message_1"
        case message2 = "message_2"
        case message3 = "message_3"
        case message4 = "message_4"
    }
}

/// Milestone дни (7, 14, 30, 50, 100)
struct MilestoneDays: Codable {
    let day7: DayMessages
    let day14: DayMessages
    let day30: DayMessages
    let day50: DayMessages
    let day100: DayMessages
    
    private enum CodingKeys: String, CodingKey {
        case day7 = "day_7"
        case day14 = "day_14"
        case day30 = "day_30"
        case day50 = "day_50"
        case day100 = "day_100"
    }
}

/// Режим возвращения (дни без работы)
struct ReturnMode: Codable {
    let level1: ReturnLevel
    let level2: ReturnLevel
    let level3: ReturnLevel
    let level4: ReturnLevel
    
    private enum CodingKeys: String, CodingKey {
        case level1 = "level_1"
        case level2 = "level_2"
        case level3 = "level_3"
        case level4 = "level_4"
    }
}

/// Уровень для режима возвращения
struct ReturnLevel: Codable {
    let name: String
    let message1: [String]
    let message2: [String]
    let message3: [String]
    let message4: [String]
    
    private enum CodingKeys: String, CodingKey {
        case name
        case message1 = "message_1"
        case message2 = "message_2"
        case message3 = "message_3"
        case message4 = "message_4"
    }
}

/// Подзаголовки
struct Subheaders: Codable {
    let message1: [String]
    let message2: [String]
    let message3: [String]
    let message4: [String]

    private enum CodingKeys: String, CodingKey {
        case message1 = "message_1"
        case message2 = "message_2"
        case message3 = "message_3"
        case message4 = "message_4"
    }
}

/// Переменные и правила
struct Variables: Codable {
    let declensionRules: DeclensionRules
    let availableVariables: [String]
    
    private enum CodingKeys: String, CodingKey {
        case declensionRules = "declension_rules"
        case availableVariables = "available_variables"
    }
}

/// Правила склонения
struct DeclensionRules: Codable {
    let description: String
    let rules: DeclensionRuleSet
}

/// Набор правил склонения
struct DeclensionRuleSet: Codable {
    let день: String
    let дня: String
    let дней: String
}

/// Конфликтные пары
struct ConflictPairs: Codable {
    let description: String
    let pairs: [ConflictPair]
}

/// Конфликтная пара
struct ConflictPair: Codable {
    let headerKeywords: [String]
    let excludeSubheaders: [String]
    
    private enum CodingKeys: String, CodingKey {
        case headerKeywords = "header_keywords"
        case excludeSubheaders = "exclude_subheaders"
    }
}

// MARK: - Loader Class

/// Загрузчик текстов из JSON файла
class EarlyEngagementTextsLoader {
    static let shared = EarlyEngagementTextsLoader()
    
    private var textsData: EarlyEngagementTextsData?
    
    private init() {
        loadTexts()
    }
    
    /// Загружает тексты из JSON файла
    private func loadTexts() {
        guard let url = Bundle.main.url(forResource: "EarlyEngagementTexts", withExtension: "json") else {
            Logger.shared.log(.error, "EarlyEngagementTextsLoader", "❌ Файл EarlyEngagementTexts.json не найден")
            return
        }
        
        do {
            let data = try Data(contentsOf: url)
            textsData = try JSONDecoder().decode(EarlyEngagementTextsData.self, from: data)
            Logger.shared.log(.info, "EarlyEngagementTextsLoader", "✅ Тексты загружены успешно")
        } catch {
            Logger.shared.log(.error, "EarlyEngagementTextsLoader", "❌ Ошибка загрузки текстов: \(error)")
        }
    }
    
    /// Получает данные текстов
    func getTextsData() -> EarlyEngagementTextsData? {
        if textsData == nil {
            Logger.shared.log(.error, "EarlyEngagementTextsLoader", "❌ КРИТИЧЕСКАЯ ОШИБКА: textsData = nil при запросе")
        } else {
            Logger.shared.log(.info, "EarlyEngagementTextsLoader", "✅ textsData успешно возвращена")
        }
        return textsData
    }
    
    /// Получает подзаголовки для конкретного сообщения
    func getSubheadersForMessage(messageIndex: Int) -> [String] {
        guard let subheaders = textsData?.subheaders else { return [] }

        // Возвращаем подзаголовки для конкретного сообщения
        switch messageIndex {
        case 0: return subheaders.message1
        case 1: return subheaders.message2
        case 2: return subheaders.message3
        case 3: return subheaders.message4
        default: return subheaders.message1
        }
    }

    /// Получает универсальные подзаголовки (для обратной совместимости)
    func getUniversalSubheaders() -> [String] {
        return getSubheadersForMessage(messageIndex: 0)
    }
    
    /// Получает конфликтные пары
    func getConflictPairs() -> [ConflictPair] {
        return textsData?.conflictPairs.pairs ?? []
    }
}

// MARK: - Utility Functions

/// Функция склонения дней
func getDaysDeclension(_ days: Int) -> String {
    let lastDigit = days % 10
    let lastTwoDigits = days % 100
    
    // Исключения для 11-14
    if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
        return "дней"
    }
    
    // Основные правила
    switch lastDigit {
    case 1:
        return "день"
    case 2, 3, 4:
        return "дня"
    default:
        return "дней"
    }
}
