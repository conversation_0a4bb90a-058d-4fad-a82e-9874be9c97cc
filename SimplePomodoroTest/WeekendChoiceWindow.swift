import Cocoa
import Foundation

/// Окно выбора выходного дня (заменяет СРВ в выходные)
class WeekendChoiceWindow: NSWindow {
    
    // MARK: - Properties

    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var restButton: NSButton!
    private var workButton: NSButton!
    private var onRestChoice: (() -> Void)?
    private var onWorkChoice: (() -> Void)?
    
    // MARK: - Initialization
    
    init() {
        // Создаем окно под иконкой приложения (увеличенная ширина для длинных подзаголовков)
        let windowRect = NSRect(x: 0, y: 0, width: 430, height: 120)

        super.init(
            contentRect: windowRect,
            styleMask: [.borderless], // Убираем стандартные кнопки
            backing: .buffered,
            defer: false
        )

        // Применяем стандартные настройки из UIStyles
        self.setupStandardWindow()
        setupUI()
    }
    
    // MARK: - Setup Methods
    
    private func setupWindow() {
        self.title = "Выходной день"
        self.isReleasedWhenClosed = false
        self.isMovable = false

        // Стандартные настройки уже применены через setupStandardWindow()
    }

    // Метод позиционирования теперь в UIStyles.swift как extension NSWindow
    
    private func setupUI() {
        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Применяем унифицированный фон из UIStyles
        self.setupUnifiedBackground(type: .rest, for: containerView)
        self.contentView = containerView

        // Используем универсальные методы создания текста из UIStyles
        titleLabel = NSTextField.createWindowTitle("🏖️ Сегодня выходной!")
        containerView.addSubview(titleLabel)

        subtitleLabel = NSTextField.createWindowSubtitle("Как хотите провести день?")
        containerView.addSubview(subtitleLabel)
        
        // Кнопка отдыха (как в BreakEndWindow)
        restButton = NSButton.createUnifiedButton(title: "Отдохнуть", type: .green, isSmall: false)
        restButton.target = self
        restButton.action = #selector(restButtonClicked)
        containerView.addSubview(restButton)

        // Кнопка работы (как в BreakEndWindow)
        workButton = NSButton.createUnifiedButton(title: "Поработать", type: .gray, isSmall: false)
        workButton.target = self
        workButton.action = #selector(workButtonClicked)
        containerView.addSubview(workButton)
        
        // Constraints (как в BreakEndWindow)
        NSLayoutConstraint.activate([
            // Container заполняет все окно
            containerView.topAnchor.constraint(equalTo: containerView.superview!.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: containerView.superview!.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: containerView.superview!.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: containerView.superview!.bottomAnchor),

            // Заголовок (сверху по центру)
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),

            // Подзаголовок (под заголовком)
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),

            // Кнопки рядом снизу (как в BreakEndWindow)
            restButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 16),
            restButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            restButton.heightAnchor.constraint(equalToConstant: 32),
            restButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),

            workButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 16),
            workButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            workButton.heightAnchor.constraint(equalToConstant: 32),
            workButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),

            // Равная ширина кнопок (как в BreakEndWindow)
            restButton.widthAnchor.constraint(equalTo: workButton.widthAnchor),
            restButton.trailingAnchor.constraint(equalTo: workButton.leadingAnchor, constant: -10)
        ])
    }
    
    // MARK: - Public Methods
    
    /// Показывает окно с выбором выходного дня
    func showWeekendChoice(onRest: @escaping () -> Void, onWork: @escaping () -> Void, statusItemFrame: NSRect? = nil) {
        self.onRestChoice = onRest
        self.onWorkChoice = onWork

        // Получаем сообщение в зависимости от количества дней без отдыха
        let message = WeekendMessageManager.shared.getMessageForDaysWithoutRest(WeekendManager.shared.daysWithoutRest)

        // Обновляем UI с заголовком и подзаголовком
        let daysWithoutRest = WeekendManager.shared.daysWithoutRest

        // Заголовок всегда одинаковый
        titleLabel.stringValue = "🏖️ Сегодня выходной!"

        // Подзаголовок зависит от количества пропущенных выходных
        switch daysWithoutRest {
        case 0:
            subtitleLabel.stringValue = "Пора восстановить силы после рабочих дней"
        case 1:
            subtitleLabel.stringValue = "Вчера пропустили выходной — сегодня время восстановиться"
        case 2:
            subtitleLabel.stringValue = "Уже 2 выходных пропущено — пора восстановить силы"
        case 3, 4:
            subtitleLabel.stringValue = "⚠️ Уже \(daysWithoutRest) выходных пропущено — напряжение накапливается"
        case 5, 6:
            subtitleLabel.stringValue = "🔴 Уже \(daysWithoutRest) выходных пропущено — риск выгорания!"
        default: // 7+ дней
            subtitleLabel.stringValue = "🚨 Уже \(daysWithoutRest)+ выходных пропущено — опасно для здоровья!"
        }

        restButton.title = message.restButtonText
        workButton.title = message.workButtonText

        // Обновляем градиент в зависимости от контекста
        updateGradientForContext()

        // ИСПРАВЛЕНИЕ: Позиционируем окно ПЕРЕД показом, если передан statusItemFrame
        if let statusFrame = statusItemFrame {
            Logger.shared.log(.info, "WeekendChoice", "🎯 ОТЛАДКА позиционирования: statusFrame = \(statusFrame)")

            let windowWidth = self.frame.width
            let windowHeight = self.frame.height
            Logger.shared.log(.info, "WeekendChoice", "🎯 Размеры окна: \(windowWidth) x \(windowHeight)")

            // Позиционируем под status item с небольшим отступом (как все остальные окна)
            let x = statusFrame.midX - windowWidth / 2
            let y = statusFrame.minY - windowHeight - 8

            let newPosition = NSPoint(x: x, y: y)
            Logger.shared.log(.info, "WeekendChoice", "🎯 Вычисленная позиция: \(newPosition)")
            self.setFrameOrigin(newPosition)
        }

        // Показываем окно
        self.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        Logger.shared.log(.info, "WeekendChoice", "🏖️ Показано окно выбора выходного дня (дней без отдыха: \(WeekendManager.shared.daysWithoutRest))")
    }

    private func updateGradientForContext() {
        guard let contentView = self.contentView else { return }

        // Определяем тип градиента в зависимости от дней без отдыха
        let daysWithoutRest = WeekendManager.shared.daysWithoutRest

        if daysWithoutRest > 0 {
            // Если есть пропущенные выходные - используем градиент отдыха (зеленый)
            self.setupUnifiedBackground(type: .rest, for: contentView)
        } else {
            // Обычный выходной - тоже градиент отдыха
            self.setupUnifiedBackground(type: .rest, for: contentView)
        }
    }

    // MARK: - Actions
    
    @objc private func restButtonClicked() {
        Logger.shared.log(.info, "WeekendChoice", "😴 Пользователь выбрал отдых")
        
        // Обрабатываем выбор в WeekendManager
        WeekendManager.shared.handleWeekendChoice(.rest)
        
        // Закрываем окно
        self.orderOut(nil)
        
        // Показываем советы по отдыху
        showRestAdvice()
        
        // Вызываем callback
        onRestChoice?()
    }
    
    @objc private func workButtonClicked() {
        Logger.shared.log(.info, "WeekendChoice", "💼 Пользователь выбрал работу")
        
        // Обрабатываем выбор в WeekendManager
        WeekendManager.shared.handleWeekendChoice(.work)
        
        // Закрываем окно
        self.orderOut(nil)
        
        // Вызываем callback
        onWorkChoice?()
    }
    
    // MARK: - Private Methods
    
    private func showRestAdvice() {
        // Показываем советы по отдыху (будет реализовано в следующем этапе)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            RestAdviceWindow.shared.showAdvice()
        }
    }
}
