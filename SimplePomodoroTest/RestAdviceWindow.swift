import Cocoa
import Foundation

/// Окно с советами по качественному отдыху
class RestAdviceWindow: NSWindow {
    
    // MARK: - Singleton
    
    static let shared = RestAdviceWindow()
    
    // MARK: - Properties
    
    private var adviceLabel: NSTextField!
    private var detailsButton: NSButton!
    
    // Массив советов по отдыху
    private let restAdvices = [
        "Меньше гаджетов и экранов - дайте глазам и мозгу отдохнуть",
        "Избегайте YouTube/TikTok, особенно коротких видео - они истощают внимание",
        "Прогулки на свежем воздухе восстанавливают энергию и улучшают настроение",
        "Встречи с друзьями и близкими - важная часть эмоционального восстановления",
        "Деятельность руками: готовка, рукоделие, садоводство - переключает мозг",
        "Меньше думайте о работе и проектах - позвольте мозгу отдохнуть",
        "Физическая активность: йога, растяжка, легкие упражнения",
        "Чтение книг развивает и одновременно расслабляет",
        "Медитация или просто тишина помогают восстановить внутренний баланс",
        "Творчество: рисование, музыка, письмо - питает душу",
        "Качественный сон - основа восстановления",
        "Общение с природой: парки, лес, водоемы успокаивают нервную систему"
    ]
    
    // MARK: - Initialization
    
    private init() {
        // Создаем окно по центру экрана
        let windowRect = NSRect(x: 0, y: 0, width: 450, height: 200)
        
        super.init(
            contentRect: windowRect,
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
        setupUI()
    }
    
    // MARK: - Setup Methods
    
    private func setupWindow() {
        self.title = "Советы по отдыху"
        self.isReleasedWhenClosed = false
        self.level = .floating
        self.center()
        
        // Применяем стандартный macOS стиль
        if let contentView = self.contentView {
            contentView.wantsLayer = true
            contentView.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        }
    }
    
    private func setupUI() {
        guard let contentView = self.contentView else { return }
        
        // Основной контейнер
        let containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)
        
        // Иконка
        let iconLabel = NSTextField()
        iconLabel.isEditable = false
        iconLabel.isBordered = false
        iconLabel.backgroundColor = NSColor.clear
        iconLabel.font = NSFont.systemFont(ofSize: 32)
        iconLabel.stringValue = "🌿"
        iconLabel.alignment = .center
        iconLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconLabel)
        
        // Заголовок
        let titleLabel = NSTextField()
        titleLabel.isEditable = false
        titleLabel.isBordered = false
        titleLabel.backgroundColor = NSColor.clear
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.stringValue = "Время качественного отдыха"
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // Совет
        adviceLabel = NSTextField()
        adviceLabel.isEditable = false
        adviceLabel.isBordered = false
        adviceLabel.backgroundColor = NSColor.clear
        adviceLabel.font = NSFont.systemFont(ofSize: 14)
        adviceLabel.textColor = NSColor.secondaryLabelColor
        adviceLabel.alignment = .center
        adviceLabel.maximumNumberOfLines = 3
        adviceLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(adviceLabel)
        
        // Кнопка "Подробнее"
        detailsButton = NSButton.createDashedButton(title: "Подробнее", type: .purple)
        detailsButton.target = self
        detailsButton.action = #selector(detailsButtonClicked)

        containerView.addSubview(detailsButton)

        // Кнопка "Понятно"
        let okButton = NSButton.createUnifiedButton(title: "Понятно", type: .green)
        okButton.target = self
        okButton.action = #selector(okButtonClicked)
        
        containerView.addSubview(okButton)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Container
            containerView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            containerView.widthAnchor.constraint(equalToConstant: 400),
            containerView.heightAnchor.constraint(equalToConstant: 160),
            
            // Icon
            iconLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 10),
            iconLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            
            // Title
            titleLabel.topAnchor.constraint(equalTo: iconLabel.bottomAnchor, constant: 5),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // Advice
            adviceLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            adviceLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            adviceLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // Buttons
            detailsButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15),
            detailsButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 50),
            detailsButton.widthAnchor.constraint(equalToConstant: 120),
            detailsButton.heightAnchor.constraint(equalToConstant: 32),
            
            okButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15),
            okButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -50),
            okButton.widthAnchor.constraint(equalToConstant: 120),
            okButton.heightAnchor.constraint(equalToConstant: 32)
        ])
    }
    
    // MARK: - Public Methods
    
    /// Показывает окно с советом по отдыху
    func showAdvice() {
        // Выбираем случайный совет
        let randomAdvice = restAdvices.randomElement() ?? restAdvices[0]
        adviceLabel.stringValue = randomAdvice
        
        // Показываем окно
        self.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
        
        Logger.shared.log(.info, "RestAdvice", "🌿 Показан совет по отдыху: \(randomAdvice.prefix(50))...")
    }
    
    // MARK: - Actions
    
    @objc private func detailsButtonClicked() {
        Logger.shared.log(.info, "RestAdvice", "📖 Нажата кнопка 'Подробнее'")
        
        // Пока что просто закрываем окно (в будущем здесь будет ссылка на статью)
        self.orderOut(nil)
        
        // TODO: Открыть статью о правильном отдыхе
        // В будущем здесь будет открытие браузера или внутреннего окна со статьей
    }
    
    @objc private func okButtonClicked() {
        Logger.shared.log(.info, "RestAdvice", "✅ Нажата кнопка 'Понятно'")
        self.orderOut(nil)
    }
}
