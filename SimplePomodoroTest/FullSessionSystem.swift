//
//  FullSessionSystem.swift
//  SimplePomodoroTest
//
//  Created by Augment Agent on 29.07.2025.
//

import Foundation

/// Система полной сессии - предоставляет пользователю выбор между адаптивным интервалом и полной сессией (52 минуты)
class FullSessionSystem {
    
    // MARK: - Singleton
    static let shared = FullSessionSystem()
    private init() {
        loadStatistics()
    }
    
    // MARK: - Constants
    private let fullSessionDuration: TimeInterval = 52 * 60 // 52 минуты
    private let minSuccessRate: Double = 0.3 // Минимальный процент успеха для показа кнопки
    private let minAttemptsForBlocking: Int = 3 // Минимум попыток для блокировки
    private let minSuccessfulIntervalsForRestore: Int = 5 // Минимум успешных интервалов для восстановления
    private let minBarForRestore: TimeInterval = 30 * 60 // Минимальная планка для восстановления (30 мин)
    
    // MARK: - Data Models
    struct FullSessionStats: Codable {
        var attempts: Int = 0          // Сколько раз выбирал полную сессию
        var completions: Int = 0       // Сколько раз завершил полную сессию
        var successRate: Double {      // Процент успеха
            return attempts > 0 ? Double(completions) / Double(attempts) : 1.0
        }
        var isBlocked: Bool = false    // Заблокирована ли кнопка
        var lastAttemptDate: Date?     // Дата последней попытки
        var lastCompletionDate: Date?  // Дата последнего успешного завершения
    }
    
    enum SessionContext {
        case horizontalDescaling(originalBar: TimeInterval)  // Горизонтальная дескалация
        case verticalDegradation(originalBar: TimeInterval)  // Вертикальная деградация
        case restCompletion(currentBar: TimeInterval)        // Завершение отдыха
        case earlyEngagement(currentBar: TimeInterval)       // Раннее вовлечение
    }
    
    // MARK: - Properties
    private var statistics = FullSessionStats()
    private var recentSuccessfulIntervals: [Date] = []
    
    // MARK: - Public Methods
    
    /// Определяет, нужно ли показывать кнопку "Полная сессия"
    func shouldShowFullSession(currentBar: TimeInterval) -> Bool {
        logDebug("FullSession", "Проверка показа кнопки: планка=\(Int(currentBar/60))мин, заблокирована=\(statistics.isBlocked)")
        
        // Не показываем если планка уже 52 минуты или больше
        if currentBar >= fullSessionDuration {
            logDebug("FullSession", "Планка >= 52 мин, кнопка не нужна")
            return false
        }
        
        // Не показываем если кнопка заблокирована
        if statistics.isBlocked {
            logDebug("FullSession", "Кнопка заблокирована из-за низкого процента успеха")
            return false
        }
        
        logDebug("FullSession", "Кнопка будет показана")
        return true
    }
    
    /// Регистрирует попытку использования полной сессии
    func recordFullSessionAttempt() {
        statistics.attempts += 1
        statistics.lastAttemptDate = Date()
        saveStatistics()
        
        Logger.shared.log(.info, "FullSession", "Зарегистрирована попытка полной сессии: всего попыток=\(statistics.attempts)")
    }
    
    /// Регистрирует успешное завершение полной сессии
    func recordFullSessionCompletion(context: SessionContext) {
        statistics.completions += 1
        statistics.lastCompletionDate = Date()
        saveStatistics()
        
        logInfo("FullSession", "Зарегистрировано завершение полной сессии: всего завершений=\(statistics.completions), процент=\(Int(statistics.successRate * 100))%")
        
        // Проверяем нужно ли заблокировать кнопку
        checkForBlocking()
    }
    
    /// Регистрирует неудачную попытку полной сессии
    func recordFullSessionFailure() {
        saveStatistics()
        
        logInfo("FullSession", "Зарегистрирована неудача полной сессии: процент успеха=\(Int(statistics.successRate * 100))%")
        
        // Проверяем нужно ли заблокировать кнопку
        checkForBlocking()
    }
    
    /// Регистрирует успешный обычный интервал (для восстановления кнопки)
    func recordSuccessfulRegularInterval() {
        let now = Date()
        recentSuccessfulIntervals.append(now)
        
        // Оставляем только последние интервалы за последние 7 дней
        let weekAgo = now.addingTimeInterval(-7 * 24 * 60 * 60)
        recentSuccessfulIntervals = recentSuccessfulIntervals.filter { $0 > weekAgo }
        
        saveStatistics()
        
        // Проверяем возможность восстановления кнопки
        checkForRestoration()
    }
    
    /// Вычисляет новую планку после успешного выполнения полной сессии
    func calculateNewBarAfterFullSession(context: SessionContext) -> TimeInterval {
        switch context {
        case .horizontalDescaling(let originalBar):
            // Горизонтальная дескалация: применяем градационный рост к оригинальной планке
            let newBar = GradualGrowthSystem.calculateGrowth(currentBar: originalBar)
            logInfo("FullSession", "Горизонтальная дескалация: \(Int(originalBar/60))мин → \(Int(newBar/60))мин")
            return newBar

        case .verticalDegradation(let originalBar):
            // Вертикальная деградация: значительное восстановление (×2, но не больше 52)
            let newBar = min(fullSessionDuration, originalBar * 2)
            logInfo("FullSession", "Вертикальная деградация: \(Int(originalBar/60))мин → \(Int(newBar/60))мин")
            return newBar

        case .restCompletion(let currentBar):
            // Завершение отдыха: применяем градационный рост
            let newBar = GradualGrowthSystem.calculateGrowth(currentBar: currentBar)
            logInfo("FullSession", "Завершение отдыха: \(Int(currentBar/60))мин → \(Int(newBar/60))мин")
            return newBar

        case .earlyEngagement(let currentBar):
            // Раннее вовлечение: применяем градационный рост
            let newBar = GradualGrowthSystem.calculateGrowth(currentBar: currentBar)
            logInfo("FullSession", "Раннее вовлечение: \(Int(currentBar/60))мин → \(Int(newBar/60))мин")
            return newBar
        }
    }
    
    /// Получает текущую статистику для отладки
    func getCurrentStatistics() -> String {
        let successPercent = Int(statistics.successRate * 100)
        let status = statistics.isBlocked ? "🚫 ЗАБЛОКИРОВАНА" : "✅ Доступна"
        
        return """
        📊 Статистика полной сессии:
        • Попытки: \(statistics.attempts)
        • Завершения: \(statistics.completions)
        • Процент успеха: \(successPercent)%
        • Статус: \(status)
        • Успешных интервалов: \(recentSuccessfulIntervals.count)
        """
    }
    
    // MARK: - Private Methods
    
    private func checkForBlocking() {
        if !statistics.isBlocked && 
           statistics.attempts >= minAttemptsForBlocking && 
           statistics.successRate < minSuccessRate {
            
            statistics.isBlocked = true
            saveStatistics()
            
            logInfo("FullSession", "🚫 Кнопка полной сессии заблокирована: процент успеха \(Int(statistics.successRate * 100))% < \(Int(minSuccessRate * 100))%")
        }
    }
    
    private func checkForRestoration() {
        if statistics.isBlocked && 
           recentSuccessfulIntervals.count >= minSuccessfulIntervalsForRestore {
            
            // Проверяем текущую планку пользователя
            // Получаем текущую планку из системы раннего вовлечения
            let currentBar = EarlyEngagementSystem.shared.getCurrentUserBar()
            if currentBar >= minBarForRestore {
                statistics.isBlocked = false
                // Сбрасываем статистику для нового шанса
                statistics.attempts = 0
                statistics.completions = 0
                saveStatistics()
                
                Logger.shared.log(.info, "FullSession", "✅ Кнопка полной сессии восстановлена: \(recentSuccessfulIntervals.count) успешных интервалов, планка \(Int(currentBar/60))мин")
            }
        }
    }
    
    // MARK: - Persistence
    
    private func saveStatistics() {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(statistics) {
            UserDefaults.standard.set(data, forKey: "FullSessionStatistics")
        }
        
        // Сохраняем также даты успешных интервалов
        let intervalData = recentSuccessfulIntervals.map { $0.timeIntervalSince1970 }
        UserDefaults.standard.set(intervalData, forKey: "RecentSuccessfulIntervals")
    }
    
    private func loadStatistics() {
        // Загружаем статистику
        if let data = UserDefaults.standard.data(forKey: "FullSessionStatistics"),
           let loadedStats = try? JSONDecoder().decode(FullSessionStats.self, from: data) {
            statistics = loadedStats
        }
        
        // Загружаем даты успешных интервалов
        if let intervalData = UserDefaults.standard.array(forKey: "RecentSuccessfulIntervals") as? [Double] {
            recentSuccessfulIntervals = intervalData.map { Date(timeIntervalSince1970: $0) }
            
            // Очищаем старые записи (старше 7 дней)
            let weekAgo = Date().addingTimeInterval(-7 * 24 * 60 * 60)
            recentSuccessfulIntervals = recentSuccessfulIntervals.filter { $0 > weekAgo }
        }
        
        logInfo("FullSession", "Загружена статистика: попытки=\(statistics.attempts), завершения=\(statistics.completions), заблокирована=\(statistics.isBlocked)")
    }
}
