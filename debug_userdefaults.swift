#!/usr/bin/swift

import Foundation

struct WorkDay: Codable {
    let date: Date
    let workedOnPriorityProject: Bo<PERSON>
}

// Читаем данные из UserDefaults для приложения uProd
let defaults = UserDefaults(suiteName: "com.local.uProd") ?? UserDefaults.standard

if let data = defaults.data(forKey: "earlyEngagement_workHistory"),
   let workHistory = try? JSONDecoder().decode([WorkDay].self, from: data) {
    
    print("📊 История работы (\(workHistory.count) записей):")
    
    let formatter = DateFormatter()
    formatter.dateFormat = "dd.MM.yyyy"
    
    for workDay in workHistory.suffix(7) { // Последние 7 дней
        let dateStr = formatter.string(from: workDay.date)
        let workedStr = workDay.workedOnPriorityProject ? "✅ работал" : "❌ не работал"
        print("  \(dateStr): \(workedStr)")
    }
} else {
    print("❌ Не удалось прочитать историю работы")
}

// Читаем последнее время работы
if let lastWorkData = defaults.data(forKey: "earlyEngagement_lastWork"),
   let lastWorkStr = String(data: lastWorkData, encoding: .utf8) {

    print("\n📅 Строка последней работы: '\(lastWorkStr)'")

    // Парсим дату в формате ISO 8601
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd HH:mm:ss Z"

    if let lastWorkDate = formatter.date(from: lastWorkStr) {
        let displayFormatter = DateFormatter()
        displayFormatter.dateFormat = "dd.MM.yyyy HH:mm"
        print("📅 Последняя работа: \(displayFormatter.string(from: lastWorkDate))")

        // Считаем дни без работы
        let calendar = Calendar.current
        let now = Date()
        let startOfToday = calendar.startOfDay(for: now)
        let startOfLastWorkDay = calendar.startOfDay(for: lastWorkDate)
        let daysSince = calendar.dateComponents([.day], from: startOfLastWorkDay, to: startOfToday).day ?? 0
        print("📅 Дней без работы: \(daysSince)")
    } else {
        print("❌ Не удалось распарсить дату")
    }
} else {
    print("❌ Не удалось прочитать время последней работы")
}

// Читаем текущую планку
if let userBarData = defaults.data(forKey: "earlyEngagement_userBar"),
   let userBarStr = String(data: userBarData, encoding: .utf8) {

    print("\n📏 Строка планки: '\(userBarStr)'")

    if let userBar = Double(userBarStr) {
        let userBarMinutes = Int(userBar / 60)
        print("📏 Текущая планка: \(userBarMinutes) минут")
    } else {
        print("❌ Не удалось конвертировать планку в число")
    }
} else {
    print("❌ Не удалось прочитать планку")
}
