#!/bin/bash

# Скрипт для запуска всех тестов унифицированной системы uProd
# Использование: ./test-unified.sh

echo "🧪 ========== ТЕСТИРОВАНИЕ УНИФИЦИРОВАННОЙ СИСТЕМЫ uProd =========="
echo ""

# Проверяем что мы в правильной директории
if [ ! -d "Tests" ] || [ ! -f "Tests/UnifiedSystemBasicTest.swift" ]; then
    echo "❌ Ошибка: Запустите скрипт из корневой директории проекта uProd"
    echo "📋 Убедитесь что папка Tests/ существует и содержит тестовые файлы"
    exit 1
fi

# Счетчики
TOTAL_TESTS=0
PASSED_TESTS=0

echo "1️⃣ Запуск базовых тестов логики..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
if swift Tests/UnifiedSystemBasicTest.swift; then
    echo "✅ Базовые тесты: ПРОЙДЕНЫ"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ Базовые тесты: ПРОВАЛЕНЫ"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo ""
echo "2️⃣ Запуск интеграционных тестов..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
if swift Tests/IntegrationTest.swift; then
    echo "✅ Интеграционные тесты: ПРОЙДЕНЫ"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ Интеграционные тесты: ПРОВАЛЕНЫ"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo ""
echo "3️⃣ Запуск полных тестов системы (опционально)..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "⚠️ Полные тесты требуют настройки окружения и могут не работать в изоляции"
echo "📋 Для запуска полных тестов используйте: swift Tests/SimpleUnifiedSystemTest.swift"

echo ""
echo "🧪 ========== РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ =========="
echo "📊 Пройдено тестов: $PASSED_TESTS из $TOTAL_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!"
    echo "✅ Унифицированная система работает корректно"
    exit 0
else
    echo "⚠️ ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!"
    echo "❌ Проверьте вывод выше для деталей"
    exit 1
fi
