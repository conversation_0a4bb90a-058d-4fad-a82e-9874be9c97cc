# Унифицированная система активности uProd

## 🎯 Цель проекта

Создать единую систему отслеживания активности пользователя, которая заменит все разрозненные системы проверки активности и обеспечит консистентное поведение для всех компонентов приложения.

## 📋 Исходные требования (5 ключевых пунктов)

### 1. ✅ Система активности во время отдыха
**Проблема**: Не учитывалось зеленое окно с кнопками во время формального отдыха
- **"Обещаю уйти"** → пользователь должен стать неактивным
- **"Отдыхаю за компом"** → качество отдыха = плохое в статистике

**Решение**: Добавить состояние FormalRest с подсостояниями RestingAtComputer/PromisedToLeave

### 2. ✅ Параллельный отдых
**Концепция**: Автоматическое засчитывание отдыха при длительной неактивности
- Начинается при **первой минуте неактивности** (хотя бы 1 бандл из 4-х был с активностью, но только при оценке всех 4-х) → запуск ParallelRestTracker
- **Активность вернулась** → сброс трекера  и ждет следующей полной минуты для активации (активируется постоянно, в фоне)
- **17 минут непрерывно** → автоматический отдых засчитан

**Сценарий**: Желтая зона → пользователь ушел → через час вернулся → система показывает "у вас был отдых" и записывает в статистику

### 3. ✅ Улучшение проверки активности (20-отрезковая система с погрешностью)
**Проблема**: Старая система считала минуту активной если ЛЮБОЙ из 4 бандов содержал активность
**Решение**: 20 отрезков по 3 секунды в минуте [0-3] [3-6] ... [57-60] с логикой погрешности
- 1 активный отрезок = случайность (погрешность) → минута неактивна
- ≥2 активных отрезка = реальная активность → минута активна
- Решает проблему "запуск таймера и уход" - теперь таймер корректно останавливается

### 4. ✅ Edge cases с порогами (ОБНОВЛЕНО)
**Старый подход**: Сложные пороги 2-5-10-17 минут с откатами эскалации
**Новый подход**: Умные адаптивные сообщения без сложных промежуточных состояний

### 5. ✅ Полная унификация (принцип DRY)
Одна система для всех случаев:
- Формальные/неформальные интервалы
- Эскалация и показ окон
- InformalSessionDetector
- Отслеживание качества отдыха

## 🧠 Новая концепция (упрощенная логика)

### Базовые принципы:
1. **Быстрая реакция**: При первых 15 секундах неактивности - останавливаем счетчик и эскалацию
2. **Умные сообщения**: Система адаптируется к фактическому времени отдыха
3. **Пользовательский контроль**: Даем информацию и выбор, не принимаем решения за пользователя
4. **Простота**: Убираем сложные промежуточные состояния

### Логика по времени отдыха (возвращение после неактивности):

#### 0-2 минуты: Микропауза
- ✅ Просто возобновляем счетчик и эскалацию
- ❌ Никаких сообщений не показываем
- 💭 Как будто ничего не было (туалет, чай)

#### 2-10 минут: Частичный отдых
- 💬 Сообщение: "Ты отдохнул X минут, отдохни еще Y минут"
- 🎯 Кнопка: "Keep Resting" → запускает официальный отдых с зачетом времени
- ✅ Возобновляем счетчик и эскалацию
- ⚠️ Если не уйдет - будет эскалация

**Логика "Keep Resting":**
- Система вычисляет оставшееся время отдыха: `стандартный_отдых - уже_отдохнул`
- Запускает официальный таймер отдыха с скорректированным временем
- Если пользователь уже отдохнул достаточно → символический 1-минутный таймер

#### 10-17 минут: Значительный отдых
- 💬 Специальное окно с двумя кнопками:
  - 🎯 "Keep Resting (осталось Y мин)" → запускает официальный отдых с зачетом времени
  - ⚡ "Start Working" - альтернативная
- ❌ НЕ возобновляем эскалацию (считаем частично отдохнувшим)
- 📊 Если выберет "Start Working" - статистика "неполный отдых"

**Логика "Keep Resting" для значительного отдыха:**
- Аналогично частичному отдыху, но с учетом большего времени
- Может привести к очень короткому официальному отдыху (1-2 минуты)

#### 17+ минут: Полный отдых
- 🔄 Полный сброс счетчика и эскалации
- 📊 Запись в статистику "хороший отдых"
- ✅ Обычное поведение после отдыха

## 🏗️ Техническая архитектура

### Упрощенные компоненты:
1. **MinuteActivityTracker** - 20 отрезков по 3 секунды с логикой погрешности (основа точности)
2. **ActivityStateManager** - управление состояниями (упрощено до 3 состояний)
3. **ParallelRestTracker** - автоматический отдых
4. **SmartRestMessageManager** - умные адаптивные сообщения
5. **UnifiedActivityChecker** - API для всех компонентов

### Состояния системы:
```
Active → Inactive → AutoRest (17+ мин)
         ↓
    SmartMessage (в зависимости от времени)
```

### Логика обработки:
```swift
// При неактивности 15 сек
func onInactivityDetected() {
    pauseOvertimeCounter()
    pauseEscalation()
    parallelRestTracker.start()
}

// При возврате активности
func onActivityReturned() {
    let restDuration = parallelRestTracker.getDuration()
    smartRestMessageManager.showMessage(for: restDuration)
}
```

## 📊 Статистика отдыха (новые категории)

1. **Полный отдых** (17+ мин) - отлично ✅
2. **Частичный с продолжением отдыха** (2-17 мин → Take a Break) - хорошо ✅  
3. **Прерванный отдых** (10-17 мин → Начать интервал) - нормально ⚠️
3. **Частичный отдых с прерыванием** (2-10 мин → Продолжение активности) - плохо ⚠️
5. **Микропаузы** (0-2 мин) - не записываем

## 🎨 UI и UX вопросы (требуют обсуждения)

### Адаптивные окна:
- Использовать существующие окна или создать новые?
- Как адаптировать размер под разные сообщения?
- Дизайн кнопок для выбора (10-17 мин случай)

### Сообщения:
- Тон сообщений (дружелюбный/нейтральный)
- Иконки и визуальные элементы
- Анимации переходов

## 🔧 Дополнительные улучшения

### 1. Debouncing
- Не показывать сообщения при возврате менее чем на 30 секунд
- Избегать "мигания" окон при частых переключениях

### 2. Накопление времени отдыха
**Сценарий**: Отошел на 5 мин → вернулся на 1 мин → снова отошел на 8 мин
**Решение**: Каждый отход считается отдельно (проще и понятнее)

### 3. Интеграция с существующими системами
- Адаптация PomodoroTimer
- Адаптация InformalSessionDetector  
- Адаптация UnifiedReminderSystem
- Интеграция с BreakTimer (формальный отдых)

## ⚠️ Открытые вопросы

### 0.1 Как обновить систему обнаружения для текущих мест, чтобы все работало?
Особенно для системы обнаружения неформальных сессий. Там ведь проверяются активные минуты. Или проблем здесь быть не должно, потому что по сути на выходе этой системы активности мы выдаем какой-то результат, значение активно минуты была или нет. (активность = true)? И также будем его использовать. То есть дальше уже как раз вот в этой вот системе обнаружения неформальных сессий. Или как все это будет устроено?

Тоже самое когда идет отдых. Как мы определяем есть активность или нет? На основе 20-отрезковой системы с погрешностью: ≥2 активных отрезка = активность (обновлено)

### 0.2 Ты учел что параллельные 

### 0.3 Статистика для ...
Не будет ли проблем с обновлением статистики? Что у нас кроме качества отдыха (для формальных интервалов) надо передавать в статистику? 
Может длительность отдыха? Как ее отслеживать? Система сложная

### 0.4 
Как учитывать активность для отдыха? Там ведь тоже он может начать работать? Хотя мы это уже учли вроде? Но там другие сообщения другие окна? Или это ок? 

КАК СЧИТАТЬ АКТИВНОСТЬ ТАМ? 20-ОТРЕЗКОВОЙ СИСТЕМОЙ ПО 3 СЕК С ПОГРЕШНОСТЬЮ! (обновлено)

### 0.5 При обнаружении активности показывать окно отдыха или окно зоны? 
Причем для разных временных диапазонов

Или погоди. Если у нас зоны, то значит он не нажал "take a rest" и конфликта нет. 

#### Надо отделньо окно отдыха переработать (просто идеи)
 - Но явно не сейчас (это отдельная задача, не связанная с системой активности)
 - Сделать кнопку начать интервал (толкьо если отдых > 10 минут)
 - Сделать показатель "качество отдыха" и показывать что отдых говно, несли сидишь за компом
 - Вообще потом сказать что у вас последние сессии качесвто отдыха очень плохое и надо чтото с этим делать. Это можно в другом окне сделать, отделньо вообще. А в окне обанружения активности вывести среднее качество последних сессий отдыха 



### 1. Дизайн окон
- Адаптировать существующие окна или создать новые?
- Как обеспечить консистентность дизайна?

### 2. Формальный отдых
- Как интегрировать с зеленым окном "обещаю уйти"/"отдыхаю за компом"?
- Логика отслеживания качества отдыха

### 3. Неформальные интервалы
- Записывать ли автоматический отдых в статистику для неформальных сессий?
- Особенности поведения для InformalSessionDetector

### 4. Существующие системы
- Как интегрировать с системой показа окон после длительного сна?
- Совместимость с AppDelegate.resetAllStatesAfterLongSleep()

## 🚀 План реализации (черновой)

### Фаза 1: Основа
1. MinuteActivityTracker с 20-отрезковой системой и логикой погрешности
2. ActivityStateManager с упрощенными состояниями
3. ParallelRestTracker для автоматического отдыха
4. Базовые тесты

### Фаза 2: Умные сообщения
5. SmartRestMessageManager
6. Адаптивные UI компоненты
7. Интеграция с существующими окнами

### Фаза 3: Интеграция
8. Адаптация всех существующих компонентов
9. Тестирование всех сценариев
10. Документация и финализация

---

## 🔍 РЕШЕНИЯ ДЛЯ ОТКРЫТЫХ ВОПРОСОВ

### ✅ 0.1 Интеграция с существующими системами обнаружения
**Решение:** На выходе MinuteActivityTracker получаем boolean "активна ли минута"
- **InformalSessionDetector** → использует данные через ComputerTimeTracker
- **ComputerTimeTracker** → использует `forceCompleteCurrentMinute()` (раз в минуту для записи статистики)
- **BreakTimer** → использует `forceCompleteCurrentMinute()` (ИСКЛЮЧЕНИЕ: нужно обнаруживать текущую активность)
- **Статус неформальных сессий** → использует `getRecentActivity()` из MinuteActivityTracker
- **ActivityStateManager** → получает данные через callbacks от MinuteActivityTracker
- **Принцип унификации**: Все системы используют MinuteActivityTracker как единый источник правды
- **Обратная совместимость обеспечена** ✅

### ✅ 0.3 Статистика отдыха
**Решение:** ParallelRestTracker отслеживает время, записываем в статистику:
```swift
struct RestStatistics {
    let duration: TimeInterval
    let type: RestType // formal, automatic, partial
    let quality: RestQuality // good, poor, interrupted
    let context: RestContext // formal_interval, informal_session
}
```

### ✅ 0.4 Активность во время отдыха
**Решение:** Использовать **20-отрезковую систему с погрешностью** для консистентности
- Зеленое окно показывается при обнаружении активности (≥2 отрезка)
- Логика "обещаю уйти"/"отдыхаю за компом" не меняется

### ✅ 0.5 Конфликт окон - НЕТ КОНФЛИКТА!
**Сценарий А:** Зоны эскалации (НЕ нажал "Take a Rest")
- Отошел → вернулся → умные сообщения нашей системы

**Сценарий Б:** Формальный отдых (нажал "Take a Rest")
- Во время отдыха активность → зеленое окно

### ✅ Дизайн окон - ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЕ
**Решение:** Адаптируем содержимое существующих окон:
- Зоны эскалации → новые тексты сообщений
- Окно отдыха → добавляем вторую кнопку для случая 10-17 мин
- Зеленое окно → без изменений

### ✅ Формальный отдых - РАЗОБРАЛИСЬ
**Решение:** Конфликта нет, это разные потоки:
- Формальный отдых = нажал "Take a Rest" → зеленое окно
- Автоматический отдых = НЕ нажал → умные сообщения

### ✅ Неформальные интервалы
**Автоматический отдых в статистику:** ДА, с пометкой "автоматический/неформальный"
**InformalSessionDetector:** При автоматическом отдыхе 17+ мин → сброс 52-минутного окна

### ⚠️ Существующие системы - ТРЕБУЕТ ВНИМАНИЯ
**Проблема:** Различать сон vs длительную неактивность
**Решение:**
```swift
func resetAllStatesAfterLongSleep() {
    resetExistingStates()
    activityStateManager.reset()
    parallelRestTracker.reset()

    if wasSleepEvent {
        // НЕ записываем как отдых в статистику
    }
}
```

## ⚠️ Потенциальные сложности

### 🔴 Основные риски:
1. **Миграция:** Много существующих компонентов для обновления
2. **Тестирование:** Множество новых сценариев
3. **Производительность:** 20 отрезков = больше проверок (но более точная система)
4. **Синхронизация:** Состояние между компонентами

### ✅ Решения:
1. **Постепенная миграция** по компонентам
2. **Обратная совместимость** через адаптеры
3. **Оптимизация** частоты проверок
4. **Тщательное тестирование** каждого этапа

---

## 🎉 СТАТУС РЕАЛИЗАЦИИ

### ✅ ПОЛНОСТЬЮ РЕАЛИЗОВАНО (Январь 2025)

#### 1. ✅ Система активности во время отдыха
- **ActivityStateManager** с состояниями working/away/formalRest
- **Временные пороги**: 0-2мин (resumeSilently), 2-10мин (partialRest), 10-17мин (chooseRestOrWork), 17+мин (fullRest)
- **Интеграция с формальным отдыхом** через состояние formalRest

#### 2. ✅ 20-отрезковая система с погрешностью
- **MinuteActivityTracker** переписан с 4 бандов (15 сек) на 20 сегментов (3 сек)
- **Логика погрешности**: 1 активный сегмент = случайность, ≥2 сегментов = реальная активность
- **Решена проблема "запуск и уход"**: таймер корректно останавливается
- **⚠️ ВАЖНОЕ ИСКЛЮЧЕНИЕ**: BreakTimer использует `forceCompleteCurrentMinute()` для обнаружения активности в реальном времени, все остальные системы используют `wasCurrentMinuteActive()` для унификации

#### 3. ✅ Унифицированная система паузы/возобновления
- **SimpleUnifiedSystem** с проверкой активности в checkEscalation()
- **Умная пауза**: остановка при 30-секундной неактивности
- **Умное возобновление**: 6-секундная (2-сегментная) стабилизация
- **Правильный расчет времени**: учет паузы в эскалации

#### 4. ✅ Система автоматического закрытия окон
- **ActivityStateManager.onLongInactivity** callback для закрытия окон после 10+ минут
- **Интеграция с AppDelegate.closeNotificationWindows()**
- **Решена проблема**: окна больше не висят часами при длительной неактивности

#### 5. ✅ Система возвращения пользователя
- **Полная реализация окон возвращения** через BreakEndWindow
- **Адаптивные режимы**: partialRest (одна кнопка), choiceRest/fullRest (две кнопки)
- **Интеграция с ActivityStateManager.onUserReturned** callback
- **Правильное позиционирование** относительно статус-бара

#### 6. ✅ Логирование и отладка
- **Исправлена система логирования**: переход с print() на logInfo()/logDebug()/logError()
- **Очистка избыточных логов**: убраны логи каждые 3 секунды
- **Детальная диагностика**: логи состояний, переходов, времени

#### 7. ✅ Исправление критических багов
- **Crash в informal session testing**: исправлен вызов отключенного informalSessionDetector
- **Компиляционные ошибки**: исправлены все ошибки сборки
- **Архитектурные проблемы**: устранены дублирующие системы неактивности
- **Crash при "Take a Rest"**: добавлены безопасные вызовы informalSessionDetector

#### 8. ✅ Логика "Keep Resting" (Январь 2025)
- **Умный зачет времени**: при нажатии "Keep Resting" система запускает официальный отдых
- **Вычисление остатка**: `стандартное_время_отдыха - уже_отдохнутое_время`
- **Техническая реализация**: `startOfficialRestWithTimeCredit(alreadyRestedMinutes:)`
- **Интеграция с PomodoroTimer**: использует `startBreakWithType(isLong:, testDuration:)`
- **Минимальный отдых**: если пользователь уже отдохнул достаточно → 1 минута символического отдыха

### 🔧 ТЕХНИЧЕСКАЯ АРХИТЕКТУРА (РЕАЛИЗОВАННАЯ)

#### Основные компоненты:
1. **ActivityStateManager** - центральная система управления состояниями активности
2. **MinuteActivityTracker** - 20-сегментная система отслеживания (3 сек/сегмент)
3. **UnifiedActivityChecker** - системный API для проверки активности (CGEventSource)
4. **SimpleUnifiedSystem** - унифицированная система эскалации с паузой/возобновлением

#### Унифицированные методы определения активности:
- **`wasCurrentMinuteActive()`** - стандартный метод для всех систем (проверяет завершенные сегменты)
- **`forceCompleteCurrentMinute()`** - ТОЛЬКО для BreakTimer (обнаруживает текущую активность в реальном времени)
- **Принцип**: Единое определение "активной минуты" через MinuteActivityTracker с логикой 2+ сегментов

#### Callback-архитектура:
```swift
// ActivityStateManager callbacks
onStopCounters: () -> Void           // Остановка счетчиков при неактивности
onResumeCounters: () -> Void         // Возобновление при возврате активности
onUserReturned: (ReturnMessage, TimeInterval) -> Void  // Показ окон возвращения
onLongInactivity: (TimeInterval) -> Void              // Закрытие окон при долгой неактивности
```

#### Временные пороги:
- **15 секунд**: Остановка счетчиков и эскалации
- **30 секунд**: Порог определения неактивности (UnifiedActivityChecker)
- **2 минуты**: Переход от resumeSilently к partialRest
- **10 минут**: Переход к chooseRestOrWork + закрытие окон
- **17 минут**: Переход к fullRest (полный отдых)

### 🧪 ТЕСТИРОВАНИЕ

#### ✅ Проверенные сценарии:
1. **Формальные интервалы**: пауза/возобновление работает корректно
2. **Неформальные интервалы**: исправлен crash, система работает
3. **Окна возвращения**: показываются правильные режимы в зависимости от времени отсутствия
4. **Автоматическое закрытие**: окна закрываются после 10+ минут неактивности
5. **Логирование**: чистые логи без спама

#### 🔄 Требует дополнительного тестирования:
1. **Конфликты окон**: поведение при возврате после 20+ минут (окно завершения + окно возвращения)
2. **Длительные сценарии**: тестирование отсутствия 30+ минут
3. **Граничные случаи**: переходы между состояниями при частых переключениях активности

### 📊 РЕЗУЛЬТАТЫ

#### ✅ Решенные проблемы:
- **Основная проблема**: Таймер больше НЕ работает при неактивности пользователя
- **Возобновление**: Таймер корректно возобновляется при возврате активности
- **Окна**: Автоматически закрываются при длительной неактивности
- **Возвращение**: Показываются адаптивные окна в зависимости от времени отсутствия
- **Стабильность**: Исправлены crashes и ошибки компиляции

#### 🎯 Достигнутые цели:
1. **Унификация**: Единая система активности для всех компонентов
2. **Точность**: 20-сегментная система с логикой погрешности
3. **Пользовательский опыт**: Умные адаптивные сообщения
4. **Надежность**: Устранены архитектурные проблемы

#### 9. ✅ Унификация определения "активной минуты" (Январь 2025)
- **Проблема**: Множественные определения активности в разных частях кода
- **Решение**: Единый источник правды через MinuteActivityTracker
- **Архитектурное исключение**: BreakTimer использует `forceCompleteCurrentMinute()` для обнаружения текущей активности
- **Документирование**: Добавлены комментарии в код и обновлена документация
- **Результат**: Консистентное поведение всех систем при определении активности

**Статус**: ✅ **СИСТЕМА ПОЛНОСТЬЮ РЕАЛИЗОВАНА И РАБОТАЕТ**
**Следующий шаг**: Долгосрочное тестирование и мониторинг в продакшене
