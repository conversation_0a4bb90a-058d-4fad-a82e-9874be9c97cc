#!/bin/bash

# Скрипт для запуска ВСЕХ тестов унифицированной системы активности
# Накапливает все тесты в одном месте для предотвращения регрессий

echo "🚀 ЗАПУСК ВСЕХ ТЕСТОВ УНИФИЦИРОВАННОЙ СИСТЕМЫ АКТИВНОСТИ"
echo "=================================================================="

TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Функция для запуска одного теста
run_test() {
    local test_name="$1"
    local test_file="$2"
    
    echo ""
    echo "📋 Запуск: $test_name"
    echo "----------------------------------------"
    
    # Компилируем и запускаем тест
    if swiftc -parse-as-library -o "$test_name" "$test_file" \
        SimplePomodoroTest/MinuteActivityTracker.swift \
        SimplePomodoroTest/ActivityStateManager.swift \
        SimplePomodoroTest/UnifiedActivityChecker.swift 2>/dev/null; then
        
        if ./"$test_name"; then
            echo "✅ $test_name ПРОЙДЕН"
            ((PASSED_TESTS++))
        else
            echo "❌ $test_name ПРОВАЛЕН"
            ((FAILED_TESTS++))
        fi
        
        # Удаляем исполняемый файл
        rm -f "$test_name"
    else
        echo "❌ $test_name НЕ КОМПИЛИРУЕТСЯ"
        ((FAILED_TESTS++))
    fi
    
    ((TOTAL_TESTS++))
}

# Запускаем все тесты унифицированной системы
run_test "RealMinuteTrackerOnlyTest" "Tests/RealMinuteTrackerOnlyTest.swift"
run_test "RealActivityStateTest" "Tests/RealActivityStateTest.swift"
run_test "RealUnifiedIntegrationTest" "Tests/RealUnifiedIntegrationTest.swift"
run_test "RealUnifiedSystemFinalTest" "Tests/RealUnifiedSystemFinalTest.swift"
run_test "RealBreakTimerSimpleTest" "Tests/RealBreakTimerSimpleTest.swift"

# Специальный тест с дополнительными зависимостями
echo ""
echo "📋 Запуск: RealUnifiedReminderTest"
echo "----------------------------------------"

if swiftc -parse-as-library -o "RealUnifiedReminderTest" "Tests/RealUnifiedReminderTest.swift" \
    SimplePomodoroTest/MinuteActivityTracker.swift \
    SimplePomodoroTest/UnifiedActivityChecker.swift \
    SimplePomodoroTest/UnifiedReminderSystem.swift \
    SimplePomodoroTest/EscalationConfig.swift \
    -framework Cocoa 2>/dev/null; then

    if ./RealUnifiedReminderTest; then
        echo "✅ RealUnifiedReminderTest ПРОЙДЕН"
        ((PASSED_TESTS++))
    else
        echo "❌ RealUnifiedReminderTest ПРОВАЛЕН"
        ((FAILED_TESTS++))
    fi

    # Удаляем исполняемый файл
    rm -f "RealUnifiedReminderTest"
else
    echo "❌ RealUnifiedReminderTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Специальный тест BreakQualityTracker с дополнительными зависимостями
echo ""
echo "📋 Запуск: RealBreakQualityTest"
echo "----------------------------------------"

if swiftc -parse-as-library -o "RealBreakQualityTest" "Tests/RealBreakQualityTest.swift" \
    SimplePomodoroTest/MinuteActivityTracker.swift \
    SimplePomodoroTest/UnifiedActivityChecker.swift \
    SimplePomodoroTest/BreakQualityTracker.swift \
    SimplePomodoroTest/ActivityDetector.swift \
    -framework Cocoa 2>/dev/null; then

    if ./RealBreakQualityTest; then
        echo "✅ RealBreakQualityTest ПРОЙДЕН"
        ((PASSED_TESTS++))
    else
        echo "❌ RealBreakQualityTest ПРОВАЛЕН"
        ((FAILED_TESTS++))
    fi

    # Удаляем исполняемый файл
    rm -f "RealBreakQualityTest"
else
    echo "❌ RealBreakQualityTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест статистики активности
echo ""
echo "📋 Запуск: RealStatisticsActivityTest"
echo "----------------------------------------"

if swiftc -o "RealStatisticsActivityTest" "Tests/RealStatisticsActivityTest.swift" \
    -framework Foundation 2>/dev/null; then

    if ./RealStatisticsActivityTest; then
        echo "✅ RealStatisticsActivityTest ПРОЙДЕН"
        ((PASSED_TESTS++))
    else
        echo "❌ RealStatisticsActivityTest ПРОВАЛЕН"
        ((FAILED_TESTS++))
    fi

    # Удаляем исполняемый файл
    rm -f "RealStatisticsActivityTest"
else
    echo "❌ RealStatisticsActivityTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест расширенной статистики отдыха (RestStatistics) - простой тест без зависимостей
echo ""
echo "📋 Запуск: RealRestStatisticsTest"
echo "----------------------------------------"

if swiftc -parse-as-library -o "RealRestStatisticsTest" "Tests/RealRestStatisticsTest.swift" 2>/dev/null; then

    if ./RealRestStatisticsTest; then
        echo "✅ RealRestStatisticsTest ПРОЙДЕН"
        ((PASSED_TESTS++))
    else
        echo "❌ RealRestStatisticsTest ПРОВАЛЕН"
        ((FAILED_TESTS++))
    fi

    # Удаляем исполняемый файл
    rm -f "RealRestStatisticsTest"
else
    echo "❌ RealRestStatisticsTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест генерации сообщений при возвращении пользователя - простой тест без зависимостей
echo ""
echo "📋 Запуск: RealReturnMessageTest"
echo "----------------------------------------"

if swiftc -parse-as-library -o "RealReturnMessageTest" "Tests/RealReturnMessageTest.swift" 2>/dev/null; then

    if ./RealReturnMessageTest; then
        echo "✅ RealReturnMessageTest ПРОЙДЕН"
        ((PASSED_TESTS++))
    else
        echo "❌ RealReturnMessageTest ПРОВАЛЕН"
        ((FAILED_TESTS++))
    fi

    # Удаляем исполняемый файл
    rm -f "RealReturnMessageTest"
else
    echo "❌ RealReturnMessageTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест окна возвращения пользователя - простой тест без зависимостей
echo ""
echo "📋 Запуск: RealReturnWindowTest"
echo "----------------------------------------"

if swift "Tests/RealReturnWindowTest.swift" > /dev/null 2>&1; then

    echo "✅ RealReturnWindowTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ RealReturnWindowTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест BreakEndWindow с новыми режимами возвращения пользователя
echo ""
echo "📋 Запуск: RealBreakEndWindowTest"
echo "----------------------------------------"

if swift "Tests/RealBreakEndWindowTest.swift" > /dev/null 2>&1; then

    echo "✅ RealBreakEndWindowTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ RealBreakEndWindowTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест интеграции статистики с унифицированной системой
echo ""
echo "📋 Запуск: RealStatisticsIntegrationTest"
echo "----------------------------------------"

if swift "Tests/RealStatisticsIntegrationTest.swift" > /dev/null 2>&1; then

    echo "✅ RealStatisticsIntegrationTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ RealStatisticsIntegrationTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест исправлений BreakEndWindow
echo ""
echo "🔧 Запуск: RealBreakEndWindowFixedTest"
echo "----------------------------------------"

if swift "Tests/RealBreakEndWindowFixedTest.swift" > /dev/null 2>&1; then
    echo "✅ RealBreakEndWindowFixedTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ RealBreakEndWindowFixedTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест SleepWakeDetector
echo ""
echo "🌙 Запуск: RealSleepWakeDetectorTest"
echo "----------------------------------------"

if swift "Tests/RealSleepWakeDetectorTest.swift" > /dev/null 2>&1; then
    echo "✅ RealSleepWakeDetectorTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ RealSleepWakeDetectorTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест компиляции SleepWakeDetector
echo ""
echo "🔧 Запуск: SimpleSleepDetectorTest"
echo "----------------------------------------"

if swift "Tests/SimpleSleepDetectorTest.swift" > /dev/null 2>&1; then
    echo "✅ SimpleSleepDetectorTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ SimpleSleepDetectorTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест интеграции с AppDelegate
echo ""
echo "🔗 Запуск: AppDelegateIntegrationTest"
echo "----------------------------------------"

if swift "Tests/AppDelegateIntegrationTest.swift" > /dev/null 2>&1; then
    echo "✅ AppDelegateIntegrationTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ AppDelegateIntegrationTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Тест готовности унифицированной системы сна
echo ""
echo "🌙 Запуск: UnifiedSleepSystemTest"
echo "----------------------------------------"

if swift "Tests/UnifiedSleepSystemTest.swift" > /dev/null 2>&1; then
    echo "✅ UnifiedSleepSystemTest ПРОЙДЕН"
    ((PASSED_TESTS++))
else
    echo "❌ UnifiedSleepSystemTest НЕ КОМПИЛИРУЕТСЯ"
    ((FAILED_TESTS++))
fi

((TOTAL_TESTS++))

# Итоги
echo ""
echo "=================================================================="
echo "📊 ИТОГИ ТЕСТИРОВАНИЯ:"
echo "   Всего тестов: $TOTAL_TESTS"
echo "   Пройдено: $PASSED_TESTS"
echo "   Провалено: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 ВСЕ ТЕСТЫ УНИФИЦИРОВАННОЙ СИСТЕМЫ ПРОЙДЕНЫ!"
    echo "✅ Система готова к продолжению разработки"
    exit 0
else
    echo "⚠️  ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!"
    echo "❌ Нужно исправить проблемы перед продолжением"
    exit 1
fi
