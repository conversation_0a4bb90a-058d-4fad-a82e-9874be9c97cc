#!/usr/bin/swift

import Foundation

print("🧹 Очистка неправильных тестовых данных")

// Получаем UserDefaults для uProd
let defaults = UserDefaults(suiteName: "com.local.uProd") ?? UserDefaults.standard

// Удаляем данные в старом формате
defaults.removeObject(forKey: "completedIntervals")
defaults.synchronize()

print("✅ Очищены неправильные данные")
print("🎯 Теперь используйте TestDataManager в приложении uProd:")
print("   1. Кликните на иконку uProd в статус-баре")
print("   2. Выберите '🧪 Тестовые данные для выходных'")
print("   3. Нажмите 'Сценарий 3' или 'Сценарий 4'")
print("   4. Проверьте контекстное меню")
