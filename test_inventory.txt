=== ИСПОЛЬЗУЕМЫЕ ТЕСТЫ В test.sh ===
run_test "Модуль 1: Неформальные сессии" "Tests/InformalSessionTest.swift"
run_test "Модуль 2: Формальные интервалы" "Tests/PomodoroTimerTest.swift"
run_test "Модуль 3: Система отдыха" "Tests/BreakSystemTest.swift"
run_test "Модуль 4: Логика показа окна" "Tests/RealAppDelegateLogicTest.swift"
run_test "Модуль 5: Унифицированная система" "Tests/UnifiedReminderSystemTest.swift"
run_test "Модуль 6: Интеграция реальной логики" "Tests/RealLogicIntegrationTest.swift"
run_test "Модуль 7: Базовая логика унификации" "Tests/UnifiedSystemBasicTest.swift"
run_test "Модуль 8: Интеграция унификации" "Tests/IntegrationTest.swift"
run_test "Модуль 7: Новая единая система" "Tests/UnifiedSystemTest.swift"
run_test "Модуль 8: Поведение кнопки тест" "Tests/TestButtonBehaviorTest.swift"
run_test "Модуль 9: Настоящие реальные тесты" "Tests/TrueRealUnifiedSystemTest.swift"
run_test "Модуль 10: Критическая зона" "Tests/CriticalZoneTest.swift"

=== ИСПОЛЬЗУЕМЫЕ ТЕСТЫ В test_unified_activity.sh ===
run_test "RealMinuteTrackerOnlyTest" "Tests/RealMinuteTrackerOnlyTest.swift"
run_test "RealActivityStateTest" "Tests/RealActivityStateTest.swift"
run_test "RealUnifiedIntegrationTest" "Tests/RealUnifiedIntegrationTest.swift"
run_test "RealUnifiedSystemFinalTest" "Tests/RealUnifiedSystemFinalTest.swift"
run_test "RealBreakTimerSimpleTest" "Tests/RealBreakTimerSimpleTest.swift"
if swiftc -parse-as-library -o "RealUnifiedReminderTest" "Tests/RealUnifiedReminderTest.swift" \
if swiftc -parse-as-library -o "RealBreakQualityTest" "Tests/RealBreakQualityTest.swift" \
if swiftc -o "RealStatisticsActivityTest" "Tests/RealStatisticsActivityTest.swift" \
if swiftc -parse-as-library -o "RealRestStatisticsTest" "Tests/RealRestStatisticsTest.swift" 2>/dev/null; then
if swiftc -parse-as-library -o "RealReturnMessageTest" "Tests/RealReturnMessageTest.swift" 2>/dev/null; then
if swift "Tests/RealReturnWindowTest.swift" > /dev/null 2>&1; then
if swift "Tests/RealBreakEndWindowTest.swift" > /dev/null 2>&1; then
if swift "Tests/RealStatisticsIntegrationTest.swift" > /dev/null 2>&1; then
if swift "Tests/RealBreakEndWindowFixedTest.swift" > /dev/null 2>&1; then
if swift "Tests/RealSleepWakeDetectorTest.swift" > /dev/null 2>&1; then
if swift "Tests/SimpleSleepDetectorTest.swift" > /dev/null 2>&1; then
if swift "Tests/AppDelegateIntegrationTest.swift" > /dev/null 2>&1; then
if swift "Tests/UnifiedSleepSystemTest.swift" > /dev/null 2>&1; then

=== ВСЕ ФАЙЛЫ ТЕСТОВ ===
Tests/ActivityStateManagerTest.swift
Tests/AppDelegateIntegrationTest.swift
Tests/BreakSystemTest.swift
Tests/ComputerTimeTrackerUpgradeTest.swift
Tests/CriticalZoneTest.swift
Tests/InformalSessionDetectorIntegrationTest.swift
Tests/InformalSessionDetectorUpgradeTest.swift
Tests/InformalSessionTest.swift
Tests/IntegrationTest.swift
Tests/MinuteActivityTrackerTest.swift
Tests/PomodoroTimerTest.swift
Tests/QuickActivityStateManagerTest.swift
Tests/QuickComputerTimeTrackerTest.swift
Tests/QuickMinuteActivityTrackerTest.swift
Tests/QuickUnifiedSystemTest.swift
Tests/RealActivityStateManagerTest.swift
Tests/RealActivityStateTest.swift
Tests/RealAppDelegateLogicTest.swift
Tests/RealBreakEndWindowFixedTest.swift
Tests/RealBreakEndWindowTest.swift
Tests/RealBreakQualityTest.swift
Tests/RealBreakTimerIntegrationTest.swift
Tests/RealBreakTimerSimpleTest.swift
Tests/RealInformalDetectorBasicTest.swift
Tests/RealInformalDetectorSimpleTest.swift
Tests/RealInformalSessionIntegrationTest.swift
Tests/RealLogicIntegrationTest.swift
Tests/RealMinuteActivityTrackerTest.swift
Tests/RealMinuteTrackerOnlyTest.swift
Tests/RealRestStatisticsTest.swift
Tests/RealReturnMessageTest.swift
Tests/RealReturnWindowTest.swift
Tests/RealSleepIntegrationTest.swift
Tests/RealSleepWakeDetectorTest.swift
Tests/RealStatisticsActivityTest.swift
Tests/RealStatisticsIntegrationTest.swift
Tests/RealStatisticsManagerTest.swift
Tests/RealUnifiedIntegrationTest.swift
Tests/RealUnifiedReminderTest.swift
Tests/RealUnifiedSystemFinalTest.swift
Tests/RealUnifiedSystemIntegrationTest.swift
Tests/RealUnifiedSystemTest.swift
Tests/RealUseCaseTest.swift
Tests/SimpleComputerTimeTrackerTest.swift
Tests/SimpleSleepDetectorTest.swift
Tests/SimpleUnifiedSystemTest.swift
Tests/TestButtonBehaviorTest.swift
Tests/TrueRealUnifiedSystemTest.swift
Tests/UnifiedActivitySystemTest.swift
Tests/UnifiedReminderSystemTest.swift
Tests/UnifiedSleepSystemTest.swift
Tests/UnifiedSystemBasicTest.swift
Tests/UnifiedSystemTest.swift
