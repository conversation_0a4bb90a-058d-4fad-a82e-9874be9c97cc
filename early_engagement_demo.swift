#!/usr/bin/env swift

import Foundation

/// ДЕМОНСТРАЦИЯ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Интерактивная программа для тестирования логики системы
struct EarlyEngagementDemo {
    
    static func main() {
        print("🌅 ДЕМОНСТРАЦИЯ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ")
        print("=" * 50)
        print("Эта программа демонстрирует работу системы раннего вовлечения uProd")
        print("Вы можете эмулировать различные сценарии и видеть результаты\n")
        
        var currentUserBar: TimeInterval = 25 * 60 // 25 минут по умолчанию
        var lastWorkTime: Date? = nil
        
        while true {
            showCurrentStatus(userBar: currentUserBar, lastWork: lastWorkTime)
            showMenu()
            
            print("Выберите действие (1-7): ", terminator: "")
            guard let input = readLine(), let choice = Int(input) else {
                print("❌ Неверный ввод. Попробуйте снова.\n")
                continue
            }
            
            switch choice {
            case 1:
                simulateWakeUp(userBar: &currentUserBar, lastWork: &lastWorkTime)
            case 2:
                simulateWork(userBar: &currentUserBar, lastWork: &lastWorkTime)
            case 3:
                simulateFailure(userBar: &currentUserBar)
            case 4:
                showMessageMatrix()
            case 5:
                showUserBarHistory(currentBar: currentUserBar)
            case 6:
                resetSystem(userBar: &currentUserBar, lastWork: &lastWorkTime)
            case 7:
                print("👋 До свидания!")
                return
            default:
                print("❌ Неверный выбор. Попробуйте снова.\n")
            }
        }
    }
    
    static func showCurrentStatus(userBar: TimeInterval, lastWork: Date?) {
        print("\n📊 ТЕКУЩЕЕ СОСТОЯНИЕ СИСТЕМЫ:")
        print("-" * 30)
        print("⏱️  Текущая планка: \(Int(userBar / 60)) минут")
        
        if let lastWork = lastWork {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            print("🕐 Последняя работа: \(formatter.string(from: lastWork))")
            
            let daysWithoutWork = calculateDaysWithoutWork(lastWork: lastWork)
            print("📅 Дни без работы: \(daysWithoutWork)")
        } else {
            print("🕐 Последняя работа: НЕТ ДАННЫХ")
            print("📅 Дни без работы: 4 (максимум)")
        }
        
        let timeOfDay = getCurrentTimeOfDay()
        let timeNames = ["Утро", "День", "Вечер", "Поздно"]
        print("🌅 Время дня: \(timeNames[timeOfDay]) (уровень \(timeOfDay))")
        
        // Показываем текущее сообщение
        let daysWithoutWork = calculateDaysWithoutWork(lastWork: lastWork)
        let message = getMessage(vertical: daysWithoutWork, horizontal: timeOfDay)
        print("💬 Текущее сообщение: \"\(message.title)\"")
        print("⏰ Предлагаемая длительность: \(Int(message.duration / 60)) минут")
    }
    
    static func showMenu() {
        print("\n🎮 ДОСТУПНЫЕ ДЕЙСТВИЯ:")
        print("-" * 20)
        print("1. 🌅 Эмулировать пробуждение")
        print("2. 💼 Эмулировать успешную работу")
        print("3. ❌ Эмулировать неудачу")
        print("4. 📋 Показать матрицу сообщений")
        print("5. 📈 Показать историю планки")
        print("6. 🔄 Сбросить систему")
        print("7. 🚪 Выход")
    }
    
    static func simulateWakeUp(userBar: inout TimeInterval, lastWork: inout Date?) {
        print("\n🌅 ЭМУЛЯЦИЯ ПРОБУЖДЕНИЯ")
        print("-" * 25)
        
        print("Выберите сценарий:")
        print("1. Обычное утро (сегодня)")
        print("2. Вчера не работал")
        print("3. 2 дня назад работал")
        print("4. 3 дня назад работал")
        print("5. Неделю назад работал")
        
        print("Выберите (1-5): ", terminator: "")
        guard let input = readLine(), let choice = Int(input) else {
            print("❌ Неверный ввод")
            return
        }
        
        let daysBack = [0, 1, 2, 3, 7][choice - 1]
        if choice >= 1 && choice <= 5 {
            lastWork = Calendar.current.date(byAdding: .day, value: -daysBack, to: Date())
            print("✅ Установлена последняя работа: \(daysBack) дней назад")
            
            let daysWithoutWork = calculateDaysWithoutWork(lastWork: lastWork)
            let timeOfDay = getCurrentTimeOfDay()
            let message = getMessage(vertical: daysWithoutWork, horizontal: timeOfDay)
            
            print("📢 Система показала бы сообщение:")
            print("   \"\(message.title)\"")
            print("   Длительность: \(Int(message.duration / 60)) минут")
        } else {
            print("❌ Неверный выбор")
        }
    }
    
    static func simulateWork(userBar: inout TimeInterval, lastWork: inout Date?) {
        print("\n💼 ЭМУЛЯЦИЯ УСПЕШНОЙ РАБОТЫ")
        print("-" * 30)
        
        let oldBar = userBar
        userBar = adaptUserBar(currentBar: userBar, success: true, duration: 25*60)
        lastWork = Date()
        
        print("✅ Пользователь успешно поработал 25 минут")
        print("📈 Планка изменилась: \(Int(oldBar / 60)) → \(Int(userBar / 60)) минут")
        print("🕐 Обновлено время последней работы")
    }
    
    static func simulateFailure(userBar: inout TimeInterval) {
        print("\n❌ ЭМУЛЯЦИЯ НЕУДАЧИ")
        print("-" * 20)
        
        let oldBar = userBar
        userBar = adaptUserBar(currentBar: userBar, success: false, duration: 10*60)
        
        print("❌ Пользователь отклонил предложение или не завершил интервал")
        print("📉 Планка изменилась: \(Int(oldBar / 60)) → \(Int(userBar / 60)) минут")
    }
    
    static func showMessageMatrix() {
        print("\n📋 МАТРИЦА СООБЩЕНИЙ (5x4)")
        print("-" * 40)
        print("Вертикаль = дни без работы, Горизонталь = время дня")
        print()
        
        let timeNames = ["Утро", "День", "Вечер", "Поздно"]
        
        // Заголовок
        print("Дни\\Время", terminator: "")
        for timeName in timeNames {
            print("\t\(timeName)", terminator: "")
        }
        print()
        
        // Строки матрицы
        for vertical in 0...4 {
            print("\(vertical) дней", terminator: "")
            for horizontal in 0...3 {
                let message = getMessage(vertical: vertical, horizontal: horizontal)
                let shortTitle = String(message.title.prefix(12))
                print("\t\(shortTitle)", terminator: "")
            }
            print()
        }
    }
    
    static func showUserBarHistory(currentBar: TimeInterval) {
        print("\n📈 ИСТОРИЯ ПЛАНКИ ПОЛЬЗОВАТЕЛЯ")
        print("-" * 35)
        
        var bar = 25.0 * 60 // Начальная планка 25 минут
        print("Начальная планка: \(Int(bar / 60)) минут")
        
        // Симуляция истории
        let events = [
            ("Успех", true, 25*60),
            ("Успех", true, 27*60),
            ("Неудача", false, 10*60),
            ("Успех", true, 25*60),
            ("Неудача", false, 15*60)
        ]
        
        for (event, success, duration) in events {
            let oldBar = bar
            bar = adaptUserBar(currentBar: bar, success: success, duration: TimeInterval(duration))
            let change = success ? "📈" : "📉"
            print("\(change) \(event): \(Int(oldBar / 60)) → \(Int(bar / 60)) минут")
        }
        
        print("Текущая планка: \(Int(currentBar / 60)) минут")
    }
    
    static func resetSystem(userBar: inout TimeInterval, lastWork: inout Date?) {
        print("\n🔄 СБРОС СИСТЕМЫ")
        print("-" * 15)
        
        userBar = 25 * 60 // 25 минут
        lastWork = nil
        
        print("✅ Система сброшена к начальным значениям")
        print("⏱️  Планка: 25 минут")
        print("🕐 Последняя работа: НЕТ ДАННЫХ")
    }
    
    // MARK: - Вспомогательные функции
    
    static func getMessage(vertical: Int, horizontal: Int) -> (title: String, duration: TimeInterval) {
        let titles = [
            ["Продолжим?", "Еще немного?", "Добавим интервал?", "Финальный рывок?"],
            ["Вернемся к работе?", "Начнем день?", "Время работать!", "Не откладываем!"],
            ["Пора возвращаться!", "Хватит откладывать!", "Начинаем сейчас!", "Время действовать!"],
            ["Серьезно, пора работать!", "Уже 3 дня!", "Начинаем немедленно!", "Хватит прокрастинировать!"],
            ["КРИТИЧНО! Начинаем!", "Уже слишком долго!", "СЕЙЧАС ИЛИ НИКОГДА!", "Экстренный старт!"]
        ]
        
        let durations: [TimeInterval] = [15*60, 20*60, 25*60, 30*60]
        
        let safeVertical = min(max(vertical, 0), 4)
        let safeHorizontal = min(max(horizontal, 0), 3)
        
        return (
            title: titles[safeVertical][safeHorizontal],
            duration: durations[safeHorizontal]
        )
    }
    
    static func adaptUserBar(currentBar: TimeInterval, success: Bool, duration: TimeInterval) -> TimeInterval {
        let minBar: TimeInterval = 2 * 60
        let maxBar: TimeInterval = 2 * 60 * 60
        
        var newBar: TimeInterval
        
        if success {
            newBar = currentBar * 1.1 // +10%
        } else {
            newBar = currentBar * 0.85 // -15%
        }
        
        return min(max(newBar, minBar), maxBar)
    }
    
    static func calculateDaysWithoutWork(lastWork: Date?) -> Int {
        guard let lastWork = lastWork else { return 4 }
        
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: lastWork, to: Date()).day ?? 0
        
        return min(max(days, 0), 4)
    }
    
    static func getCurrentTimeOfDay() -> Int {
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch hour {
        case 6..<12: return 0  // Утро
        case 12..<16: return 1 // День
        case 16..<20: return 2 // Вечер
        default: return 3      // Поздно
        }
    }
}

extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// Запуск демонстрации
EarlyEngagementDemo.main()
