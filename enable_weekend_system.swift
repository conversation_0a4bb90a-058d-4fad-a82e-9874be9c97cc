#!/usr/bin/swift

import Foundation

print("🔧 Включение системы выходных")

// Получаем UserDefaults для uProd
let defaults = UserDefaults(suiteName: "com.local.uProd") ?? UserDefaults.standard

// Включаем систему выходных
defaults.set(true, forKey: "weekendSystemEnabled")
print("✅ Система выходных включена")

// Устанавливаем стратегию 3/1
defaults.set("3/1 (рекомендуемая)", forKey: "weekendStrategy")
print("✅ Установлена стратегия 3/1")

// Устанавливаем минимальные очки для доступа (если нужно)
defaults.set(100, forKey: "userPoints")
print("✅ Установлены очки: 100")

// Синхронизируем изменения
defaults.synchronize()
print("✅ Настройки сохранены")

print("\n🎯 Теперь можно тестировать систему выходных!")
print("Перезапустите uProd или подождите несколько секунд для применения изменений.")
