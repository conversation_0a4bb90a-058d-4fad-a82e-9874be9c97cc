# Система матрицы кнопок (ButtonMatrix)

## 🎯 Назначение

Система ButtonMatrix заменяет захардкоженные кнопки в системе раннего вовлечения на модульную, предсказуемую систему генерации кнопок. Каждая позиция в матрице (вертикаль × горизонталь) генерирует определенный набор кнопок с рассчитанным временем.

## 🏗️ Архитектура

### ButtonComponent
Основной компонент кнопки:
```swift
struct ButtonComponent {
    let text: String           // "Начать работу (35 мин)"
    let duration: TimeInterval // 35 * 60 секунд
    let type: ButtonType       // .primary, .fullSession, etc.
    let context: String        // "calculated_bar", "full_session", etc.
}
```

### ButtonType
Типы кнопок в системе:
- `primary` - Основная кнопка (рассчитанная планка)
- `fullBar` - Полная планка (до горизонтальной дескалации)
- `fullSession` - Полная сессия (52 мин)
- `planMinimum` - План-минимум (3 мин)
- `later` - Отложить
- `snooze` - Через 30 мин

## 📊 Логика генерации кнопок

### Основные правила:
1. **Первая кнопка** - всегда рассчитанная планка (результат вертикальной + горизонтальной адаптации)
2. **Вторая кнопка** - для горизонтали 1+ показывать полную планку (до дескалации)
3. **Третья кнопка** - полная сессия (52 мин), если планка < 52
4. **Служебные кнопки** - "Позже"

### Примеры генерации:

#### Горизонтальная дескалация (планка 30 мин):
- **1-е сообщение (100%)**: `[Начать работу (34 мин)] | [Полная сессия (52 мин)] | [Позже]`
- **2-е сообщение (50%)**: `[Начать работу (17 мин)] | [Полная планка (34 мин)] | [Полная сессия (52 мин)] | [Позже]`
- **3-е сообщение (25%)**: `[Начать работу (8 мин)] | [Полная планка (34 мин)] | [Полная сессия (52 мин)] | [Позже]`
- **4-е сообщение (3 мин)**: `[Начать работу (3 мин)] | [Полная планка (34 мин)] | [Полная сессия (52 мин)] | [Позже]`

#### Вертикальная адаптация (7+ дней):
- **Любое сообщение**: `[Начать работу (3 мин)] | [Полная сессия (52 мин)] | [Позже]`

#### Большая планка (52+ мин):
- **1-е сообщение**: `[Начать работу (52 мин)] | [Позже]` (без полной сессии)

## 🧮 Расчет планок

### Вертикальная адаптация (по дням без работы):
- **0 дней**: планка по градационной системе (GradualGrowthSystem)
- **1 день**: планка × 0.77 (-23%)
- **2-3 дня**: планка × 0.48 (-52%)
- **4-6 дней**: планка × 0.29 (-71%)
- **7+ дней**: план-минимум (3 мин)

### Горизонтальная адаптация (по времени дня):
- **1-е сообщение**: 100% вертикальной планки
- **2-е сообщение**: 50% вертикальной планки (но не меньше 3 мин)
- **3-е сообщение**: 25% вертикальной планки (но не меньше 3 мин)
- **4-е сообщение**: план-минимум (3 мин)

## 🔧 Использование

### Генерация кнопок:
```swift
let buttons = ButtonMatrix.generateButtons(
    vertical: daysLevel,      // 0-4 (дни без работы)
    horizontal: messageIndex, // 0-3 (время дня)
    baseBar: TimeInterval(30 * 60) // базовая планка в секундах
)
```

### Интеграция с Early Engagement:
```swift
let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
    daysWithoutWork: daysLevel,
    messageIndex: messageIndex,
    baseBarMinutes: initialBarMinutes
)
```

### Форматирование для отладки:
```swift
// Краткое отображение
let buttonTexts = ButtonMatrix.formatButtonsForDebug(buttons)
// Результат: "[Начать работу (35 мин)] | [Полная сессия (52 мин)] | [Позже]"

// Детальное отображение
let detailed = ButtonMatrix.createDetailedButtonDescription(buttons)
// Результат: многострочное описание с типами и временем
```

## 🧪 Тестирование

### Тестовые сценарии:
1. **Базовая генерация** - проверка создания кнопок
2. **Горизонтальная дескалация** - проверка уменьшения времени
3. **Фиксированные уровни** - проверка 15 мин и 3 мин
4. **Вертикальная адаптация** - проверка план-минимума для 7+ дней
5. **Полная сессия** - показ/скрытие при планке < 52 мин
6. **Полная планка** - показ для горизонтали 1+
7. **Полная матрица** - все 20 комбинаций (5×4)
8. **Форматирование** - корректность отображения

### Запуск тестов:
```bash
swift Tests/ButtonMatrixTest.swift
```

## 📱 Интеграция с отладочным окном

### До (захардкожено):
```swift
🔘 Кнопки: "\(processedMessage.buttonText)" | "Не сейчас" | "Через 30 мин"
```

### После (модульно):
```swift
let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(...)
let buttonTexts = ButtonMatrix.formatButtonsForDebug(buttons)
🔘 Кнопки: \(buttonTexts)

🔘 Детальная информация о кнопках:
   Кнопка 1: [Начать работу (35 мин)] - тип: primary, время: 35 мин
   Кнопка 2: [Полная сессия (52 мин)] - тип: fullSession, время: 52 мин
   Кнопка 3: [Позже] - тип: later, время: 0 мин
```

## 🔄 Преимущества новой системы

1. **Модульность** - кнопки как компоненты, не просто текст
2. **Предсказуемость** - каждая позиция матрицы → определенный набор кнопок
3. **Расширяемость** - легко добавить новые типы кнопок
4. **Тестируемость** - полное покрытие тестами всех сценариев
5. **Отладочность** - детальная информация о каждой кнопке
6. **Совместимость** - интеграция с существующей системой

## 🚀 Дальнейшее развитие

### Возможные улучшения:
1. **Интеграция с реальными окнами** - использование ButtonMatrix в EarlyEngagementWindow
2. **Дополнительные типы кнопок** - "Через 15 мин", "Микро-сессия (1 мин)"
3. **Условная логика** - скрытие кнопок на основе истории пользователя
4. **Персонализация** - адаптация кнопок под предпочтения пользователя
5. **A/B тестирование** - разные варианты кнопок для экспериментов

### Файлы системы:
- `SimplePomodoroTest/ButtonMatrix.swift` - основная логика
- `Tests/ButtonMatrixTest.swift` - тесты системы
- `SimplePomodoroTest/EarlyEngagementDebugWindow.swift` - интеграция с отладкой
- `docs/button-matrix-system.md` - документация

## 📋 Связанные системы

- **Message Construction Matrix** - матрица сообщений 5×4
- **Early Engagement System** - система раннего вовлечения
- **Gradual Growth System** - система постепенного роста планок
- **Full Session System** - система полных сессий (52 мин)
- **Session Growing System** - система роста сессий после завершения
