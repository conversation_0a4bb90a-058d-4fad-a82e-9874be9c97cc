# SimpleUnifiedSystem - Полностью унифицированная система uProd

## 🎯 Цель
Создать единую систему напоминаний для ВСЕХ случаев с полным соблюдением принципа DRY:
- ✅ **Формальные интервалы** (Pomodoro завершен)
- ✅ **Неформальные сессии** (обнаружена работа без таймера)
- ✅ **Эскалация напоминаний** (continuous work reminders)
- ✅ **Статус-бар таймер** (растущий счетчик переработки)
- ✅ **Проверка активности** (единая для всех типов)

## 🏗️ Архитектура

### 🔄 SimpleUnifiedSystem - Ядро системы
**Единый класс для всех типов интервалов:**
```swift
// Формальные интервалы (запускается через onOvertimeColorChanged)
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "formal", isTest: false, intervalDuration: PomodoroTimer.workDuration)

// Неформальные интервалы (запускается при обнаружении работы)
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: false, intervalDuration: 52 * 60)

// Тестирование (ускоренный режим)
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: true, intervalDuration: 52 * 60)
```

### 📊 Единая система уровней эскалации
**Используется `OvertimeConfig` логика:**
- **Уровень 0** (0-1 мин): 🎉 Session completed! / 🌿 Time for rest
- **Уровень 1** (1-3 мин): ⚠️ Yellow zone
- **Уровень 2** (3-5 мин): 🟠 Orange zone  
- **Уровень 3** (5-10 мин): 🔴 Red zone
- **Уровень 4** (10+ мин): 🚨 Critical zone

### ⏱️ Единый статус-бар таймер
**Растущий счетчик для всех типов:**
- **Отображение**: Общее время (интервал + переработка) в формате ⏱️ 52:23
- **Формальные интервалы**: Начинают с настроенной длительности (25 или 52 минуты)
- **Неформальные интервалы**: Начинают с 52:00 (фиксированно)
- **Цвета**: Рассчитываются по времени ПЕРЕРАБОТКИ (не общему времени)
  - 0-1 мин переработки: серый ⏱️
  - 1-2 мин переработки: желтый ⚠️
  - 3-4 мин переработки: оранжевый 🟠
  - 5+ мин переработки: красный 🔴
- **Тест-режим**: Эскалация ускорена (3 сек = 1 мин), но отображение показывает реальное время

## 🎮 Единая проверка активности

### UnifiedActivityChecker - DRY принцип
**Единые правила для ВСЕХ случаев:**
- `UnifiedActivityChecker.shared.isUserCurrentlyActive()` - активен ли сейчас (15 сек)
- Отслеживание: мышь, клавиатура, клики, скролл, медиа-приложения
- **Если неактивен** → окна НЕ показываются, таймер продолжает идти
- **При возвращении** → показывается актуальный уровень эскалации

## 📱 Единые окна и кнопки

### ModernCompletionWindow - Единое окно для всех
**Режимы работы:**
- `.sessionCompleted` - для формальных интервалов
- `.restSuggestion` - для неформальных интервалов

**Единые кнопки (✅ РЕАЛИЗОВАНО):**
- **"Take a break"** - зеленая градиентная (для всех типов)
- **"I need a couple of minutes"** - фиолетовый цвет через `UnifiedButtonConfig.postponeColor` (единый для всех типов интервалов)

## 🔧 Техническая реализация

### Протокол делегата
```swift
protocol SimpleUnifiedSystemDelegate: AnyObject {
    func showEscalationReminder(minutes: Int, level: Int, for intervalType: String)
    func updateStatusBar(overtimeMinutes: Int, totalMinutes: Int, seconds: Int)
    func recordIntervalStatistics(duration: TimeInterval, intervalType: String)
}
```

### Интеграция с AppDelegate
```swift
// Формальные интервалы
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "formal", isTest: false)

// Неформальные интервалы  
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: true)

// Остановка при отдыхе
SimpleUnifiedSystem.shared.stopEscalation()
```

### Статистика (✅ РЕАЛИЗОВАНО)
**Поддержка типов интервалов:**
```swift
// Расширенная структура CompletedInterval
struct CompletedInterval {
    let date: Date
    let duration: TimeInterval
    let projectId: UUID?
    let intervalType: String // "formal" или "informal" - ДОБАВЛЕНО
}

// Запись статистики через делегат
statisticsManager.recordCompletedInterval(duration: duration, intervalType: "formal")
statisticsManager.recordCompletedInterval(duration: duration, intervalType: "informal")

// Анализ по типам интервалов
let formalIntervals = statisticsManager.getCompletedIntervalsByType("formal")
let informalIntervals = statisticsManager.getCompletedIntervalsByType("informal")
```

## 🧪 Тестирование

### Тест-режим
- **Ускорение**: 10 секунд = 1 минута эскалации
- **Отображение**: Реальное время (не ускоренное)
- **Активность**: Проверка отключена в тест-режиме

### Кнопка тестирования
```swift
// В SettingsWindow
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: true)
```

## 🎯 Преимущества унифицированной системы

### ✅ Полное соблюдение DRY
- **Один класс** для всех типов интервалов
- **Одна логика** эскалации и таймеров
- **Один статус-бар** для всех случаев
- **Одна проверка активности** для всех компонентов

### ✅ Легкость расширения
- **Новые кнопки**: Добавить колбэк в `ModernCompletionWindow`
- **Новые типы интервалов**: Передать новый `intervalType` в `startSimpleEscalation()`
- **Изменение времени эскалации**: Один метод `getEscalationLevel()`
- **Изменение таймера**: Один метод `updateStatusBar()`

### ✅ Консистентность
- **Одинаковое поведение** для формальных и неформальных интервалов
- **Единые анимации** и позиционирование окон
- **Единые цвета** и эмодзи в статус-баре
- **Единая логика активности** для всех случаев

## 🚀 Масштабирование

### Добавление новых функций
1. **Общее время (интервал + переработка)**:
   ```swift
   func startSimpleEscalation(for intervalType: String, intervalDuration: TimeInterval = 0)
   ```

2. **Новые типы кнопок**:
   ```swift
   // В ModernCompletionWindow
   var onCustomAction: (() -> Void)?
   ```

3. **Статистика по типам**:
   ```swift
   statisticsManager.recordInterval(type: intervalType, duration: minutes * 60)
   ```

### Архитектурные принципы
- **Единая точка конфигурации** - все настройки в одном месте
- **Протокол-делегат** - легко добавлять новые методы
- **Слабые ссылки** - предотвращение утечек памяти
- **Модульность** - каждый компонент отвечает за свою область

## 📝 Текущий статус

### ✅ Реализовано
- Единая система эскалации для формальных и неформальных интервалов
- Растущий статус-бар таймер с реальным временем
- Интеграция с существующей `OvertimeConfig` логикой
- Проверка активности через `UnifiedActivityChecker`
- Остановка старых конфликтующих таймеров
- Тест-режим с разумным ускорением
- Правильные окна для разных типов интервалов

### 🔄 В процессе
- Унификация текста и цвета кнопки "I need a couple of minutes"
- Добавление поддержки общего времени (интервал + переработка)
- Расширение статистики для учета типов интервалов

### 🎯 Планируется
- Создание единой конфигурации кнопок в `SimpleUnifiedSystem`
- Добавление новых типов кнопок через единую систему
- Интеграция с системой проектов для статистики

## 🔧 Решение архитектурных вопросов

### 1. Расширение кнопок
**Проблема**: Как добавлять новые кнопки после "Take a break"?
**Решение**:
```swift
// В ModernCompletionWindow добавить новые колбэки
var onLongBreak: (() -> Void)?
var onCustomAction: (() -> Void)?

// В setupSessionCompletedButtons() создать дополнительные кнопки
button4 = createGradientButton(title: "Long break", color: .blue, isSmall: true)
button4.action = #selector(longBreakClicked)
```

### 2. Статистика с типами интервалов
**Проблема**: Как учитывать формальные/неформальные интервалы в статистике?
**Решение**:
```swift
// Расширить CompletedInterval
struct CompletedInterval {
    let intervalType: String // "formal" или "informal"
    let escalationLevel: Int // максимальный достигнутый уровень
}

// В AppDelegate при завершении
statisticsManager.recordInterval(
    type: intervalType,
    duration: totalDuration,
    maxEscalationLevel: currentLevel
)
```

### 3. Общее время (интервал + переработка)
**Проблема**: Показывать общее время вместо только переработки?
**Решение**:
```swift
// В SimpleUnifiedSystem
func startSimpleEscalation(for intervalType: String, intervalDuration: TimeInterval = 0) {
    self.intervalDuration = intervalDuration
}

// В checkEscalation()
let totalMinutes = Int(intervalDuration / 60) + displayMinutes
delegate?.updateStatusBar(minutes: totalMinutes, seconds: displaySeconds)
```

### 4. Логика активности при уходе
**Проблема**: Что происходит когда пользователь ушел?
**Решение**: ✅ Уже реализовано
- Таймер продолжает идти (время учитывается)
- Окна НЕ показываются при неактивности
- При возвращении показывается актуальный уровень

### 5. Унификация кнопки "I need a couple of minutes"
**Проблема**: Разные тексты и цвета для формальных/неформальных
**Решение**:
```swift
// В SimpleUnifiedSystem создать конфигурацию
struct UnifiedButtonConfig {
    static let postponeText = "I need a couple of minutes"
    static let postponeColor = NSColor(red: 0.7, green: 0.6, blue: 0.8, alpha: 1.0)
    static let breakText = "Take a break"
    static let breakColor = NSColor.green
}

// В ModernCompletionWindow использовать единую конфигурацию
button1 = createDashedButton(
    title: UnifiedButtonConfig.postponeText,
    color: UnifiedButtonConfig.postponeColor
)
```

## 🚨 Решенные проблемы

### ✅ Конфликт таймеров в статус-баре
**Проблема**: Два таймера обновляли статус-бар (старая ⚠️ и новая ⏱️ системы)
**Решение**: Отключили старую систему для `.overtime` состояния:
```swift
// В AppDelegate
pomodoroTimer.onTimeUpdate = { [weak self] (timeRemaining, overtimeElapsed) in
    if self?.pomodoroTimer.state != .overtime {
        self?.updateStatusItem() // Только для НЕ-переработки
    }
}
```

### ✅ Неправильные окна для неформальных интервалов
**Проблема**: Показывался "Session completed" вместо "Time for rest"
**Решение**: Правильная логика в `showEscalationReminder()`:
```swift
if intervalType == "informal" && level == 0 {
    // Пропускаем - окно уже показано правильное
} else {
    // Показываем эскалацию
}
```

### ✅ Таймер не останавливался при "Take a break"
**Проблема**: SimpleUnifiedSystem продолжал работать после начала отдыха
**Решение**: Добавили остановку в `startUnifiedBreak()`:
```swift
SimpleUnifiedSystem.shared.stopEscalation()
```

### ✅ Формальные интервалы не использовали унифицированную систему
**Проблема**: Формальные интервалы использовали старую систему, показывали время переработки с 0:00
**Решение**: Включили `onOvertimeColorChanged` callback и исправили расчет длительности:
```swift
pomodoroTimer.onOvertimeColorChanged = { [weak self] (colorLevel: Int) in
    DispatchQueue.main.async {
        self?.handleFormalOvertimeWithUnifiedSystem(colorLevel: colorLevel)
    }
}

// ИСПРАВЛЕНО: Правильная длительность интервала
let formalDuration = PomodoroTimer.workDuration  // Вместо timeRemaining + overtimeElapsed
```

### ✅ Неформальные интервалы использовали старую систему
**Проблема**: Неформальные интервалы запускали `UnifiedReminderSystem` вместо `SimpleUnifiedSystem`
**Решение**: Заменили вызов на унифицированную систему:
```swift
// БЫЛО (строка 2588):
unifiedSystem.startEscalation(for: "informal", isTest: isTest)

// СТАЛО:
SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: false, intervalDuration: 52 * 60)
```

### ✅ Неправильные цвета статус-бара
**Проблема**: Цвета рассчитывались по общему времени вместо времени переработки
**Решение**: Разделили параметры в протоколе:
```swift
func updateStatusBar(overtimeMinutes: Int, totalMinutes: Int, seconds: Int)
// overtimeMinutes - для расчета цветов
// totalMinutes - для отображения
```

### ✅ Унификация кнопок и статистики
**Добавлено**:
- `UnifiedButtonConfig` для единого стиля кнопок
- Поле `intervalType` в статистику
- Методы анализа статистики по типам интервалов

## 🔮 Планируемые улучшения

### 1. Автоматические отдыхи при долгом отсутствии
**Проблема**: Если пользователь ушел на 17+ минут, система продолжает показывать переработку
**Решение**:
- Отслеживать неактивность пользователя
- После 17 минут неактивности автоматически засчитывать отдых
- Для формальных интервалов: записать в статистику как полноценный отдых
- Для неформальных: сбросить счетчик переработки
- Запустить параллельный счетчик отдыха

### 2. Система остановки таймера при уходе
**Проблема**: Таймер продолжает идти когда пользователь неактивен
**Решение**:
- Останавливать таймер при обнаружении неактивности (1+ минута)
- Возобновлять при возвращении пользователя
- Сложная логика: 4 блока по 15 секунд для определения активности

### 3. Параллельный счетчик отдыха
**Проблема**: Нет отслеживания качества отдыха в реальном времени
**Решение**:
- Запускать счетчик отдыха параллельно с основным таймером
- Отслеживать активность во время отдыха
- Записывать в статистику только завершенные отдыхи
