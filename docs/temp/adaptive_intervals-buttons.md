Так, ну короче, сегодня мы работаем, продолжаем работать с над системой внедрения раннего пропуждения, адаптивных интервалов. Инструкции в соответствующих местах лежат Можешь изучить для лучшего понимания @/Users/<USER>/EVO/uProd/docs/early-engagement-task.md 

Сегодня мы будем работать над кнопками. Над матрицей кнопок. Смотри 166-176 строки в файле @/Users/<USER>/EVO/uProd/todo.md 

Для лучшего понимания по кнопкам советую изучить `/docs/session-growing-system.md` и системы полных планок `/docs/full-session-system.md`

У нас же же есть некая матрица сообщений для раннего пробуждения. Вертикаль и горизонталь. Где рассчитывается сколько минут должен работать. Вот также понятно и предсказуемо должно быть с кнопками. 

А то сейчас там просто тупо кнопки в виде текста. А должно быть как модули. Например: 
- Кнопка текущей планки
- Кнопка полной сессии (52 мин) `/docs/full-session-system.md`
- Кнопка план-минимум (3 мин)
- и т.д.

Единственное, что пока понятно:
- Первая кнопка (основная) будет размером текущей планки. Но не всегда, там есть исключения. В предложениях по горизонтали (в течении дня) вместо нее будут дескалирующие планки (например -50% для второго сообщения). Например если текущая планка сегодня 30 минут, то для первого сообщения дня будет 30 мин, а для второго 15. Потом третье сообщение фиксировано 15 мин. И четвертое фиксировано 3 мин.
- В матрице по горизнтали для 3 и 4 уровней первая кнопка уже понятна. 15 минут и 3 мин в соответсвии с 3 и 4 уровнем горизонтали
- Дополнительная кнопка - полная сессия. 

Матрица немного усложняется тем, что будет не одна кнопка, а несколько. 

Все это надо внедрить так, чтобы в отладочном окне я мог выбрать окно по вертикали, горизонтали и увидеть кнопки. Но не просто текстом захардкожены, а чтобы подставилась нужная, заранее определенная кнопка. Типа как компонент. 

- Может добавить элементы Кнопка 1 - [value], кнопка 2 - [value]. Чтобы прям видеть что и как туда подставляется. КОмпонент такой-то, с таким-то временем. Не знаю.

В отладочном окне сейчас работает только расчет текущей планки (расчетная планка). Тексты и кнопки там просто захаркожены, что неправильно. 

Давай рассмотрим примеры того как должны формироваться кнопки. 

1. Допустим он работал вчера (0 уровень по вертикали). ИЗначальный интервал +30 мин. 

📍 Позиция: 0 дней (вчера работал) × 1-е сообщение (сразу при пробуждении)

⏱️ Изначальная планка: 30 мин
🧮 Расчет: 30 +14% × 1.0 (100%) = 35 мин
✅ Рассчитанная планка: 35 мин

📝 Заголовок: "Продолжаем работу с uMemo?"
📄 Текст: "Вчера был продуктивный день. Начнем с 35 минут?"
🔘 Кнопки: "[Начать работу (35 мин)]" | "[Начать полный интервал (52 мин)]" | "Позже"

2. Второй пример. Допустим он 7 и более дней не работал над приоритетным проектом
📍 Позиция: 7+ дней не работал × 1-е сообщение (сразу при пробуждении)
🎯 Приоритетный проект: uMemo

⏱️ Изначальная планка: 30 мин
🧮 Расчет: план-минимум (3 мин) × 1.0 (100%) = 3 мин
✅ Рассчитанная планка: 3 мин

📝 Заголовок: "Пора возвращаться к жизни!"
📄 Текст: "Слишком долго без uMemo. Начнем с 3 минут"
🔘 Кнопки: "[Начать работу (3 мин)]" | "[Начать полный интервал (52 мин)]" | "Позже"

При этом надо как-то учесть момент что при горизонтали для второго и последующих сообщений нужно второй кнопкой показывать:
- Либо полную расчетную планку. Мы же вычли 50% для второо сообщенияг. Например текущая планка была 30 мин. Второе сообщение будет 15 мин. Тогда вторая кнопка будет 30 мин. 
- Либо полную сессию (52 мин). Я не знаю что лучше. Может это нужно показывать третьей кнопкой, а может это уже перебор. 
- Возможно полную сессию лучше пока не надо предлагать. Лучше его текущую планку. Чтобы небыло переизбытка кнопок. Но что если текущая планка у него маленькая? Хотя если маленькая, то зачем ему 52? Пусть сначала начнет работать нормально, потом о 52 минутах поговорим. Но с другой стороны может быть случай когда я например не могу начать работу сразу рано утром. И начинаю через час. А если я хочу начать полноцненный нормальный интервал? Корчое не знаю, идей нет. 

---

Как видишь система получается запутанной. Много зависимостей и сложных условий. Поэтому я хочу какую-то простую и понятную систему. 

Давй сначала обсудим здесь концепцию, найдем правильное решение. И потом только будем внедрять. Изучи существующий проект и все что нужно через codebase-retrieval.

Используй MCP-сервер Sequential Thinking. Если это необходимо. 


---
## Только 3 кнопки
Не должно быть альтернативных кнопок "полная сессия 52 мин" и "полный планка" вместе.
Иначе получается 4 кнопки, а это перегруз интерфейса. Максимум 3. 

Как быть? Надо найти элегантное решение. Вот мысли: 
- Если есть кнопка полной планки, то не показывать "полную сессию" 
    - Но это решение грубое. И не решает проблему. Что если у меня планка 52 минуты и я не начал сразу после пробуждения? Все? Поезд ушел? Хотя у меня все равно будет 52, потому что я вырастил планку до 52. Но все равно не нравится решение. 
    - Тем более там будет "взращивание сессии" до 52 в любом случае
- Экспериментально - вообще не показывать им время планки в кнопках. 
    - Но тут есть проблема в том, что тогда они не смогут выбрать полную сессию когда надо. Может и не страшно? Сделают текущую планку, а дальше черз взрощенную дотянут до полной? 
- решения скорее всего нет, либо показывать 3 либо 4
- можно объединить и показывать кнопку "начать полную сессию"
- либо сделать условия *если, то*, и показывать что-то одно. Но это тоже так себе. Хочется чтобы было 3 кнопки.
- может есть смысл вычислять разницу между 52 и максимальной планкой. Если небольшая, то... Иначе... Что? Хрен знает, просто идеи
-;например если... Он заканчивал >90% последних интервалов, то можно ему показать 52, если нет, то нет.  Мы же может все что угодно проверять в "если"
- надо ещё учитывать что 52 минуты вообще может скрываться, если пользователь брал их и не выполнил. 
- а может и правда не нужны 52 минуты. Но наверное решение там, где нужно проверить есть ли смысл ему давать или нет. Если он молодец в целом, то можно. Если не молодец, то не надо. 
- хотя как это решает проблему? Допустим я молодец и что мне показывать 4 кнопки? Все равно проблема не решена. 
- полный интервал должен быть показан всегда. От этого и надо отталкиваться. Он важнее. Но тогда если это условие, и условие что кнопок должно быть 3, то решения просто нет. Кнопку "Позже" убрать мы не можем. Лучшее решение - объединить их. Может если разница между ними небольшая

- Показывать только fullBar, пусть пользователь дойдет до 52 через естественный рост GradualGrowthSystem.

