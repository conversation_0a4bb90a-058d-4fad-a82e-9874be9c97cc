# 🎯 DailyWorkloadManager - Менеджер ежедневной нагрузки

## 📋 Назначение системы

### Главная цель
**Не надоедать пользователю излишними сообщениями** - система должна понимать когда пользователь достиг разумной дневной нормы работы и прекратить предложения.

### Дополнительные цели
1. **Лимитировать переработчиков** - предотвращать ситуации когда пользователь делает слишком много в первый день, а потом выгорает
2. **Четко определять завершение рабочего дня** - понимать когда задать вопросы обратной связи
3. **Собирать обратную связь** - спрашивать "Сложно ли было сегодня?" для адаптации системы

Система срабывает либо в рабочие часы. Либо при достижении дневной планки продуктивности.

## 🎮 Принцип работы

Система использует **очки продуктивности** для измерения дневной нагрузки. У каждого пользователя есть **"планка очков"** (аналог currentUserBar) - целевое количество очков на день. Когда цель достигнута, система значительно сокращает количество предложений.

## 📊 Система очков

### Базовая формула
```
Очки сессии = Базовые очки + Контекстный бонус
Базовые очки = минуты работы (1 мин = 1 очко)
```

### Контекстные бонусы по уровням пользователя

**Beginner** (не работал 3+ дня):
- Микросессии (1-5 мин): +100% бонус (3 мин = 3+6 = 9 очков)
- Короткие (6-15 мин): +100% бонус (10 мин = 10+10 = 20 очков)  
- Длинные (16+ мин): без бонуса (30 мин = 30+0 = 30 очков)

**Starter** (1-2 дня работы):
- Микросессии: +100% бонус
- Средние (6-25 мин): +50% бонус (20 мин = 20+10 = 30 очков)
- Длинные (26+ мин): +33% бонус (52 мин = 52+17 = 69 очков)

**Regular** (3+ дня стабильной работы):
- Микросессии: без бонуса (3 мин = 3+0 = 3 очка)
- Средние: без бонуса
- Длинные (25+ мин): +50% бонус (52 мин = 52+26 = 78 очков)

### Примеры расчета

**Новичок:**
- 5×3мин = (3+6)×5 = 45 очков ✨ (микросессии ценны)
- 1×52мин = 52+0 = 52 очка

**Опытный:**
- 5×3мин = 3×5 = 15 очков
- 1×52мин = 52+26 = 78 очков ✨ (длинные сессии ценнее)

## 🎯 Планка очков (адаптивная)

### Стартовые значения
- **Beginner**: 30 очков (~3-4 микросессии)
- **Starter**: 60 очков (~2-3 средние сессии)
- **Regular**: 120 очков (~1-2 длинные сессии)
- **Advanced**: 200 очков (~3-4 длинные сессии)

### Адаптация планки
```swift
if (сегодняшние_очки >= планка) {
    планка = планка × 1.15  // +15% при успехе
} else {
    планка = планка × 0.85  // -15% при неудаче
}
```

**Важно:** Планка растет постепенно, не скачками. Если новичок сделал 200 очков, планка станет не 200, а 35 (30×1.15).

## ⏰ Адаптивная система предложений

### Основная логика
```swift
func shouldOfferSession() -> Bool {
    // 1. Проверяем рабочие часы
    if (!isWithinWorkingHours()) return false
    
    // 2. Проверяем достижение цели
    let progress = todayPoints / targetPoints
    if (progress >= 1.0) {
        return shouldOfferBonusSession()  // Редко
    }
    
    // 3. Проверяем отказы (для микросессий)
    if (microSessionRefusals >= 3) {
        showEndDayDialog()
        return false
    }
    
    return true
}
```

### Адаптивные интервалы
Базовый интервал модифицируется по формуле:
```
Итоговый интервал = Базовый × Время_дня × Отказы × Прогресс
```

**Время дня:**
- Утром (9-12): ×0.8 (чаще на 20%)
- Днем (12-15): ×1.0 (обычно)  
- Вечером (15-18): ×1.3 (реже на 30%)

**Отказы (для микросессий):**
- 0 отказов: ×1.0 (30 мин)
- 1 отказ: ×2.0 (60 мин)
- 2 отказа: ×3.0 (90 мин)
- 3+ отказа: показать диалог "Может хватит на сегодня?"

**Прогресс к цели:**
- 0-80%: ×1.0 (обычно)
- 80-100%: ×2.0 (реже)
- 100%+: ×4.0 (очень редко)

## 🏗️ Интеграция с существующими системами

### Логика показа окон после сессий

**Микросессии (<15 мин):**
- `startAdaptiveRest()` → БЕЗ отдыха, БЕЗ таймера
- `scheduleNextMicroSessionOffer()` → через 30 мин **MicroSessionOfferWindow**

**Средние сессии (15-30 мин):**
- `startAdaptiveRest()` → 15 мин отдыха С таймером
- После отдыха → **BreakEndWindow**

**Длинные сессии (30+ мин):**
- `startAdaptiveRest()` → 17 мин отдыха С таймером
- После отдыха → **BreakEndWindow**

### Существующие окна (используем как есть)
1. **EarlyEngagementWindow** - утренние предложения
2. **SessionGrowthWindow** - взращивание сессий (показывается ДО startAdaptiveRest)
3. **BreakEndWindow** - предложения после отдыха 15+ минут

### Новые окна (нужно создать)
1. **MicroSessionOfferWindow** - "Хочешь еще поработать?" после микросессий через 30/60/90 мин
2. **DailyGoalCompletedWindow** - обратная связь при достижении цели
3. **GentleAfternoonOfferWindow** - мягкое предложение через 3 часа после отказов

### Унифицированный дизайн
Создать базовый класс `BaseStyledWindow` с едиными стилями:
- Радиальные градиенты из `ModernCompletionWindow`
- Единые методы создания кнопок
- Общая система цветов и шрифтов

### Интеграция в существующий код
```swift
// EarlyEngagementSystem
func shouldShowEngagementMessage() -> Bool {
    return DailyWorkloadManager.shared.shouldOfferSession()
}

// AppDelegate (микросессии)
func scheduleNextMicroSessionOffer() {
    let baseInterval: TimeInterval = 30 * 60
    let adaptedInterval = DailyWorkloadManager.shared.getNextOfferInterval(baseInterval)
    
    Timer.scheduledTimer(withTimeInterval: adaptedInterval, repeats: false) { _ in
        if DailyWorkloadManager.shared.shouldOfferSession() {
            self.showMicroSessionOffer()
        }
    }
}
```

## 🎯 Обработка особых случаев

### Взращивание сессий
**Важно:** Если пользователь начал с 3 мин и дорос до 30 мин - это ОДНА сессия на 30 минут, не несколько маленьких.

### Достижение дневной цели
Когда `todayPoints >= targetPoints`:
1. Показать **DailyGoalCompletedWindow** с вопросом "Как прошел день?"
2. Перейти в режим редких предложений (одно после обеда максимум)
3. Если отказался от послеобеденного предложения - больше не беспокоить

### Система отказов для микросессий
**Эскалация интервалов:**
1. 1-й отказ: следующее предложение через 60 мин (30×2)
2. 2-й отказ: следующее предложение через 90 мин (30×3)
3. 3-й отказ: диалог "Может хватит на сегодня?"
   - "Да, хватит" → отстаем + планируем мягкое предложение через 3 часа
   - "Нет, просто чуть позже" → продолжаем с интервалом 90 мин + планируем мягкое предложение

### Мягкое послеобеденное предложение
Показывается через 3 часа после любого ответа на вопрос "Может хватит на сегодня?", если до конца рабочего дня остается 3+ часа.
Текст: "Может все-таки поработаем? Настроение могло измениться."

### Дни лени
Система обрабатывает через:
- Эскалацию интервалов для микросессий
- Кнопку "Later" в BreakEndWindow (тоже считается отказом)
- Мягкие предложения через 3 часа

## 📈 Примеры работы системы

### Новичок (первый день)
```
Планка: 30 очков
9:30 - EarlyEngagementWindow: 3 мин → Да → 9 очков (30%)
10:00 - MicroSessionOfferWindow: 5 мин → Да → +15 очков = 24 очка (80%)
10:45 - MicroSessionOfferWindow: "До цели немного!" → Да → +9 очков = 33 очка (110%)
11:30 - Цель достигнута! → DailyGoalCompletedWindow "Как прошел день?"
14:30 - GentleAfternoonOfferWindow: "Может все-таки поработаем?" → Нет → больше не беспокоить
Результат: Планка завтра = 35 очков
```

### Опытный пользователь
```
Планка: 120 очков
10:00 - EarlyEngagementWindow: 25 мин → Да → 78 очков (65%)
12:00 - BreakEndWindow: "До цели 42 очка" → Start New Session, 30 мин → +45 очков = 123 очка (102%)
12:30 - Цель достигнута! → DailyGoalCompletedWindow "Как прошел день?"
15:30 - GentleAfternoonOfferWindow: "Может все-таки поработаем?" → Нет → больше не беспокоить
Результат: Планка завтра = 138 очков
```

### Пример эскалации отказов (микросессии)
```
10:00 - MicroSessionOfferWindow: "Хочешь поработать?" → Нет (1-й отказ)
11:00 - MicroSessionOfferWindow: "Хочешь поработать?" → Нет (2-й отказ)
12:30 - MicroSessionOfferWindow: "Хочешь поработать?" → Нет (3-й отказ)
12:30 - Диалог: "Может хватит на сегодня?" → "Да, хватит"
15:30 - GentleAfternoonOfferWindow: "Может все-таки поработаем?" → "Да, попробуем" → минималочка 3 мин
```

## 🔧 Технические детали

### Основной класс
```swift
class DailyWorkloadManager {
    private var currentPointsBar: Int = 30
    private var todayRefusals: Int = 0
    private var userLevel: UserLevel
    private var workingHours: TimeRange
    
    func shouldOfferSession() -> Bool
    func getNextOfferInterval(baseInterval: TimeInterval) -> TimeInterval
    func calculateSessionPoints(minutes: Int) -> Int
    func handleSessionCompleted(minutes: Int)
    func handleUserRefusal()
    func hasReachedDailyGoal() -> Bool
}
```

### Интеграция с существующими системами
- **EarlyEngagementSystem**: проверка `shouldOfferSession()` перед показом
- **Микросессии**: адаптивные интервалы через `getNextOfferInterval()`
- **Обычные интервалы**: аналогично микросессиям
- **StatisticsManager**: запись очков в дополнение к времени

---

*Система обеспечивает органичную адаптацию под пользователя и решает проблему навязчивых предложений.*
