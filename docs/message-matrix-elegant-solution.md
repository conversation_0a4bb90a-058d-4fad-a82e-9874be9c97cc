# 💬 Система текстов раннего вовлечения: Элегантное решение

> **Статус:** ✅ Система полностью реализована и работает.
>
> **JSON с текстами:** `/SimplePomodoroTest/Resources/EarlyEngagementTexts.json`
>
> **Архивный документ:** `early-engagement-texts-system.md` (сохранен для истории)

## 🎯 Суть решения

**Ключевая идея:** Разделить ответственность между заголовком и подзаголовком для минимизации объема работы при максимальной гибкости.

### **📋 Принцип разделения:**
- **ЗАГОЛОВОК** = Уровень (философия) + Горизонталь (срочность) = уникальный для каждой ячейки
- **ПОДЗАГОЛОВОК** = Универсальная горизонтальная эскалация = 4 варианта на ВСЕ уровни

## 📊 Объем работы

### **⚠️ Режим ВОЗВРАЩЕНИЯ:**
- **Заголовки:** 4 уровня × 4 горизонтали = **16 уникальных заголовков**
- **Подзаголовки:** **4 универсальных** (переиспользуются для всех уровней)
- **Итого:** 20 текстов

### **🔥 Режим STREAK:**
- **Заголовки:** **4 базовых с переменными** + специальные для ключевых дней
- **Подзаголовки:** **Те же 4 универсальных**
- **Итого:** 8 текстов (+ специальные)

### **🎯 ОБЩИЙ ИТОГ:**
**24 уникальных текста** вместо 40+ в полной матрице = **экономия 40%**

## 🔄 Универсальные подзаголовки (горизонтальная эскалация)

> **Работают для ВСЕХ уровней и режимов**
> **Основаны на [принципе раннего начала](../principles/early-start-principle.md)**

### **1-е сообщение (100% планки):**
Главный тейк - "Сразу после пробуждения - самое легкое время начать".  

**Варианты:**
- "Сразу после пробуждения - самое легкое время начать"
- "Утром, пока мозг свежий - лучший момент для старта"
- "После пробуждения сопротивление минимально - начинай"
- "Первые минуты дня - золотое время для старта"
- "Утром концентрация максимальна - используй это"
- "Сейчас минимальное сопротивление - действуй"
- "Утренняя энергия - используй ее для главного"
- "Пока внимание не рассеялось - фокусируйся"

### **2-е сообщение (50% планки, через 20 минут):**
Крайне желательно сказать что-то типа "Используй сокращенную сессию" 
Шаблон: Временная метка + Основной Мотивационный утвержденный текст + Сокращенная сессия

**Варианты:**
- "20 минут после пробуждения - увлечься сложнее, но легко переключиться. Сокращенная сессия"
- "Полчаса утра прошло - пока легко сосредоточиться. Используй сниженный интервал"
- "20 минут без дела - сопротивление растет, но шанс есть. Начни сокращенную сессию"
- "20 минут прошло - пора переключаться. Используй сокращенную сессию"
- "Отвлечения накапливаются - возвращайся к важному. Используй сокращенную сессию"

### **3-е сообщение (25% планки, через час):**
Крайне желательно сказать что-то типа Используй сокращенную сессию" . Еще более скоращенную чем 2-е сообщение
Шаблон: Временная метка + Основной Мотивационный утвержденный текст + Сокращенная сессия 

**Варианты:**
- "Час после пробуждения - начинать сложнее, но реально. Мини-сессия как спасение"
- "Час утра прошел - внимание рассеяно, но можно собраться. Экстренная сессия"
- "Час без дела - сопротивление серьезное, но шанс есть. Используй план-минимум"
- "Утро на исходе - последний легкий шанс. Сокращенная сессия"
- "Час прошел - переключиться сложнее, но возможно. Мини-сессия"
- "Час потерян, но день еще можно спасти. План-минимум"
- "Критическая точка - дальше будет только хуже. Экстренные меры"

### **4-е сообщение (3 минуты, через 2 часа):**
Крайне желательно сказать что это минимум 3 минуты. 
Шаблон: Временная метка + Основной Мотивационный утвержденный текст + Сокращенная сессия (план минимум 3 минуты)

**Варианты:**
- "2+ часа после пробуждения - последний легкий шанс. План-минимум 3 минуты"
- "Утро почти кончилось - критический момент для старта. Всего 3 минуты"
- "Полдня прошло - экстренные меры. Хотя бы 3 минуты"
- "Критическая точка дня - сейчас или никогда. План-минимум 3 минуты"

## ⚠️ Заголовки для режима ВОЗВРАЩЕНИЯ

### **🟢 Уровень 1: 1 день без работы**
**Философия:** "Критический момент - развилка между возвращением и деградацией"
**Тон:** Серьезный, но поддерживающий, подчеркивающий важность момента
**Цель:** Не дать одному дню превратиться в паттерн избегания
**Подход:** "Сейчас решается все - работа или прокрастинация"

> **🎯 ПРИНЦИП ЗАГОЛОВКОВ ДЛЯ РЕЖИМА ВОЗВРАЩЕНИЯ:**
> - **1-е сообщение:** Констатация факта (сколько дней не работал)
> - **2-4 сообщения:** Мотивационные смыслы, сильные идеи, инфа о размере сессии если есть место (скращенная, план-минимум) 
> - Место в заголовке используется для максимального воздействия, а не повторения того какой это день пропуска. Он уже знает это. 

**1-е сообщение (констатация факта):**
- "1 день пропуска. Не дай одному дню превратиться в два"

**2-е сообщение (мотивационные смыслы):**
- "Не затягивай: Важно вернуться к работе как можно раньше. Сейчас!"
- "Всего 1 день пропуска или начало деградации?"
- "Каждое откладывание - закрепляет привычку откладывать"

**3-е сообщение (философия предотвращения):**
- "Ты теряешь контроль. Не дай одному дню превратиться в месяц"
- "Останови деградацию на корню. Используй сокращенную сессию"
- "Каждое откладывание - гвоздь в гроб продуктивности. Особенно сегодня"

**4-е сообщение (ультиматум):**
- "Последний шанс. Не превращай 1 день в привычку избегания. Всего 3 минуты"
- "Финальная попытка остановить деградацию. Всего 3 минуты"

### **🟡 Уровень 2: 2-3 дня без работы**
**Философия:** "Уже видна тенденция, но еще не критично. Начинает формироваться паттерн избегания"
**Тон:** Мягкое напоминание с легким беспокойством
**Цель:** Прервать негативную тенденцию пока не поздно
**Подход:** "Хватит откладывать, но без драмы"

**1-е сообщение (констатация факта):**
- "[X] дня без работы. Тенденция становится заметной"
- "Уже [X] дня пропуска. Пора остановить это"
- "[X] дня без прогресса. Не дай стать привычкой"

**2-е сообщение (мотивационные смыслы):**
- "Каждый день откладывания делает возвращение сложнее"
- "Пауза затягивается. Верни контроль над ситуацией"
- "Прокрастинация набирает силу. Останови ее сейчас"
- "Ты формируешь паттерн избегания. Сломай его"

**3-е сообщение (философия предотвращения):**
- "Не дай перерыву стать образом жизни"
- "Каждый день без работы - шаг к деградации"
- "Сопротивление растет. Действуй пока не поздно"
- "Привычка откладывать крепнет. Используй сокращенную сессию"
- "Вспоминаешь ощущение прокрастинатора? Вернись к себе!"

**4-е сообщение (ультиматум):**
- "Последний легкий шанс вернуться. План-минимум 3 минуты"
- "Завтра будет уже сложнее. Начни хотя бы 3 минуты"
- "Критическая точка: сейчас или деградация продолжится"

### **🟠 Уровень 3: 4-6 дней без работы**
**Философия:** "Это уже серьезная проблема. Сформировался паттерн избегания"
**Тон:** Настойчивый, с нотками тревоги
**Цель:** Встряхнуть, показать серьезность ситуации
**Подход:** "Стоп! Это уже не случайность"

**1-е сообщение (констатация факта):**
- "[X] дней без работы. Это уже серьезная проблема"
- "Почти неделя пропуска. Паттерн избегания сформирован"
- "[X] дней деградации. Пора решать проблему"

**2-е сообщение (мотивационные смыслы):**
- "Ты потерял контроль над ситуацией. Вернем его?"
- "Паттерн избегания укрепился. Давай сломаем его?"
- "Прокрастинация временно победила. Время контратаковать"

**3-е сообщение (философия предотвращения):**
- "Ситуация выходит из-под контроля. Экстренные меры"
- "Деградация набирает обороты. Мини-сессия как спасение"
- "Критическая точка пройдена. Только радикальные действия"
- "Паттерн избегания стал привычкой. Ломай немедленно"
- "Вспоминаешь ощущение прокрастинатора? Вернись к себе!"

**4-е сообщение (ультиматум):**
- "Неделя на носу. Последний шанс не скатиться окончательно"
- "Финальная попытка. 3 минуты или полная деградация"

### **🔴 Уровень 4: 7+ дней без работы**
**Философия:** "Критическое возвращение к проекту. Радикальные меры для преодоления длительного застоя"
**Тон:** Ультимативный, но поддерживающий
**Цель:** Мотивировать к немедленному действию, а не к размышлениям
**Подход:** "Последний шанс вернуться - действуй сейчас"

**1-е сообщение (констатация факта):**
- "Неделя без работы - но еще не поздно вернуться"
- "7 дней пропуска. Критический момент для возвращения"
- "Неделя деградации. Пора кардинально менять подход"

**2-е сообщение (мотивационные смыслы):**
- "Критический момент: действуй или признай поражение"
- "Либо возвращение, либо окончательный отказ от проекта"
- "Экстренное возвращение к проекту. Сейчас или никогда"
- "Проект ждет тебя. Последний шанс на возвращение"

**3-е сообщение (философия предотвращения):**
- "Последняя попытка спасти проект от забвения"
- "Критическая ситуация: проект умирает"
- "Неделя молчания - проект на грани провала"
- "Экстренная реанимация проекта. Мини-сессия"

**4-е сообщение (ультиматум):**
- "Всё! 3 минуты или закрытие проекта"
- "Последний шанс. Проект или забвение?"
- "Критическая развилка: работа или признание поражения"

## 🚨⚠️⚠️⚠️⚠️⚠️⚠️ После 7 дней - РАЗБОР ПОЛЕТОВ

> **Если пользователь не начал работу даже на 7+ день:**

### **День 8+: Полноэкранное сообщение "Разбор полетов"**
В режиме интерактивного квиза.
⚠️
- Анализ причин длительного перерыва
- Переоценка приоритетов проекта
- Предложение изменить подход к работе
- Возможность архивировать/заморозить проект
- Настройка новых параметров (время, интервалы)
- Выбор нового фокусного проекта

- "Проект потерял приоритет или ты потерял мотивацию?"

**Цель:** Честный анализ и перезапуск, а не продолжение давления

## 🔥 Заголовки для режима STREAK


### **📊 УРОВНИ ЗРЕЛОСТИ STREAK'А**

> **Для всех сообщений используются разные смыслы в зависимости от зрелости streak'а**

#### **🌱 Уровень 1: Формирование (дни 1-14)**
- **Психология:** Хрупкая привычка, легко потерять
- **Тон:** Осторожный, поддерживающий
- **Смыслы:** "формируется", "легко потерять", "закрепляем"

#### **💪 Уровень 2: Укрепление (дни 15-60)**
- **Психология:** Привычка есть, но нужна стабильность
- **Тон:** Уверенный, мотивирующий
- **Смыслы:** "укрепляем", "не теряй темп", "традиция"

#### **🏆 Уровень 3: Мастерство (дни 61+)**
- **Психология:** Устойчивая привычка, но расслабляться нельзя
- **Тон:** Уважительный, но бдительный
- **Смыслы:** "поддерживаем уровень", "не расслабляйся", "стандарт качества"


### **🎨 УНИВЕРСАЛЬНЫЕ ВАРИАНТЫ ПО УРОВНЯМ ЗРЕЛОСТИ :**

#### ✅ **1-е сообщение (сразу после пробуждения):**
**Уровень 1 (1-14 дней):**
- "Уже {streak_days} {declension} работы! Закрепляем привычку! Сейчас самое время!"
- "{streak_days} {declension} подряд! Формируем навык! Пока легко - действуй!"
- "Серия {streak_days} {declension}! Укрепляем начало! Момент идеальный!"

**Уровень 2 (15-60 дней):**
- "Уже {streak_days} {declension} работы! Поддерживаем темп! Сейчас легче всего!"
- "{streak_days} {declension} подряд! Укрепляем привычку! Не упусти магию утра!"
- "Серия {streak_days} {declension}! Стабильность растет! Начинаем?"

**Уровень 3 (61+ дней):**
- "Уже {streak_days} {declension} работы! Уровень мастера! Лови момент!"
- "{streak_days} {declension} подряд! Высокий стандарт! Начина"
- "Серия {streak_days} {declension}! Профессиональный уровень! Используй легкость момента!"

#### ✅  **2-е сообщение (20 минут после пробуждения):**
**Уровень 1 (1-14 дней):**
- "Привычка еще хрупкая! Лучше не откладывать и начать раньше"
- "Не дай паттерну откладывания вернуться! Начинаем сейчас?"

**Уровень 2 (15-60 дней):**
- "Укрепляем привычку! Не упусти магию утра!"
- "Стабильность требует постоянства! Не упускай момент!"

**Уровень 3 (61+ дней):**
- "Поддерживаем высокий стандарт! Действуй, пока легко!"


#### ✅ **3-е сообщение (час после пробуждения):**
**Уровень 1 (1-14 дней):**
- "Довольно поздно для хрупкой привычки! Экстренные меры!"
- "Час без дела - критично для формировоания привычки!"

**Уровень 2 (15-60 дней):**
- "Уже поздновато, но ты справлялся и с худшим! Просто начни. 
- "Довольно поздно - привычка под вопросом! Мини-старт!"
- "Довольно поздно - повышенный риск! "

**Уровень 3 (61+ дней):**
- "Час промедления - не уровень мастера! Привычка может вернуться"
- "Довольно поздно - повышенный риск!"
- "Вспоминаешь ощущение прокрастинатора? Вернись к себе!"


#### ✅ **4-е сообщение (2+ часа после пробуждения):**
ФОрмула: не потеряй Х дней

**Уровень 1 (1-14 дней):**
- "Уже сильно поздно! Последний шанс спасти {streak_days} {declension}!"
- "Уже сильно поздно! Привычка еще слабая. Не потеряй {streak_days} {declension}!"

**Уровень 2 (15-60 дней):**
- "Поздновато! Привычка поможет, но {streak_days} {declension} под риском!"
- "Серъезный риск! Привычка помогает, но лучше не рисковать и начать сейчас!"

**Уровень 3 (61+ дней):**
- "Поздновато! Привычка должна помочь, но лучше начать сейчас. 
- "Привычка сильная, но не расслабляйся. Не потеряй {streak_days} {declension}"

### **🎉 Специальные заголовки для ключевых дней:**
> **Переопределяют базовые для особых дат**

#### ✅ **День 1 (ПЕРВЫЙ ДЕНЬ!):**

- **1-е сообщение:** 
Формула `[ПЕРВЫЙ ДЕНЬ ПОЗАДИ] + [САМОЕ СЛОЖНОЕ СДЕЛАНО] + [ТЕПЕРЬ ЗАКРЕПЛЕНИЕ]`
"ПЕРВЫЙ ДЕНЬ ПОЗАДИ! Самое сложное сделано! Теперь закрепление"
"День 1 завершен! Начать было труднее всего! Формируем новую привычку"
"Первый день позади! Дальше будет легче! Теперь закрепляем"

- **2-е сообщение:** 
Формула `[ОДИН ДЕНЬ ХОРОШО] + [НО НАВЫК ЕЩЕ СЛАБ] + [НЕ ЗАТЯГИВАЙ]`
"Не дай паттерну откладывания вернуться! Лучше не затягивать"
"Один день хорошо, но навык постоянства еще не сформирован! Лучше не затягивать"
"Начало положено, но навык еще слаб! Лучше не затягивать"

- **3-е сообщение:** 
Формула 
"Один день легко потерять! Сохрани импульс"
"Привычка действовать или откладывать формируется прямо сейчас!"

- **4-е сообщение:** 
Формула: `[БУДЕТ ОБИДНО ПОТЕРЯТЬ НАЧАЛО]`
"Будет обидно потерять отличное начало!"
"Не дай старым привычкам вернуться!"
"Не потеряй шанс изменить жизнь!"

#### ✅ **День 2 (ПРИВЫЧКА ФОРМИРУЕТСЯ!):**

- **1-е сообщение:**
Формула `[ДВА ДНЯ ПОДРЯД] + [ПРИВЫЧКА ФОРМИРУЕТСЯ] + [ПРОДОЛЖАЕМ]`
"ДВА ДНЯ ПОДРЯД! Привычка начинает формироваться! Продолжаем закрепление"
"Второй день! Уже не случайность, а начало хорошей привычки! Закрепляем!"
"2 дня подряд! Паттерн работы формируется! Не останавливаемся!"

- **2-е сообщение:**
Формула `[ХОРОШЕЕ НАЧАЛО] + [НО ЕЩЕ ХРУПКО] + [НЕ ЗАТЯГИВАЙ]`
"Отличное начало! Но привычка еще хрупкая! Не затягивай!"
"Два дня - хороший старт! Но навык еще слаб! Продолжай сейчас!"
"Хорошая серия! Но легко сорваться! Лучше не откладывать!"

- **3-е сообщение:**
Формула
"Хорошее начало под угрозой! Привычка формируется именно сейчас!"
"Два дня работы могут пропасть! Не дай этому случиться!"
"Привычка действовать или откладывать формируется прямо сейчас!"

- **4-е сообщение:**
Формула `[ОБИДНО ПОТЕРЯТЬ ХОРОШИЙ СТАРТ]`
"Будет очень обидно потерять такой хороший старт!"
"Не дай двум дням пропасть зря!"
""Отличное начало - не теряй его сейчас!"

#### ✅ **День 3 (УКРЕПЛЕНИЕ НАЧАЛА!):**

- **1-е сообщение:**
Формула `[ТРИ ДНЯ ПОДРЯД] + [НАЧАЛО УКРЕПЛЯЕТСЯ] + [ПРОДОЛЖАЕМ СЕРИЮ]`
"ТРИ ДНЯ ПОДРЯД! Начало укрепляется! Продолжаем закрепление!"
"Третий день! Привычка становится крепче! Не останавливаемся!"
"3 дня подряд! Отличная серия формируется! Закрепляем успех!"

- **2-е сообщение:**
Формула `[ХОРОШАЯ СЕРИЯ] + [НО ОСТОРОЖНО] + [НЕ РАССЛАБЛЯЙСЯ]`
"Хорошая серия! Но еще рано расслабляться! Продолжай!"
"Три дня - отличный старт! Но привычка еще формируется! Не откладывай!"
"Серия растет! Но легко сорваться! Лучше не откладывать!"

- **3-е сообщение:**
Формула `[ТРИ ДНЯ ПОД УГРОЗОЙ] + [КРИТИЧЕСКИЙ ЭТАП]`
"Хорошая серия может прерваться! Привычка еще не закрепилась!"

- **4-е сообщение:**
Формула `[ЖАЛКО ТЕРЯТЬ СЕРИЮ]`
"Будет жалко потерять такую хорошую серию!"
"Не дай трем дням пропасть зря!"
"Три дня работы на кону! Не дай им пропасть!"
"Три дня под угрозой! Критический этап формирования привычки!"


#### ✅ **День 5 (СЕРЬЕЗНАЯ ВЕХА!):**

- **1-е сообщение:**
Формула `[ПЯТЬ ДНЕЙ ПОДРЯД] + [СЕРЬЕЗНАЯ ВЕХА] + [ПОЧТИ НЕДЕЛЯ]`
"ПЯТЬ ДНЕЙ ПОДРЯД! Серьезная веха! Почти неделя работы! Продолжаем закрепление!"
"Пятый день! Это уже серьезный результат! Продолжаем закрепление!"
"5 дней подряд! Отличная серия растет! До недели осталось немного!"

- **2-е сообщение:**
Формула `[ОТЛИЧНАЯ СЕРИЯ] + [НО НЕ РАССЛАБЛЯЙСЯ] + [ДО НЕДЕЛИ БЛИЗКО]`
"Отличная серия! Но не расслабляйся! До недели близко!"
"Пять дней - серьезный результат! Но привычка еще формируется! Не откладывай!"
"Хорошая серия растет! Но легко сорваться перед неделей! Продолжай!"

- **3-е сообщение:**
Формула `[ПЯТЬ ДНЕЙ ПОД УГРОЗОЙ] + [БЛИЗКО К НЕДЕЛЕ]`
"Пять дней под угрозой! Так близко к неделе!"
"Серьезная серия может прерваться! Не дай этому случиться!"
"Пять дней работы на кону! Привычка еще не закрепилась!"

- **4-е сообщение:**
Формула `[ОЧЕНЬ ЖАЛКО ТЕРЯТЬ СЕРИЮ]`
"Будет очень жалко потерять такую серьезную серию!"
"Не дай пяти дням пропасть зря!"


#### **День 7 (неделя):**
- **1-е:** "НЕДЕЛЯ! 7 дней подряд - это уже мини-привычка! Но начинай пораньше"
- **2-е:** "Не расслабляйся! Неделя это еще хрупко. Лучше не затягивать"
- **3-е:** "Уже поздно! Риск потери недельной серии. "
- **4-е:** "Неделя на кону! Не потеряй 7 дней"

#### **День 14 (две недели):**
- **1-е:** "ДВЕ НЕДЕЛИ! Настоящая привычка! Начинай пораньше"
- **2-е:** "14 дней подряд! Лучше не затягивать"
- **3-е:** "Уже поздно! Риск потери 2 недельной серии."
- **4-е:** "14 дней на кону! Не потеряй две недели"

#### **День 30 (месяц):**
- **1-е:** "МЕСЯЦ! 30 дней - ты машина! Начинай пораньше"
- **2-е:** "Ты машина! Но не расслабляйся! Не откладывай!"
- **3-е:** "Уже поздно! Риск потери месячной серии"
- **4-е:** "Месяц на кону! Не потеряй 30 дней"

#### **День 50:**
- **1-е:** "50 дней - это уже образ жизни! Начинай пораньше"
- **2-е:** "Начнем пораньше? Даже после 50 дней лучше не затягивать"
- **3-е:** "Уже поздно! Риск потери 50-дневной серии."
- **4-е:** "50 дней на кону! Не потеряй легенду"


#### **День 100:**
- **1-е:** "100 дней - невозможное возможно! Начинай пораньше"
- **2-е:** "Начнем пораньше? Даже после 100 дней лучше не затягивать"
- **3-е:** "Уже поздновато! Риск сорваться повышен"
- **4-е:** "100 дней на кону! Не потеряй историю"

### **🎯 ПРИНЦИПЫ СОЗДАНИЯ ТЕКСТОВ:**

#### **❌ ИЗБЕГАТЬ:**
- **Напряжение:** "Не расслабляйся!", "Держись!", "Терпи!"
- **Принуждение:** "Лучше не затягивать", "Должен работать"
- **Страх:** "Под угрозой!", "Критично!", "Опасно!"
- **Стресс:** "Аларм!", "Экстренно!", "Срочно!"

#### **✅ СТРЕМИТЬСЯ К:**
- **Новая норма:** "Это твой стиль жизни", "Твоя новая норма"
- **Легкость:** "Ты привык начинать рано", "Это естественно для тебя"
- **Удовольствие:** "Утренняя работа - твоя сила", "Кайф от раннего старта"
- **Стандарт:** "Это твой уровень", "Возвращайся к своему ритму"

#### **🎨 ФИЛОСОФИЯ:**
Мы создаем **новый образ жизни**, а не временный режим. Пользователь должен **кайфовать** от раннего начала работы, а не терпеть его. Это его **новая идентичность**, а не борьба с собой.

#### **💡 МОЩНЫЙ ПРИЕМ - КОНТРАСТ ИДЕНТИЧНОСТЕЙ:**

**🔥 Для 3-го и 4-го сообщений (когда уже поздно):**

**Показать контраст:**
- "Вспоминаешь, каково это быть прокрастинатором?"
- "Чувствуешь этот гнет? Это не ты настоящий!"
- "Вот оно - состояние откладывания. Неприятно, да?"

**Предложить выход:**
- "Вернись к себе настоящему! Начни сейчас - и от этого чувства не останется следа"
- "Ты не прокрастинатор! Докажи это себе прямо сейчас"
- "Завтра начнешь рано - и не будет этого тяжелого ощущения"

**Показать урок:**
- "Вот тебе последствия утреннего откладывания - тяжелее начать и гнет прокрастинации"
- "Видишь разницу? Утром легко, сейчас тяжело. Запомни это на завтра"

> **TODO:** Переписать тексты в соответствии с этими принципами
> **TODO:** Добавить контраст идентичностей в поздние сообщения

### **📝 Правила склонения:**
- **1, 21, 31, 41...** → "день" (1 день, 21 день)
- **2-4, 22-24, 32-34...** → "дня" (2 дня, 3 дня, 23 дня)
- **5-20, 25-30, 35-40...** → "дней" (5 дней, 10 дней, 25 дней)

## 🎨 Примеры работы системы

### **Пример 1: Возвращение, уровень 1, 1-е сообщение**
- **Заголовок:** "Вчера не работал - не страшно!"
- **Подзаголовок:** "Сейчас самое легкое время начать"

### **Пример 2: Возвращение, уровень 4, 4-е сообщение**
- **Заголовок:** "Неделя без работы... Последний шанс?"
- **Подзаголовок:** "Завтра будет еще сложнее начать"

### **Пример 3: Streak 5 дней, 2-е сообщение**
- **Заголовок:** "Не хочется терять 5 дней прогресса" (5 → "дней")
- **Подзаголовок:** "Чем дольше ждешь, тем сложнее будет"

### **Пример 4: Streak 30 дней, 1-е сообщение**
- **Заголовок:** "Месяц продуктивности!" (специальный)
- **Подзаголовок:** "Сейчас самое легкое время начать"

---

## 🔧 ТЕХНИЧЕСКАЯ РЕАЛИЗАЦИЯ

### **Конфликтные пары (исключения при рандомизации)**

> **Цель:** Избежать дублирования ключевых фраз в заголовке и подзаголовке

```yaml
Конфликты_заголовок_подзаголовок:
  - ключевые_слова_заголовка: ["последний шанс"]
    исключить_подзаголовки: ["последний легкий шанс", "последний шанс"]

  - ключевые_слова_заголовка: ["3 минуты", "хотя бы"]
    исключить_подзаголовки: ["3 минуты", "хотя бы 3 минуты", "всего 3 минуты"]

  - ключевые_слова_заголовка: ["час", "прошел час"]
    исключить_подзаголовки: ["час без дела", "час после пробуждения", "час утра прошел"]

  - ключевые_слова_заголовка: ["сокращен", "снижен", "мини-сессия"]
    исключить_подзаголовки: ["сниженный интервал", "сокращенная сессия", "мини-сессия"]

  - ключевые_слова_заголовка: ["финальн", "критическ"]
    исключить_подзаголовки: ["финальная попытка", "критический момент"]

  - ключевые_слова_заголовка: ["экстренн"]
    исключить_подзаголовки: ["экстренные меры", "экстренная сессия"]
```

**Примечание:** Используются корневые слова для автоматического учета всех склонений (сокращенная/сокращенную/сокращенной и т.д.)

### **Дополнительные примеры склонения:**
- **Streak 1 день:** "Уже 1 день работы!" (1 → "день")
- **Streak 2 дня:** "Уже 2 дня работы!" (2 → "дня")
- **Streak 21 день:** "Уже 21 день работы!" (21 → "день")
- **Streak 23 дня:** "Уже 23 дня работы!" (23 → "дня")

## ✅ Преимущества решения

1. **Минимальный объем работы:** 24 текста вместо 40+
2. **Максимальная гибкость:** Каждый заголовок точно отражает ситуацию
3. **Переиспользование:** 4 подзаголовка работают везде
4. **Логичность:** Четкое разделение ответственности
5. **Масштабируемость:** Легко добавлять новые уровни
6. **Элегантность:** Простая и понятная система

## 🔧 Техническая реализация склонения

### **Swift функция для склонения:**
```swift
func getDaysDeclension(_ days: Int) -> String {
    let lastDigit = days % 10
    let lastTwoDigits = days % 100

    // Исключения для 11-14
    if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
        return "дней"
    }

    switch lastDigit {
    case 1:
        return "день"
    case 2, 3, 4:
        return "дня"
    default:
        return "дней"
    }
}
```

### **Примеры работы:**
- `getDaysDeclension(1)` → "день" → "Уже 1 день работы!"
- `getDaysDeclension(2)` → "дня" → "Уже 2 дня работы!"
- `getDaysDeclension(5)` → "дней" → "Уже 5 дней работы!"
- `getDaysDeclension(21)` → "день" → "Уже 21 день работы!"
- `getDaysDeclension(23)` → "дня" → "Уже 23 дня работы!"

## 🚀 Пошаговый план реализации

### ✅ **ШАГ 1: Финализировать подзаголовки**
- 4 универсальных варианта
- Проверить совместимость со всеми заголовками

### ✅ **ШАГ 2: Разработать философию уровней**
- Определить четкую философию для каждого из 4 уровней возвращения
- Убедиться что философия логично переходит в заголовки

### ✅ **ШАГ 3: Финализировать заголовки для возвращения**
- 16 заголовков (4 уровня × 4 горизонтали)
- Основываясь на философии каждого уровня
- Редактировать прямо в этом документе

### ✅ **ШАГ 4: Финализировать заголовки для streak**
- 4 базовых шаблона с переменными ✅
- Специальные заголовки для ключевых дней (7, 14, 30, 50, 100) ✅


### **ШАГ 5: Создать файл с текстами для кода**

```json
{
  "return_mode": {
    "level_1": {
      "message_1": "Вчера не работал - не страшно!",
      "message_2": "Давай начинать! Вчера пропустил, но шанс есть"
    }
  },
  "streak_mode": {
    "base_templates": {
      "message_1": "Уже {days} {declension} работы!"
    }
  },
  "universal_subtitles": [
    "Лучшее время начать - прямо сейчас",
    "Шанс еще есть, но время идет"
  ]
}
```

### **ШАГ 6: Обновить документацию**
Тексты в `/SimplePomodoroTest/Resources/EarlyEngagementTexts.json`
- Ссылки на финальные тексты
- Описание структуры хранения

---

## 🔧 Техническая реализация

### **Элегантная архитектура:**
- **Заголовки:** 16 для возвращения + 4 базовых для streak + специальные для ключевых дней
- **Подзаголовки:** 4 универсальных для всех ситуаций
- **Склонение:** Функция `getDaysDeclension()` для правильных падежей

### **Алгоритм выбора сообщения:**
1. **Определить режим:** Streak (работал сегодня) или Возвращение (дни без работы)
2. **Выбрать заголовок:** По уровню и горизонтали (для возвращения) или по дням streak
3. **Выбрать подзаголовок:** Универсальный по горизонтальной позиции
4. **Подставить переменные:** [streak_days], [focused_project] с правильным склонением

## 📏 Ограничения UI

- **Заголовок:** максимум 50 символов
- **Подзаголовок:** максимум 120 символов
- **Кнопка:** максимум 25 символов

> ⚠️ **Важно:** Превышение лимитов приведет к обрезанию текста в интерфейсе

## 📋 Переменные для подстановки

### **Доступные переменные:**
- `[focused_project]` - название приоритетного проекта
- `[streak_days]` - количество дней streak с правильным склонением

### **Примеры использования:**
- "Уже [streak_days] работы над [focused_project]!"
- "Не хочется терять [streak_days] прогресса"

## ⚠️ Текущий статус

### **✅ Что реализовано:**
- Полностью реализована streak-инфраструктура
- Корректно подсчитывает и хранит streak (непрерывные дни работы)
- Подставляет `[streak_days]` в тексты уровня 0
- ButtonMatrix корректно рассчитывает планки для streak (vertical = -1)
- Система персистентности (WorkDay, workHistory)
- **Элегантное решение архитектуры** заголовков/подзаголовков
- **Все тексты из документации** загружены в JSON
- **Система подзаголовков** с 4 наборами по индексу сообщения

### **🎯 Система полностью реализована:**
- **Архитектура элегантного решения** полностью реализована
- **24 текста вместо 40+** = экономия 40% работы
- **Универсальные подзаголовки** для всех ситуаций
- **Правила склонения** для русского языка
- **JSON с финальными текстами** готов к использованию

### **ШАГ 7: Внедрение в код**
- Реализовать функцию склонения `getDaysDeclension()`
- Реализовать **Конфликтные пары (исключения при рандомизации)**
- Внедрить в отладочное окно для тестирования (Так как у нас несколько разных сообщений, рандомизатор есть, то нужно сделать кнопку, там вот где отладочное сообщение, над ним можно сделать или под ним можно сделать типа кнопка другой пример или или типа ну типа там же как бы что бы они подставлялись там заголовок под заголовок используются разные как-то вот надо это учить)
- Протестировать все комбинации
- Внедрить в основную систему
- Добавить поддержку переменных [streak_days], [focused_project]

---

> **Статус:** 

---

> **Результат:** Элегантная система, которая решает проблему объема работы при сохранении максимальной гибкости и точности сообщений.



---

