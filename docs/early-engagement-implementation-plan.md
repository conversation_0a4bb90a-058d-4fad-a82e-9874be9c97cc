# 🚀 План внедрения системы раннего вовлечения в uProd

## 📋 Обзор проекта

Система раннего вовлечения (Early Engagement System) - это двумерная система эскалации, которая помогает пользователям начинать работу над приоритетными проектами утром, используя адаптивные интервалы и принцип "минимального плана".

## 🎯 Ключевые концепции

- **Двумерная эскалация**: Дни без работы (ВЕРТИКАЛЬ) × Время дня (ГОРИЗОНТАЛЬ)
- **Адаптивные планки**: Плавающее значение, изменяющееся ежедневно на основе успехов/неудач
- **Матрица сообщений**: 5×4 = 20 вариантов персонализированных сообщений
- **Принцип минимального плана**: "Лучше 2 минуты, чем ничего"

## 🏗️ Архитектурная интеграция

### Существующие компоненты для использования:
- **SleepWakeDetector** → Детекция пробуждения компьютера
- **IntervalNotificationWindow** → Базовый UI для сообщений
- **ProjectManager** → Работа с приоритетным проектом (Ichiban)
- **PomodoroTimer** → Запуск интервалов с адаптивной длительностью
- **Logger** → Детальное логирование всех операций

## 📝 Детальный план задач

### 🔥 Фаза 1: Основные компоненты (Приоритет: ВЫСОКИЙ)

#### 1.1 EarlyEngagementSystem (Основной класс)
- [/] **Создание основного класса EarlyEngagementSystem**
  - Центральный класс для управления всей системой
  - Интеграция с матрицей сообщений и адаптивными планками
  - Логика эскалации и обнуления после успеха

#### 1.2 MessageConstructionMatrix (Матрица сообщений)
- [ ] **Определение структуры данных для матрицы сообщений**
  - Enum для уровней дней без работы (0-4)
  - Enum для времени дня (утро, день, вечер, ночь)
  - Структура для хранения 20 вариантов с плейсхолдерами
- [ ] **Реализация логики выбора сообщения из матрицы**
  - Метод определения текущего уровня
  - Выбор соответствующего сообщения
  - Подстановка переменных [focused_project], [current_bar]

#### 1.3 UserBarSystem (Адаптивные планки)
- [ ] **Создание структуры UserBar для хранения планок**
  - Текущая планка пользователя
  - История изменений
  - Логика адаптации
- [ ] **Реализация алгоритма адаптации планок**
  - Изменение на основе успехов/неудач
  - Учет времени дня
  - Послеобеденная корректировка

### 🎨 Фаза 2: Пользовательский интерфейс (Приоритет: ВЫСОКИЙ)

#### 2.1 EarlyEngagementWindow (UI компонент)
- [ ] **Создание UI компонентов для EarlyEngagementWindow**
  - Специальные кнопки с адаптивными интервалами
  - Текстовые поля с эмоциональными крючками
  - Анимации и визуальные эффекты
- [ ] **Интеграция окна с системой позиционирования**
  - Логика позиционирования на экране
  - Учет существующих окон
  - Предпочтения пользователя

### 🔗 Фаза 3: Системная интеграция (Приоритет: СРЕДНИЙ)

#### 3.1 Интеграция с существующими системами
- [ ] **Интеграция с SleepWakeDetector для запуска системы**
  - Добавление в systemDidWake()
  - Проверка времени последней работы
- [ ] **Интеграция с ProjectManager для работы с приоритетным проектом**
  - Использование getPriorityProject()
  - Подстановка [focused_project] в сообщения
- [ ] **Интеграция с PomodoroTimer для запуска интервалов**
  - Запуск с адаптивной длительностью
  - Обработка согласия пользователя

#### 3.2 Координация в AppDelegate
- [ ] **Добавление в AppDelegate координации всех компонентов**
  - Инициализация EarlyEngagementSystem
  - Настройка колбэков
  - Координация с существующими системами

### 📊 Фаза 4: Данные и аналитика (Приоритет: СРЕДНИЙ)

#### 4.1 Персистентность данных
- [ ] **Создание системы персистентности данных**
  - Сохранение планок пользователя
  - История вовлечения
  - Энергетический профиль

#### 4.2 Статистика и мониторинг
- [ ] **Создание EngagementStatistics для отслеживания эффективности**
  - Время показа сообщений
  - Реакции пользователя
  - Успешность запуска интервалов
- [ ] **Интеграция с Logger для детального логирования**
  - Логирование всех операций
  - Отладка и мониторинг работы

### 🧪 Фаза 5: Тестирование (Приоритет: КРИТИЧЕСКИЙ)

#### 5.1 Комплексное тестирование
- [ ] **Написание тестов для всех компонентов системы**
  - Тесты для EarlyEngagementSystem
  - Тесты для UserBarSystem
  - Тесты для MessageConstructionMatrix
  - **ОБЯЗАТЕЛЬНО**: Намеренная поломка функционала для проверки тестов

## ⚡ Критические моменты

### 🚨 Обязательные требования:
1. **Тестирование тестов** - каждый тест должен ловить реальные поломки
2. **Реальная логика** - никаких mock-версий основных компонентов
3. **Детальное логирование** - использовать Logger.swift, НЕ print()
4. **Интеграция с существующей архитектурой** - следовать паттернам проекта

### 🎯 Ключевые интеграционные точки:
- **SleepWakeDetector.systemDidWake()** → Запуск системы
- **ProjectManager.getPriorityProject()** → Получение фокусного проекта
- **PomodoroTimer.startInterval()** → Запуск адаптивных интервалов
- **IntervalNotificationWindow** → Базовый UI для сообщений

## 📈 Ожидаемые результаты

1. **Увеличение вовлеченности** пользователей в утренние часы
2. **Адаптивная система** подстраивающаяся под индивидуальные паттерны
3. **Минимальная инвазивность** - интеграция без нарушения существующих процессов
4. **Измеримая эффективность** через систему статистики

## 🚀 Следующие шаги

1. **Начать с EarlyEngagementSystem** - создать основной класс
2. **Реализовать MessageConstructionMatrix** - матрицу сообщений
3. **Создать UserBarSystem** - адаптивные планки
4. **Интегрировать с UI** - EarlyEngagementWindow
5. **Добавить тесты** - с обязательной проверкой реальной логики
