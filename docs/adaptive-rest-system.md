# 🌱 Адаптивная система отдыха и роста планки

> **Ключевая концепция:** Справедливая система наград - больше работал, больше награда

## 🎯 Философия системы

**Проблема:** Раньше 3 минуты работы и 30 минут работы давали одинаковый рост планки и отдых.

**Решение:** Двухуровневая система, которая мотивирует к более длинным сессиям через:
- Адаптивный рост планки
- Адаптивный отдых
- Систему повторных предложений

---

## 📊 Правила роста планки

### **Формула роста в зависимости от длительности сессии:**

| Тип сессии | Длительность | Рост планки | Пример |
|------------|-------------|-------------|---------|
| **Микро-сессия** | ≤ текущей планки | **+30%** | 3 мин → 4 мин |
| **Средняя сессия** | 15-29 мин | **+50%** | 3 мин → 5 мин |
| **Неформальная сессия** | 30-51 мин | **+80%** | 3 мин → 5 мин |
| **Полная сессия** | 52+ мин | **+100%** | 3 мин → 6 мин |

### **Примеры роста для разных планок:**

```
Планка 3 мин:
- 3 мин работы → 4 мин планка (+30%)
- 20 мин работы → 5 мин планка (+50%)  
- 30 мин работы → 5 мин планка (+80%)
- 52 мин работы → 6 мин планка (+100%)

Планка 10 мин:
- 10 мин работы → 13 мин планка (+30%)
- 20 мин работы → 15 мин планка (+50%)
- 30 мин работы → 18 мин планка (+80%)
- 52 мин работы → 20 мин планка (+100%)
```

---

## ⏰ Правила адаптивного отдыха

### **Логика отдыха:**

| Длительность работы | Отдых | Поведение |
|-------------------|-------|-----------|
| **< 15 мин** | **БЕЗ отдыха** | Через 30 мин новое предложение |
| **15-29 мин** | **15 мин отдыха** | С таймером |
| **30+ мин** | **17 мин отдыха** | С таймером (стандарт) |

### **Мотивационная логика:**

- **Микро-сессии:** "Хорошо, но можешь больше! Попробуй еще через 30 мин"
- **Средние сессии:** "Неплохо! Заслужил короткий отдых"
- **Полные сессии:** "Отлично! Заслужил полноценный отдых"

---

## 🎮 Сценарии использования

### **Сценарий 1: Микро-сессия (втягивание)**
```
1. Пользователь: 3 мин → "Хватит"
2. Система: 
   - ❌ НЕТ таймера отдыха
   - ✅ Планка: 3 → 4 мин (+30%)
   - ✅ Через 30 мин: "Давай 4 минуты?"
3. Цель: Постепенно втягивать микро-сессиями
```

### **Сценарий 2: Полная сессия (награда)**
```
1. Пользователь: 3 мин → "Еще 13 мин" → "Еще 14 мин" = 30 мин
2. Система:
   - ✅ 17 мин отдыха С таймером
   - ✅ Планка: 3 → 5 мин (+80%)
   - ✅ Через 17 мин: Обычный цикл с новой планкой
3. Цель: Большая награда за полную сессию
```

### **Сценарий 3: Средняя сессия**
```
1. Пользователь: 3 мин → "Еще 13 мин" = 16 мин → "Хватит"
2. Система:
   - ✅ 15 мин отдыха С таймером
   - ✅ Планка: 3 → 5 мин (+50%)
   - ✅ Через 15 мин: Обычный цикл
3. Цель: Промежуточная награда
```

---

## 🔧 Техническая реализация

### **Ключевые функции:**

1. **`calculateAdaptiveGrowth()`** - Расчет роста планки
2. **`startAdaptiveRest()`** - Запуск адаптивного отдыха
3. **`scheduleNextMicroSessionOffer()`** - Планирование повторных предложений

### **Логика определения типа сессии:**

```swift
let completedMinutes = Int(totalCompleted / 60)
let oldBarMinutes = Int(oldBar / 60)

if completedMinutes <= oldBarMinutes {
    // МИКРО-СЕССИЯ: +30%
} else if completedMinutes < 30 {
    // СРЕДНЯЯ СЕССИЯ: +50%
} else if completedMinutes < 52 {
    // НЕФОРМАЛЬНАЯ СЕССИЯ: +80%
} else {
    // ПОЛНАЯ СЕССИЯ: +100%
}
```

### **Интеграция с системой взращивания:**

- Система взращивания считает ОБЩУЮ длительность сессии
- При нажатии "Хватит" вызывается `startAdaptiveRest()`
- Рост планки применяется к ОБЩЕЙ длительности, а не к отдельным сегментам

---

## 📈 Ожидаемые результаты

### **Поведенческие изменения:**
- Пользователи будут стремиться к 30+ минутным сессиям
- Микро-сессии станут "ступенькой" к полным сессиям
- Система будет справедливо награждать усилия

### **Метрики успеха:**
- Увеличение средней длительности сессий
- Рост количества 30+ минутных сессий
- Улучшение удержания пользователей

---

## 🐛 Отладка

### **Ключевые логи для проверки:**
```
🌱 Микро-сессия (3 мин): рост +30%
🌱 Средняя сессия (16 мин): рост +50%
🌱 Неформальная сессия (30 мин): рост +80%
🌱 Полная сессия (52 мин): рост +100%

🌱 Микро-сессия (3 мин): БЕЗ отдыха
🌱 Средняя сессия (16 мин): 15 мин отдыха
🌱 Полная сессия (30 мин): 17 мин отдыха
```

### **Проверочные точки:**
1. Вызывается ли `startAdaptiveRest()` вместо стандартного отдыха?
2. Правильно ли `getCurrentSessionTotalDuration()` возвращает общую длительность?
3. Применяется ли новая планка в EarlyEngagementSystem?
