# Система взращивания сессии (Session Growing System)

## 🎯 Цель системы

Постепенно увеличить время работы **ПОСЛЕ** выполнения интервала, используя психологическую инерцию движения.

> **Принцип:** "Начать сложно, продолжать легче"

## 🧠 Психологическая основа

### Проблема стандартного роста планок
- **3 мин + 10% = 3.3 мин** ≈ 3 мин (незаметно!)
- **10 мин + 10% = 11 мин** (+1 мин - мало заметно)
- **Восстановление с 3 до 52 мин = ~2 недели** (демотивирующе долго)

### Решение через инерцию
- После выполнения интервала пользователь уже "в потоке"
- Самое сложное (начало) уже позади
- Предлагаем продолжить, пока есть импульс

## ⚡ Когда активируется

Система срабатывает **ПОСЛЕ** успешного выполнения любого интервала:
- Основной интервал завершен ✅
- Время интервала < 52 мин (времени полной сессии)
- Пользователь еще не закрыл приложение
- Время дня позволяет продолжить работу

## 💬 Примеры сообщений

### Базовый шаблон
```
Отлично! {выполненное_время} выполнено ✅
Самое сложное (начало) уже позади!
Давай еще {предлагаемое_время}?

[Еще {время}] [Хватит на сегодня]
```

### Конкретные примеры
```
Отлично! 3 минуты выполнены ✅
Самое сложное (начало) уже позади!
Давай еще 5 минут?

[Еще 5 мин] [Хватит на сегодня]
```

```
Супер! 8 минут в потоке ✅
Momentum набран, давай еще 12 минут?

[Еще 12 мин] [Пока хватит, хочу отдыхать]
```

## 🔄 Логика предложений

### Формула расчета следующего интервала
```swift
следующий_интервал = текущий_выполненный × 1.5
```

### Ограничения
- **Максимум 3 предложения** за одну сессию
- **Учет времени дня** (не предлагать поздно вечером)

### Пример последовательности
```
Интервал 1: 3 мин → предложить 5 мин (3 × 1.5 ≈ 5)
Интервал 2: 5 мин → предложить 8 мин (5 × 1.5 ≈ 8)  
Интервал 3: 8 мин → предложить 12 мин (8 × 1.5 = 12)
СТОП (максимум 3 предложения)
```

## 📊 Обновление планки пользователя
Прямая связь с `/docs/early-engagement-task.md` и `/docs/adaptive-user-bar-system.md
`
### Проблема, которую решаем

**Ситуация:** Пользователь с планкой 3 минуты выполнил полную сессию 52 минуты через систему взращивания.

**Проблемы стандартного подхода:**
- ❌ **Оставить планку 3 мин** → завтра снова будет мучиться с маленькими интервалами
- ❌ **Поднять планку до 52 мин** → завтра не сможет выполнить, провал и откат назад

**Решение:** Умеренный рост планки, учитывающий реальные возможности пользователя на завтра.

### Формула умеренного роста
```swift
новая_планка = min(
    общее_выполненное × 0.6,    // 60% от ОБЩЕГО выполненного времени
    старая_планка × 3           // Максимум в 3 раза больше старой
)
```

**⚠️ КРИТИЧЕСКИ ВАЖНО - Расчет общего времени:**
```swift
// ПРАВИЛЬНО: Считаем ВСЮ сессию взращивания
общее_выполненное = базовый_интервал + продление_1 + продление_2 + ...

// Пример:
// 3 мин (базовый) + 5 мин (продление) + 10 мин (продление) = 18 мин ОБЩЕГО
// новая_планка = min(18 × 0.6, 3 × 3) = min(10.8, 9) = 9 мин

// НЕПРАВИЛЬНО: Считать только базовый интервал
// общее_выполненное = 3 мин (только базовый) ❌
```

**Логика формулы:**
- **60% от ОБЩЕГО выполненного** - учитываем ВСЮ работу, включая продления
- **Максимум ×3** - предотвращает слишком резкие скачки, которые пугают

### Примеры расчета с взращиванием

| Старая планка | Сессия взращивания | Общее время | Расчет | Новая планка | Рост |
|---------------|-------------------|-------------|---------|--------------|------|
| 3 мин | 3→5→8 мин | **16 мин** | min(16×0.6, 3×3) = min(9.6, 9) | **9 мин** | ×3.0 |
| 3 мин | 3→5 мин | **8 мин** | min(8×0.6, 3×3) = min(4.8, 9) | **5 мин** | ×1.7 |
| 10 мин | 10→15→23 мин | **48 мин** | min(48×0.6, 10×3) = min(28.8, 30) | **29 мин** | ×2.9 |
| 20 мин | 20→30→45 мин | **95 мин** | min(95×0.6, 20×3) = min(57, 60) | **57 мин** | ×2.9 |

**Детальный пример:**
```
Старая планка: 3 минуты

Сессия взращивания:
1. Базовый интервал: 3 мин ✅
2. Предложение: "Еще 5 мин?" → согласился → 5 мин ✅
3. Предложение: "Еще 8 мин?" → согласился → 8 мин ✅

ОБЩЕЕ выполненное время: 3 + 5 + 8 = 16 минут

Расчет новой планки:
новая_планка = min(16 × 0.6, 3 × 3) = min(9.6, 9) = 9 минут

Результат: планка выросла с 3 до 9 минут (×3 раза!)
```

### Обоснование коэффициентов

**Почему 0.6 (60%)?**
- ✅ **Реалистично** - если выполнил 18 мин, то 11 мин завтра точно сможет
- ✅ **Заметный прогресс** - планка растет значительно (не +10%)
- ✅ **Безопасный запас** - учитывает, что завтра может быть сложнее
- ✅ **Мотивирующе** - пользователь видит результат своих усилий

**Почему максимум ×3?**
- ✅ **Предотвращает шок** - скачок с 3 до 18 мин может напугать
- ✅ **Постепенность** - навык концентрации развивается поэтапно
- ✅ **Сохраняет мотивацию** - успех завтра более вероятен
- ✅ **Учитывает психологию** - большие скачки часто ведут к провалам

## 🎪 Сценарии восстановления

### Сценарий 1: "После недели простоя"
```
Стартовая планка: 3 мин

День 1: 3→5→8 мин (итого 16 мин) → новая планка: 9 мин
День 2: 9→14→21 мин (итого 44 мин) → новая планка: 26 мин  
День 3: 26→39→52 мин (итого 117 мин) → новая планка: 52 мин

Результат: восстановление за 3 дня вместо 3 недель! 🚀
```

### Сценарий 2: "Постепенное наращивание"
```
Стартовая планка: 10 мин

День 1: 10→15 мин (итого 25 мин) → новая планка: 15 мин
День 2: 15→23→35 мин (итого 73 мин) → новая планка: 44 мин
День 3: 44→52 мин (итого 96 мин) → новая планка: 52 мин
```

## 🛡️ Ограничения безопасности

### Временные ограничения
- **Не предлагать после 22:00**
- **Учитывать общее время работы за день**
- **Максимум 4 часа активной работы в день**

### Пользовательские ограничения
- **Возможность отключить систему**
- **Настройка максимального количества предложений**
- **Персональные временные рамки**

## 📈 Метрики эффективности

### Отслеживаемые показатели
- **Процент принятых предложений**
- **Средняя длина сессий взращивания**
- **Скорость роста планки**
- **Влияние на общую продуктивность**
- **Время восстановления после простоев**

### Критерии успеха
- **>50% принятых предложений** - система работает
- **<30% принятых предложений** - нужна корректировка
- **Рост планки в 2-3 раза быстрее** стандартной системы

## 🔧 Техническая реализация

### Основные компоненты
```swift
class SessionGrowingEngine {
    private var sessionSegments: [TimeInterval] = []  // Все сегменты сессии

    func shouldOfferGrowth(completedInterval: TimeInterval) -> Bool
    func calculateNextInterval(current: TimeInterval) -> TimeInterval
    func addSessionSegment(duration: TimeInterval)     // Добавить сегмент
    func getTotalSessionTime() -> TimeInterval         // Получить общее время
    func updateUserBar(oldBar: TimeInterval) -> TimeInterval
}
```

### ⚠️ Критический момент - Учет всех сегментов
```swift
// ПРАВИЛЬНАЯ реализация:
class SessionGrowingEngine {
    private var sessionSegments: [TimeInterval] = []

    func startSession(initialDuration: TimeInterval) {
        sessionSegments = [initialDuration]  // Начинаем с базового интервала
    }

    func addGrowthSegment(duration: TimeInterval) {
        sessionSegments.append(duration)    // Добавляем продление
    }

    func finishSession(oldBar: TimeInterval) -> TimeInterval {
        let totalTime = sessionSegments.reduce(0, +)  // Сумма ВСЕХ сегментов
        let newBar = min(totalTime * 0.6, oldBar * 3)
        sessionSegments = []  // Очищаем для следующей сессии
        return newBar
    }
}
```

### Интеграция с существующей системой
1. **Старт сессии** → `startSession(initialDuration)`
2. **После каждого интервала** → проверка условий продления
3. **Согласие на продление** → `addGrowthSegment(duration)`
4. **Завершение сессии** → `finishSession()` с учетом ВСЕХ сегментов
5. **Обновление планки** → на основе общего времени сессии

## 🎯 Ключевые преимущества

- ✅ **Быстрое восстановление** - дни вместо недель
- ✅ **Психологически естественно** - использует инерцию
- ✅ **Безопасный рост** - умеренные коэффициенты
- ✅ **Гибкая настройка** - можно адаптировать под пользователя
- ✅ **Заметный прогресс** - мотивирующие результаты


# Возможные проблемы
- Как интегрировать в текущие окна завершения интервалов? 
    - Условия срабатывания - если время сессии было меньше 52 минут
- 