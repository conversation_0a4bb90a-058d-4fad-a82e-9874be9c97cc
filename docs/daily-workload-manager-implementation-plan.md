# 🚀 План реализации DailyWorkloadManager

## 📋 Обзор задач

### Фаза 1: Создание основного компонента ✅ ЗАВЕРШЕНА
- [x] Создать класс `DailyWorkloadManager` ✅
- [x] Реализовать систему очков и уровней пользователя ✅
- [x] Реализовать адаптивную планку очков ✅
- [x] Добавить логику адаптивных интервалов ✅

### Фаза 2: Интеграция с существующими системами ✅ ЗАВЕРШЕНА
- [x] Интегрировать с `EarlyEngagementSystem` ✅
- [x] Интегрировать с микросессиями в `AppDelegate` ✅
- [x] Интегрировать с обычными интервалами ✅
- [ ] Обновить `StatisticsManager` для записи очков ⏳

### Фаза 3: Создание новых UI компонентов ✅ ЗАВЕРШЕНА
- [x] Создать базовый класс `BaseStyledWindow` ✅ (через UIStyles.swift)
- [x] Создать `MicroSessionOfferWindow` ✅
- [x] Создать `DailyGoalCompletedWindow` ✅
- [x] Создать `GentleAfternoonOfferWindow` ✅
- [x] Интегрировать новые окна в `AppDelegate` ✅

### Фаза 4: Тестирование и отладка ⏳ В ПРОЦЕССЕ
- [ ] Создать тесты для `DailyWorkloadManager` ⏳
- [ ] Протестировать интеграцию с существующими системами ⏳
- [ ] Отладить адаптивные интервалы ⏳

## 🔧 Детальный план реализации

### 1. Создание DailyWorkloadManager.swift

```swift
import Foundation

/// Уровни пользователя для адаптации системы очков
enum UserLevel: String, CaseIterable {
    case beginner = "beginner"     // 3+ дня без работы
    case starter = "starter"       // 1-2 дня работы
    case regular = "regular"       // 3+ дня стабильной работы
    case advanced = "advanced"     // Высокий уровень продуктивности
    
    var displayName: String {
        switch self {
        case .beginner: return "Новичок"
        case .starter: return "Начинающий"
        case .regular: return "Обычный"
        case .advanced: return "Продвинутый"
        }
    }
}

/// Менеджер ежедневной нагрузки - центральная система управления предложениями работы
class DailyWorkloadManager {
    static let shared = DailyWorkloadManager()
    
    // MARK: - Properties
    
    /// Текущая планка очков (аналог currentUserBar)
    private var currentPointsBar: Int {
        get { UserDefaults.standard.integer(forKey: "dailyPointsBar") }
        set { UserDefaults.standard.set(newValue, forKey: "dailyPointsBar") }
    }
    
    /// Количество отказов от микросессий сегодня
    private var microSessionRefusals: Int = 0

    /// Количество отказов от BreakEndWindow сегодня
    private var breakEndRefusals: Int = 0
    
    /// Время последнего отказа
    private var lastRefusalTime: Date?
    
    /// Текущий уровень пользователя
    private var userLevel: UserLevel = .beginner
    
    /// Рабочие часы (из настроек)
    private var workingHours: (start: Int, end: Int) {
        let start = UserDefaults.standard.integer(forKey: "workingHoursStart")
        let end = UserDefaults.standard.integer(forKey: "workingHoursEnd")
        return (start: start == 0 ? 9 : start, end: end == 0 ? 18 : end)
    }
    
    // MARK: - Initialization
    
    private init() {
        // Устанавливаем начальную планку если не задана
        if currentPointsBar == 0 {
            currentPointsBar = getInitialPointsBar(level: .beginner)
        }
        
        // Определяем текущий уровень пользователя
        updateUserLevel()
        
        // Сбрасываем отказы в начале нового дня
        resetDailyCountersIfNeeded()
    }
    
    // MARK: - Public Methods
    
    /// Проверяет можно ли предложить сессию
    func shouldOfferSession() -> Bool {
        // 1. Проверяем рабочие часы
        guard isWithinWorkingHours() else { return false }
        
        // 2. Проверяем достижение дневной цели
        let progress = getDailyProgress()
        if progress >= 1.0 {
            return shouldOfferBonusSession()
        }
        
        // 3. Проверяем количество отказов от микросессий
        if microSessionRefusals >= 3 {
            return false  // Показываем диалог в handleMicroSessionRefusal
        }
        
        return true
    }
    
    /// Возвращает адаптированный интервал между предложениями
    func getNextOfferInterval(_ baseInterval: TimeInterval) -> TimeInterval {
        var interval = baseInterval
        
        // Адаптация по времени дня
        interval *= getTimeOfDayMultiplier()
        
        // Адаптация по отказам
        interval *= getRefusalMultiplier()
        
        // Адаптация по прогрессу к цели
        interval *= getProgressMultiplier()
        
        return interval
    }
    
    /// Рассчитывает очки за сессию
    func calculateSessionPoints(minutes: Int) -> Int {
        let basePoints = minutes
        let contextBonus = calculateContextBonus(minutes: minutes, level: userLevel)
        return basePoints + contextBonus
    }
    
    /// Обрабатывает завершение сессии
    func handleSessionCompleted(minutes: Int) {
        let points = calculateSessionPoints(minutes: minutes)
        addTodayPoints(points)
        
        // Сбрасываем счетчик отказов при успешной сессии
        todayRefusals = 0
        
        // Проверяем достижение цели
        if hasReachedDailyGoal() {
            showDailyGoalCompletedDialog()
        }
        
        Logger.shared.log(.info, "DailyWorkload", "✅ Сессия завершена: \(minutes) мин = \(points) очков")
    }
    
    /// Обрабатывает отказ от микросессии
    func handleMicroSessionRefusal() {
        microSessionRefusals += 1

        Logger.shared.log(.info, "DailyWorkload", "❌ Отказ от микросессии #\(microSessionRefusals)")

        if microSessionRefusals >= 3 {
            showEndDayDialog()
        } else {
            // Планируем следующее предложение с увеличенным интервалом
            let baseInterval: TimeInterval = 30 * 60
            let multiplier = getRefusalMultiplier(refusals: microSessionRefusals)
            let nextInterval = baseInterval * multiplier

            scheduleNextMicroSessionOffer(after: nextInterval)
        }
    }

    /// Обрабатывает отказ от BreakEndWindow (кнопка "Later")
    func handleBreakEndRefusal() {
        breakEndRefusals += 1

        Logger.shared.log(.info, "DailyWorkload", "❌ Отказ от BreakEnd #\(breakEndRefusals)")

        // Общий счетчик отказов для диалога "хватит на сегодня"
        let totalRefusals = microSessionRefusals + breakEndRefusals
        if totalRefusals >= 3 {
            showEndDayDialog()
        }
    }
    
    /// Проверяет достигнута ли дневная цель
    func hasReachedDailyGoal() -> Bool {
        return getTodayPoints() >= currentPointsBar
    }
    
    /// Возвращает прогресс к дневной цели (0.0 - 1.0+)
    func getDailyProgress() -> Double {
        return Double(getTodayPoints()) / Double(currentPointsBar)
    }
    
    /// Возвращает текущую планку очков
    func getCurrentPointsBar() -> Int {
        return currentPointsBar
    }
    
    /// Возвращает очки за сегодня
    func getTodayPoints() -> Int {
        return UserDefaults.standard.integer(forKey: "todayPoints_\(todayDateString())")
    }
    
    // MARK: - Private Methods
    
    private func updateUserLevel() {
        let daysWithoutWork = getDaysWithoutWork()
        let avgSessionLength = getAverageSessionLength(lastDays: 7)
        
        if daysWithoutWork >= 3 {
            userLevel = .beginner
        } else if avgSessionLength >= 30 {
            userLevel = .regular
        } else {
            userLevel = .starter
        }
        
        Logger.shared.log(.info, "DailyWorkload", "👤 Уровень пользователя: \(userLevel.displayName)")
    }
    
    private func getInitialPointsBar(level: UserLevel) -> Int {
        switch level {
        case .beginner: return 30
        case .starter: return 60
        case .regular: return 120
        case .advanced: return 200
        }
    }
    
    private func calculateContextBonus(minutes: Int, level: UserLevel) -> Int {
        switch level {
        case .beginner:
            if minutes <= 5 { return minutes * 2 }
            if minutes <= 15 { return minutes * 1 }
            return 0
            
        case .starter:
            if minutes <= 5 { return minutes * 1 }
            if minutes <= 25 { return minutes / 2 }
            return minutes / 3
            
        case .regular:
            if minutes <= 5 { return 0 }
            if minutes >= 25 { return minutes / 2 }
            return 0
            
        case .advanced:
            if minutes >= 30 { return minutes / 2 }
            return 0
        }
    }
    
    private func isWithinWorkingHours() -> Bool {
        let hour = Calendar.current.component(.hour, from: Date())
        return hour >= workingHours.start && hour < workingHours.end
    }
    
    private func getTimeOfDayMultiplier() -> Double {
        let hour = Calendar.current.component(.hour, from: Date())
        
        if hour < 12 {
            return 0.8  // Утром чаще
        } else if hour > 15 {
            return 1.3  // Вечером реже
        } else {
            return 1.0  // Днем обычно
        }
    }
    
    private func getRefusalMultiplier() -> Double {
        switch todayRefusals {
        case 0: return 1.0
        case 1: return 1.5
        case 2: return 2.0
        default: return 3.0
        }
    }
    
    private func getProgressMultiplier() -> Double {
        let progress = getDailyProgress()
        
        if progress >= 1.0 {
            return 4.0  // Очень редко после достижения цели
        } else if progress >= 0.8 {
            return 2.0  // Реже когда близко к цели
        } else {
            return 1.0  // Обычно
        }
    }
    
    private func shouldOfferBonusSession() -> Bool {
        // Одно предложение после обеда максимум
        let hour = Calendar.current.component(.hour, from: Date())
        return hour >= 13 && hour <= 15 && !wasPostLunchOfferMade()
    }
    
    private func addTodayPoints(_ points: Int) {
        let key = "todayPoints_\(todayDateString())"
        let current = UserDefaults.standard.integer(forKey: key)
        UserDefaults.standard.set(current + points, forKey: key)
    }
    
    private func todayDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: Date())
    }
    
    private func resetDailyCountersIfNeeded() {
        let lastResetDate = UserDefaults.standard.string(forKey: "lastDailyReset")
        let today = todayDateString()
        
        if lastResetDate != today {
            todayRefusals = 0
            UserDefaults.standard.set(today, forKey: "lastDailyReset")
            UserDefaults.standard.removeObject(forKey: "postLunchOfferMade_\(today)")
            
            // Адаптируем планку на основе вчерашних результатов
            adaptPointsBarBasedOnYesterday()
        }
    }
    
    private func adaptPointsBarBasedOnYesterday() {
        // Получаем очки за вчера
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date())!
        let yesterdayString = DateFormatter().string(from: yesterday)
        let yesterdayPoints = UserDefaults.standard.integer(forKey: "todayPoints_\(yesterdayString)")
        
        // Адаптируем планку
        let success = yesterdayPoints >= currentPointsBar
        
        if success {
            currentPointsBar = Int(Double(currentPointsBar) * 1.15)
        } else {
            currentPointsBar = Int(Double(currentPointsBar) * 0.85)
        }
        
        // Ограничения по уровню
        let minPoints = getInitialPointsBar(level: userLevel)
        let maxPoints = minPoints * 4
        currentPointsBar = max(minPoints, min(maxPoints, currentPointsBar))
        
        Logger.shared.log(.info, "DailyWorkload", "📊 Планка адаптирована: \(currentPointsBar) очков")
    }
    
    private func showEndDayDialog() {
        showDialog(
            title: "Может хватит на сегодня?",
            message: "Вы уже несколько раз отказались. Завершить рабочий день?",
            buttons: ["Да, хватит", "Нет, просто чуть позже"]
        ) { response in
            if response == "Да, хватит" {
                // В любом случае планируем мягкое предложение через 3 часа
                self.scheduleGentleAfternoonOffer()
            } else {
                // Продолжаем с максимальным интервалом + мягкое предложение
                self.scheduleNextMicroSessionOffer(after: 90 * 60)
                self.scheduleGentleAfternoonOffer()
            }
        }
    }

    private func getRefusalMultiplier(refusals: Int) -> Double {
        switch refusals {
        case 0: return 1.0  // 30 мин
        case 1: return 2.0  // 60 мин
        case 2: return 3.0  // 90 мин
        default: return 3.0
        }
    }

    // Заглушки для методов которые нужно реализовать
    private func getDaysWithoutWork() -> Int { return 0 }
    private func getAverageSessionLength(lastDays: Int) -> Double { return 20.0 }
    private func wasPostLunchOfferMade() -> Bool { return false }
    private func showDailyGoalCompletedDialog() { }
    private func scheduleNextMicroSessionOffer(after interval: TimeInterval) { }
    private func scheduleGentleAfternoonOffer() { }
}
```

### 2. Интеграция с EarlyEngagementSystem

В `EarlyEngagementSystem.swift` добавить проверку:

```swift
private func shouldShowEngagementMessage(wakeTime: Date, sleepDuration: TimeInterval) -> Bool {
    // Существующие проверки...
    let isGoodTime = hour >= 6 && hour <= 12
    let isRealSleep = sleepDuration > 30 * 60
    let alreadyShownToday = wasMessageShownToday()
    
    // НОВОЕ: Проверка через DailyWorkloadManager
    let workloadAllows = DailyWorkloadManager.shared.shouldOfferSession()
    
    return isGoodTime && isRealSleep && !alreadyShownToday && workloadAllows
}
```

### 3. Создание MicroSessionOfferWindow

Новое окно для предложений после микросессий:

```swift
import Cocoa

class MicroSessionOfferWindow: NSWindow {
    var onAccept: (() -> Void)?
    var onDecline: (() -> Void)?
    
    // UI элементы и логика аналогично EarlyEngagementWindow
}
```

### 4. Создание DailyGoalCompletedWindow

Окно обратной связи при достижении цели:

```swift
import Cocoa

class DailyGoalCompletedWindow: NSWindow {
    var onFeedback: ((String) -> Void)?
    
    // Показывает вопрос "Как прошел день?" с вариантами ответов
}
```

## 🧪 План тестирования

### Создать тесты для:
1. Расчета очков для разных уровней пользователя
2. Адаптации планки очков
3. Логики адаптивных интервалов
4. Определения уровня пользователя
5. Интеграции с существующими системами

### Тестовые сценарии:
1. Новичок делает микросессии
2. Опытный пользователь делает длинные сессии
3. Достижение дневной цели
4. Эскалация отказов от микросессий (30→60→90 мин)
5. Обработка кнопки "Later" в BreakEndWindow
6. Мягкие предложения через 3 часа
7. Переход между уровнями пользователя
8. Работа в рабочие/нерабочие часы

## 📅 Временные рамки

- **Фаза 1**: 2-3 дня (основной компонент)
- **Фаза 2**: 3-4 дня (интеграция + обработка отказов)
- **Фаза 3**: 3-4 дня (новые UI + BaseStyledWindow)
- **Фаза 4**: 3-4 дня (тестирование + калибровка)

**Общее время**: 11-15 дней разработки

## 🎯 Приоритеты реализации

### Высокий приоритет:
1. DailyWorkloadManager (основная логика)
2. Интеграция с микросессиями (эскалация отказов)
3. MicroSessionOfferWindow
4. Интеграция с BreakEndWindow

### Средний приоритет:
5. DailyGoalCompletedWindow
6. GentleAfternoonOfferWindow
7. BaseStyledWindow

### Низкий приоритет:
8. Расширенная статистика
9. Дополнительные настройки

## ⚠️ Риски и ограничения

1. **Сложность интеграции** - много существующих систем
2. **Калибровка очков** - может потребоваться несколько итераций
3. **Пользовательский опыт** - нужно тщательно тестировать на реальных пользователях
4. **Производительность** - система должна работать быстро

## 🎯 Критерии успеха

1. Система корректно определяет когда прекратить предложения
2. Пользователи не жалуются на навязчивость
3. Новички успешно входят в ритм работы
4. Опытные пользователи получают адекватную нагрузку
5. Система адаптируется под изменения в поведении пользователя
