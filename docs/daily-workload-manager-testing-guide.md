# 🧪 Руководство по тестированию DailyWorkloadManager

## 📊 Статус реализации

### ✅ ЗАВЕРШЕНО (готово к тестированию):
- **Фаза 1**: Основной компонент DailyWorkloadManager ✅
- **Фаза 2**: Интеграция с существующими системами ✅  
- **Фаза 3**: Новые UI компоненты ✅

### ⏳ ОСТАЛОСЬ:
- Обновление StatisticsManager для отображения очков
- Создание автоматических тестов
- Ручное тестирование и отладка

## 🎯 Как тестировать основной функционал

### 1. Тестирование адаптивных интервалов (30→60→90 мин)

**Сценарий**: Проверка эскалации при отказах от микросессий

**⚡ РЕЖИМ БЫСТРОГО ТЕСТИРОВАНИЯ ВКЛЮЧЕН!**
- Интервалы сокращены с минут до секунд: 30 сек → 60 сек → 90 сек
- Для продакшена нужно установить соответсвующий режим "быстрое тестирование предложений" в настройках

**Шаги**:
1. Запустить uProd
2. Сделать микросессию (<15 мин) - должно появиться предложение через **30 секунд**
3. Отказаться от предложения (кнопка "Later")
4. Следующее предложение должно быть через **60 секунд**
5. Отказаться еще раз - следующее через **90 секунд**
6. Отказаться в третий раз - должен появиться диалог "Может хватит на сегодня?"

**Ожидаемый результат**: Интервалы увеличиваются: 30сек→60сек→90сек (в тест режиме)

### 2. Тестирование системы очков

**Сценарий**: Проверка подсчета очков и достижения дневной цели

**📊 Как посмотреть очки**:
```bash
# В логах - ищите сообщения с тегом "DailyWorkload"
tail -f '/Users/<USER>/Library/Containers/com.local.uProd/Data/Library/Application Support/uProd/Logs/uProd_*.log' | grep "📊 Очки DailyWorkloadManager"

# Или вызовите в коде (для отладки):
DailyWorkloadManager.shared.logPointsInfo()
```

**Шаги**:
1. Запустить uProd и проверить логи для текущей планки
2. Сделать несколько сессий разной длительности (5 мин, 15 мин, 25 мин)
3. После каждой сессии проверять накопленные очки в логах
4. При достижении планки должно появиться окно DailyGoalCompletedWindow

**Ожидаемый результат**: Очки накапливаются, при достижении цели показывается поздравление

### 3. Тестирование рабочих часов

**Сценарий**: Система не должна предлагать работу вне рабочего времени

**⚙️ НАСТРОЙКА РАБОЧИХ ЧАСОВ**:
- По умолчанию: 9:00-18:00
- Читается из UserDefaults: `workingHoursStart` и `workingHoursEnd`
- ✅ **В настройках есть UI для изменения рабочих часов** (вкладка "Основные")

**Шаги для тестирования**:
1. **Временно изменить рабочие часы в коде** (для тестирования):
   ```swift
   // В DailyWorkloadManager.swift, метод workingHours
   return (start: 10, end: 16) // Тестовые часы 10-16
   ```
2. Изменить системное время на нерабочие часы (например, 17:00)
3. Попробовать запустить микросессию
4. Система не должна предлагать новые сессии

**Ожидаемый результат**: Предложения работы только в рабочие часы

**🔧 TODO**: Добавить UI для настройки рабочих часов в SettingsWindow

### 4. Тестирование мягких предложений

**Сценарий**: После выбора "Да, хватит" через 3 часа показывается мягкое предложение

**Шаги**:
1. Отказаться от 3 микросессий подряд
2. Выбрать **"Да, хватит"** в диалоге "хватит на сегодня"
3. Через 3 часа (или 3 минуты в тестовом режиме) должно появиться GentleAfternoonOfferWindow

**ВАЖНО**: Если выбрать "Нет, просто чуть позже", мягкое предложение НЕ планируется

**Ожидаемый результат**: Мягкое предложение с текстом типа "Может все-таки поработаем?"

## 🔧 Отладка через логи

Все действия DailyWorkloadManager логируются с тегом "DailyWorkload":

```bash
# Просмотр логов в реальном времени
tail -f '/Users/<USER>/Library/Containers/com.local.uProd/Data/Library/Application Support/uProd/Logs/uProd_*.log' | grep DailyWorkload
```

**Ключевые сообщения**:
- `✅ Сессия завершена: X мин = Y очков`
- `❌ Отказ от микросессии #N`
- `⏰ Запланировано предложение через X мин`
- `🌅 Время для мягкого предложения!`

## 🧪 Создание автоматических тестов

Следующий шаг - создать тест файл `Tests/DailyWorkloadManagerTest.swift`:

```swift
@main struct DailyWorkloadManagerTest {
    static func main() {
        testPointsCalculation()
        testAdaptiveIntervals()
        testWorkingHours()
        testDailyGoalReached()
        print("✅ Все тесты DailyWorkloadManager прошли!")
    }
    
    static func testPointsCalculation() {
        // Тест расчета очков для разных уровней пользователя
    }
    
    static func testAdaptiveIntervals() {
        // Тест адаптивных интервалов при отказах
    }
    
    static func testWorkingHours() {
        // Тест проверки рабочих часов
    }
    
    static func testDailyGoalReached() {
        // Тест достижения дневной цели
    }
}
```

## 🎯 Критерии успеха

### ✅ Система работает правильно если:
1. **Адаптивные интервалы**: 30→60→90 мин при отказах
2. **Рабочие часы**: Предложения только в 9-18 (или настроенное время)
3. **Система очков**: Корректный подсчет и адаптация планки
4. **Обратная связь**: Показ поздравления при достижении цели
5. **Эскалация**: Диалог "хватит на сегодня" после 3 отказов
6. **Мягкие предложения**: Через 3 часа после отказов

### ❌ Проблемы для исправления:
- Предложения вне рабочих часов
- Неправильные интервалы между предложениями
- Отсутствие обратной связи при достижении цели
- Навязчивые предложения после отказов

## 🚀 Готовность к тестированию

**ДА, можно приступать к тестированию!**

Основной функционал DailyWorkloadManager полностью реализован и интегрирован. Система готова к ручному тестированию всех сценариев.

**⚡ ВАЖНО - Режим тестирования**:
- Сейчас включен режим быстрого тестирования (30 сек вместо 30 мин)
- **Для продакшена** установите `isTestMode = false` в `DailyWorkloadManager.swift:36`

Рекомендуемый порядок:
1. **Сначала ручное тестирование** - проверить основные сценарии
2. **Создание автоматических тестов** - для регрессионного тестирования
3. **Обновление StatisticsManager** - добавить отображение очков в UI
4. **Добавить UI для рабочих часов** - в SettingsWindow

Начинайте с тестирования адаптивных интервалов - это ключевая функция системы!
