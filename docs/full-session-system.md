# Система полной сессии (Full Session System)

## 🎯 Цель системы

Предоставить пользователю возможность **сразу начать с полной сессии (52 минуты)** в любой ситуации, когда система предлагает меньший интервал.

> **Принцип:** "Дать выбор тем, кто готов работать в полную силу"

## 🧠 Психологическая основа

### Проблема принудительного снижения
- Пользователь может быть готов к полной работе
- Горизонтальная дескалация (-50%) может быть избыточной
- Вертикальная деградация может быть временной
- Принуждение к маленьким интервалам демотивирует

### Решение через выбор
- **Основная кнопка** - адаптивное предложение системы
- **Альтернативная кнопка** - полная сессия для готовых
- Пользователь сам решает, на что способен

## 📍 Где используется

### 1️⃣ Горизонтальная дескалация
**Ситуация:** Время дня снижает предлагаемый интервал

```
┌─────────────────────────────────────┐
│ Второе сообщение дня 🌅             │
│ Сегодня: 26 минут (-50%)            │
│                                     │
│ [Начать 26 мин] [Полная сессия (52)]│
└─────────────────────────────────────┘
```

```
┌─────────────────────────────────────┐
│ Третье сообщение дня 🌆             │
│ Сегодня: 13 минут (25% планки)      │
│                                     │
│ [Начать 13 мин] [Полная сессия (52)]│
└─────────────────────────────────────┘
```

### 2️⃣ Завершение отдыха
**Ситуация:** Пользователь завершил отдых, система предлагает текущую планку

```
┌─────────────────────────────────────┐
│ Отдых завершен! 🎯                  │
│ Текущая планка: 15 минут            │
│                                     │
│ [Начать 15 мин] [Полная сессия (52)]│
└─────────────────────────────────────┘
```

### 3️⃣ Первое сообщение дня (Early Engagement)
**Ситуация:** Утреннее предложение работы над приоритетным проектом

```
┌─────────────────────────────────────┐
│ Доброе утро! 🌅                     │
│ Приоритетный проект: 35 минут       │
│                                     │
│ [Начать 35 мин] [Полная сессия (52)]│
└─────────────────────────────────────┘
```

```
┌─────────────────────────────────────┐
│ 7 дней без работы 😔                │
│ Текущая планка: 3 минуты            │
│                                     │
│ [Начать 3 мин] [Полная сессия (52)] │
└─────────────────────────────────────┘
```

## 🚫 Условия скрытия второй кнопки

### Когда НЕ показывать "Полную сессию"
```swift
if (текущая_планка >= 52) {
    // Показываем только [52 мин]
    // Вторую кнопку НЕ показываем
}
```

**Пример:**
```
┌─────────────────────────────────────┐
│ Доброе утро! 🌅                     │
│ Текущая планка: 52 минуты           │
│                                     │
│ [Начать 52 мин]                     │
│                                     │
└─────────────────────────────────────┘
```

## 📊 Влияние на планку пользователя

### При успешном выполнении полной сессии

**Сценарий 1: Горизонтальная дескалация**
```swift
// Пользователь выбрал полную сессию вместо дескалированной
if (выполнил_полную_сессию && была_горизонтальная_дескалация) {
    // Планка растет по градационной системе
    новая_планка = применить_градационный_рост(текущая_планка)
}
```

**Сценарий 2: Вертикальная деградация**
```swift
// Пользователь выбрал полную сессию вместо деградированной планки
if (выполнил_полную_сессию && была_вертикальная_деградация) {
    // Значительное восстановление планки
    новая_планка = min(52, старая_планка * 2)
}
```

### 📊 Градационная система роста планок

**🚀 Быстрый старт (маленькие планки):**
- **3-7 мин: +60%** (3→5, 5→8, 7→11)

**⚡ Активный рост (средние планки):**
- **8-15 мин: +40%** (8→11, 10→14, 15→21)
- **16-25 мин: +25%** (16→20, 20→25, 25→31)

**🎯 Стабилизация (большие планки):**
- **26-40 мин: +15%** (26→30, 30→35, 40→46)
- **41-52 мин: +10%** (41→45, 45→50, 50→52)

### Примеры восстановления планки

| Ситуация | Была планка | Выбрал | Выполнил | Формула | Новая планка |
|----------|-------------|---------|----------|---------|--------------|
| Горизонталь | 40 мин | 52 мин | 52 мин | 40×1.15 | 46 мин |
| Горизонталь | 15 мин | 52 мин | 52 мин | 15×1.40 | 21 мин |
| Вертикаль | 3 мин | 52 мин | 52 мин | 3×2 | 6 мин |
| Вертикаль | 10 мин | 52 мин | 52 мин | 10×2 | 20 мин |
| Отдых | 25 мин | 52 мин | 52 мин | 25×1.25 | 31 мин |

## 🛡️ Система безопасности

### Мониторинг использования
```swift
struct FullSessionStats {
    var attempts: Int          // Сколько раз выбирал
    var completions: Int       // Сколько раз завершил
    var successRate: Double    // Процент успеха
}
```

### Условия блокировки
```swift
if (successRate < 0.3 && attempts >= 3) {
    // Убираем кнопку "Полная сессия"
    // Пользователь не справляется с 52 минутами
    hideFullSessionButton = true
}
```

### Условия восстановления
```swift
if (hideFullSessionButton && 
    последние_5_обычных_интервалов_успешны &&
    текущая_планка >= 30) {
    // Возвращаем кнопку
    hideFullSessionButton = false
}
```

## 🎪 Сценарии использования

### Сценарий 1: "Быстрое восстановление"
```
День 1: Планка 3 мин → выбрал 52 мин → выполнил → планка 30 мин
День 2: Планка 30 мин → выбрал 52 мин → выполнил → планка 52 мин
Результат: восстановление за 2 дня!
```

### Сценарий 2: "Преодоление горизонтали"
```
Утром: Планка 40 мин → работал 40 мин
Днем: Предложение 20 мин → выбрал 52 мин → работал 52 мин
Результат: не потерял продуктивность из-за времени дня
```

### Сценарий 3: "Неудачная попытка"
```
Попытка 1: 3 мин → выбрал 52 мин → бросил через 10 мин
Попытка 2: 3 мин → выбрал 52 мин → бросил через 15 мин  
Попытка 3: 3 мин → выбрал 52 мин → бросил через 5 мин
Результат: кнопка "Полная сессия" скрыта
```

## 💬 Тексты сообщений

### Базовые шаблоны
```
Основная кнопка: "Начать {адаптивное_время}"
Альтернативная: "Полная сессия (52 мин)"
```

### Контекстные варианты
```
// Для вертикальной деградации
"Начать план-минимум (3 мин)"
"Полная сессия (52 мин)"

// Для горизонтальной дескалации  
"Начать сегодня (26 мин)"
"Полная сессия (52 мин)"

// Для обычной работы
"Начать интервал (35 мин)"
"Полная сессия (52 мин)"
```

## 📈 Метрики эффективности

### Отслеживаемые показатели
- **Частота выбора полной сессии** (% от всех стартов)
- **Процент завершения полных сессий**
- **Скорость восстановления планки**
- **Влияние на общую продуктивность**
- **Частота блокировки кнопки**

### Критерии успеха
- **>70% завершения** полных сессий - кнопка работает правильно
- **<30% завершения** - нужна корректировка условий показа
- **10-20% выбора** полной сессии - оптимальная частота использования

## 🔧 Техническая реализация

### Основные компоненты
```swift
class FullSessionSystem {
    func shouldShowFullSession(currentBar: TimeInterval) -> Bool
    func handleFullSessionCompletion(completed: TimeInterval, context: SessionContext)
    func updateSuccessRate(userId: String, completed: Bool)
}
```

### Интеграция с UI
```swift
// В каждом сообщении Early Engagement
if (currentBar < 52 && fullSessionSystem.shouldShowFullSession(currentBar)) {
    showButtons([
        adaptiveButton,    // Основная кнопка
        fullSessionButton  // "Полная сессия (52)"
    ])
} else {
    showButtons([adaptiveButton])
}
```

## 🎯 Ключевые преимущества

- ✅ **Свобода выбора** - пользователь решает сам
- ✅ **Быстрое восстановление** - для готовых к полной работе
- ✅ **Преодоление ограничений** - горизонтальной дескалации
- ✅ **Безопасность** - автоматическое скрытие при неудачах
- ✅ **Гибкость** - работает в любом контексте системы
