# Градационная система роста планок (Gradual Growth System)

## 🎯 Цель системы

Обеспечить **оптимальный рост планки** в зависимости от текущего размера, избегая как слишком медленного роста (демотивация), так и слишком быстрого (провалы).

> **Принцип:** "Чем меньше планка, тем быстрее должен быть рост"

## 🧠 Психологическая основа

### Проблема единого процента роста
- **Маленькие планки (3-7 мин):** +10% = незаметный рост, демотивация
- **Большие планки (40+ мин):** +10% = комфортный рост, не пугает
- **Средние планки (15-25 мин):** +10% = слишком медленно для активной фазы

### Решение через градацию
- **Быстрый старт** для маленьких планок - быстрее выйти из "ямы"
- **Активный рост** для средних планок - использовать импульс
- **Стабилизация** для больших планок - не пугать скачками

## 📊 Формула градационного роста

```swift
func calculateGradualGrowth(currentBar: Int) -> Int {
    switch currentBar {
    case 3...7:   return Int(currentBar * 1.60)  // +60%
    case 8...15:  return Int(currentBar * 1.40)  // +40%
    case 16...25: return Int(currentBar * 1.25)  // +25%
    case 26...40: return Int(currentBar * 1.15)  // +15%
    case 41...52: return Int(currentBar * 1.10)  // +10%
    default:      return min(currentBar + 1, 52) // Безопасность
    }
}
```

## 🚀 Диапазоны роста

### **Быстрый старт (3-7 минут): +60%**
```
3 мин → 5 мин   (+2 мин, заметно!)
4 мин → 6 мин   (+2 мин)
5 мин → 8 мин   (+3 мин)
6 мин → 10 мин  (+4 мин)
7 мин → 11 мин  (+4 мин)
```

**Обоснование:**
- ✅ **Быстрый выход** из зоны "план-минимум"
- ✅ **Заметный прогресс** - мотивирует продолжать
- ✅ **Психологически важно** - показать, что система работает

### **Активный рост (8-15 минут): +40%**
```
8 мин → 11 мин   (+3 мин)
10 мин → 14 мин  (+4 мин)
12 мин → 17 мин  (+5 мин)
15 мин → 21 мин  (+6 мин)
```

**Обоснование:**
- ✅ **Активная фаза** - пользователь набирает обороты
- ✅ **Хороший темп** - не слишком быстро, не слишком медленно
- ✅ **Строительство привычки** - закрепление навыка

### **Активный рост (16-25 минут): +25%**
```
16 мин → 20 мин  (+4 мин)
20 мин → 25 мин  (+5 мин)
25 мин → 31 мин  (+6 мин)
```

**Обоснование:**
- ✅ **Переходная зона** - от активного роста к стабилизации
- ✅ **Умеренный темп** - не пугает, но прогресс заметен
- ✅ **Подготовка к большим планкам**

### **Стабилизация (26-40 минут): +15%**
```
26 мин → 30 мин  (+4 мин)
30 мин → 35 мин  (+5 мин)
35 мин → 40 мин  (+5 мин)
40 мин → 46 мин  (+6 мин)
```

**Обоснование:**
- ✅ **Зона комфорта** - пользователь уже может работать долго
- ✅ **Осторожный рост** - не создавать стресс
- ✅ **Приближение к максимуму** - подготовка к 52 минутам

### **Финальная стабилизация (41-52 минуты): +10%**
```
41 мин → 45 мин  (+4 мин)
45 мин → 50 мин  (+5 мин)
50 мин → 52 мин  (+2 мин, достигнут максимум)
```

**Обоснование:**
- ✅ **Максимальная осторожность** - близко к пределу
- ✅ **Сохранение достижений** - не рисковать провалом
- ✅ **Финальный рывок** к 52 минутам

## 📈 Сравнение с единым ростом +10%

### Время достижения 52 минут

| Стартовая планка | Единый +10% | Градационная | Ускорение |
|------------------|-------------|--------------|-----------|
| 3 мин | ~100 дней | ~25 дней | **×4 быстрее** |
| 10 мин | ~55 дней | ~15 дней | **×3.7 быстрее** |
| 20 мин | ~27 дней | ~8 дней | **×3.4 быстрее** |
| 30 мин | ~17 дней | ~5 дней | **×3.4 быстрее** |

### Пример восстановления с 3 минут

**Единый рост +10%:**
```
День 1: 3 → 3.3 ≈ 3 мин (незаметно)
День 5: 3 → 4 мин
День 15: 4 → 6 мин
День 30: 6 → 10 мин
День 60: 10 → 20 мин
День 100: 20 → 52 мин
```

**Градационный рост:**
```
День 1: 3 → 5 мин (+60%, заметно!)
День 2: 5 → 8 мин (+60%)
День 3: 8 → 11 мин (+40%)
День 5: 11 → 15 мин (+40%)
День 8: 15 → 21 мин (+40%)
День 12: 21 → 26 мин (+25%)
День 18: 26 → 40 мин (+15%)
День 25: 40 → 52 мин (+15%)
```

## 🎪 Применение в разных системах

### В системе взращивания сессий
```swift
// После выполнения взращенной сессии
let totalCompleted = 18 // минут
let oldBar = 3 // минут

// Используем формулу умеренного роста (60% от выполненного, макс ×3)
let moderateGrowth = min(totalCompleted * 0.6, oldBar * 3) // min(10.8, 9) = 9

// Применяем градационный рост к результату
let newBar = calculateGradualGrowth(9) // 9 × 1.40 = 13 минут
```

### В системе полной сессии
```swift
// Пользователь выполнил полную сессию 52 мин при планке 15 мин
let currentBar = 15
let newBar = calculateGradualGrowth(currentBar) // 15 × 1.40 = 21 минута
```

### В обычном росте планки
```swift
// Пользователь выполнил свою обычную планку
let currentBar = 25
let newBar = calculateGradualGrowth(currentBar) // 25 × 1.25 = 31 минута
```

## 🛡️ Ограничения безопасности

### Максимальные пределы
```swift
let newBar = min(calculateGradualGrowth(currentBar), 52) // Не больше 52 минут
```

### Проверка реалистичности
```swift
// Если рост слишком большой для пользователя
if (newBar > currentBar * 2) {
    newBar = currentBar * 2 // Максимум удвоение
}
```

### Откат при провалах
```swift
// Если пользователь не справился с новой планкой
if (failed) {
    newBar = currentBar * 0.8 // Откат на 20%
}
```

## 🔧 Техническая реализация

### Основная функция
```swift
class GradualGrowthSystem {
    static func calculateGrowth(currentBar: Int) -> Int {
        let newBar: Int
        
        switch currentBar {
        case 3...7:   newBar = Int(Double(currentBar) * 1.60)
        case 8...15:  newBar = Int(Double(currentBar) * 1.40)
        case 16...25: newBar = Int(Double(currentBar) * 1.25)
        case 26...40: newBar = Int(Double(currentBar) * 1.15)
        case 41...52: newBar = Int(Double(currentBar) * 1.10)
        default:      newBar = currentBar + 1
        }
        
        return min(newBar, 52) // Максимум 52 минуты
    }
}
```

### Интеграция с другими системами
```swift
// В SessionGrowingSystem
let moderateGrowth = min(totalCompleted * 0.6, oldBar * 3)
let finalBar = GradualGrowthSystem.calculateGrowth(moderateGrowth)

// В FullSessionSystem  
let newBar = GradualGrowthSystem.calculateGrowth(currentBar)

// В обычном PomodoroTimer
let newBar = GradualGrowthSystem.calculateGrowth(userBar)
```

## 🎯 Ключевые преимущества

- ✅ **Быстрое восстановление** - в 3-4 раза быстрее единого роста
- ✅ **Психологически обоснованно** - учитывает особенности каждого диапазона
- ✅ **Заметный прогресс** - мотивирует на всех этапах
- ✅ **Безопасность** - не создает стресс большими скачками
- ✅ **Универсальность** - применимо во всех системах роста планок
