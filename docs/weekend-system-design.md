# Система выходных uProd - Техническое описание

## 🎯 ФИНАЛЬНОЕ ВИДЕНИЕ СИСТЕМЫ ВЫХОДНЫХ

### **Стратегии работы:**
- **3/1** (рекомендуемая) - смещающиеся циклы: 3 дня работы → 1 день отдыха
- **5/2** (классическая) - фиксированные выходные дни недели
- **Своя** - ручной выбор дней

### **Логика смещения 3/1:**
✅ **Правильно понимаю:** Если пользователь работает в запланированный выходной, весь цикл смещается:
- Работал: Пн-Вт-Ср, планировался отдых в Чт
- Поработал в Чт → следующий отдых предлагается в Пт  
- Отдыхал 3 дня (Пт-Сб-Вс) → следующий цикл: Пн-Вт-Ср работа, Чт отдых

### **Система прогрессивных напоминаний:**
В окне "отдохнуть/поработать" (заменяет СРВ в выходные):

**1 день без отдыха:**
- "Сегодня выходной. Вчера ты работал, но сегодня рекомендуется отдохнуть"
- Кнопки: "Отдохнуть" / "Поработать"

**2 дня без отдыха:**
- "Ты уже 2 дня работаешь без отдыха. Рекомендуется восстановиться"
- Кнопки: "Отдохнуть" / "Поработать"

**3-4 дня без отдыха:**
- "⚠️ Ты работаешь уже [X] дня без отдыха. Напряжение накапливается - настоятельно рекомендуется отдохнуть!"
- Кнопки: "Отдохнуть" / "Все равно работать"

**5-6 дней без отдыха:**
- "🔴 Ты работаешь уже [X] дней без отдыха! Срочно отдохни, чтобы избежать выгорания!"
- Кнопки: "Отдохнуть" / "Игнорировать"

**7+ дней без отдыха:**
- "🚨 Ты работаешь уже [X] дней подряд! Это крайне опасно для здоровья. Немедленно отдохни!"
- Кнопки: "Отдохнуть" / "Я понимаю риски"

### **Контекстные советы по отдыху:**
После выбора "Отдохнуть" - красивая плашка посреди экрана (macOS стиль):

**Рекомендации:**
- Меньше гаджетов и экранов
- Избегайте YouTube/TikTok (особенно короткие видео!)
- Прогулки на свежем воздухе
- Встречи с друзьями
- Деятельность руками (готовка, рукоделие)
- Меньше думайте о работе и проектах
- Больше восстановительной активности

**Кнопка "Подробнее"** → ведет на статью о правильном отдыхе (пока заглушка)

### **Интеграция с системой:**
- **Доступность:** с уровня Regular (120 очков)
- **Счетчик:** "дней без отдыха" в контекстном меню (4-й пункт)
- **Уведомления:** "завтра выходной" в окне завершения дня
- **Статистика:** блок "соблюдение выходных %" в окне статистики
- **Стрики:** в выходной не растут/не сбрасываются

---

## 📋 ДЕТАЛЬНЫЙ ПЛАН РЕАЛИЗАЦИИ

### **ЭТАП 1: Создание базовой системы выходных**

#### **1.1 Создать WeekendManager.swift**
```swift
class WeekendManager {
    enum WorkStrategy: String, CaseIterable {
        case threeOne = "3/1"
        case fiveTwo = "5/2" 
        case custom = "custom"
    }
    
    // Методы:
    - calculateNextWeekendDay(strategy:, lastWeekend:) -> Date
    - isWeekendDay(date:) -> Bool
    - getDaysWithoutRest() -> Int
    - shouldShowWeekendChoice() -> Bool
    - handleWeekendChoice(choice: rest/work)
}
```

#### **1.2 Интеграция с DailyWorkloadManager**
- Добавить проверку уровня доступности (Regular = 120 очков)
- Добавить методы для работы с WeekendManager

#### **1.3 Модификация ActivityStateManager**
- Добавить проверку `WeekendManager.isWeekendDay()` 
- В выходные дни вместо СРВ вызывать `showWeekendChoice()`

### **ЭТАП 2: UI для выбора стратегий**

#### **2.1 Модификация SettingsWindow.swift**
- Во вкладке "Время" добавить над рабочими днями:
  - Выпадающий список стратегий
  - Тултип "?" с описанием стратегий  
  - Деактивация выбора дней при готовых стратегиях

#### **2.2 Создание WeekendChoiceWindow.swift**
```swift
class WeekendChoiceWindow: NSWindow {
    // Заменяет СРВ в выходные дни
    // Показывает прогрессивные сообщения
    // Кнопки: "Отдохнуть" / "Поработать" (меняются по уровням)
}
```

### **ЭТАП 3: Система прогрессивных напоминаний**

#### **3.1 Создать WeekendMessageManager.swift**
```swift
struct WeekendMessage {
    let title: String
    let message: String  
    let restButtonText: String
    let workButtonText: String
    let level: Int // 1-7+ дней без отдыха
}

class WeekendMessageManager {
    func getMessageForDaysWithoutRest(_ days: Int) -> WeekendMessage
}
```

#### **3.2 Интеграция с уведомлениями**
- Модификация окна завершения дня для "завтра выходной"
- Добавление в контекстное меню счетчика дней без отдыха

### **ЭТАП 4: Контекстные советы по отдыху**

#### **4.1 Создать RestAdviceWindow.swift**
```swift
class RestAdviceWindow: NSWindow {
    // macOS стиль окна
    // Рандомные советы из массива
    // Кнопка "Подробнее" (заглушка)
    // Закрытие по крестику
}
```

#### **4.2 Массив советов**
```swift
let restAdvices = [
    "Меньше гаджетов и экранов",
    "Избегайте YouTube/TikTok", 
    "Прогулки на свежем воздухе",
    // ... остальные советы
]
```

### **ЭТАП 5: Статистика и интеграция**

#### **5.1 Модификация StatisticsWindow.swift**
- Добавить блок "Соблюдение выходных" 
- Показывать процент по периодам (неделя/месяц/год)

#### **5.2 Модификация AppDelegate.swift**
- Добавить в контекстное меню счетчик дней без отдыха
- Интеграция с существующими системами

### **ЭТАП 6: Тестирование и отладка**

#### **6.1 Создать WeekendSystemTest.swift**
- Тесты логики смещения циклов 3/1
- Тесты прогрессивных напоминаний
- Тесты интеграции с уровнями доступа

---

## 🔧 КЛЮЧЕВЫЕ ИНТЕГРАЦИОННЫЕ ТОЧКИ:

1. **ActivityStateManager** → проверка выходных вместо СРВ
2. **DailyWorkloadManager** → проверка уровня доступа  
3. **SettingsWindow** → UI выбора стратегий
4. **AppDelegate** → контекстное меню + уведомления
5. **StatisticsWindow** → блок статистики выходных

## 🎯 ТЕХНИЧЕСКИЕ РЕШЕНИЯ:

### **Хранение данных (UserDefaults):**
- `weekendStrategy`: String - выбранная стратегия
- `lastWeekendDate`: Date - дата последнего выходного
- `daysWithoutRest`: Int - счетчик дней без отдыха
- `weekendComplianceHistory`: [String: Bool] - история соблюдения

### **Интеграция с существующими системами:**
- Использовать существующую архитектуру callback'ов
- Интегрироваться с системой уровней DailyWorkloadManager
- Использовать существующие UI стили из UIStyles.swift
- Следовать паттернам существующих окон уведомлений
