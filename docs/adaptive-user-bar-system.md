# 🎯 Система адаптивных планок пользователя

> **Ключевая концепция uProd** - динамическая адаптация интервалов и нагрузки под успехи и неудачи пользователя

## 🎌 Философия системы планок

**Принцип "как в спортзале":** Есть прогресс (несколько дней) - увеличиваем планку. Есть неудачи - уменьшаем. 

**Ключевая идея:** Вместо статичных интервалов используем динамическую "текущую планку" - базовый показатель пользователя, который адаптируется под его результаты.

### 🔄 Принципы адаптации:
- **Быстрое таяние при неудачах** - чтобы не демотивировать
- **Медленный рост при успехах** - устойчивое развитие
- **Сохранение мотивации** - планка всегда достижима
- **Прогрессивная нагрузка** - постепенное увеличение сложности

---

## 📊 ЧАСТЬ А: Планка продолжительности интервала [РЕАЛИЗУЕМ СЕЙЧАС]

### **Базовые параметры:**
- **Максимальная планка:** 52 минуты (потолок роста)
- **Минимальная планка:** 5 минут (пол снижения)
- **План-минимум:** 1-3 минуты (всегда доступен)

### **Принцип работы текущей планки:**

**КЛЮЧЕВОЕ ПОНИМАНИЕ:** Текущая планка - это **плавающее значение**, которое меняется каждый день в зависимости от поведения пользователя.

**Пример работы:**
```
День 1: Текущая планка = 40 мин
Пользователь поработал → День 2: Планка = 44 мин (+10%)
Пользователь пропустил → День 3: Планка рассчитывается по уровню пропусков
```

### **Формула расчета при пропусках:**

**Если пользователь НЕ работал несколько дней, планка снижается:**
- **Уровень 0** (работал вчера): планка растет по [градационной системе](gradual-growth-system.md) (+10% до +60%)
- **Уровень 1** (1 день пропуск): планка × 77% (-23%)
- **Уровень 2-3** (2-3 дня): планка × 58% (-42%)
- **Уровень 4-6** (4-6 дней): планка × 29% (-71%)
- **Уровень 7+** (неделя+): планка = план-минимум (1-3 мин)

### **Логика роста планки:**
```
Если пользователь работал вчера:
→ Текущая планка увеличивается по градационной системе (+10% до +60%)
→ Максимум до 52 минут

Если пропуски:
→ Текущая планка снижается согласно формуле выше
→ Минимум до 5 минут (потом план-минимум)
```

### **Интеграция с матрицей сообщений:**

**ВАЖНО:** Текущая планка - это основа для всех предложений дня!

**Пример работы (если текущая планка пользователя = 30 мин):**
1. **Первое предложение дня** - 100% текущей планки = 30 мин
2. **Второе предложение** - 50% текущей планки = 15 мин
3. **Третье предложение** - 25% текущей планки = 7 мин
4. **Четвертое предложение** - план-минимум = 2-3 мин

**Тон сообщений** зависит от уровня дней без работы (не от размера планки)

### **Конкретный пример работы системы:**

```
ДЕНЬ 1: Пользователь начинает с планки 40 мин
- Поработал → ДЕНЬ 2: Планка = 44 мин (+10%)

ДЕНЬ 2: Текущая планка = 44 мин
- Пропустил → ДЕНЬ 3: Планка = 44 × 0.77 = 34 мин (-23%)

ДЕНЬ 3: Текущая планка = 34 мин
- Пропустил второй день → ДЕНЬ 4: Планка = 34 × 0.58 = 20 мин (-42%)

ДЕНЬ 4: Текущая планка = 20 мин
Предложения в течение дня:
• 1-е: 20 мин (100% от текущей планки)
• 2-е: 10 мин (50% от текущей планки)
• 3-е: 15 мин (фиксированный минимум)
• 4-е: 2-3 мин (план-минимум)
```

---

## 🔢 ЧАСТЬ Б: Планка количества интервалов в день [КОНЦЕПЦИЯ]

### **Принцип "спортзал":**
Если застрял на весе (например, жмешь 100 кг 3 подхода по 8 раз и не можешь больше), можно сделать 4 подхода по 6 раз. В итоге будет больше, а значит в следующий раз сможешь сделать больше чем 3х8. 

**Аналогично в uProd:**
- Сейчас: 3 интервала по 52 минуты
- Следующий уровень: 4 интервала по 40-45 минут

### **Логика адаптации количества:**
```
Текущая нагрузка: X интервалов по Y минут

Если справляется несколько дней:
→ Вариант 1: (X+1) интервалов по (Y-10) минут
→ Вариант 2: X интервалов по (Y+10) минут

Если не справляется:
→ (X-1) интервал по Y минут
→ Или X интервалов по (Y-10) минут
```

### **Связь с Частью А:**
- Часть А определяет продолжительность каждого интервала
- Часть Б определяет количество интервалов в день
- Обе части работают синхронно для оптимальной нагрузки

### **TODO для будущей реализации:**
- [ ] Анализ успешности выполнения интервалов
- [ ] Алгоритм выбора: больше интервалов или длиннее интервалы
- [ ] Интеграция с календарем и планированием дня
- [ ] Система тестирования новых планок
- [ ] Персонализация под тип пользователя

---

## 🔮 Будущее развитие системы

### **Динамическая адаптация:**
- Анализ паттернов работы за неделю/месяц
- Учет времени дня и дня недели
- Персонализация под индивидуальные особенности
- Показатель "постоянства" пользователя

### **Интеграция с другими системами:**
- Система барьеров (помощь при неудачах)
- Мотивационные сообщения
- Анализ продуктивности
- Долгосрочное планирование

---

## 📋 Техническая реализация

### **Хранение данных:**
```swift
struct UserBar {
    var currentDurationBar: Int = 52  // текущая планка продолжительности
    var currentCountBar: Int = 1      // текущая планка количества (будущее)
    var daysWithoutWork: Int = 0      // дни без работы
    var lastWorkDate: Date?           // последний день работы
}
```

### **Расчет планки:**
```swift
func calculateNewBar(currentBar: Int, daysWithoutWork: Int) -> Int {
    switch daysWithoutWork {
    case 0:
        // Работал вчера - используем градационную систему роста
        return GradualGrowthSystem.calculateGrowth(currentBarMinutes: currentBar)
    case 1:
        return Int(Double(currentBar) * 0.77)  // -23%
    case 2...3:
        return Int(Double(currentBar) * 0.58)  // -42%
    case 4...6:
        return Int(Double(currentBar) * 0.29)  // -71%
    default:
        return 3  // план-минимум
    }
}
```

---

## ✅ Критерии готовности

**Часть А (продолжительность):**
- [ ] Формула расчета планки реализована
- [ ] Интеграция с матрицей сообщений
- [ ] Тестирование на разных уровнях
- [ ] Логирование изменений планки

**Часть Б (количество) - концепция:**
- [ ] Документация принципов готова
- [ ] Связь с Частью А определена
- [ ] План реализации создан
