#!/usr/bin/swift

import Foundation

print("🔍 Отладка системы тестовых данных")
print("=" + String(repeating: "=", count: 49))

// Получаем UserDefaults для uProd
let defaults = UserDefaults(suiteName: "com.local.uProd") ?? UserDefaults.standard

// Функция для добавления тестового интервала (копия из TestDataManager)
func addTestInterval(daysAgo: Int, durationMinutes: Int) {
    let calendar = Calendar.current
    let now = Date()
    let targetDate = calendar.date(byAdding: .day, value: -daysAgo, to: now) ?? now
    
    // Создаем тестовый интервал
    let interval: [String: Any] = [
        "startTime": targetDate,
        "duration": TimeInterval(durationMinutes * 60),
        "type": "test",
        "projectId": "test-project"
    ]
    
    // Получаем существующие интервалы
    var intervals = defaults.array(forKey: "completedIntervals") as? [[String: Any]] ?? []
    
    // Добавляем новый интервал
    intervals.append(interval)
    
    // Сохраняем
    defaults.set(intervals, forKey: "completedIntervals")
    defaults.synchronize()
    
    let formatter = DateFormatter()
    formatter.dateFormat = "dd.MM HH:mm"
    print("✅ Добавлен тестовый интервал: \(formatter.string(from: targetDate)) - \(durationMinutes)мин")
}

// Функция для очистки тестовых данных
func clearTestData() {
    let intervals = defaults.array(forKey: "completedIntervals") as? [[String: Any]] ?? []
    let nonTestIntervals = intervals.filter { interval in
        let type = interval["type"] as? String ?? ""
        return type != "test"
    }
    
    defaults.set(nonTestIntervals, forKey: "completedIntervals")
    defaults.synchronize()
    print("🧹 Очищены тестовые данные")
}

// Функция для показа всех интервалов
func showAllIntervals() {
    let intervals = defaults.array(forKey: "completedIntervals") as? [[String: Any]] ?? []
    print("\n📊 Всего интервалов: \(intervals.count)")
    
    let formatter = DateFormatter()
    formatter.dateFormat = "dd.MM HH:mm"
    
    for (index, interval) in intervals.enumerated() {
        if let startTime = interval["startTime"] as? Date,
           let duration = interval["duration"] as? TimeInterval,
           let type = interval["type"] as? String {
            
            let durationMin = Int(duration / 60)
            print("   \(index + 1). \(formatter.string(from: startTime)) - \(durationMin)мин (\(type))")
        }
    }
}

// Функция для проверки активности по дням
func checkActivityByDays() {
    let calendar = Calendar.current
    let today = Date()
    
    print("\n🔍 Проверка активности по дням:")
    
    for dayOffset in 0..<7 {
        let checkDate = calendar.date(byAdding: .day, value: -dayOffset, to: today) ?? today
        let dateStr = DateFormatter().string(from: checkDate)
        
        // Проверяем наличие интервалов в этот день
        let intervals = defaults.array(forKey: "completedIntervals") as? [[String: Any]] ?? []
        let dayIntervals = intervals.filter { interval in
            if let startTime = interval["startTime"] as? Date {
                return calendar.isDate(startTime, inSameDayAs: checkDate)
            }
            return false
        }
        
        let dayName = dayOffset == 0 ? "Сегодня" : "\(dayOffset) дн. назад"
        print("   \(dayName) (\(dateStr)): \(dayIntervals.count) интервалов")
    }
}

// Тестируем сценарий 3
print("\n🧪 ТЕСТ СЦЕНАРИЯ 3: Работал ЧТ+ПТ+СБ, сегодня НЕ работал")

// Очищаем данные
clearTestData()
showAllIntervals()

// Добавляем 3 дня работы (не включая сегодня)
addTestInterval(daysAgo: 3, durationMinutes: 30) // ЧТ
addTestInterval(daysAgo: 2, durationMinutes: 30) // ПТ
addTestInterval(daysAgo: 1, durationMinutes: 30) // СБ

showAllIntervals()
checkActivityByDays()

print("\n🎯 Теперь проверьте контекстное меню uProd!")
print("Должно показать: выходной СЕГОДНЯ")
