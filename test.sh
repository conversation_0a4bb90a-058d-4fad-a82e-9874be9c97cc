#!/bin/bash

#
# test.sh - Автоматическое тестирование uProd
#
# Запускает все юнит-тесты и интеграционные тесты
# Возвращает код ошибки если тесты провалены
#

set -e  # Остановка при первой ошибке

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 uProd Auto-Testing System${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}Включает тесты унифицированной системы${NC}"
echo ""

# Проверяем наличие Swift
if ! command -v swift &> /dev/null; then
    echo -e "${RED}❌ Swift не найден! Установите Xcode Command Line Tools.${NC}"
    exit 1
fi

# Переходим в директорию проекта
cd "$(dirname "$0")"

echo -e "${YELLOW}📂 Рабочая директория: $(pwd)${NC}"
echo ""

# Счетчики тестов
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Функция запуска теста с зависимостями
run_test() {
    local test_name="$1"
    local test_file="$2"

    echo -e "${BLUE}🔍 Запуск: $test_name${NC}"
    echo "   Файл: $test_file"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    # Проверяем существование файла
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}❌ Файл теста не найден: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # Определяем необходимые зависимости для компиляции
    local dependencies=""

    # Базовые зависимости для большинства тестов
    if [ -f "SimplePomodoroTest/MinuteActivityTracker.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/MinuteActivityTracker.swift"
    fi
    if [ -f "SimplePomodoroTest/UnifiedActivityChecker.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/UnifiedActivityChecker.swift"
    fi
    if [ -f "SimplePomodoroTest/ActivityStateManager.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/ActivityStateManager.swift"
    fi
    if [ -f "SimplePomodoroTest/Logger.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/Logger.swift"
    fi

    # Запускаем тест напрямую через swift (тесты написаны как скрипты)
    if swift "$test_file" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $test_name: ПРОШЕЛ${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ $test_name: ПРОВАЛЕН${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Функция запуска теста системы раннего вовлечения с полными зависимостями
run_engagement_test() {
    local test_name="$1"
    local test_file="$2"

    echo -e "${BLUE}🔍 Запуск: $test_name${NC}"
    echo "   Файл: $test_file"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    # Проверяем существование файла
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}❌ Файл теста не найден: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # Зависимости для системы раннего вовлечения
    local dependencies=""

    # Основные компоненты системы раннего вовлечения
    if [ -f "SimplePomodoroTest/EarlyEngagementSystem.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/EarlyEngagementSystem.swift"
    fi
    if [ -f "SimplePomodoroTest/MessageConstructionMatrix.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/MessageConstructionMatrix.swift"
    fi
    if [ -f "SimplePomodoroTest/EngagementStatistics.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/EngagementStatistics.swift"
    fi

    # Базовые зависимости
    if [ -f "SimplePomodoroTest/Logger.swift" ]; then
        dependencies="$dependencies SimplePomodoroTest/Logger.swift"
    fi

    # Запускаем тест напрямую через swift (тесты написаны как скрипты)
    if swift "$test_file" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $test_name: ПРОШЕЛ${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ $test_name: ПРОВАЛЕН${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Запускаем тесты
echo -e "${YELLOW}🚀 Запуск юнит-тестов...${NC}"
echo ""

# Модуль 1: Система неформальных сессий
run_test "Модуль 1: Неформальные сессии" "Tests/InformalSessionTest.swift"

# Модуль 2: Формальные интервалы (Pomodoro)
run_test "Модуль 2: Формальные интервалы" "Tests/PomodoroTimerTest.swift"

# Модуль 3: Система отдыха
run_test "Модуль 3: Система отдыха" "Tests/BreakSystemTest.swift"

# Модуль 4: ТЕСТ ЛОГИКИ ПОКАЗА ОКНА (защита от регрессий)
run_test "Модуль 4: Логика показа окна" "Tests/RealAppDelegateLogicTest.swift"

# Модуль 5: УНИФИЦИРОВАННАЯ СИСТЕМА НАПОМИНАНИЙ (новый)
run_test "Модуль 5: Унифицированная система" "Tests/UnifiedReminderSystemTest.swift"

# Модуль 6: ИНТЕГРАЦИОННЫЕ ТЕСТЫ РЕАЛЬНОЙ ЛОГИКИ (новый)
run_test "Модуль 6: Интеграция реальной логики" "Tests/RealLogicIntegrationTest.swift"

# Модуль 7: БАЗОВЫЕ ТЕСТЫ УНИФИЦИРОВАННОЙ СИСТЕМЫ (2024)
run_test "Модуль 7: Базовая логика унификации" "Tests/UnifiedSystemBasicTest.swift"

# Модуль 8: ИНТЕГРАЦИОННЫЕ ТЕСТЫ УНИФИКАЦИИ (2024)
run_test "Модуль 8: Интеграция унификации" "Tests/IntegrationTest.swift"

# Модуль 11: НОВАЯ ЕДИНАЯ СИСТЕМА НАПОМИНАНИЙ (исправлено дублирование)
run_test "Модуль 11: Новая единая система" "Tests/UnifiedSystemTest.swift"

# Модуль 12: ПОВЕДЕНИЕ КНОПКИ ТЕСТ (исправлено дублирование)
run_test "Модуль 12: Поведение кнопки тест" "Tests/TestButtonBehaviorTest.swift"

# Модуль 9: НАСТОЯЩИЕ РЕАЛЬНЫЕ ТЕСТЫ (защита от регрессий)
run_test "Модуль 9: Настоящие реальные тесты" "Tests/TrueRealUnifiedSystemTest.swift"

# Модуль 10: ТЕСТЫ КРИТИЧЕСКОЙ ЗОНЫ (новый)
run_test "Модуль 10: Критическая зона" "Tests/CriticalZoneTest.swift"

# Модуль 10.5: СИСТЕМА РАННЕГО ВОВЛЕЧЕНИЯ (2025) - ВРЕМЕННО ОТКЛЮЧЕНО
# echo ""
# echo -e "${YELLOW}🔄 Запуск тестов системы раннего вовлечения...${NC}"
# run_engagement_test "Модуль 10.5.1: Основная система раннего вовлечения" "Tests/EarlyEngagementSystemTest.swift"
# run_engagement_test "Модуль 10.5.2: Матрица конструирования сообщений" "Tests/MessageConstructionMatrixTest.swift"
# run_engagement_test "Модуль 10.5.3: Статистика раннего вовлечения" "Tests/EngagementStatisticsTest.swift"
# run_engagement_test "Модуль 10.5.4: Система матрицы кнопок" "Tests/ButtonMatrixTest.swift"

# Модуль 11: УНИФИЦИРОВАННАЯ СИСТЕМА АКТИВНОСТИ (2025)
echo ""
echo -e "${YELLOW}🔄 Запуск тестов унифицированной системы активности...${NC}"
if [ -f "test_unified_activity.sh" ]; then
    if bash test_unified_activity.sh; then
        echo -e "${GREEN}✅ Модуль 11: Унифицированная система активности: ВСЕ ТЕСТЫ ПРОШЛИ${NC}"
        # Фиксированное количество тестов унифицированной системы
        UNIFIED_TESTS=18
        TOTAL_TESTS=$((TOTAL_TESTS + UNIFIED_TESTS))
        PASSED_TESTS=$((PASSED_TESTS + UNIFIED_TESTS))
    else
        echo -e "${RED}❌ Модуль 11: Унифицированная система активности: ЕСТЬ ПРОБЛЕМЫ${NC}"
        # Фиксированное количество тестов унифицированной системы
        UNIFIED_TESTS=18
        TOTAL_TESTS=$((TOTAL_TESTS + UNIFIED_TESTS))
        FAILED_TESTS=$((FAILED_TESTS + UNIFIED_TESTS))
    fi
else
    echo -e "${RED}❌ Файл test_unified_activity.sh не найден${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 12: ИНТЕГРАЦИЯ С СИСТЕМОЙ СНА
echo ""
echo -e "${YELLOW}🔄 Запуск теста интеграции с системой сна...${NC}"
if swift Tests/SleepIntegrationTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 12: Интеграция с системой сна: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 12: Интеграция с системой сна: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 13: РЕАЛЬНЫЕ ПОРОГИ СНА И НЕАКТИВНОСТИ
echo ""
echo -e "${YELLOW}🔄 Запуск теста реальных порогов сна и неактивности...${NC}"
if swift Tests/RealSleepThresholdsTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 13: Реальные пороги сна и неактивности: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 13: Реальные пороги сна и неактивности: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 13.5: МАППИНГ ИНДЕКСОВ DEBUG ОКНА (2025)
echo ""
echo -e "${YELLOW}🔄 Запуск теста маппинга индексов debug окна...${NC}"
if swift Tests/DebugWindowMappingTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 13.5: Маппинг индексов debug окна: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 13.5: Маппинг индексов debug окна: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 14: КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ СИСТЕМЫ
echo ""
echo -e "${YELLOW}🔄 Запуск комплексного тестирования унифицированной системы...${NC}"
if swift Tests/ComprehensiveSystemTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 14: Комплексное тестирование системы: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 14: Комплексное тестирование системы: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 15: ТЕСТИРОВАНИЕ ГРАНИЧНЫХ СЛУЧАЕВ
echo ""
echo -e "${YELLOW}🔄 Запуск тестирования граничных случаев (edge cases)...${NC}"
if swift Tests/EdgeCasesTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 15: Тестирование граничных случаев: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 15: Тестирование граничных случаев: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 16: НАГРУЗОЧНОЕ ТЕСТИРОВАНИЕ
echo ""
echo -e "${YELLOW}🔄 Запуск нагрузочного тестирования 4-бандовой системы...${NC}"
if swift Tests/LoadTestingTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 16: Нагрузочное тестирование: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 16: Нагрузочное тестирование: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 17: ТЕСТИРОВАНИЕ СИНХРОНИЗАЦИИ
echo ""
echo -e "${YELLOW}🔄 Запуск тестирования синхронизации системы активности...${NC}"
if swift Tests/SynchronizationTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 17: Тестирование синхронизации: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 17: Тестирование синхронизации: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 18: ФИНАЛЬНАЯ ВАЛИДАЦИЯ
echo ""
echo -e "${YELLOW}🎯 Запуск финальной валидации системы активности...${NC}"
if swift Tests/FinalValidationTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 18: Финальная валидация: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 18: Финальная валидация: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Модуль 19: СИСТЕМНАЯ ИНТЕГРАЦИЯ (КРИТИЧЕСКИ ВАЖНО!)
echo ""
echo -e "${YELLOW}🔧 Запуск системного интеграционного теста...${NC}"
if swift Tests/SystemIntegrationTest.swift > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Модуль 19: Системная интеграция: ТЕСТ ПРОШЕЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Модуль 19: Системная интеграция: ПРОВАЛ${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

echo ""
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "📈 Всего тестов: $TOTAL_TESTS"
echo -e "✅ Прошло: $PASSED_TESTS"
echo -e "❌ Провалено: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    SUCCESS_RATE=100
else
    SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
fi

echo -e "📊 Успешность: ${SUCCESS_RATE}%"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ВСЕ ТЕСТЫ ПРОШЛИ!${NC}"
    echo -e "${GREEN}✅ Система готова к сборке.${NC}"
    exit 0
else
    echo -e "${RED}⚠️ ЕСТЬ ПРОБЛЕМЫ!${NC}"
    echo -e "${RED}❌ Сборка должна быть остановлена.${NC}"
    echo ""
    echo -e "${YELLOW}💡 Исправьте провалившиеся тесты перед продолжением.${NC}"
    exit 1
fi
