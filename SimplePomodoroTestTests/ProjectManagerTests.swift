import XCTest
@testable import uProd

class ProjectManagerTests: XCTestCase {
    
    var projectManager: ProjectManager!
    
    override func setUp() {
        super.setUp()
        projectManager = ProjectManager()
        
        // Очищаем данные перед каждым тестом
        projectManager.clearAllData()
    }
    
    override func tearDown() {
        // Очищаем данные после каждого теста
        projectManager.clearAllData()
        projectManager = nil
        super.tearDown()
    }
    
    // MARK: - CRUD Tests
    
    func testCreateProject() {
        let project = projectManager.createProject(name: "Test Project", type: .work)
        
        XCTAssertEqual(project.name, "Test Project")
        XCTAssertEqual(project.type, .work)
        XCTAssertTrue(project.isActive)
        XCTAssertFalse(project.isArchived)
        
        let allProjects = projectManager.getActiveProjects()
        XCTAssertEqual(allProjects.count, 1)
        XCTAssertEqual(allProjects.first?.id, project.id)
    }
    
    func testUpdateProject() {
        var project = projectManager.createProject(name: "Original Name", type: .work)
        project.name = "Updated Name"
        project.type = .personal
        
        projectManager.updateProject(project)
        
        let updatedProject = projectManager.getProject(by: project.id)
        XCTAssertEqual(updatedProject?.name, "Updated Name")
        XCTAssertEqual(updatedProject?.type, .personal)
    }
    
    func testDeleteProject() {
        let project = projectManager.createProject(name: "To Delete", type: .work)
        
        // Добавляем в избранное
        projectManager.addToFavorites(project)
        XCTAssertTrue(projectManager.isFavorite(project))
        
        // Удаляем проект
        projectManager.deleteProject(project)
        
        // Проверяем, что проект удален
        XCTAssertNil(projectManager.getProject(by: project.id))
        XCTAssertEqual(projectManager.getActiveProjects().count, 0)
        XCTAssertFalse(projectManager.isFavorite(project))
    }
    
    // MARK: - Favorites Tests
    
    func testAddToFavorites() {
        let project = projectManager.createProject(name: "Favorite Project", type: .work)
        
        projectManager.addToFavorites(project)
        
        XCTAssertTrue(projectManager.isFavorite(project))
        
        let favorites = projectManager.getFavoriteProjects()
        XCTAssertEqual(favorites.count, 1)
        XCTAssertEqual(favorites.first?.id, project.id)
    }
    
    func testRemoveFromFavorites() {
        let project = projectManager.createProject(name: "Favorite Project", type: .work)
        
        projectManager.addToFavorites(project)
        XCTAssertTrue(projectManager.isFavorite(project))
        
        projectManager.removeFromFavorites(project)
        XCTAssertFalse(projectManager.isFavorite(project))
        
        let favorites = projectManager.getFavoriteProjects()
        XCTAssertEqual(favorites.count, 0)
    }
    
    func testFavoritesLimit() {
        // Создаем 6 проектов
        var projects: [Project] = []
        for i in 1...6 {
            let project = projectManager.createProject(name: "Project \(i)", type: .work)
            projects.append(project)
        }
        
        // Добавляем первые 5 в избранное
        for i in 0..<5 {
            projectManager.addToFavorites(projects[i])
        }
        
        XCTAssertEqual(projectManager.getFavoriteProjects().count, 5)
        
        // Пытаемся добавить 6-й проект
        projectManager.addToFavorites(projects[5])
        
        // Должно остаться 5 проектов
        XCTAssertEqual(projectManager.getFavoriteProjects().count, 5)
        XCTAssertFalse(projectManager.isFavorite(projects[5]))
    }
    
    func testReorderFavorites() {
        let project1 = projectManager.createProject(name: "Project 1", type: .work)
        let project2 = projectManager.createProject(name: "Project 2", type: .work)
        let project3 = projectManager.createProject(name: "Project 3", type: .work)
        
        projectManager.addToFavorites(project1)
        projectManager.addToFavorites(project2)
        projectManager.addToFavorites(project3)
        
        var favorites = projectManager.getFavoriteProjects()
        XCTAssertEqual(favorites[0].id, project1.id)
        XCTAssertEqual(favorites[1].id, project2.id)
        XCTAssertEqual(favorites[2].id, project3.id)
        
        // Перемещаем первый проект на второе место
        projectManager.reorderFavorites(from: 0, to: 1)
        
        favorites = projectManager.getFavoriteProjects()
        XCTAssertEqual(favorites[0].id, project2.id)
        XCTAssertEqual(favorites[1].id, project1.id)
        XCTAssertEqual(favorites[2].id, project3.id)
    }
    
    // MARK: - Archive Tests
    
    func testArchiveProject() {
        let project = projectManager.createProject(name: "To Archive", type: .work)
        
        // Добавляем в избранное
        projectManager.addToFavorites(project)
        XCTAssertTrue(projectManager.isFavorite(project))
        
        // Архивируем
        projectManager.archiveProject(project)
        
        // Проверяем, что проект архивирован
        let archivedProjects = projectManager.getArchivedProjects()
        XCTAssertEqual(archivedProjects.count, 1)
        XCTAssertEqual(archivedProjects.first?.id, project.id)
        
        // Проверяем, что проект не в активных
        let activeProjects = projectManager.getActiveProjects()
        XCTAssertEqual(activeProjects.count, 0)
        
        // Проверяем, что проект удален из избранного
        XCTAssertFalse(projectManager.isFavorite(project))
    }
    
    func testUnarchiveProject() {
        let project = projectManager.createProject(name: "To Unarchive", type: .work)
        
        // Архивируем
        projectManager.archiveProject(project)
        XCTAssertEqual(projectManager.getArchivedProjects().count, 1)
        XCTAssertEqual(projectManager.getActiveProjects().count, 0)
        
        // Разархивируем
        projectManager.unarchiveProject(project)
        
        // Проверяем, что проект снова активен
        let activeProjects = projectManager.getActiveProjects()
        XCTAssertEqual(activeProjects.count, 1)
        XCTAssertEqual(activeProjects.first?.id, project.id)
        
        // Проверяем, что проект не в архиве
        XCTAssertEqual(projectManager.getArchivedProjects().count, 0)
    }
    
    // MARK: - Usage Tracking Tests
    
    func testMarkProjectAsUsed() {
        let project = projectManager.createProject(name: "Used Project", type: .work)
        
        // Изначально lastUsedAt должно быть nil
        XCTAssertNil(project.lastUsedAt)
        
        // Отмечаем как использованный
        projectManager.markProjectAsUsed(project.id)
        
        // Проверяем, что lastUsedAt обновилось
        let updatedProject = projectManager.getProject(by: project.id)
        XCTAssertNotNil(updatedProject?.lastUsedAt)
    }
    
    // MARK: - Filter Tests
    
    func testGetProjectsByType() {
        let workProject = projectManager.createProject(name: "Work Project", type: .work)
        let personalProject = projectManager.createProject(name: "Personal Project", type: .personal)
        let learningProject = projectManager.createProject(name: "Learning Project", type: .learning)
        
        let workProjects = projectManager.getProjects(by: .work)
        XCTAssertEqual(workProjects.count, 1)
        XCTAssertEqual(workProjects.first?.id, workProject.id)
        
        let personalProjects = projectManager.getProjects(by: .personal)
        XCTAssertEqual(personalProjects.count, 1)
        XCTAssertEqual(personalProjects.first?.id, personalProject.id)
        
        let learningProjects = projectManager.getProjects(by: .learning)
        XCTAssertEqual(learningProjects.count, 1)
        XCTAssertEqual(learningProjects.first?.id, learningProject.id)
    }
    
    // MARK: - Default Projects Tests
    
    func testDefaultProjectsCreation() {
        // Очищаем данные
        projectManager.clearAllData()

        // Создаем новый менеджер (должен создать проекты по умолчанию)
        let newProjectManager = ProjectManager()

        let projects = newProjectManager.getActiveProjects()
        XCTAssertEqual(projects.count, 3)

        let favorites = newProjectManager.getFavoriteProjects()
        XCTAssertEqual(favorites.count, 3)

        // Очищаем после теста
        newProjectManager.clearAllData()
    }

    // MARK: - Work/Personal Project Tests

    func testCreateWorkProject() {
        let project = projectManager.createProject(name: "Work Task", type: .work, isWorkRelated: true)

        XCTAssertTrue(project.isWorkRelated)

        let workProjects = projectManager.getWorkProjects()
        XCTAssertEqual(workProjects.count, 1)
        XCTAssertEqual(workProjects.first?.id, project.id)

        let personalProjects = projectManager.getPersonalProjects()
        XCTAssertEqual(personalProjects.count, 0)
    }

    func testCreatePersonalProject() {
        let project = projectManager.createProject(name: "Personal Task", type: .personal, isWorkRelated: false)

        XCTAssertFalse(project.isWorkRelated)

        let personalProjects = projectManager.getPersonalProjects()
        XCTAssertEqual(personalProjects.count, 1)
        XCTAssertEqual(personalProjects.first?.id, project.id)

        let workProjects = projectManager.getWorkProjects()
        XCTAssertEqual(workProjects.count, 0)
    }

    func testAutoWorkRelatedDetection() {
        // Проект типа .work должен автоматически стать рабочим
        let workProject = projectManager.createProject(name: "Auto Work", type: .work)
        XCTAssertTrue(workProject.isWorkRelated)

        // Проект типа .personal должен автоматически стать личным
        let personalProject = projectManager.createProject(name: "Auto Personal", type: .personal)
        XCTAssertFalse(personalProject.isWorkRelated)

        // Проект типа .learning должен автоматически стать личным
        let learningProject = projectManager.createProject(name: "Auto Learning", type: .learning)
        XCTAssertFalse(learningProject.isWorkRelated)
    }

    func testGetProjectsGroupedByWorkType() {
        let workProject1 = projectManager.createProject(name: "Work 1", type: .work, isWorkRelated: true)
        let workProject2 = projectManager.createProject(name: "Work 2", type: .creative, isWorkRelated: true)
        let personalProject1 = projectManager.createProject(name: "Personal 1", type: .personal, isWorkRelated: false)
        let personalProject2 = projectManager.createProject(name: "Personal 2", type: .health, isWorkRelated: false)

        let grouped = projectManager.getProjectsGroupedByWorkType()

        XCTAssertEqual(grouped.work.count, 2)
        XCTAssertEqual(grouped.personal.count, 2)

        let workIds = Set(grouped.work.map { $0.id })
        XCTAssertTrue(workIds.contains(workProject1.id))
        XCTAssertTrue(workIds.contains(workProject2.id))

        let personalIds = Set(grouped.personal.map { $0.id })
        XCTAssertTrue(personalIds.contains(personalProject1.id))
        XCTAssertTrue(personalIds.contains(personalProject2.id))
    }

    func testProjectColors() {
        let project = projectManager.createProject(name: "Colored Project", type: .work, color: "#FF0000")

        XCTAssertEqual(project.color, "#FF0000")
        XCTAssertEqual(project.effectiveColor, "#FF0000")

        // Проект без цвета должен использовать цвет по умолчанию
        let projectWithoutColor = projectManager.createProject(name: "Default Color", type: .work)
        XCTAssertEqual(projectWithoutColor.effectiveColor, "#3B82F6") // Синий для work
    }

    // MARK: - Priority Project Tests (Ichiban)

    func testSetPriorityProject() {
        let project = projectManager.createProject(name: "Priority Project", type: .work)

        // Изначально нет приоритетного проекта
        XCTAssertFalse(projectManager.hasPriorityProject())
        XCTAssertNil(projectManager.getPriorityProject())

        // Устанавливаем приоритетный проект
        projectManager.setPriorityProject(project)

        XCTAssertTrue(projectManager.hasPriorityProject())
        XCTAssertEqual(projectManager.getPriorityProject()?.id, project.id)
        XCTAssertTrue(projectManager.isPriorityProject(project))
    }

    func testSetPriorityProjectById() {
        let project = projectManager.createProject(name: "Priority Project", type: .work)

        // Устанавливаем приоритетный проект по ID
        projectManager.setPriorityProject(by: project.id)

        XCTAssertTrue(projectManager.hasPriorityProject())
        XCTAssertEqual(projectManager.getPriorityProject()?.id, project.id)
    }

    func testResetPriorityProject() {
        let project = projectManager.createProject(name: "Priority Project", type: .work)

        // Устанавливаем и сбрасываем приоритетный проект
        projectManager.setPriorityProject(project)
        XCTAssertTrue(projectManager.hasPriorityProject())

        projectManager.setPriorityProject(nil)
        XCTAssertFalse(projectManager.hasPriorityProject())
        XCTAssertNil(projectManager.getPriorityProject())
    }

    func testPriorityProjectAutoResetOnArchive() {
        let project = projectManager.createProject(name: "Priority Project", type: .work)

        // Устанавливаем приоритетный проект
        projectManager.setPriorityProject(project)
        XCTAssertTrue(projectManager.hasPriorityProject())

        // Архивируем проект
        projectManager.archiveProject(project)

        // Приоритет должен сброситься
        XCTAssertFalse(projectManager.hasPriorityProject())
        XCTAssertNil(projectManager.getPriorityProject())
    }

    func testPriorityProjectAutoResetOnDelete() {
        let project = projectManager.createProject(name: "Priority Project", type: .work)

        // Устанавливаем приоритетный проект
        projectManager.setPriorityProject(project)
        XCTAssertTrue(projectManager.hasPriorityProject())

        // Удаляем проект
        projectManager.deleteProject(project)

        // Приоритет должен сброситься
        XCTAssertFalse(projectManager.hasPriorityProject())
        XCTAssertNil(projectManager.getPriorityProject())
    }

    func testSetInvalidPriorityProject() {
        let project = projectManager.createProject(name: "Test Project", type: .work)

        // Архивируем проект
        projectManager.archiveProject(project)

        // Попытка установить архивированный проект как приоритетный должна игнорироваться
        projectManager.setPriorityProject(project)

        XCTAssertFalse(projectManager.hasPriorityProject())
        XCTAssertNil(projectManager.getPriorityProject())
    }
}
