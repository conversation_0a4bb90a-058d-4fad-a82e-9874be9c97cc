#!/usr/bin/swift

import Foundation

// Структура CompletedInterval для декодирования
struct CompletedInterval: Codable {
    let date: Date
    let duration: TimeInterval
    let projectId: UUID?
    let intervalType: String
}

func checkCurrentData() -> Int {
    let defaults = UserDefaults(suiteName: "com.local.uProd") ?? UserDefaults.standard
    
    if let data = defaults.data(forKey: "completedIntervals") {
        do {
            let intervals = try JSONDecoder().decode([CompletedInterval].self, from: data)
            let testIntervals = intervals.filter { $0.intervalType == "test" }
            
            if !testIntervals.isEmpty {
                print("📊 Найдено \(testIntervals.count) тестовых интервалов:")
                
                let formatter = DateFormatter()
                formatter.dateFormat = "dd.MM HH:mm"
                
                for (index, interval) in testIntervals.enumerated() {
                    let durationMin = Int(interval.duration / 60)
                    print("   \(index + 1). \(formatter.string(from: interval.date)) - \(durationMin)мин")
                }
            }
            
            return testIntervals.count
        } catch {
            print("❌ Ошибка декодирования: \(error)")
            return 0
        }
    }
    
    return 0
}

print("🔍 Мониторинг тестовых данных")
print("=" + String(repeating: "=", count: 40))

let initialCount = checkCurrentData()
print("📋 Начальное количество тестовых интервалов: \(initialCount)")

print("\n🎯 ИНСТРУКЦИЯ:")
print("1. Откройте uProd")
print("2. Выберите '🧪 Тестовые данные для выходных'")
print("3. Нажмите 'Сценарий 3' или 'Сценарий 4'")
print("4. Этот скрипт покажет, добавились ли данные")

print("\n⏳ Ожидание изменений... (нажмите Ctrl+C для выхода)")

var lastCount = initialCount
while true {
    sleep(2)
    let currentCount = checkCurrentData()
    
    if currentCount != lastCount {
        print("\n🔄 ИЗМЕНЕНИЕ ОБНАРУЖЕНО!")
        print("   Было: \(lastCount) интервалов")
        print("   Стало: \(currentCount) интервалов")
        
        if currentCount > lastCount {
            print("   ✅ Данные добавлены!")
        } else {
            print("   🧹 Данные удалены!")
        }
        
        lastCount = currentCount
    }
}
