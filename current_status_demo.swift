#!/usr/bin/env swift

import Cocoa
import Foundation

/// ДЕМОНСТРАЦИЯ ТЕКУЩЕГО СОСТОЯНИЯ СИСТЕМЫ РАННЕГО ВОВЛЕЧЕНИЯ
/// Показывает как рассчитывается текущая планка и состояние системы
class CurrentStatusApp: NSObject, NSApplicationDelegate {
    
    var window: NSWindow!
    var currentBarLabel: NSTextField!
    var daysLabel: NSTextField!
    var timeOfDayLabel: NSTextField!
    var messageLabel: NSTextField!
    var calculationLabel: NSTextField!
    var refreshButton: NSButton!
    var simulateButton: NSButton!
    
    // Симуляция данных
    var currentUserBar: TimeInterval = 25 * 60 // 25 минут по умолчанию
    var lastWorkTime: Date? = nil
    var successHistory: [Bool] = [true, false, true, true, false] // История последних 5 дней
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        setupWindow()
        updateStatus()
    }
    
    func setupWindow() {
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 450),
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "📊 Текущее состояние системы раннего вовлечения"
        window.center()
        window.makeKeyAndOrderFront(nil)
        
        let contentView = NSView(frame: window.contentView!.bounds)
        contentView.autoresizingMask = [.width, .height]
        window.contentView = contentView
        
        var yPosition: CGFloat = 400
        let margin: CGFloat = 20
        let spacing: CGFloat = 20
        
        // Заголовок
        let titleLabel = createLabel(text: "📊 АНАЛИЗ ТЕКУЩЕГО СОСТОЯНИЯ", fontSize: 18, bold: true)
        titleLabel.frame = NSRect(x: margin, y: yPosition, width: 560, height: 25)
        contentView.addSubview(titleLabel)
        yPosition -= 40
        
        // Текущая планка
        currentBarLabel = createLabel(text: "⏱️ Текущая планка: загрузка...", fontSize: 14)
        currentBarLabel.frame = NSRect(x: margin, y: yPosition, width: 560, height: 20)
        contentView.addSubview(currentBarLabel)
        yPosition -= spacing
        
        // Дни без работы
        daysLabel = createLabel(text: "📅 Дни без работы: загрузка...", fontSize: 14)
        daysLabel.frame = NSRect(x: margin, y: yPosition, width: 560, height: 20)
        contentView.addSubview(daysLabel)
        yPosition -= spacing
        
        // Время дня
        timeOfDayLabel = createLabel(text: "🌅 Время дня: загрузка...", fontSize: 14)
        timeOfDayLabel.frame = NSRect(x: margin, y: yPosition, width: 560, height: 20)
        contentView.addSubview(timeOfDayLabel)
        yPosition -= spacing
        
        // Текущее сообщение
        messageLabel = createLabel(text: "💬 Текущее сообщение: загрузка...", fontSize: 14)
        messageLabel.frame = NSRect(x: margin, y: yPosition, width: 560, height: 40)
        messageLabel.maximumNumberOfLines = 2
        contentView.addSubview(messageLabel)
        yPosition -= 50
        
        // Расчет планки
        calculationLabel = createLabel(text: "🧮 Расчет планки: загрузка...", fontSize: 12)
        calculationLabel.frame = NSRect(x: margin, y: yPosition, width: 560, height: 80)
        calculationLabel.maximumNumberOfLines = 4
        contentView.addSubview(calculationLabel)
        yPosition -= 100
        
        // Кнопки
        refreshButton = createButton(title: "🔄 Обновить", color: NSColor.systemBlue)
        refreshButton.frame = NSRect(x: margin, y: yPosition, width: 120, height: 30)
        refreshButton.target = self
        refreshButton.action = #selector(refreshStatus)
        contentView.addSubview(refreshButton)
        
        simulateButton = createButton(title: "🎲 Симулировать день", color: NSColor.systemOrange)
        simulateButton.frame = NSRect(x: margin + 140, y: yPosition, width: 150, height: 30)
        simulateButton.target = self
        simulateButton.action = #selector(simulateDay)
        contentView.addSubview(simulateButton)
        yPosition -= 50
        
        // Информация
        let infoLabel = createLabel(text: "ℹ️ Это демонстрация расчета текущей планки пользователя.\nПланка адаптируется на основе истории успехов и неудач.", fontSize: 11)
        infoLabel.frame = NSRect(x: margin, y: yPosition - 30, width: 560, height: 40)
        infoLabel.maximumNumberOfLines = 3
        contentView.addSubview(infoLabel)
    }
    
    func createLabel(text: String, fontSize: CGFloat, bold: Bool = false) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.isEditable = false
        label.isSelectable = true
        label.backgroundColor = NSColor.clear
        label.textColor = NSColor.labelColor
        
        if bold {
            label.font = NSFont.boldSystemFont(ofSize: fontSize)
        } else {
            label.font = NSFont.systemFont(ofSize: fontSize)
        }
        
        return label
    }
    
    func createButton(title: String, color: NSColor) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.bezelStyle = .rounded
        button.contentTintColor = color
        return button
    }
    
    @objc func refreshStatus() {
        updateStatus()
        print("🔄 Статус обновлен")
    }
    
    @objc func simulateDay() {
        // Симулируем прохождение дня
        let success = Bool.random()
        successHistory.append(success)
        if successHistory.count > 5 {
            successHistory.removeFirst()
        }
        
        // Обновляем планку
        currentUserBar = adaptUserBar(currentBar: currentUserBar, success: success, duration: 25*60)
        
        if success {
            lastWorkTime = Date()
        }
        
        updateStatus()
        
        let result = success ? "✅ УСПЕХ" : "❌ НЕУДАЧА"
        print("🎲 Симуляция дня: \(result)")
    }
    
    func updateStatus() {
        // Текущая планка
        let barMinutes = Int(currentUserBar / 60)
        currentBarLabel.stringValue = "⏱️ Текущая планка: \(barMinutes) минут"
        
        // Дни без работы
        let daysWithoutWork = calculateDaysWithoutWork()
        daysLabel.stringValue = "📅 Дни без работы: \(daysWithoutWork)"
        
        // Время дня
        let timeOfDay = getCurrentTimeOfDay()
        let timeNames = ["Утро (6-12)", "День (12-16)", "Вечер (16-20)", "Поздно (20-6)"]
        timeOfDayLabel.stringValue = "🌅 Время дня: \(timeNames[timeOfDay]) - уровень \(timeOfDay)"
        
        // Текущее сообщение
        let message = getCurrentMessage(vertical: daysWithoutWork, horizontal: timeOfDay)
        let messageDuration = Int(message.duration / 60)
        messageLabel.stringValue = "💬 Текущее сообщение: \"\(message.title)\"\n⏰ Длительность: \(messageDuration) минут"
        
        // Расчет планки
        let successCount = successHistory.filter { $0 }.count
        let successRate = Double(successCount) / Double(successHistory.count)
        calculationLabel.stringValue = """
        🧮 Расчет планки:
        История: \(successHistory.map { $0 ? "✅" : "❌" }.joined(separator: " "))
        Успешность: \(successCount)/\(successHistory.count) = \(String(format: "%.1f", successRate * 100))%
        Базовая планка: 25 мин → Текущая: \(barMinutes) мин
        """
    }
    
    func calculateDaysWithoutWork() -> Int {
        guard let lastWork = lastWorkTime else { return 5 } // Если никогда не работал
        
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: lastWork, to: now)
        return max(0, components.day ?? 0)
    }
    
    func getCurrentTimeOfDay() -> Int {
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch hour {
        case 6..<12: return 0  // Утро
        case 12..<16: return 1 // День
        case 16..<20: return 2 // Вечер
        default: return 3      // Поздно
        }
    }
    
    func getCurrentMessage(vertical: Int, horizontal: Int) -> (title: String, duration: TimeInterval) {
        let titles = [
            ["Продолжим?", "Еще немного?", "Добавим интервал?", "Финальный рывок?"],
            ["Вернемся к работе?", "Начнем день?", "Время работать!", "Не откладываем!"],
            ["Пора возвращаться!", "Хватит откладывать!", "Начинаем сейчас!", "Время действовать!"],
            ["Серьезно, пора работать!", "Уже 3 дня!", "Начинаем немедленно!", "Хватит прокрастинировать!"],
            ["КРИТИЧНО! Начинаем!", "Уже слишком долго!", "СЕЙЧАС ИЛИ НИКОГДА!", "Экстренный старт!"]
        ]
        
        let durations: [TimeInterval] = [15*60, 20*60, 25*60, 30*60]
        
        let safeVertical = min(max(vertical, 0), 4)
        let safeHorizontal = min(max(horizontal, 0), 3)
        
        return (
            title: titles[safeVertical][safeHorizontal],
            duration: durations[safeHorizontal]
        )
    }
    
    func adaptUserBar(currentBar: TimeInterval, success: Bool, duration: TimeInterval) -> TimeInterval {
        let baseBar: TimeInterval = 25 * 60 // 25 минут
        let minBar: TimeInterval = 5 * 60   // 5 минут
        let maxBar: TimeInterval = 60 * 60  // 60 минут
        
        var newBar = currentBar
        
        if success {
            // При успехе увеличиваем планку на 10%
            newBar = currentBar * 1.1
        } else {
            // При неудаче уменьшаем планку на 15%
            newBar = currentBar * 0.85
        }
        
        // Ограничиваем планку разумными пределами
        newBar = max(minBar, min(maxBar, newBar))
        
        return newBar
    }
}

// Запуск приложения
let app = NSApplication.shared
let delegate = CurrentStatusApp()
app.delegate = delegate
app.setActivationPolicy(.regular)
app.run()
