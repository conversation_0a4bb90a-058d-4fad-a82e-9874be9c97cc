# 🚀 Быстрый гид по тестированию uProd

## 🎯 **ГЛАВНЫЕ ПРАВИЛА:**
1. **ОДИН ТЕСТ НА ФУНКЦИЮ** - не создавай дубликаты!
2. **РЕАЛЬНЫЕ ТЕСТЫ ПЕРВЫЕ** - они защищают от регрессий!

### **✅ ПРАВИЛЬНЫЙ ПОРЯДОК:**

#### 1️⃣ **РЕАЛЬНЫЙ ТЕСТ** (защита от регрессий)
```bash
# Создай тест, который читает реальный код
cp Tests/RealAppDelegateLogicTest.swift Tests/MyRealTest.swift
```

#### 2️⃣ **ИНТЕГРАЦИОННЫЙ ТЕСТ** (полный путь)
```bash
# Создай тест полного пути от входа до результата
cp Tests/InformalSessionTest.swift Tests/MyIntegrationTest.swift
```

#### 3️⃣ **MOCK-ТЕСТ** (только алгоритмы)
```bash
# ТОЛЬКО если нужно протестировать чистые алгоритмы
cp Tests/WindowAppearanceTest.swift Tests/MyMockTest.swift
```

## 🚨 **ОПАСНОСТИ:**

### **❌ Mock-тесты = ИЛЛЮЗИЯ БЕЗОПАСНОСТИ**
- Mock-тесты показывают "все хорошо" ✅
- Реальный код сломан ❌
- **Результат:** Ложная уверенность!

### **✅ Реальные тесты = НАСТОЯЩАЯ ЗАЩИТА**
- Читают исходный код приложения
- Ловят реальные поломки
- **Результат:** Реальная защита от регрессий!

## 🧪 **БЫСТРЫЕ КОМАНДЫ:**

```bash
# Запустить все тесты
./test.sh

# Запустить только реальные тесты (приоритет 1)
swift Tests/RealAppDelegateLogicTest.swift
swift Tests/RealWindowLogicTest.swift

# Запустить только интеграционные тесты (приоритет 2)
swift Tests/InformalSessionTest.swift

# Запустить Mock-тесты (приоритет 3)
swift Tests/WindowAppearanceTest.swift
```

## 📋 **ЧЕКЛИСТ ДЛЯ НОВОЙ ФУНКЦИИ:**

- [ ] **ОДИН главный тест:** НЕ создавай дубликаты!
- [ ] **Реальный тест:** Читает код и проверяет логику
- [ ] **Добавлен в test.sh:** Автоматически запускается при сборке
- [ ] **Обновлен TESTING.md:** Документирован новый тест
- [ ] **Удалены старые тесты:** Если заменяешь существующий тест

## 🎯 **ПОМНИ:**
**Цель тестов - защитить от регрессий, а не показать зеленые галочки!**
